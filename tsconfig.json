{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "skipDefaultLibCheck": true, "noErrorTruncation": true, "paths": {"@app/*": ["apps/*/src"], "@app/game-config": ["libs/game-config/src"], "@libs/*": ["libs/*/src"], "@common/*": ["libs/common/src/*"], "@common/redis": ["libs/common/src/redis"], "@shared/*": ["libs/shared/src/*"], "@gateway/*": ["apps/gateway/src/*"], "@auth/*": ["apps/auth/src/*"], "@character/*": ["apps/character/src/*"], "@game/*": ["apps/game/src/*"], "@club/*": ["apps/club/src/*"], "@match/*": ["apps/match/src/*"], "@card/*": ["apps/card/src/*"], "@hero/*": ["apps/hero/src/*"], "@economy/*": ["apps/economy/src/*"], "@social/*": ["apps/social/src/*"], "@activity/*": ["apps/activity/src/*"], "@app/database": ["libs/database/src"], "@app/database/*": ["libs/database/src/*"], "@app/common": ["libs/common/src"], "@app/common/*": ["libs/common/src/*"], "@app/game-config/*": ["libs/game-config/src/*"], "@app/game-constants": ["libs/game-constants/src"], "@app/game-constants/*": ["libs/game-constants/src/*"], "@app/game-utils": ["libs/game-utils/src"], "@app/game-utils/*": ["libs/game-utils/src/*"], "@app/game-types": ["libs/game-types/src"], "@app/game-types/*": ["libs/game-types/src/*"]}}, "include": ["apps/**/*", "libs/**/*"], "exclude": ["node_modules", "dist", "examples", "examples/**/*", "**/examples", "**/examples/**/*", "libs/common/src/redis/examples", "libs/common/src/redis/examples/**/*", "apps/gateway/examples", "apps/gateway/examples/**/*", "apps/auth/examples", "apps/auth/examples/**/*", "**/*.spec.ts", "**/*.e2e-spec.ts", "test/**/*", "scripts/**/*", "docs/**/*", "**/scripts/**/*", "**/tests/**/*", "**/*.example.ts", "**/*.test.ts", "**/test-*.ts", "**/test-*.js", "apps/auth/scripts", "apps/auth/scripts/**/*", "apps/gateway/tests", "apps/gateway/tests/**/*", "libs/common/src/redis/tests", "libs/common/src/redis/tests/**/*"]}