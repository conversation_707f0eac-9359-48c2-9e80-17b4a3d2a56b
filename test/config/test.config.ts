/**
 * 测试配置文件
 * 
 * 包含所有测试相关的配置信息
 */

export interface TestConfig {
  services: {
    auth: {
      baseUrl: string;
      healthEndpoint: string;
    };
    gateway: {
      baseUrl: string;
      websocketUrl: string;
      healthEndpoint: string;
    };
  };
  database: {
    mongodb: {
      uri: string;
      testDatabase: string;
    };
    redis: {
      host: string;
      port: number;
      password: string;
      testDatabase: number;
    };
  };
  auth: {
    jwt: {
      secret: string;
      expiresIn: string;
      algorithm: string;
    };
    testUsers: {
      admin: TestUser;
      user: TestUser;
      manager: TestUser;
    };
  };
  testing: {
    timeout: {
      unit: number;
      integration: number;
      e2e: number;
    };
    retries: {
      default: number;
      network: number;
    };
    delays: {
      short: number;
      medium: number;
      long: number;
    };
  };
  performance: {
    thresholds: {
      responseTime: number;
      throughput: number;
      errorRate: number;
    };
    loadTest: {
      duration: string;
      arrivalRate: number;
      maxVusers: number;
    };
  };
}

export interface TestUser {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  roles: string[];
  permissions: string[];
}

// 默认测试配置
export const testConfig: TestConfig = {
  services: {
    auth: {
      baseUrl: process.env.AUTH_BASE_URL || 'http://localhost:3002',
      healthEndpoint: '/health',
    },
    gateway: {
      baseUrl: process.env.GATEWAY_BASE_URL || 'http://localhost:3001',
      websocketUrl: process.env.GATEWAY_WS_URL || 'ws://localhost:3001',
      healthEndpoint: '/health',
    },
  },
  database: {
    mongodb: {
      uri: process.env.MONGODB_URI || '*****************************************************************',
      testDatabase: 'test',
    },
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6380'),
      password: process.env.REDIS_PASSWORD || 'test123',
      testDatabase: 1,
    },
  },
  auth: {
    jwt: {
      secret: process.env.JWT_SECRET || 'test-jwt-secret-key-for-testing-only',
      expiresIn: '1h',
      algorithm: 'HS256',
    },
    testUsers: {
      admin: {
        username: 'testadmin',
        email: '<EMAIL>',
        password: 'Admin123!@#',
        firstName: 'Test',
        lastName: 'Admin',
        roles: ['admin', 'user'],
        permissions: ['*'],
      },
      user: {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'User123!@#',
        firstName: 'Test',
        lastName: 'User',
        roles: ['user'],
        permissions: ['user:read', 'user:update'],
      },
      manager: {
        username: 'testmanager',
        email: '<EMAIL>',
        password: 'Manager123!@#',
        firstName: 'Test',
        lastName: 'Manager',
        roles: ['manager', 'user'],
        permissions: ['user:read', 'user:update', 'team:manage'],
      },
    },
  },
  testing: {
    timeout: {
      unit: 5000,        // 5秒
      integration: 30000, // 30秒
      e2e: 60000,        // 60秒
    },
    retries: {
      default: 3,
      network: 5,
    },
    delays: {
      short: 100,   // 100ms
      medium: 1000, // 1秒
      long: 5000,   // 5秒
    },
  },
  performance: {
    thresholds: {
      responseTime: 100,  // 100ms
      throughput: 1000,   // 1000 req/s
      errorRate: 0.01,    // 1%
    },
    loadTest: {
      duration: '60s',
      arrivalRate: 10,
      maxVusers: 100,
    },
  },
};

// 环境特定配置
export const getTestConfig = (): TestConfig => {
  const env = process.env.NODE_ENV || 'test';
  
  switch (env) {
    case 'test':
      return testConfig;
    case 'ci':
      return {
        ...testConfig,
        testing: {
          ...testConfig.testing,
          timeout: {
            unit: 10000,
            integration: 60000,
            e2e: 120000,
          },
        },
      };
    default:
      return testConfig;
  }
};

// 测试数据生成器配置
export const testDataConfig = {
  faker: {
    locale: 'zh_CN',
    seed: 12345, // 固定种子确保测试数据一致性
  },
  users: {
    count: 100,
    batchSize: 10,
  },
  games: {
    count: 50,
    batchSize: 5,
  },
  teams: {
    count: 20,
    batchSize: 5,
  },
};

// API端点配置
export const apiEndpoints = {
  auth: {
    register: '/auth/register',
    login: '/auth/login',
    logout: '/auth/logout',
    refresh: '/auth/refresh',
    verifyToken: '/auth/verify-token',
    resetPassword: '/auth/reset-password',
    confirmReset: '/auth/confirm-reset',
    mfa: {
      status: '/auth/mfa/status',
      setup: '/auth/mfa/setup',
      enable: '/auth/mfa/enable',
      disable: '/auth/mfa/disable',
      verify: '/auth/mfa/verify',
      backupCodes: '/auth/mfa/backup-codes',
    },
  },
  users: {
    me: '/users/me',
    profile: '/users/me/profile',
    password: '/users/me/password',
    sessions: '/users/me/sessions',
    list: '/users',
    create: '/users',
    update: '/users/:id',
    delete: '/users/:id',
  },
  gateway: {
    health: '/health',
    metrics: '/metrics',
    proxy: '/api/*',
    websocket: '/socket.io',
  },
};

// WebSocket事件配置
export const websocketEvents = {
  connection: 'connection',
  disconnect: 'disconnect',
  error: 'error',
  auth: {
    authenticate: 'authenticate',
    authenticated: 'authenticated',
    authError: 'auth_error',
  },
  rooms: {
    join: 'join-room',
    leave: 'leave-room',
    joined: 'room-joined',
    left: 'room-left',
    error: 'room-error',
  },
  messages: {
    send: 'send-message',
    receive: 'message',
    broadcast: 'broadcast',
  },
  game: {
    matchStart: 'match-start',
    matchEnd: 'match-end',
    playerAction: 'player-action',
    gameState: 'game-state',
  },
};

// 测试标签和分类
export const testTags = {
  unit: 'unit',
  integration: 'integration',
  e2e: 'e2e',
  performance: 'performance',
  security: 'security',
  smoke: 'smoke',
  regression: 'regression',
  critical: 'critical',
  auth: 'auth',
  gateway: 'gateway',
  websocket: 'websocket',
  database: 'database',
  cache: 'cache',
};

// 测试数据清理配置
export const cleanupConfig = {
  collections: [
    'users',
    'sessions',
    'permissions',
    'roles',
    'securityevents',
    'loginhistories',
  ],
  redis: {
    patterns: [
      'test:*',
      'session:*',
      'cache:*',
      'rate-limit:*',
    ],
  },
  files: [
    'test-logs/*.log',
    'test-results/*.json',
    'test-results/*.html',
  ],
};

export default testConfig;
