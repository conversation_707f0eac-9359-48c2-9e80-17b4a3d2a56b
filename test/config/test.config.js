/**
 * 本机测试配置文件
 */

require('dotenv').config();

const testConfig = {
  // 服务配置
  services: {
    auth: {
      baseUrl: `http://localhost:${process.env.AUTH_PORT || 3002}`,
      port: process.env.AUTH_PORT || 3002
    },
    gateway: {
      baseUrl: `http://localhost:${process.env.GATEWAY_PORT || 3001}`,
      websocketUrl: `ws://localhost:${process.env.GATEWAY_PORT || 3001}`,
      port: process.env.GATEWAY_PORT || 3001
    }
  },

  // 数据库配置
  database: {
    redis: {
      host: process.env.REDIS_HOST || '***************',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || '123456'
    },
    mongodb: {
      uri: process.env.MONGODB_URI || '*****************************************************************'
    }
  },

  // 测试数据
  testData: {
    users: {
      admin: {
        username: 'admin',
        email: '<EMAIL>',
        password: 'Admin123!@#',
        roles: ['admin']
      },
      user: {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'User123!@#',
        roles: ['user']
      },
      moderator: {
        username: 'moderator',
        email: '<EMAIL>',
        password: 'Mod123!@#',
        roles: ['moderator']
      }
    },
    
    // 测试房间
    rooms: {
      public: 'public-room',
      private: 'private-room',
      game: 'game-room-1'
    },

    // 测试消息
    messages: {
      chat: {
        type: 'chat',
        content: 'Hello, this is a test message!'
      },
      system: {
        type: 'system',
        content: 'System notification test'
      },
      game: {
        type: 'game',
        content: 'Game event test',
        data: { action: 'move', position: { x: 10, y: 20 } }
      }
    }
  },

  // 测试配置
  testing: {
    timeout: 10000,
    retries: 3,
    parallel: false,
    
    // 性能基准
    performance: {
      apiResponseTime: 500, // ms
      websocketLatency: 100, // ms
      cacheHitRatio: 0.8,
      concurrentUsers: 100
    },

    // 安全测试配置
    security: {
      bruteForceAttempts: 5,
      rateLimitRequests: 100,
      passwordMinLength: 8,
      tokenExpiryTime: 3600 // seconds
    },

    // 负载测试配置
    load: {
      rampUpTime: 30, // seconds
      duration: 60, // seconds
      maxUsers: 1000,
      requestsPerSecond: 100
    }
  },

  // 环境配置
  environment: {
    nodeEnv: process.env.NODE_ENV || 'test',
    logLevel: process.env.LOG_LEVEL || 'info',
    enableDebug: process.env.DEBUG === 'true',
    enableMetrics: process.env.ENABLE_METRICS === 'true'
  },

  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'test-jwt-secret',
    expiresIn: process.env.JWT_EXPIRES_IN || '1h',
    algorithm: 'HS256'
  },

  // 功能特性开关
  features: {
    mfa: true,
    rbac: true,
    caching: true,
    rateLimit: true,
    circuitBreaker: true,
    loadBalancer: true,
    websocket: true,
    monitoring: true
  },

  // 测试覆盖范围
  coverage: {
    auth: {
      registration: true,
      login: true,
      mfa: true,
      passwordManagement: true,
      sessionManagement: true,
      permissions: true,
      security: true
    },
    gateway: {
      routing: true,
      authentication: true,
      rateLimit: true,
      loadBalancer: true,
      circuitBreaker: true,
      caching: true,
      websocket: true,
      proxy: true,
      monitoring: true
    }
  },

  // 报告配置
  reporting: {
    format: 'json',
    saveToFile: true,
    includeDetails: true,
    outputDir: './test-results',
    
    // 报告类型
    types: {
      unit: true,
      integration: true,
      functional: true,
      performance: true,
      security: true,
      api: true,
      websocket: true
    }
  }
};

module.exports = testConfig;
