/**
 * Artillery负载测试处理器
 * 提供测试过程中需要的辅助函数
 */

const axios = require('axios');

// 预创建的测试用户池
const testUsers = [];
let userIndex = 0;

/**
 * 初始化测试用户池
 */
async function initializeTestUsers() {
  console.log('初始化测试用户池...');
  
  const authBaseUrl = 'http://localhost:3002';
  const userCount = 50; // 预创建50个用户
  
  for (let i = 0; i < userCount; i++) {
    const userData = {
      username: `artillery_user_${i}`,
      email: `artillery${i}@test.com`,
      password: 'Artillery123!@#',
      confirmPassword: 'Artillery123!@#',
      firstName: `Artillery${i}`,
      lastName: 'Test'
    };
    
    try {
      // 尝试注册用户
      await axios.post(`${authBaseUrl}/auth/register`, userData, { timeout: 10000 });
    } catch (error) {
      // 用户可能已存在，忽略错误
    }
    
    try {
      // 登录获取令牌
      const loginResponse = await axios.post(
        `${authBaseUrl}/auth/login`,
        {
          identifier: userData.username,
          password: userData.password
        },
        { timeout: 10000 }
      );
      
      testUsers.push({
        ...userData,
        token: loginResponse.data.data.tokens.accessToken
      });
    } catch (error) {
      console.error(`创建用户 ${userData.username} 失败:`, error.message);
    }
  }
  
  console.log(`成功初始化 ${testUsers.length} 个测试用户`);
}

/**
 * 设置认证信息
 */
function setupAuth(context, events, done) {
  // 如果用户池为空，使用动态创建
  if (testUsers.length === 0) {
    // 动态创建用户
    const randomId = Math.floor(Math.random() * 10000);
    const userData = {
      username: `dynamic_user_${randomId}`,
      email: `dynamic${randomId}@test.com`,
      password: 'Dynamic123!@#'
    };
    
    // 注册并登录
    axios.post('http://localhost:3002/auth/register', {
      ...userData,
      confirmPassword: userData.password,
      firstName: 'Dynamic',
      lastName: 'Test'
    }, { timeout: 10000 })
    .then(() => {
      return axios.post('http://localhost:3002/auth/login', {
        identifier: userData.username,
        password: userData.password
      }, { timeout: 10000 });
    })
    .then(response => {
      context.vars.accessToken = response.data.data.tokens.accessToken;
      context.vars.username = userData.username;
      done();
    })
    .catch(error => {
      // 如果注册失败，尝试登录
      axios.post('http://localhost:3002/auth/login', {
        identifier: userData.username,
        password: userData.password
      }, { timeout: 10000 })
      .then(response => {
        context.vars.accessToken = response.data.data.tokens.accessToken;
        context.vars.username = userData.username;
        done();
      })
      .catch(loginError => {
        console.error('认证设置失败:', loginError.message);
        done();
      });
    });
  } else {
    // 使用预创建的用户
    const user = testUsers[userIndex % testUsers.length];
    userIndex++;
    
    context.vars.accessToken = user.token;
    context.vars.username = user.username;
    done();
  }
}

/**
 * 生成随机字符串
 */
function generateRandomString(length = 8) {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 验证响应数据
 */
function validateResponse(context, events, done) {
  const response = context.response;
  
  if (response && response.body) {
    try {
      const data = JSON.parse(response.body);
      
      // 验证响应格式
      if (!data.success) {
        events.emit('error', new Error(`API响应失败: ${data.message || '未知错误'}`));
      }
      
      // 记录响应时间
      if (context.response.timings) {
        const responseTime = context.response.timings.response;
        if (responseTime > 1000) { // 超过1秒的请求
          console.log(`慢请求检测: ${context.request.url} - ${responseTime}ms`);
        }
      }
      
    } catch (error) {
      events.emit('error', new Error(`响应解析失败: ${error.message}`));
    }
  }
  
  done();
}

/**
 * 错误处理
 */
function handleError(context, events, done) {
  const error = context.error;
  
  if (error) {
    // 记录错误详情
    console.error('请求错误:', {
      url: context.request?.url,
      method: context.request?.method,
      error: error.message,
      code: error.code
    });
    
    // 根据错误类型进行分类
    if (error.code === 'ECONNREFUSED') {
      events.emit('error', new Error('服务连接被拒绝'));
    } else if (error.code === 'ETIMEDOUT') {
      events.emit('error', new Error('请求超时'));
    } else if (error.response && error.response.status >= 500) {
      events.emit('error', new Error('服务器内部错误'));
    }
  }
  
  done();
}

/**
 * 性能监控
 */
function monitorPerformance(context, events, done) {
  const response = context.response;
  
  if (response) {
    const responseTime = response.timings?.response || 0;
    const statusCode = response.statusCode;
    
    // 记录性能指标
    events.emit('histogram', 'response_time', responseTime);
    events.emit('counter', `status_${statusCode}`, 1);
    
    // 检查性能阈值
    if (responseTime > 500) {
      events.emit('counter', 'slow_requests', 1);
    }
    
    if (statusCode >= 400) {
      events.emit('counter', 'error_requests', 1);
    }
  }
  
  done();
}

/**
 * 清理资源
 */
function cleanup(context, events, done) {
  // 清理WebSocket连接
  if (context.ws) {
    context.ws.close();
  }
  
  // 清理其他资源
  if (context.cleanup) {
    context.cleanup.forEach(fn => {
      try {
        fn();
      } catch (error) {
        console.error('清理资源失败:', error.message);
      }
    });
  }
  
  done();
}

/**
 * WebSocket消息处理
 */
function handleWebSocketMessage(context, events, done) {
  if (context.ws) {
    context.ws.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        
        // 记录消息类型
        events.emit('counter', `ws_message_${message.event || 'unknown'}`, 1);
        
        // 验证消息格式
        if (message.error) {
          events.emit('error', new Error(`WebSocket错误: ${message.error}`));
        }
        
      } catch (error) {
        events.emit('error', new Error(`WebSocket消息解析失败: ${error.message}`));
      }
    });
    
    context.ws.on('error', (error) => {
      events.emit('error', new Error(`WebSocket连接错误: ${error.message}`));
    });
  }
  
  done();
}

// 导出函数
module.exports = {
  setupAuth,
  validateResponse,
  handleError,
  monitorPerformance,
  cleanup,
  handleWebSocketMessage,
  
  // 工具函数
  generateRandomString,
  
  // 初始化函数
  initializeTestUsers
};

// 如果直接运行此文件，初始化用户池
if (require.main === module) {
  initializeTestUsers().then(() => {
    console.log('用户池初始化完成');
    process.exit(0);
  }).catch(error => {
    console.error('用户池初始化失败:', error.message);
    process.exit(1);
  });
}
