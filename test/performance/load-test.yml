config:
  target: 'http://localhost:3001'
  phases:
    # 预热阶段
    - duration: 30
      arrivalRate: 5
      name: "预热阶段"
    # 增压阶段
    - duration: 60
      arrivalRate: 10
      rampTo: 50
      name: "增压阶段"
    # 稳定负载阶段
    - duration: 120
      arrivalRate: 50
      name: "稳定负载阶段"
    # 峰值负载阶段
    - duration: 60
      arrivalRate: 100
      name: "峰值负载阶段"
    # 降压阶段
    - duration: 30
      arrivalRate: 100
      rampTo: 10
      name: "降压阶段"
  
  # 性能阈值
  ensure:
    p95: 200  # 95%的请求响应时间应小于200ms
    p99: 500  # 99%的请求响应时间应小于500ms
    maxErrorRate: 1  # 错误率应小于1%
  
  # 全局变量
  variables:
    authBaseUrl: 'http://localhost:3002'
    gatewayBaseUrl: 'http://localhost:3001'
  
  # 处理器
  processor: './load-test-processor.js'

scenarios:
  # 认证服务负载测试
  - name: "认证服务测试"
    weight: 30
    flow:
      - post:
          url: "{{ authBaseUrl }}/auth/register"
          json:
            username: "loadtest_{{ $randomString() }}"
            email: "loadtest{{ $randomString() }}@test.com"
            password: "LoadTest123!@#"
            confirmPassword: "LoadTest123!@#"
            firstName: "Load"
            lastName: "Test"
          capture:
            - json: "$.data.user.username"
              as: "username"
      - post:
          url: "{{ authBaseUrl }}/auth/login"
          json:
            identifier: "{{ username }}"
            password: "LoadTest123!@#"
          capture:
            - json: "$.data.tokens.accessToken"
              as: "accessToken"
      - post:
          url: "{{ authBaseUrl }}/auth/verify-token"
          json:
            token: "{{ accessToken }}"
      - get:
          url: "{{ authBaseUrl }}/auth/profile"
          headers:
            Authorization: "Bearer {{ accessToken }}"

  # 网关服务负载测试
  - name: "网关服务测试"
    weight: 50
    flow:
      # 先获取认证令牌
      - function: "setupAuth"
      - get:
          url: "{{ gatewayBaseUrl }}/health"
      - get:
          url: "{{ gatewayBaseUrl }}/api/users/me"
          headers:
            Authorization: "Bearer {{ accessToken }}"
      - get:
          url: "{{ gatewayBaseUrl }}/api/users/profile"
          headers:
            Authorization: "Bearer {{ accessToken }}"

  # 混合负载测试
  - name: "混合负载测试"
    weight: 20
    flow:
      - function: "setupAuth"
      # 并发请求
      - parallel:
        - get:
            url: "{{ authBaseUrl }}/health"
        - get:
            url: "{{ gatewayBaseUrl }}/health"
        - post:
            url: "{{ authBaseUrl }}/auth/verify-token"
            json:
              token: "{{ accessToken }}"
        - get:
            url: "{{ gatewayBaseUrl }}/api/users/me"
            headers:
              Authorization: "Bearer {{ accessToken }}"

  # WebSocket连接测试
  - name: "WebSocket连接测试"
    weight: 10
    engine: ws
    flow:
      - function: "setupAuth"
      - connect:
          url: "ws://localhost:3001"
          headers:
            Authorization: "Bearer {{ accessToken }}"
      - send:
          payload: |
            {
              "event": "join-room",
              "data": { "roomId": "load-test-room" }
            }
      - think: 5
      - send:
          payload: |
            {
              "event": "send-message",
              "data": {
                "roomId": "load-test-room",
                "type": "chat",
                "content": "Load test message"
              }
            }
      - think: 2
