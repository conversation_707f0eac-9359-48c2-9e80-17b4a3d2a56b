/**
 * 测试工具函数
 * 
 * 提供测试过程中常用的工具函数和辅助方法
 */

import { faker } from '@faker-js/faker';
import * as jwt from 'jsonwebtoken';
import * as speakeasy from 'speakeasy';
import { testConfig, TestUser } from '../config/test.config';
import axios, { AxiosResponse } from 'axios';
import { MongoClient, Db } from 'mongodb';
import Redis from 'ioredis';

// 配置faker
faker.locale = 'zh_CN';
faker.seed(12345);

/**
 * 用户工厂类
 */
export class UserFactory {
  /**
   * 创建测试用户数据
   */
  static create(overrides: Partial<any> = {}): any {
    return {
      username: faker.internet.userName(),
      email: faker.internet.email(),
      password: 'Test123!@#',
      confirmPassword: 'Test123!@#',
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      dateOfBirth: faker.date.birthdate({ min: 18, max: 65, mode: 'age' }),
      phoneNumber: faker.phone.number(),
      country: 'CN',
      language: 'zh',
      timezone: 'Asia/Shanghai',
      gamePreferences: {
        notifications: true,
        autoSave: true,
        theme: 'light',
        gameLanguage: 'zh',
        soundEnabled: true,
        musicEnabled: true,
      },
      ...overrides,
    };
  }

  /**
   * 创建管理员用户
   */
  static createAdmin(overrides: Partial<any> = {}): any {
    return this.create({
      username: 'admin_' + faker.string.alphanumeric(6),
      email: `admin_${faker.string.alphanumeric(6)}@test.com`,
      roles: ['admin', 'user'],
      ...overrides,
    });
  }

  /**
   * 创建普通用户
   */
  static createUser(overrides: Partial<any> = {}): any {
    return this.create({
      username: 'user_' + faker.string.alphanumeric(6),
      email: `user_${faker.string.alphanumeric(6)}@test.com`,
      roles: ['user'],
      ...overrides,
    });
  }

  /**
   * 批量创建用户
   */
  static createBatch(count: number, overrides: Partial<any> = {}): any[] {
    return Array.from({ length: count }, () => this.create(overrides));
  }
}

/**
 * JWT工具类
 */
export class JWTHelper {
  /**
   * 生成测试JWT令牌
   */
  static generateToken(payload: any, options: any = {}): string {
    const defaultPayload = {
      sub: faker.string.uuid(),
      username: faker.internet.userName(),
      email: faker.internet.email(),
      roles: ['user'],
      permissions: ['user:read'],
      iat: Math.floor(Date.now() / 1000),
      ...payload,
    };

    const defaultOptions = {
      secret: testConfig.auth.jwt.secret,
      expiresIn: testConfig.auth.jwt.expiresIn,
      algorithm: testConfig.auth.jwt.algorithm,
      ...options,
    };

    return jwt.sign(defaultPayload, defaultOptions.secret, {
      expiresIn: defaultOptions.expiresIn,
      algorithm: defaultOptions.algorithm as jwt.Algorithm,
    });
  }

  /**
   * 生成过期令牌
   */
  static generateExpiredToken(payload: any = {}): string {
    return this.generateToken(payload, { expiresIn: '-1h' });
  }

  /**
   * 生成无效令牌
   */
  static generateInvalidToken(): string {
    return 'invalid.jwt.token';
  }

  /**
   * 解析JWT令牌
   */
  static decodeToken(token: string): any {
    return jwt.decode(token);
  }

  /**
   * 验证JWT令牌
   */
  static verifyToken(token: string): any {
    return jwt.verify(token, testConfig.auth.jwt.secret);
  }
}

/**
 * MFA工具类
 */
export class MFAHelper {
  /**
   * 生成TOTP密钥
   */
  static generateSecret(): string {
    return speakeasy.generateSecret({
      name: 'Football Manager Test',
      issuer: 'Football Manager',
      length: 32,
    }).base32;
  }

  /**
   * 生成TOTP代码
   */
  static generateTOTPCode(secret: string): string {
    return speakeasy.totp({
      secret,
      encoding: 'base32',
    });
  }

  /**
   * 验证TOTP代码
   */
  static verifyTOTPCode(secret: string, code: string): boolean {
    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token: code,
      window: 2,
    });
  }

  /**
   * 生成备用码
   */
  static generateBackupCodes(count: number = 10): string[] {
    return Array.from({ length: count }, () => 
      faker.string.alphanumeric(8).toUpperCase()
    );
  }
}

/**
 * HTTP客户端工具类
 */
export class HTTPClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseURL: string, defaultHeaders: Record<string, string> = {}) {
    this.baseURL = baseURL;
    this.defaultHeaders = defaultHeaders;
  }

  /**
   * GET请求
   */
  async get(path: string, headers: Record<string, string> = {}): Promise<AxiosResponse> {
    return axios.get(`${this.baseURL}${path}`, {
      headers: { ...this.defaultHeaders, ...headers },
      timeout: testConfig.testing.timeout.integration,
    });
  }

  /**
   * POST请求
   */
  async post(path: string, data: any = {}, headers: Record<string, string> = {}): Promise<AxiosResponse> {
    return axios.post(`${this.baseURL}${path}`, data, {
      headers: { ...this.defaultHeaders, ...headers },
      timeout: testConfig.testing.timeout.integration,
    });
  }

  /**
   * PUT请求
   */
  async put(path: string, data: any = {}, headers: Record<string, string> = {}): Promise<AxiosResponse> {
    return axios.put(`${this.baseURL}${path}`, data, {
      headers: { ...this.defaultHeaders, ...headers },
      timeout: testConfig.testing.timeout.integration,
    });
  }

  /**
   * DELETE请求
   */
  async delete(path: string, headers: Record<string, string> = {}): Promise<AxiosResponse> {
    return axios.delete(`${this.baseURL}${path}`, {
      headers: { ...this.defaultHeaders, ...headers },
      timeout: testConfig.testing.timeout.integration,
    });
  }

  /**
   * 设置认证头
   */
  setAuthToken(token: string): void {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  /**
   * 清除认证头
   */
  clearAuthToken(): void {
    delete this.defaultHeaders['Authorization'];
  }
}

/**
 * 数据库工具类
 */
export class DatabaseHelper {
  private mongoClient: MongoClient | null = null;
  private redisClient: Redis | null = null;

  /**
   * 连接MongoDB
   */
  async connectMongoDB(): Promise<Db> {
    if (!this.mongoClient) {
      this.mongoClient = new MongoClient(testConfig.database.mongodb.uri);
      await this.mongoClient.connect();
    }
    return this.mongoClient.db(testConfig.database.mongodb.testDatabase);
  }

  /**
   * 连接Redis
   */
  async connectRedis(): Promise<Redis> {
    if (!this.redisClient) {
      this.redisClient = new Redis({
        host: testConfig.database.redis.host,
        port: testConfig.database.redis.port,
        password: testConfig.database.redis.password,
        db: testConfig.database.redis.testDatabase,
      });
    }
    return this.redisClient;
  }

  /**
   * 清理MongoDB数据
   */
  async cleanupMongoDB(): Promise<void> {
    const db = await this.connectMongoDB();
    const collections = ['users', 'sessions', 'permissions', 'roles', 'securityevents'];
    
    for (const collection of collections) {
      try {
        await db.collection(collection).deleteMany({});
      } catch (error) {
        // 忽略集合不存在的错误
      }
    }
  }

  /**
   * 清理Redis数据
   */
  async cleanupRedis(): Promise<void> {
    const redis = await this.connectRedis();
    const patterns = ['test:*', 'session:*', 'cache:*', 'rate-limit:*'];
    
    for (const pattern of patterns) {
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    }
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    if (this.mongoClient) {
      await this.mongoClient.close();
      this.mongoClient = null;
    }
    if (this.redisClient) {
      await this.redisClient.quit();
      this.redisClient = null;
    }
  }
}

/**
 * 等待工具函数
 */
export class WaitHelper {
  /**
   * 等待指定时间
   */
  static async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 等待条件满足
   */
  static async waitFor(
    condition: () => Promise<boolean> | boolean,
    timeout: number = 30000,
    interval: number = 1000
  ): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      if (await condition()) {
        return;
      }
      await this.sleep(interval);
    }
    
    throw new Error(`等待条件超时 (${timeout}ms)`);
  }

  /**
   * 等待服务健康
   */
  static async waitForService(url: string, timeout: number = 60000): Promise<void> {
    await this.waitFor(async () => {
      try {
        const response = await axios.get(`${url}/health`, { timeout: 5000 });
        return response.status === 200;
      } catch {
        return false;
      }
    }, timeout);
  }
}

/**
 * 测试数据清理工具
 */
export class TestCleanup {
  private static dbHelper = new DatabaseHelper();

  /**
   * 清理所有测试数据
   */
  static async cleanupAll(): Promise<void> {
    await this.dbHelper.cleanupMongoDB();
    await this.dbHelper.cleanupRedis();
  }

  /**
   * 清理特定集合
   */
  static async cleanupCollection(collectionName: string): Promise<void> {
    const db = await this.dbHelper.connectMongoDB();
    await db.collection(collectionName).deleteMany({});
  }

  /**
   * 清理Redis键
   */
  static async cleanupRedisKeys(pattern: string): Promise<void> {
    const redis = await this.dbHelper.connectRedis();
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
  }

  /**
   * 关闭连接
   */
  static async close(): Promise<void> {
    await this.dbHelper.close();
  }
}

/**
 * 性能测试工具
 */
export class PerformanceHelper {
  /**
   * 测量函数执行时间
   */
  static async measureTime<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
    const startTime = Date.now();
    const result = await fn();
    const duration = Date.now() - startTime;
    return { result, duration };
  }

  /**
   * 并发执行测试
   */
  static async concurrentTest<T>(
    fn: () => Promise<T>,
    concurrency: number,
    iterations: number
  ): Promise<{ results: T[]; durations: number[]; errors: Error[] }> {
    const results: T[] = [];
    const durations: number[] = [];
    const errors: Error[] = [];

    const batches = Math.ceil(iterations / concurrency);
    
    for (let batch = 0; batch < batches; batch++) {
      const batchSize = Math.min(concurrency, iterations - batch * concurrency);
      const promises = Array.from({ length: batchSize }, async () => {
        try {
          const { result, duration } = await this.measureTime(fn);
          results.push(result);
          durations.push(duration);
        } catch (error) {
          errors.push(error as Error);
        }
      });
      
      await Promise.all(promises);
    }

    return { results, durations, errors };
  }

  /**
   * 计算统计信息
   */
  static calculateStats(durations: number[]): {
    min: number;
    max: number;
    avg: number;
    p50: number;
    p95: number;
    p99: number;
  } {
    const sorted = durations.sort((a, b) => a - b);
    const len = sorted.length;
    
    return {
      min: sorted[0],
      max: sorted[len - 1],
      avg: sorted.reduce((a, b) => a + b, 0) / len,
      p50: sorted[Math.floor(len * 0.5)],
      p95: sorted[Math.floor(len * 0.95)],
      p99: sorted[Math.floor(len * 0.99)],
    };
  }
}

// 导出所有工具类
export {
  UserFactory,
  JWTHelper,
  MFAHelper,
  HTTPClient,
  DatabaseHelper,
  WaitHelper,
  TestCleanup,
  PerformanceHelper,
};
