{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "../", "testRegex": ".*\\.integration-spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["apps/**/*.(t|j)s", "libs/**/*.(t|j)s", "!apps/**/*.spec.ts", "!apps/**/*.integration-spec.ts", "!apps/**/*.e2e-spec.ts", "!libs/**/*.spec.ts", "!libs/**/*.integration-spec.ts", "!libs/**/*.e2e-spec.ts", "!**/node_modules/**", "!**/dist/**", "!**/examples/**"], "coverageDirectory": "./test-results/integration-coverage", "testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/test/setup/integration-setup.ts"], "testTimeout": 30000, "maxWorkers": 1, "forceExit": true, "detectOpenHandles": true, "verbose": true, "moduleNameMapping": {"^@common/(.*)$": "<rootDir>/libs/common/src/$1", "^@auth/(.*)$": "<rootDir>/apps/auth/src/$1", "^@gateway/(.*)$": "<rootDir>/apps/gateway/src/$1"}, "globalSetup": "<rootDir>/test/setup/global-setup.ts", "globalTeardown": "<rootDir>/test/setup/global-teardown.ts"}