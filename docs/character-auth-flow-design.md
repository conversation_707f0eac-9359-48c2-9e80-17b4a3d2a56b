# 角色创建/登录流程设计

## 📋 概述

本文档设计了一套清晰、安全的角色创建和登录流程，明确了各个服务的职责边界，确保系统的安全性和可维护性。

## 🏗️ 架构设计原则

### **职责分离**
- **Gateway**: 业务流程编排，API网关
- **Auth服务**: 纯认证逻辑，Token管理
- **Character服务**: 角色数据管理，业务逻辑

### **安全原则**
- 所有角色操作必须基于真实存在的角色
- Token必须包含真实的角色信息
- 严格的权限验证和归属检查

## 🔄 完整业务流程

### **流程1：用户首次进入游戏**

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as 网关
    participant Auth as Auth服务
    participant Character as Character服务

    Note over Client,Character: 1. 账户登录阶段
    Client->>Gateway: POST /auth/login (username, password)
    Gateway->>Auth: 账户认证
    Auth-->>Gateway: 返回账户Token
    Gateway-->>Client: 返回账户Token

    Note over Client,Character: 2. 选择区服阶段
    Client->>Gateway: GET /server-discovery/list
    Gateway-->>Client: 返回区服列表

    Note over Client,Character: 3. 查询角色阶段
    Client->>Gateway: GET /character/list?serverId=server001
    Gateway->>Character: 查询角色列表
    Character-->>Gateway: 返回空列表[]
    Gateway-->>Client: 返回空角色列表

    Note over Client,Character: 4. 创建角色阶段
    Client->>Gateway: POST /character/create
    Gateway->>Character: 创建角色
    Character-->>Gateway: 返回角色信息
    Gateway-->>Client: 返回创建成功

    Note over Client,Character: 5. 角色登录阶段
    Client->>Gateway: POST /character/login
    Gateway->>Character: 验证角色归属
    Character-->>Gateway: 返回角色信息
    Gateway->>Auth: 生成角色Token
    Auth-->>Gateway: 返回角色Token
    Gateway-->>Client: 返回角色Token + 角色信息
```

### **流程2：老用户重新登录**

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as 网关
    participant Auth as Auth服务
    participant Character as Character服务

    Note over Client,Character: 1. 账户登录阶段
    Client->>Gateway: POST /auth/login
    Gateway->>Auth: 账户认证
    Auth-->>Gateway: 返回账户Token
    Gateway-->>Client: 返回账户Token

    Note over Client,Character: 2. 查询历史区服
    Client->>Gateway: GET /user/server-history
    Gateway-->>Client: 返回历史区服列表

    Note over Client,Character: 3. 查询指定区服角色
    Client->>Gateway: GET /character/list?serverId=server001
    Gateway->>Character: 查询角色列表
    Character-->>Gateway: 返回角色列表[角色1, 角色2]
    Gateway-->>Client: 返回角色列表

    Note over Client,Character: 4. 直接角色登录
    Client->>Gateway: POST /character/login (characterId)
    Gateway->>Character: 验证角色归属
    Character-->>Gateway: 返回角色信息
    Gateway->>Auth: 生成角色Token
    Auth-->>Gateway: 返回角色Token
    Gateway-->>Client: 返回角色Token + 角色信息
```

## 🔧 详细API设计

### **1. 角色列表查询**

```http
GET /api/character/list?serverId=server001
Authorization: Bearer {accountToken}
```

**响应**:
```json
{
  "success": true,
  "message": "获取角色列表成功",
  "characters": [
    {
      "characterId": "char_123456789",
      "userId": "user_123",
      "serverId": "server001",
      "name": "传奇教练",
      "level": 15,
      "faceIcon": 12,
      "createdAt": "2024-01-15T10:30:00Z",
      "lastActiveAt": "2024-01-20T15:45:00Z",
      "status": "active",
      "isNewbie": false
    }
  ],
  "totalCharacters": 1,
  "server": {
    "id": "server001",
    "name": "竞技一区",
    "status": "active"
  }
}
```

### **2. 创建角色**

```http
POST /api/character/create
Authorization: Bearer {accountToken}
Content-Type: application/json

{
  "serverId": "server001",
  "name": "传奇教练",
  "faceIcon": 15,
  "qualified": 4
}
```

**响应**:
```json
{
  "success": true,
  "message": "角色创建成功",
  "character": {
    "characterId": "char_987654321",
    "userId": "user_123",
    "serverId": "server001",
    "name": "传奇教练",
    "level": 1,
    "faceIcon": 15,
    "createdAt": "2024-01-21T10:30:00Z",
    "lastActiveAt": "2024-01-21T10:30:00Z",
    "status": "active",
    "isNewbie": true
  },
  "server": {
    "id": "server001",
    "name": "竞技一区",
    "status": "active"
  }
}
```

### **3. 角色登录**

```http
POST /api/character/login
Authorization: Bearer {accountToken}
Content-Type: application/json

{
  "serverId": "server001",
  "characterId": "char_987654321"
}
```

**响应**:
```json
{
  "success": true,
  "message": "角色登录成功",
  "characterToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 14400,
  "expiresAt": "2024-01-21T18:30:00Z",
  "character": {
    "characterId": "char_987654321",
    "userId": "user_123",
    "serverId": "server001",
    "name": "传奇教练",
    "level": 1,
    "faceIcon": 15,
    "createdAt": "2024-01-21T10:30:00Z",
    "lastActiveAt": "2024-01-21T14:30:00Z",
    "status": "active",
    "isNewbie": true
  },
  "server": {
    "id": "server001",
    "name": "竞技一区",
    "status": "active"
  },
  "session": {
    "id": "session_abc123",
    "expiresAt": "2024-01-21T18:30:00Z"
  }
}
```

## 🔒 安全验证机制

### **1. 权限验证层次**

```typescript
// 1. 账户Token验证（所有接口）
@UseGuards(JwtAuthGuard)

// 2. 角色归属验证（角色登录）
async validateCharacterOwnership(userId: string, characterId: string) {
  const character = await this.characterService.getById(characterId);
  if (character.userId !== userId) {
    throw new BadRequestException('角色不属于当前用户');
  }
}

// 3. 角色状态验证
if (character.status === 'banned') {
  throw new BadRequestException('角色已被封禁');
}

// 4. 区服状态验证
const server = await this.validateServerStatus(serverId);
if (server.status !== 'active') {
  throw new BadRequestException('区服暂时不可用');
}
```

### **2. Token安全设计**

```typescript
// 角色Token Payload结构
interface CharacterTokenPayload {
  // 账户信息
  sub: string;              // 用户ID
  username: string;         // 用户名
  
  // 角色信息
  characterId: string;      // 角色ID（必须真实存在）
  characterName: string;    // 角色名称
  serverId: string;         // 区服ID
  
  // 会话信息
  sessionId: string;        // 会话ID
  iat: number;             // 签发时间
  exp: number;             // 过期时间
  
  // 权限信息
  roles: string[];         // 角色权限
  permissions: string[];   // 具体权限
}
```

### **3. 业务规则验证**

```typescript
// 角色创建限制
const MAX_CHARACTERS_PER_SERVER = 3;
const MIN_NAME_LENGTH = 2;
const MAX_NAME_LENGTH = 12;

// 角色名称验证
async validateCharacterName(serverId: string, name: string) {
  // 1. 长度验证
  if (name.length < MIN_NAME_LENGTH || name.length > MAX_NAME_LENGTH) {
    throw new BadRequestException('角色名称长度不符合要求');
  }
  
  // 2. 敏感词过滤
  if (await this.containsSensitiveWords(name)) {
    throw new BadRequestException('角色名称包含敏感词');
  }
  
  // 3. 重名检查
  const existing = await this.characterService.findByName(serverId, name);
  if (existing) {
    throw new ConflictException('角色名称已存在');
  }
}

// 角色数量限制
async validateCharacterLimit(userId: string, serverId: string) {
  const characters = await this.characterService.getList(userId, serverId);
  if (characters.length >= MAX_CHARACTERS_PER_SERVER) {
    throw new BadRequestException(`每个区服最多创建${MAX_CHARACTERS_PER_SERVER}个角色`);
  }
}
```

## 📊 错误处理和用户体验

### **错误码设计**

```typescript
enum CharacterErrorCode {
  // 角色相关
  CHARACTER_NOT_FOUND = 'CHARACTER_NOT_FOUND',
  CHARACTER_NOT_OWNED = 'CHARACTER_NOT_OWNED',
  CHARACTER_BANNED = 'CHARACTER_BANNED',
  CHARACTER_NAME_EXISTS = 'CHARACTER_NAME_EXISTS',
  CHARACTER_LIMIT_EXCEEDED = 'CHARACTER_LIMIT_EXCEEDED',
  
  // 区服相关
  SERVER_NOT_AVAILABLE = 'SERVER_NOT_AVAILABLE',
  SERVER_MAINTENANCE = 'SERVER_MAINTENANCE',
  
  // Token相关
  TOKEN_INVALID = 'TOKEN_INVALID',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
}
```

### **用户友好的错误信息**

```typescript
const ERROR_MESSAGES = {
  [CharacterErrorCode.CHARACTER_NOT_FOUND]: '角色不存在，请检查角色ID',
  [CharacterErrorCode.CHARACTER_NOT_OWNED]: '该角色不属于您，无法操作',
  [CharacterErrorCode.CHARACTER_BANNED]: '角色已被封禁，请联系客服',
  [CharacterErrorCode.CHARACTER_NAME_EXISTS]: '角色名称已存在，请选择其他名称',
  [CharacterErrorCode.CHARACTER_LIMIT_EXCEEDED]: '每个区服最多只能创建3个角色',
  [CharacterErrorCode.SERVER_NOT_AVAILABLE]: '区服暂时不可用，请稍后重试',
  [CharacterErrorCode.SERVER_MAINTENANCE]: '区服正在维护中，请稍后重试',
};
```

## 🎯 总结

### **核心优势**

1. **职责清晰**: 每个服务职责单一，边界明确
2. **安全可靠**: 多层验证，确保数据安全
3. **用户友好**: 清晰的错误提示，良好的用户体验
4. **易于维护**: 模块化设计，便于扩展和维护
5. **性能优化**: 合理的缓存策略，减少不必要的调用

### **技术特点**

- ✅ **零侵入**: Auth服务职责纯化，不处理业务逻辑
- ✅ **高内聚**: 相关功能集中在对应服务中
- ✅ **低耦合**: 服务间通过明确的接口通信
- ✅ **可扩展**: 支持多区服、多角色的扩展需求

这套设计确保了系统的安全性、可维护性和用户体验的完美平衡！
