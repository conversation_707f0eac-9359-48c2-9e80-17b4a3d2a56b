# Auth服务与Gateway职责边界优化指南

## 概述

本文档明确定义了Auth服务和Gateway在多区服游戏系统中的职责边界，作为后续架构优化的约束和指导原则。

## 核心原则

### 1. 职责分离原则
- **Auth服务**：负责认证业务逻辑和Token生命周期管理
- **Gateway**：负责请求路由和Token验证代理

### 2. 数据归属原则
- **Auth服务**：拥有用户认证数据、Token状态、会话信息
- **Gateway**：只缓存验证结果，不拥有认证数据

### 3. 性能优化原则
- 通过**缓存验证结果**而非**重复实现逻辑**来优化性能
- 缓存时间应该短暂（1-5分钟），确保安全性

## Auth服务职责定义

### ✅ Auth服务应该负责的功能

#### 1. 用户认证管理
```typescript
// 用户注册、登录、密码管理
async register(registerDto: RegisterDto): Promise<AuthResult>
async login(loginDto: LoginDto): Promise<AuthResult>
async changePassword(userId: string, oldPassword: string, newPassword: string): Promise<void>
async resetPassword(email: string): Promise<void>
```

#### 2. Token生命周期管理
```typescript
// 账户Token管理
async generateTokenPair(user: User): Promise<TokenPair>
async validateToken(token: string): Promise<TokenValidationResult>
async revokeToken(token: string): Promise<void>
async refreshToken(refreshToken: string): Promise<TokenPair>

// 角色Token管理
async generateCharacterToken(request: CharacterTokenRequest): Promise<CharacterTokenResponse>
async validateCharacterToken(token: string): Promise<CharacterTokenPayload>
async revokeCharacterToken(token: string): Promise<void>
async refreshCharacterToken(refreshToken: string): Promise<CharacterTokenPair>
```

#### 3. Token黑名单管理
```typescript
// Token黑名单的增删查改
async addToBlacklist(token: string, reason: string): Promise<void>
async isTokenBlacklisted(token: string): Promise<boolean>
async removeFromBlacklist(token: string): Promise<void>
async cleanExpiredBlacklist(): Promise<void>
```

#### 4. 会话管理
```typescript
// 用户会话的创建、验证、终止
async createSession(userId: string, deviceInfo: DeviceInfo): Promise<Session>
async validateSession(sessionId: string): Promise<SessionValidationResult>
async terminateSession(sessionId: string): Promise<void>
async terminateAllUserSessions(userId: string): Promise<void>
```

#### 5. 角色权限管理
```typescript
// 角色管理
async createRole(createRoleDto: CreateRoleDto): Promise<Role>
async updateRole(roleId: string, updateRoleDto: UpdateRoleDto): Promise<Role>
async deleteRole(roleId: string): Promise<void>
async getRoles(): Promise<Role[]>
async getRoleById(roleId: string): Promise<Role>
async assignRoleToUser(userId: string, roleId: string): Promise<void>
async revokeRoleFromUser(userId: string, roleId: string): Promise<void>

// 权限管理
async createPermission(createPermissionDto: CreatePermissionDto): Promise<Permission>
async updatePermission(permissionId: string, updatePermissionDto: UpdatePermissionDto): Promise<Permission>
async deletePermission(permissionId: string): Promise<void>
async getPermissions(): Promise<Permission[]>
async getPermissionById(permissionId: string): Promise<Permission>
async checkUserPermission(userId: string, resource: string, action: string): Promise<boolean>
async getUserPermissions(userId: string): Promise<string[]>
```

#### 6. 系统管理功能
```typescript
// 管理员仪表板
async getAdminDashboard(adminId: string): Promise<AdminDashboardDto>
async getSystemStatus(): Promise<SystemStatusDto>
async getSystemHealth(): Promise<HealthCheckDto>
async getSystemMetrics(): Promise<SystemMetricsDto>

// 用户管理
async getUsers(filters: UserFilterDto): Promise<User[]>
async getUserById(userId: string): Promise<User>
async updateUserStatus(userId: string, status: UserStatus): Promise<void>
async lockUser(userId: string, reason: string): Promise<void>
async unlockUser(userId: string): Promise<void>
async resetUserPassword(userId: string): Promise<string>
async terminateUserSession(userId: string, sessionId: string): Promise<void>
async bulkUserOperations(operations: BulkUserOperationDto[]): Promise<void>

// 系统维护
async performMaintenance(adminId: string, actionDto: AdminActionDto): Promise<any>
async createSystemBackup(adminId: string): Promise<BackupResult>
async getSystemLogs(options: LogQueryOptions): Promise<SystemLog[]>
async getSystemSettings(): Promise<SystemSettings>
async updateSystemSettings(adminId: string, settings: SystemSettings): Promise<void>

// 审计管理
async getAuditLogs(filters: AuditLogFilterDto): Promise<AuditLog[]>
async exportAuditData(filters: AuditLogFilterDto): Promise<Buffer>

// 统计分析
async getStatisticsOverview(): Promise<StatisticsOverviewDto>
async getUserStatistics(timeRange: TimeRangeDto): Promise<UserStatisticsDto>
async getSessionStatistics(timeRange: TimeRangeDto): Promise<SessionStatisticsDto>
async getSecurityStatistics(timeRange: TimeRangeDto): Promise<SecurityStatisticsDto>
async getPerformanceStatistics(timeRange: TimeRangeDto): Promise<PerformanceStatisticsDto>
```

### ❌ Auth服务不应该负责的功能
- 请求路由和转发
- API网关功能
- 业务数据聚合
- 客户端会话状态管理
- 游戏业务逻辑

## Gateway职责定义

### ✅ Gateway应该负责的功能

#### 1. 请求认证代理
```typescript
// 提取Token并调用Auth服务验证
@Injectable()
export class AuthProxyGuard implements CanActivate {
  constructor(
    private readonly authService: AuthService,
    private readonly userContextInjector: UserContextInjector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    // 1. 提取Token
    const token = this.extractToken(request);
    if (!token) {
      throw new UnauthorizedException('Token not found');
    }

    // 2. 调用Auth服务验证（可缓存结果）
    const validationResult = await this.validateTokenWithCache(token);
    if (!validationResult.valid) {
      throw new UnauthorizedException('Invalid token');
    }

    // 3. 注入用户信息到请求对象
    this.userContextInjector.injectUserContext(request, validationResult);

    // 4. 检查Token作用域（如果接口有要求）
    const requiredScope = this.getRequiredScope(context);
    if (requiredScope && validationResult.scope !== requiredScope) {
      throw new UnauthorizedException(
        `Token scope mismatch. Required: ${requiredScope}, Got: ${validationResult.scope}`
      );
    }

    return true;
  }

  private extractToken(request: Request): string | null {
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    return null;
  }

  private getRequiredScope(context: ExecutionContext): 'account' | 'character' | null {
    // 从装饰器获取要求的Token作用域
    return this.reflector.get<'account' | 'character'>('tokenScope', context.getHandler());
  }
}
```

#### 2. 验证结果缓存
```typescript
// 缓存Auth服务的验证结果以提高性能
async validateTokenWithCache(token: string): Promise<ValidationResult> {
  const cacheKey = `token_validation:${this.hashToken(token)}`;
  
  // 检查缓存
  const cached = await this.redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // 调用Auth服务
  const result = await this.authServiceClient.validateToken(token);
  
  // 缓存结果（短时间）
  await this.redis.setex(cacheKey, 300, JSON.stringify(result)); // 5分钟
  
  return result;
}
```

#### 3. 用户信息注入
```typescript
// 将验证后的用户信息注入到请求对象中，供下游服务使用
@Injectable()
export class UserContextInjector {

  injectUserContext(request: Request, validationResult: ValidationResult): void {
    // 注入基础用户信息
    request.user = {
      id: validationResult.user.id,
      username: validationResult.user.username,
      email: validationResult.user.email,
      roles: validationResult.user.roles || [],
      permissions: validationResult.user.permissions || [],
      tokenScope: validationResult.scope,
      sessionId: validationResult.sessionId,
      deviceId: validationResult.deviceId,
    };

    // 如果是角色Token，注入角色上下文
    if (validationResult.scope === 'character') {
      request.character = {
        characterId: validationResult.characterId,
        serverId: validationResult.serverId,
        characterName: validationResult.characterName,
      };
    }

    // 注入请求元数据
    request.authContext = {
      authenticated: true,
      authMethod: 'jwt',
      tokenType: validationResult.scope,
      validatedAt: new Date(),
    };
  }
}
```

#### 4. 请求路由和转发
```typescript
// 根据认证结果路由请求到相应的微服务
async routeRequest(request: AuthenticatedRequest): Promise<void> {
  const targetService = this.determineTargetService(request.path);

  // 确保用户信息已注入
  if (!request.user) {
    throw new UnauthorizedException('User context not found');
  }

  // 添加内部服务调用头
  const headers = {
    ...request.headers,
    'X-User-Id': request.user.id,
    'X-User-Roles': request.user.roles.join(','),
    'X-Token-Scope': request.user.tokenScope,
  };

  if (request.character) {
    headers['X-Character-Id'] = request.character.characterId;
    headers['X-Server-Id'] = request.character.serverId;
  }

  return await this.microserviceClient.forward(targetService, {
    ...request,
    headers
  });
}
```

#### 5. 跨服务数据聚合
```typescript
// 聚合多个微服务的数据为客户端提供统一接口
async getServerListWithHistory(userId: string): Promise<ServerListResponse> {
  const [serverList, userHistory] = await Promise.all([
    this.serverDiscoveryService.getAvailableServers(),
    this.profileService.getUserServerHistory(userId)
  ]);

  return this.mergeServerListWithHistory(serverList, userHistory);
}
```

### ❌ Gateway不应该负责的功能
- Token生成和签发
- Token黑名单管理
- 用户会话创建和管理
- 用户认证业务逻辑
- 密码验证和管理

## Gateway核心职责总结

Gateway在认证流程中的四个核心职责：

### 1. ✅ Token提取
- 从HTTP Header中提取Bearer Token
- 从WebSocket连接中提取认证信息
- 支持多种Token格式（JWT、API Key等）

### 2. ✅ 调用Auth服务验证
- 将Token发送给Auth服务进行验证
- 缓存验证结果以提高性能
- 处理验证失败的情况

### 3. ✅ 用户信息注入
- 将验证后的用户信息注入到请求对象
- 设置标准的微服务间传递Header
- 确保下游服务能够获取用户上下文

### 4. ✅ 请求路由
- 根据认证结果路由到相应的微服务
- 传递用户上下文信息
- 处理路由失败和降级

## 接口设计规范

### Auth服务对外接口

#### 1. Token验证接口
```typescript
// POST /api/auth/validate-token
// 供Gateway调用的Token验证接口
interface TokenValidationRequest {
  token: string;
  requiredScope?: 'account' | 'character';
}

interface TokenValidationResponse {
  valid: boolean;
  user?: UserInfo;
  scope?: string;
  expiresAt?: Date;
  error?: string;
}
```

#### 2. Token管理接口
```typescript
// POST /api/auth/revoke-token
// Token撤销接口
interface TokenRevocationRequest {
  token: string;
  reason: string;
}

// POST /api/auth/refresh-token
// Token刷新接口
interface TokenRefreshRequest {
  refreshToken: string;
}
```

#### 3. 角色权限管理接口
```typescript
// 角色管理接口
// GET /api/auth/roles - 获取角色列表
// POST /api/auth/roles - 创建角色
// PUT /api/auth/roles/:id - 更新角色
// DELETE /api/auth/roles/:id - 删除角色
// POST /api/auth/roles/:id/assign - 分配角色给用户
// DELETE /api/auth/roles/:id/revoke - 撤销用户角色

interface CreateRoleRequest {
  name: string;
  description: string;
  permissions: string[];
  inherits?: string[];
  priority?: number;
}

interface AssignRoleRequest {
  userId: string;
  roleId: string;
}

// 权限管理接口
// GET /api/auth/permissions - 获取权限列表
// POST /api/auth/permissions - 创建权限
// PUT /api/auth/permissions/:id - 更新权限
// DELETE /api/auth/permissions/:id - 删除权限

interface CreatePermissionRequest {
  resource: string;
  action: string;
  name: string;
  description: string;
  category?: string;
  level?: number;
}

// 权限检查接口
// POST /api/auth/check-permission
interface PermissionCheckRequest {
  userId: string;
  resource: string;
  action: string;
  context?: Record<string, any>;
}

interface PermissionCheckResponse {
  allowed: boolean;
  reason?: string;
  matchedPermissions?: string[];
}
```

#### 4. 管理员功能接口
```typescript
// 管理员仪表板
// GET /api/auth/admin/dashboard
interface AdminDashboardResponse {
  systemOverview: {
    totalUsers: number;
    activeUsers: number;
    totalSessions: number;
    activeSessions: number;
    systemUptime: number;
    lastBackup: Date;
  };
  userMetrics: {
    newUsersToday: number;
    newUsersThisWeek: number;
    newUsersThisMonth: number;
    userGrowthRate: number;
  };
  securityMetrics: {
    failedLoginAttempts: number;
    blockedIPs: number;
    suspiciousActivities: number;
    mfaEnabledUsers: number;
  };
  recentActivities: ActivityLog[];
  systemAlerts: SystemAlert[];
}

// 用户管理
// GET /api/auth/admin/users - 获取用户列表
// GET /api/auth/admin/users/:id - 获取用户详情
// PUT /api/auth/admin/users/:id/status - 更新用户状态
// POST /api/auth/admin/users/:id/lock - 锁定用户
// DELETE /api/auth/admin/users/:id/lock - 解锁用户
// POST /api/auth/admin/users/:id/reset-password - 重置密码
// DELETE /api/auth/admin/users/:id/sessions/:sessionId - 终止会话

interface UserManagementRequest {
  status?: 'active' | 'inactive' | 'suspended';
  lockReason?: string;
  roles?: string[];
}

// 系统管理
// GET /api/auth/admin/system/status - 系统状态
// GET /api/auth/admin/system/health - 健康检查
// POST /api/auth/admin/system/maintenance - 系统维护
// POST /api/auth/admin/system/backup - 创建备份

interface SystemMaintenanceRequest {
  action: 'cleanup' | 'optimize' | 'restart' | 'update';
  parameters?: Record<string, any>;
  scheduledTime?: Date;
}

// 审计管理
// GET /api/auth/admin/audit/logs - 审计日志
// POST /api/auth/admin/audit/export - 导出审计数据

interface AuditLogFilter {
  startDate?: Date;
  endDate?: Date;
  userId?: string;
  action?: string;
  resource?: string;
  ipAddress?: string;
}

// 统计分析
// GET /api/auth/admin/statistics/overview - 统计概览
// GET /api/auth/admin/statistics/users - 用户统计
// GET /api/auth/admin/statistics/sessions - 会话统计
// GET /api/auth/admin/statistics/security - 安全统计

interface StatisticsRequest {
  timeRange: 'day' | 'week' | 'month' | 'quarter' | 'year';
  startDate?: Date;
  endDate?: Date;
  groupBy?: 'hour' | 'day' | 'week' | 'month';
}
```

### Gateway对外接口

#### 1. 统一认证入口
```typescript
// POST /api/gateway/auth/login
// 代理到Auth服务的登录接口
async login(@Body() loginDto: LoginDto): Promise<LoginResponse> {
  return await this.authServiceClient.login(loginDto);
}
```

### 用户信息注入规范

#### 1. 注入的用户信息结构
```typescript
// 注入到request.user的标准结构
interface InjectedUser {
  id: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  tokenScope: 'account' | 'character';
  sessionId: string;
  deviceId?: string;
}

// 角色Token时额外注入到request.character
interface InjectedCharacter {
  characterId: string;
  serverId: string;
  characterName?: string;
}

// 认证上下文信息注入到request.authContext
interface InjectedAuthContext {
  authenticated: boolean;
  authMethod: 'jwt' | 'api_key' | 'session';
  tokenType: 'account' | 'character';
  validatedAt: Date;
  expiresAt?: Date;
}
```

#### 2. 微服务间传递的Header规范
```typescript
// Gateway向下游微服务传递的标准Header
interface MicroserviceHeaders {
  'X-User-Id': string;           // 用户ID
  'X-User-Roles': string;        // 用户角色（逗号分隔）
  'X-Token-Scope': string;       // Token作用域
  'X-Session-Id': string;        // 会话ID
  'X-Character-Id'?: string;     // 角色ID（角色Token时）
  'X-Server-Id'?: string;        // 服务器ID（角色Token时）
  'X-Auth-Method': string;       // 认证方式
  'X-Request-Id': string;        // 请求追踪ID
}
```

#### 3. 下游服务获取用户信息的装饰器
```typescript
// 下游微服务使用的装饰器
@Injectable()
export class CurrentUserDecorator {
  createParamDecorator() {
    return createParamDecorator((data: unknown, ctx: ExecutionContext) => {
      const request = ctx.switchToHttp().getRequest();

      // 从Gateway注入的信息构建用户对象
      return {
        id: request.headers['x-user-id'],
        username: request.user?.username,
        email: request.user?.email,
        roles: request.headers['x-user-roles']?.split(',') || [],
        tokenScope: request.headers['x-token-scope'],
        sessionId: request.headers['x-session-id'],
        characterId: request.headers['x-character-id'],
        serverId: request.headers['x-server-id'],
      };
    });
  }
}

// 使用示例
@Get('profile')
async getProfile(@CurrentUser() user: InjectedUser) {
  return await this.userService.getProfile(user.id);
}
```

#### 2. 数据聚合接口
```typescript
// GET /api/gateway/user/profile
// 聚合用户相关数据
async getUserProfile(@CurrentUser() user: User): Promise<UserProfileResponse> {
  const [profile, serverHistory, preferences] = await Promise.all([
    this.profileService.getUserProfile(user.id),
    this.profileService.getUserServerHistory(user.id),
    this.profileService.getUserPreferences(user.id)
  ]);
  
  return { profile, serverHistory, preferences };
}
```

## 性能优化策略

### 1. 缓存策略
```typescript
// Gateway缓存配置
const CACHE_CONFIG = {
  TOKEN_VALIDATION: {
    ttl: 300, // 5分钟
    key: (token: string) => `token_validation:${hashToken(token)}`
  },
  USER_INFO: {
    ttl: 600, // 10分钟
    key: (userId: string) => `user_info:${userId}`
  },
  PERMISSIONS: {
    ttl: 1800, // 30分钟
    key: (userId: string) => `permissions:${userId}`
  }
};
```

### 2. 批量验证优化
```typescript
// 支持批量Token验证以减少网络调用
async validateTokensBatch(tokens: string[]): Promise<ValidationResult[]> {
  const uncachedTokens = await this.filterUncachedTokens(tokens);
  
  if (uncachedTokens.length > 0) {
    const results = await this.authServiceClient.validateTokensBatch(uncachedTokens);
    await this.cacheValidationResults(results);
  }
  
  return await this.getValidationResults(tokens);
}
```

## 安全约束

### 1. Token传输安全
- Gateway与Auth服务间通信必须使用HTTPS
- Token在缓存中应该使用哈希值作为key，不直接存储原始Token
- 缓存的验证结果不应包含敏感信息

### 2. 缓存安全
```typescript
// 安全的Token哈希
function hashToken(token: string): string {
  return crypto.createHash('sha256').update(token).digest('hex').substring(0, 16);
}

// 安全的缓存数据
interface CachedValidationResult {
  valid: boolean;
  userId: string;
  scope: string;
  expiresAt: number;
  // 不缓存敏感信息如email、phone等
}
```

### 3. 用户信息注入安全
```typescript
// 安全的用户信息注入
@Injectable()
export class SecureUserContextInjector {

  injectUserContext(request: Request, validationResult: ValidationResult): void {
    // 1. 验证注入数据的完整性
    if (!validationResult.user?.id) {
      throw new UnauthorizedException('Invalid user data in validation result');
    }

    // 2. 过滤敏感信息，只注入必要的用户信息
    request.user = {
      id: validationResult.user.id,
      username: validationResult.user.username,
      // 不注入密码、手机号等敏感信息
      roles: this.sanitizeRoles(validationResult.user.roles),
      permissions: this.sanitizePermissions(validationResult.user.permissions),
      tokenScope: validationResult.scope,
      sessionId: validationResult.sessionId,
    };

    // 3. 防止Header注入攻击
    const sanitizedHeaders = this.sanitizeHeaders({
      'X-User-Id': validationResult.user.id,
      'X-User-Roles': validationResult.user.roles?.join(',') || '',
      'X-Token-Scope': validationResult.scope,
      'X-Session-Id': validationResult.sessionId,
    });

    // 4. 添加请求签名防止篡改
    const signature = this.generateRequestSignature(request.user);
    request.headers['X-User-Signature'] = signature;
  }

  private sanitizeRoles(roles: string[]): string[] {
    // 验证角色名称格式，防止注入攻击
    return roles?.filter(role => /^[a-zA-Z0-9_-]+$/.test(role)) || [];
  }

  private sanitizePermissions(permissions: string[]): string[] {
    // 验证权限格式，防止注入攻击
    return permissions?.filter(perm => /^[a-zA-Z0-9_:-]+$/.test(perm)) || [];
  }

  private sanitizeHeaders(headers: Record<string, string>): Record<string, string> {
    const sanitized = {};
    for (const [key, value] of Object.entries(headers)) {
      // 移除危险字符，防止Header注入
      sanitized[key] = value.replace(/[\r\n\x00]/g, '');
    }
    return sanitized;
  }

  private generateRequestSignature(user: any): string {
    // 生成请求签名，下游服务可以验证用户信息的完整性
    const payload = `${user.id}:${user.tokenScope}:${Date.now()}`;
    return crypto.createHmac('sha256', process.env.INTERNAL_SECRET).update(payload).digest('hex');
  }
}
```

### 3. 故障处理
```typescript
// Gateway的降级策略
async validateTokenWithFallback(token: string): Promise<ValidationResult> {
  try {
    return await this.validateTokenWithCache(token);
  } catch (error) {
    // Auth服务不可用时的降级策略
    if (this.isAuthServiceUnavailable(error)) {
      return await this.handleAuthServiceDowngrade(token);
    }
    throw error;
  }
}
```

## 迁移计划

### 阶段1：移除Gateway中的越权功能（1-2天）
1. 删除Gateway中的Token生成逻辑
2. 删除Gateway中的Token黑名单管理
3. 删除Gateway中的会话管理功能
4. 删除Gateway中的用户认证逻辑
5. 保留Auth服务中的角色权限管理功能
6. 保留Auth服务中的管理员功能模块

### 阶段2：实现正确的代理模式（2-3天）
1. 实现Token验证代理
2. 实现验证结果缓存
3. 更新所有认证守卫
4. 测试性能和功能

### 阶段3：优化和监控（1-2天）
1. 添加性能监控
2. 优化缓存策略
3. 添加降级机制
4. 完善错误处理

## 监控指标

### Auth服务监控
- Token生成速率
- Token验证成功率
- Token黑名单命中率
- 会话创建/销毁速率
- 角色权限查询频率
- 权限检查成功率
- 管理员操作频率
- 系统维护执行状态
- 用户管理操作统计
- 审计日志生成速率

### Gateway监控
- 认证代理响应时间
- 缓存命中率
- Auth服务调用频率
- 认证失败率

## 相关服务职责说明

### User-History服务重新定位

基于客户端体验分析，user-history应该重新定位为：

#### 1. 数据特征
- **非实时数据** - 定时同步即可，不需要实时更新
- **展示用途** - 主要用于服务器选择界面的用户体验优化
- **聚合数据** - 来自多个区服的角色数据快照

#### 2. 架构设计
```typescript
// 独立的Profile服务负责用户历史数据
apps/profile/
├── src/
│   ├── domain/
│   │   ├── user-server-history/    # 用户区服历史
│   │   ├── character-snapshots/    # 角色快照
│   │   └── user-preferences/       # 用户偏好
│   └── infrastructure/
│       └── sync/                   # 定时同步工具
```

#### 3. 数据同步策略
- **管理后台** - 维护服务器列表和配置
- **定时同步工具** - 从各区服同步用户游玩数据
- **Gateway** - 只提供查询接口，不管理数据

#### 4. 接口设计
```typescript
// Gateway提供的聚合接口
GET /api/gateway/server-discovery/list
Response: {
  servers: ServerInfo[];           // 实时服务器列表
  userHistory: UserServerHistory;  // 用户历史快照
  recommendations: string[];       // 推荐服务器
}
```

## 总结

通过明确Auth服务和Gateway的职责边界，我们可以：

1. **提高系统安全性** - 认证逻辑集中在Auth服务，减少安全风险
2. **提升性能** - 通过合理的缓存策略优化响应时间
3. **增强可维护性** - 清晰的职责分离便于开发和维护
4. **保证可扩展性** - 各服务可以独立扩展和优化

## Auth服务完整职责总结

Auth服务作为认证授权的核心服务，应该负责以下完整功能：

### ✅ 核心认证功能
- 用户注册、登录、密码管理
- JWT Token生命周期管理
- 会话管理和状态跟踪
- 多因素认证(MFA)支持

### ✅ 角色权限管理
- 角色的创建、更新、删除、查询
- 权限的创建、更新、删除、查询
- 用户角色分配和撤销
- 权限检查和验证
- 基于资源和操作的细粒度权限控制

### ✅ 系统管理功能
- 管理员仪表板和系统概览
- 用户管理（状态更新、锁定解锁、密码重置）
- 系统维护和备份管理
- 系统配置和设置管理
- 审计日志管理和导出
- 统计分析和报表生成

### ❌ 不应该负责的功能
- 业务数据管理（如角色快照、游戏统计）
- 跨服务数据聚合
- 请求路由和转发
- 客户端状态管理

这个架构设计将作为后续所有认证相关开发的约束和指导原则。
