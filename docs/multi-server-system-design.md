# 分区分服系统设计文档

## 📋 概述

本文档详细设计足球经理游戏的分区分服系统，支持玩家在不同服务器间选择游戏，实现数据隔离、跨服功能、合服操作等核心需求。

## 🎯 核心需求分析

### **1. 基础功能需求**
- ✅ **账号统一管理**: 一个账号可以在多个区服游戏
- ✅ **区服列表展示**: 显示所有可用区服及其状态
- ✅ **区服状态管理**: 新服、开启中、维护中、即将合服等状态
- ✅ **区服选择**: 玩家可以选择进入指定区服
- ✅ **历史记录**: 记录玩家曾经游戏过的区服
- ✅ **最后游戏区服**: 记录并快速进入上次游戏的区服

### **2. 数据隔离需求**
- ✅ **完全数据隔离**: 同一账号在不同区服的数据完全独立
- ✅ **独立游戏进度**: 俱乐部、球员、比赛记录等数据按区服隔离
- ✅ **独立经济系统**: 金币、道具、交易记录按区服隔离
- ✅ **独立社交系统**: 好友、公会、聊天记录按区服隔离

### **3. 跨服功能需求**
- ✅ **跨服排行榜**: 支持全区服排行榜展示
- ✅ **跨服战斗**: 支持不同区服玩家间的对战
- ✅ **跨服活动**: 支持全区服参与的特殊活动
- ✅ **跨服聊天**: 支持跨区服的世界频道

### **4. 合服功能需求**
- ✅ **合服策略**: 支持多个区服合并为一个区服
- ✅ **数据迁移**: 安全可靠的数据合并和迁移
- ✅ **冲突处理**: 处理合服时的数据冲突（如重名、排名等）
- ✅ **补偿机制**: 合服后的玩家补偿和调整

## 🏗️ 系统架构设计

### **1. 基于现有架构的整体设计**

> **设计原则**: 最大化复用现有的网关、认证、缓存等基础设施，通过扩展而非重构来实现分区分服功能。

```
┌─────────────────────────────────────────────────────────────┐
│                 现有网关服务 (Gateway Service)                │
│  - HTTP代理  - WebSocket网关  - 路由分发  - 限流保护          │
│  + 区服路由  + 跨服代理                                       │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                现有认证服务 (Auth Service)                    │
│  - 账号管理  - JWT认证  - 权限控制  - 会话管理                │
│  + 区服授权  + 玩家历史  + 区服令牌                           │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│            现有Redis服务 + 区服管理扩展                       │
│  - 缓存服务  - 分布式锁  - 消息队列                          │
│  + 区服状态缓存  + 跨服数据同步  + 区服路由缓存               │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    游戏微服务集群                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   区服实例 1                             │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│  │  │ User Service│ │ Club Service│ │Match Service│       │ │
│  │  │ Game Service│ │ Card Service│ │Player Service│      │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   区服实例 N                             │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│  │  │ User Service│ │ Club Service│ │Match Service│       │ │
│  │  │ Game Service│ │ Card Service│ │Player Service│      │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                现有数据存储 + 分片扩展                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │  全局MongoDB  │  │ 区服1 MongoDB │ │ 区服N MongoDB │          │
│  │ - 账号信息   │  │ - 用户数据   │  │ - 用户数据   │          │
│  │ - 区服配置   │  │ - 俱乐部数据  │  │ - 俱乐部数据  │          │
│  │ - 跨服数据   │  │ - 比赛数据   │  │ - 比赛数据   │          │
│  │ - 玩家历史   │  │ - 卡片数据   │  │ - 卡片数据   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              现有Redis + 分区分服扩展                    │ │
│  │  - 全局缓存: 区服列表、玩家历史、跨服数据                │ │
│  │  - 区服缓存: 按serverId前缀隔离的游戏数据缓存            │ │
│  │  - 跨服缓存: 排行榜、战斗匹配、活动数据                  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### **2. 基于现有架构的服务扩展设计**

> **核心思路**: 在现有微服务基础上扩展功能，而不是创建全新的服务，确保与现有基础设施的兼容性。

#### **2.1 网关服务扩展 (Gateway Service Enhancement)**
```typescript
// 扩展现有网关服务
apps/gateway/src/
├── domain/
│   ├── server-routing/            # 新增: 区服路由模块
│   │   ├── server-router.service.ts      # 区服路由服务
│   │   ├── server-load-balancer.ts       # 区服负载均衡
│   │   └── cross-server-proxy.ts         # 跨服代理
│   └── websocket/                 # 扩展现有WebSocket
│       ├── server-gateway.ts      # 区服WebSocket网关
│       └── cross-server-events.ts # 跨服事件处理
├── infrastructure/
│   └── microservices/             # 扩展现有微服务客户端
│       └── server-discovery.ts    # 区服发现客户端
```

#### **2.2 认证服务扩展 (Auth Service Enhancement)**
```typescript
// 扩展现有认证服务，不破坏现有功能
apps/auth/src/
├── domain/                        # 扩展现有业务层
│   ├── server-auth/               # 新增: 区服认证模块
│   │   ├── server-auth.service.ts # 区服认证服务
│   │   ├── server-token.service.ts # 区服令牌管理
│   │   └── server-selection.service.ts # 区服选择逻辑
│   └── player-history/            # 新增: 玩家历史模块
│       ├── player-history.service.ts
│       ├── player-history.repository.ts
│       └── server-preference.service.ts
├── infrastructure/                # 扩展现有基础设施层
│   └── database/
│       ├── entities/
│       │   ├── server.entity.ts   # 区服实体
│       │   ├── player-server.entity.ts # 玩家区服关系
│       │   └── player-history.entity.ts # 玩家历史
│       └── repositories/
│           ├── server.repository.ts
│           ├── player-server.repository.ts
│           └── player-history.repository.ts
└── app/                          # 扩展现有应用层
    ├── controllers/
    │   ├── server.controller.ts   # 区服管理控制器
    │   └── server-selection.controller.ts # 区服选择控制器
```

#### **2.3 游戏服务扩展 (Game Services Enhancement)**
```typescript
// 扩展现有游戏微服务，支持多区服部署
apps/user/src/                     # 用户服务扩展
├── domain/
│   └── server-context/            # 新增: 区服上下文
│       ├── server-context.service.ts # 区服上下文管理
│       └── cross-server-user.service.ts # 跨服用户服务
└── infrastructure/
    └── database/
        └── server-aware-repository.ts # 区服感知的数据访问

apps/club/src/                     # 俱乐部服务扩展
├── domain/
│   └── cross-server/              # 新增: 跨服功能
│       ├── cross-server-ranking.service.ts # 跨服排行榜
│       └── cross-server-battle.service.ts # 跨服战斗
```

#### **2.4 Redis服务扩展**

> **详细实现**: 参考 [Redis前缀架构v3.0文档](./redis-prefix-architecture-v3.md)

扩展现有Redis服务以支持分区分服的缓存需求。

#### **2.5 微服务通信扩展 (MicroserviceKit Enhancement)**
```typescript
// 扩展现有微服务通信库
libs/common/src/microservice-kit/
├── server-aware/                  # 新增: 区服感知通信
│   ├── server-aware-client.ts     # 区服感知客户端
│   ├── cross-server-client.ts     # 跨服通信客户端
│   └── server-discovery.service.ts # 区服发现服务
└── routing/                       # 新增: 智能路由
    ├── server-router.ts           # 区服路由器
    └── load-balancer.ts           # 负载均衡器
```

### **3. 与现有基础设施的集成策略**

#### **3.1 现有网关路由系统集成**
```typescript
// 扩展现有网关路由规则，支持区服路由
// 基于 docs/gateway-routing-rules-specification.md

// 现有路由规则保持不变:
// /api/auth/* -> auth-service
// /api/user/* -> user-service

// 新增区服相关路由:
// /api/servers/* -> auth-service (区服管理)
// /api/server/{serverId}/user/* -> user-service-{serverId}
// /api/server/{serverId}/club/* -> club-service-{serverId}
// /api/cross-server/* -> 跨服代理逻辑

interface ServerAwareRouting {
  // 全局路由 (无区服上下文)
  global: {
    '/api/auth/*': 'auth-service',
    '/api/servers/*': 'auth-service',
    '/api/cross-server/*': 'cross-server-proxy'
  };

  // 区服路由 (带区服上下文)
  serverAware: {
    '/api/server/{serverId}/user/*': 'user-service-{serverId}',
    '/api/server/{serverId}/club/*': 'club-service-{serverId}',
    '/api/server/{serverId}/match/*': 'match-service-{serverId}',
    '/api/server/{serverId}/card/*': 'card-service-{serverId}'
  };
}
```

#### **3.2 现有认证系统集成**
```typescript
// 扩展现有JWT认证，支持区服上下文
// 基于现有的 JWT + MFA + 权限系统

interface ServerAwareJWTPayload extends JWTPayload {
  // 保持现有字段不变
  userId: string;
  email: string;
  roles: string[];
  permissions: string[];

  // 新增区服相关字段
  currentServerId?: string;        // 当前所在区服
  serverHistory: string[];         // 历史区服列表
  crossServerEnabled: boolean;     // 是否启用跨服功能
}

// 扩展现有认证中间件
class ServerAwareAuthMiddleware extends AuthMiddleware {
  async validateServerAccess(token: string, serverId: string): Promise<boolean> {
    // 验证用户是否有权限访问指定区服
    // 复用现有的权限验证逻辑
  }
}
```

#### **3.3 Redis前缀架构v3.0集成**

> **详细设计参考**: [Redis前缀架构v3.0文档](./redis-prefix-architecture-v3.md)

基于职责分工的分区分服Redis键前缀方案：

```typescript
// 职责分工
RedisModule: 构建基础前缀 {环境}:{项目}:
RedisService: 动态添加 {global|cross|server}:{服务}:

// 最终键格式
区服数据：{环境}:{项目}:{区服}:{服务}:{具体键}
全局数据：{环境}:{项目}:global:{服务}:{具体键}
跨服数据：{环境}:{项目}:cross:{服务}:{具体键}

// 判断逻辑
dataType: 'global' → global: 前缀
dataType: 'cross' → cross: 前缀
dataType: 'server' 或未指定 → server{SERVER_ID}: 前缀

// 使用示例
await redisService.set('servers:list', data, 300, 'global');
// 实际键: 'dev:fm:global:auth:servers:list'

await redisService.set('user:profile:123', data, 1800); // 未指定，默认当前区服
// 实际键: 'dev:fm:server1:auth:user:profile:123'

// 缓存装饰器示例
@Cacheable({
  key: 'ranking:#{type}',
  ttl: 600,
  dataType: 'cross'
})
```

**核心优势**：
- ✅ **职责清晰**：RedisModule和RedisService分工明确
- ✅ **单Redis实例**：避免多实例复杂性
- ✅ **默认区服**：未指定dataType时自动使用当前区服
- ✅ **数据隔离**：不同类型数据完全隔离

#### **3.4 现有微服务通信集成**
```typescript
// 基于现有的 MicroserviceKit
// 扩展 libs/common/src/microservice-kit

// 现有服务名称保持不变
const EXISTING_SERVICES = {
  AUTH_SERVICE: 'auth-service',
  CHARACTER_SERVICE: 'user-service',
  CLUB_SERVICE: 'club-service',
  // ...
};

// 新增区服感知的服务调用
class ServerAwareMicroserviceClient extends MicroserviceClientService {
  async callWithServerContext<T>(
    serviceName: string,
    pattern: string,
    data: any,
    serverId?: string
  ): Promise<T> {
    // 如果指定了serverId，路由到对应区服的服务实例
    // 否则使用默认路由逻辑
    const targetService = serverId
      ? `${serviceName}-${serverId}`
      : serviceName;

    return this.call(targetService, pattern, { ...data, serverId });
  }
}
```

#### **3.5 现有数据库配置集成**
```typescript
// 基于现有的 MongoDB 配置
// 扩展 apps/auth/src/config/database.config.ts

interface ServerAwareDatabaseConfig {
  // 保持现有全局数据库配置
  global: {
    uri: process.env.MONGODB_URI,
    // ... 现有配置保持不变
  };

  // 新增区服数据库配置
  servers: {
    [serverId: string]: {
      uri: process.env[`MONGODB_URI_SERVER_${serverId.toUpperCase()}`] ||
           `${process.env.MONGODB_URI}_server_${serverId}`,
      // 继承全局配置的其他选项
    };
  };
}
```

### **4. 动态部署策略**

> **详细部署配置**: 参考 [Redis前缀架构v3.0文档](./redis-prefix-architecture-v3.md) 了解完整的Docker、Kubernetes动态部署方案。

#### **4.1 精简环境变量部署**
```bash
# 精简的环境变量配置
export SERVER_ID=1                    # 唯一必需的分区分服环境变量
export ENABLE_MULTI_SERVER=true       # 功能开关

# 启动不同区服实例
npm run start:auth                     # SERVER_ID=1 的认证服务
npm run start:user                     # SERVER_ID=1 的用户服务
```

#### **4.2 容器化精简部署**
```yaml
# 精简的Docker配置
auth-server1:
  image: fm-auth:latest
  environment:
    - SERVER_ID=1                      # 唯一的分区分服环境变量
    - REDIS_HOST=redis
    - MONGODB_URI=${MONGODB_URI_SERVER1}

user-server1:
  image: fm-user:latest
  environment:
    - SERVER_ID=1                      # 服务名从MICROSERVICE_NAMES获取
    - REDIS_HOST=redis
    - MONGODB_URI=${MONGODB_URI_SERVER1}
```

#### **4.2 配置管理策略**
```typescript
// 基于现有的环境变量管理
// 扩展 libs/common/src/config

interface MultiServerConfig {
  // 保持现有配置结构
  app: AppConfig;
  database: DatabaseConfig;
  redis: RedisConfig;

  // 新增多服务器配置
  multiServer: {
    enabled: boolean;
    currentServerId?: string;
    serverList: ServerConfig[];
    crossServerEnabled: boolean;
  };
}
```

## 📊 数据模型设计

### **1. 区服信息表 (servers)**
```typescript
interface Server {
  id: string;                      // 区服ID
  name: string;                    // 区服名称
  displayName: string;             // 显示名称
  region: string;                  // 区域 (如: 华东、华南、美西)
  status: ServerStatus;            // 区服状态
  capacity: number;                // 容量上限
  currentPlayers: number;          // 当前在线人数
  totalPlayers: number;            // 总注册人数
  openTime: Date;                  // 开服时间
  lastMaintenance: Date;           // 最后维护时间
  version: string;                 // 游戏版本
  features: string[];              // 特性标签 (如: 新手友好、竞技向)
  mergeTarget?: string;            // 合服目标 (如果即将合服)
  mergeTime?: Date;                // 预计合服时间
  config: ServerConfig;            // 区服配置
  createdAt: Date;
  updatedAt: Date;
}

enum ServerStatus {
  NEW = 'new',                     // 新服
  OPEN = 'open',                   // 开启中
  BUSY = 'busy',                   // 繁忙
  FULL = 'full',                   // 爆满
  MAINTENANCE = 'maintenance',     // 维护中
  MERGING = 'merging',            // 合服中
  MERGED = 'merged',              // 已合服
  CLOSED = 'closed'               // 已关闭
}

interface ServerConfig {
  maxPlayers: number;              // 最大玩家数
  newPlayerLimit: number;          // 新玩家限制
  experienceRate: number;          // 经验倍率
  dropRate: number;                // 掉落倍率
  enableCrossServer: boolean;      // 是否启用跨服
  maintenanceWindow: {             // 维护窗口
    start: string;                 // 开始时间 (HH:mm)
    end: string;                   // 结束时间 (HH:mm)
    timezone: string;              // 时区
  };
}
```

### **2. 玩家区服关系表 (player_servers)**
```typescript
interface PlayerServer {
  id: string;                      // 关系ID
  playerId: string;                // 玩家ID (全局账号ID)
  serverId: string;                // 区服ID
  characterId: string;             // 角色ID (区服内唯一)
  characterName: string;           // 角色名称
  level: number;                   // 等级
  avatar: string;                  // 头像URL或ID
  clubName?: string;               // 俱乐部名称
  clubLevel?: number;              // 俱乐部等级
  managerTitle?: string;           // 经理称号
  totalAssets?: number;            // 总资产 (金币等)
  achievements: string[];          // 主要成就列表
  lastLoginTime: Date;             // 最后登录时间
  totalPlayTime: number;           // 总游戏时长 (秒)
  gameProgress: {                  // 游戏进度
    season: number;                // 当前赛季
    league: string;                // 当前联赛
    ranking: number;               // 联赛排名
    matchesPlayed: number;         // 已比赛场次
  };
  isActive: boolean;               // 是否活跃
  createdAt: Date;                 // 创建时间
  updatedAt: Date;
}
```

### **3. 玩家历史记录表 (player_history)**
```typescript
interface PlayerHistory {
  id: string;                      // 记录ID
  playerId: string;                // 玩家ID
  serverId: string;                // 区服ID
  lastServerId: string;            // 上次游戏的区服ID
  serverHistory: ServerHistoryItem[]; // 区服历史记录
  preferences: PlayerPreferences;  // 玩家偏好设置
  createdAt: Date;
  updatedAt: Date;
}

interface ServerHistoryItem {
  serverId: string;                // 区服ID
  serverName: string;              // 区服名称
  firstLoginTime: Date;            // 首次登录时间
  lastLoginTime: Date;             // 最后登录时间
  totalPlayTime: number;           // 总游戏时长
  maxLevel: number;                // 最高等级
  achievements: string[];          // 成就列表
}

interface PlayerPreferences {
  autoSelectLastServer: boolean;   // 自动选择上次区服
  preferredRegion: string;         // 偏好区域
  showFullServers: boolean;        // 显示爆满区服
  sortBy: 'name' | 'players' | 'ping'; // 排序方式
}
```

## 🔄 核心业务流程

### **1. 玩家登录流程**
```mermaid
sequenceDiagram
    participant C as 客户端
    participant A as 认证服务
    participant SM as 区服管理服务
    participant GS as 游戏服务

    C->>A: 账号登录请求
    A->>A: 验证账号密码
    A->>SM: 获取玩家区服历史
    SM->>A: 返回区服历史和偏好
    A->>C: 返回登录成功 + 区服信息

    C->>SM: 请求区服列表
    SM->>SM: 获取可用区服列表
    SM->>C: 返回区服列表 + 推荐区服

    C->>SM: 选择区服进入
    SM->>SM: 验证区服状态和容量
    SM->>A: 生成区服访问令牌
    A->>C: 返回区服令牌

    C->>GS: 使用区服令牌连接游戏服务
    GS->>A: 验证区服令牌
    A->>GS: 令牌验证成功
    GS->>C: 进入游戏世界
```

### **2. 区服选择流程**
```typescript
// 区服选择业务逻辑
class ServerSelectionService {
  async getServerList(playerId: string): Promise<ServerListResponse> {
    // 1. 获取玩家历史记录
    const playerHistory = await this.getPlayerHistory(playerId);
    
    // 2. 获取所有可用区服
    const allServers = await this.getAllServers();
    
    // 3. 过滤可用区服
    const availableServers = this.filterAvailableServers(allServers);
    
    // 4. 添加玩家相关信息
    const serversWithPlayerInfo = await this.enrichWithPlayerInfo(
      availableServers, 
      playerId
    );
    
    // 5. 排序和推荐
    const sortedServers = this.sortAndRecommend(
      serversWithPlayerInfo, 
      playerHistory.preferences
    );
    
    return {
      servers: sortedServers,
      lastServer: playerHistory.lastServerId,
      recommendations: this.generateRecommendations(sortedServers, playerHistory)
    };
  }
}
```

## 🌐 跨服功能设计

### **1. 跨服排行榜系统**
```typescript
// 跨服排行榜服务
class CrossServerRankingService {
  async updateGlobalRanking(serverId: string, rankings: RankingData[]): Promise<void> {
    // 1. 接收各区服排行榜数据
    // 2. 合并到全局排行榜
    // 3. 重新计算全服排名
    // 4. 推送更新到各区服
  }

  async getGlobalRanking(type: RankingType, limit: number): Promise<GlobalRanking[]> {
    // 返回全服排行榜
  }
}

interface GlobalRanking {
  rank: number;                    // 全服排名
  playerId: string;                // 玩家ID
  playerName: string;              // 玩家名称
  serverId: string;                // 所在区服
  serverName: string;              // 区服名称
  score: number;                   // 分数
  data: any;                       // 排行榜相关数据
}
```

### **2. 跨服战斗系统**
```typescript
// 跨服战斗匹配服务
class CrossServerBattleService {
  async matchPlayers(playerId: string, battleType: BattleType): Promise<BattleMatch> {
    // 1. 获取玩家战力和偏好
    // 2. 在全服范围内匹配对手
    // 3. 创建跨服战斗房间
    // 4. 通知双方玩家
  }

  async createCrossServerBattle(player1: Player, player2: Player): Promise<Battle> {
    // 创建跨服战斗实例
  }
}
```

## 🔄 合服功能设计

### **1. 合服策略**
```typescript
enum MergeStrategy {
  SIMPLE_MERGE = 'simple',         // 简单合并 (数据直接合并)
  COMPETITIVE_MERGE = 'competitive', // 竞争合并 (保留最优数据)
  ADDITIVE_MERGE = 'additive',     // 累加合并 (数值类数据累加)
  CUSTOM_MERGE = 'custom'          // 自定义合并规则
}

interface MergeConfig {
  strategy: MergeStrategy;
  sourceServers: string[];         // 源区服列表
  targetServer: string;            // 目标区服
  mergeTime: Date;                 // 合服时间
  dataMapping: MergeDataMapping;   // 数据映射规则
  conflictResolution: ConflictResolution; // 冲突解决策略
  compensation: CompensationConfig; // 补偿配置
}
```

### **2. 数据迁移流程**
```typescript
class ServerMergeService {
  async executeMerge(mergeConfig: MergeConfig): Promise<MergeResult> {
    // 1. 预检查阶段
    await this.preCheckMerge(mergeConfig);

    // 2. 数据备份阶段
    await this.backupServerData(mergeConfig.sourceServers);

    // 3. 停服维护阶段
    await this.setServersToMaintenance(mergeConfig.sourceServers);

    // 4. 数据迁移阶段
    const migrationResult = await this.migrateData(mergeConfig);

    // 5. 冲突解决阶段
    await this.resolveConflicts(migrationResult.conflicts, mergeConfig);

    // 6. 数据验证阶段
    await this.validateMergedData(mergeConfig.targetServer);

    // 7. 补偿发放阶段
    await this.distributeCompensation(mergeConfig);

    // 8. 开服阶段
    await this.reopenServer(mergeConfig.targetServer);

    return migrationResult;
  }
}
```

## 🛠️ 技术实现方案

### **1. 数据库分片策略**
```typescript
// 数据库分片配置
interface DatabaseSharding {
  // 全局数据库 (不分片)
  global: {
    accounts: 'global_db',         // 账号信息
    servers: 'global_db',          // 区服信息
    crossServerData: 'global_db'   // 跨服数据
  };

  // 按区服分片
  serverShards: {
    [serverId: string]: {
      users: `server_${serverId}_db`,      // 用户数据
      clubs: `server_${serverId}_db`,      // 俱乐部数据
      matches: `server_${serverId}_db`,    // 比赛数据
      economy: `server_${serverId}_db`     // 经济数据
    };
  };
}
```

### **2. 缓存策略**

> **详细缓存设计**: 参考 [Redis前缀架构v3.0文档](./redis-prefix-architecture-v3.md) 了解完整的缓存键设计、TTL策略和多实例配置。

- **全局缓存**: 区服列表、玩家角色预览、账号信息
- **区服缓存**: 用户数据、俱乐部数据、比赛数据（按区服隔离）
- **跨服缓存**: 全局排行榜、跨服战斗、全局活动

### **3. 消息队列设计**

基于Redis Pub/Sub和Bull队列的消息处理：

- **区服管理**: 区服状态更新、容量变化、玩家进出
- **跨服功能**: 排行榜更新、战斗匹配、全局事件
- **合服操作**: 合服流程控制、进度通知、状态同步

## 📱 客户端接口设计

### **1. 区服列表接口**
```typescript
// GET /api/servers/list
interface ServerListResponse {
  servers: ServerInfo[];
  playerHistory: PlayerServerHistory[];
  lastServerId?: string;
  recommendations: ServerRecommendation[];
  summary: {
    totalServers: number;          // 总区服数
    playedServers: number;         // 已游戏区服数
    activeServers: number;         // 活跃区服数
  };
}

interface ServerInfo {
  id: string;
  name: string;
  displayName: string;
  region: string;
  status: ServerStatus;
  playerCount: number;
  capacity: number;
  ping?: number;                   // 延迟 (ms)
  hasCharacter: boolean;           // 是否有角色
  characterInfo?: CharacterPreview; // 角色预览信息
  tags: string[];                  // 标签 (新服、推荐等)
  openTime: Date;                  // 开服时间
  features: string[];              // 特性 (如: 新手友好、竞技向)
}

interface CharacterPreview {
  characterId: string;             // 角色ID
  characterName: string;           // 角色名称
  level: number;                   // 等级
  avatar: string;                  // 头像URL
  clubName?: string;               // 俱乐部名称
  clubLevel?: number;              // 俱乐部等级
  managerTitle?: string;           // 经理称号 (如: 新手经理、职业经理)
  totalAssets?: number;            // 总资产
  gameProgress: {
    season: number;                // 当前赛季
    league: string;                // 当前联赛名称
    ranking: number;               // 联赛排名
    matchesPlayed: number;         // 已比赛场次
    winRate: number;               // 胜率 (0-1)
  };
  achievements: AchievementPreview[]; // 主要成就
  lastLoginTime: Date;             // 最后登录时间
  totalPlayTime: number;           // 总游戏时长 (小时)
  isActive: boolean;               // 是否为活跃角色
}

interface AchievementPreview {
  id: string;                      // 成就ID
  name: string;                    // 成就名称
  icon: string;                    // 成就图标
  rarity: 'common' | 'rare' | 'epic' | 'legendary'; // 稀有度
}

interface ServerRecommendation {
  serverId: string;
  reason: 'new_server' | 'low_population' | 'friend_playing' | 'similar_progress';
  description: string;             // 推荐理由描述
  priority: number;                // 推荐优先级 (1-10)
}
```

### **2. 区服进入接口**
```typescript
// POST /api/servers/enter
interface EnterServerRequest {
  serverId: string;
  characterId?: string;            // 可选，指定角色
}

interface EnterServerResponse {
  success: boolean;
  serverToken: string;             // 区服访问令牌
  gameServerUrl: string;           // 游戏服务器地址
  characterInfo: CharacterInfo;
  serverInfo: ServerInfo;
}
```

## 🔄 角色预览数据存储和同步策略

### **1. 低频同步机制设计**

#### **1.1 定时批量同步策略**
```typescript
// 角色预览数据同步服务 - 优化性能版本
class CharacterPreviewSyncService {
  private syncQueue = new Map<string, CharacterSyncTask>();
  private readonly SYNC_INTERVAL = 5 * 60 * 1000; // 5分钟同步一次
  private readonly BATCH_SIZE = 100; // 每批处理100个角色

  constructor() {
    // 启动定时同步任务
    setInterval(() => this.processSyncQueue(), this.SYNC_INTERVAL);
  }

  /**
   * 将角色标记为需要同步（非即时）
   */
  async markForSync(serverId: string, characterId: string, priority: SyncPriority = 'normal'): Promise<void> {
    const key = `${serverId}:${characterId}`;

    this.syncQueue.set(key, {
      serverId,
      characterId,
      priority,
      markedAt: new Date(),
      retryCount: 0
    });

    // 只有高优先级任务才考虑即时同步
    if (priority === 'high' && this.syncQueue.size < 10) {
      await this.syncSingleCharacter(serverId, characterId);
      this.syncQueue.delete(key);
    }
  }

  /**
   * 批量处理同步队列
   */
  private async processSyncQueue(): Promise<void> {
    if (this.syncQueue.size === 0) return;

    const tasks = Array.from(this.syncQueue.values())
      .sort((a, b) => this.getPriorityWeight(b.priority) - this.getPriorityWeight(a.priority))
      .slice(0, this.BATCH_SIZE);

    const results = await Promise.allSettled(
      tasks.map(task => this.syncSingleCharacter(task.serverId, task.characterId))
    );

    // 处理同步结果，移除成功的任务
    tasks.forEach((task, index) => {
      const key = `${task.serverId}:${task.characterId}`;
      if (results[index].status === 'fulfilled') {
        this.syncQueue.delete(key);
      } else {
        // 失败的任务增加重试次数
        task.retryCount++;
        if (task.retryCount >= 3) {
          this.syncQueue.delete(key); // 超过重试次数，放弃同步
        }
      }
    });
  }

  /**
   * 同步单个角色数据
   */
  private async syncSingleCharacter(serverId: string, characterId: string): Promise<void> {
    try {
      // 1. 从区服数据库获取角色数据
      const characterData = await this.getCharacterFromServer(serverId, characterId);
      if (!characterData) return;

      // 2. 提取预览数据
      const previewData = this.extractPreviewData(characterData, serverId);

      // 3. 更新全局数据库
      await this.updateGlobalCharacterPreview(previewData);

      // 4. 更新缓存
      await this.updatePreviewCache(previewData);

    } catch (error) {
      console.error(`Failed to sync character ${serverId}:${characterId}`, error);
      throw error;
    }
  }

  private extractPreviewData(characterData: any, serverId: string): CharacterPreview {
    return {
      playerId: characterData.playerId,
      serverId,
      characterId: characterData.id,
      characterName: characterData.name,
      level: characterData.level,
      avatar: characterData.avatar || 'default-avatar.png',
      clubName: characterData.club?.name,
      clubLevel: characterData.club?.level,
      managerTitle: characterData.title,
      totalAssets: characterData.economy?.totalAssets || 0,
      gameProgress: {
        season: characterData.progress?.currentSeason || 1,
        league: characterData.progress?.currentLeague || '新手联赛',
        ranking: characterData.progress?.leagueRanking || 0,
        matchesPlayed: characterData.stats?.matchesPlayed || 0,
        winRate: characterData.stats?.winRate || 0
      },
      achievements: characterData.achievements?.slice(0, 3) || [],
      lastLoginTime: characterData.lastLoginTime,
      totalPlayTime: characterData.totalPlayTime || 0,
      isActive: this.isCharacterActive(characterData.lastLoginTime),
      syncedAt: new Date()
    };
  }

  private isCharacterActive(lastLoginTime: Date): boolean {
    const daysSinceLogin = (Date.now() - lastLoginTime.getTime()) / (1000 * 60 * 60 * 24);
    return daysSinceLogin <= 7; // 7天内登录过算活跃
  }
}

interface CharacterSyncTask {
  serverId: string;
  characterId: string;
  priority: SyncPriority;
  markedAt: Date;
  retryCount: number;
}

type SyncPriority = 'low' | 'normal' | 'high';
```

#### **1.2 触发同步的策略**
```typescript
// 简化的同步触发策略
enum SyncTriggers {
  // 高优先级 - 影响区服选择的关键数据
  CHARACTER_LOGIN = 'login',        // 角色登录
  CHARACTER_LOGOUT = 'logout',      // 角色登出

  // 普通优先级 - 定期同步即可
  LEVEL_CHANGE = 'level',           // 等级变化
  CLUB_CHANGE = 'club',             // 俱乐部变化
  PROGRESS_UPDATE = 'progress',     // 游戏进度更新

  // 低优先级 - 批量同步
  ACHIEVEMENT_UPDATE = 'achievement', // 成就更新
  ASSET_UPDATE = 'asset'            // 资产更新
}

// 简化的事件处理
@Injectable()
class CharacterSyncEventHandler {
  constructor(private syncService: CharacterPreviewSyncService) {}

  // 登录/登出 - 高优先级同步
  @OnEvent('character.login')
  async handleLogin(event: { serverId: string; characterId: string }) {
    await this.syncService.markForSync(event.serverId, event.characterId, 'high');
  }

  @OnEvent('character.logout')
  async handleLogout(event: { serverId: string; characterId: string }) {
    await this.syncService.markForSync(event.serverId, event.characterId, 'high');
  }

  // 其他变化 - 普通优先级
  @OnEvent(['character.level.up', 'club.created', 'club.renamed'])
  async handleNormalPriorityEvents(event: { serverId: string; characterId: string }) {
    await this.syncService.markForSync(event.serverId, event.characterId, 'normal');
  }

  // 低优先级事件 - 批量处理
  @OnEvent(['achievement.unlocked', 'economy.update'])
  async handleLowPriorityEvents(event: { serverId: string; characterId: string }) {
    await this.syncService.markForSync(event.serverId, event.characterId, 'low');
  }
}

### **2. 优化的缓存和存储策略**

> **Redis架构详细设计**: 参考 [Redis前缀架构v3.0文档](./redis-prefix-architecture-v3.md) 了解完整的Redis键空间设计、多实例配置和缓存装饰器适配方案。

#### **2.1 独立的角色预览数据存储设计**

> **设计原则**: 角色预览数据完全独立存储，与区服业务数据物理隔离，确保查询性能和系统稳定性。

```typescript
// 全局数据库 - 角色预览专用表
interface CharacterPreviewEntity {
  id: string;                      // 主键: {playerId}_{serverId}
  playerId: string;                // 玩家ID (全局唯一)
  serverId: string;                // 区服ID
  characterId: string;             // 角色ID (区服内唯一)

  // === 核心展示信息 (经常查询) ===
  characterName: string;           // 角色名称
  level: number;                   // 等级
  avatar: string;                  // 头像URL

  // === 俱乐部信息 ===
  clubName?: string;               // 俱乐部名称
  clubLevel?: number;              // 俱乐部等级
  clubLogo?: string;               // 俱乐部徽标

  // === 经理信息 ===
  managerTitle?: string;           // 经理称号
  managerRank?: string;            // 经理等级
  totalAssets?: number;            // 总资产 (简化显示)

  // === 游戏进度摘要 ===
  currentSeason: number;           // 当前赛季
  currentLeague: string;           // 当前联赛
  leagueRanking: number;           // 联赛排名
  matchesPlayed: number;           // 已比赛场次
  winRate: number;                 // 胜率 (0-1)

  // === 成就摘要 (只存储展示用的前3个) ===
  topAchievements: AchievementPreview[]; // 最多3个主要成就
  totalAchievements: number;       // 总成就数量

  // === 活跃状态 ===
  lastLoginTime: Date;             // 最后登录时间
  totalPlayTime: number;           // 总游戏时长 (小时)
  isActive: boolean;               // 是否活跃 (7天内登录)

  // === 数据管理 ===
  syncedAt: Date;                  // 最后同步时间
  dataVersion: number;             // 数据版本号 (防冲突)
  syncPriority: 'low' | 'normal' | 'high'; // 同步优先级

  // === 审计字段 ===
  createdAt: Date;                 // 创建时间
  updatedAt: Date;                 // 更新时间
}

// 专门优化的索引设计
const CHARACTER_PREVIEW_INDEXES = [
  // 主查询索引
  { playerId: 1, lastLoginTime: -1 },      // 玩家角色列表 (按活跃度排序)
  { playerId: 1, serverId: 1 },            // 玩家在特定区服的角色

  // 管理查询索引
  { serverId: 1, isActive: 1, level: -1 }, // 区服活跃角色统计
  { syncedAt: 1, syncPriority: -1 },       // 同步任务查询

  // 分析查询索引
  { isActive: 1, lastLoginTime: -1 },      // 全局活跃用户分析
  { currentLeague: 1, leagueRanking: 1 }   // 跨服排行榜查询
];

// 数据库分离架构
interface DatabaseArchitecture {
  // 全局数据库 - 只存储跨服数据
  globalDatabase: {
    name: 'football_manager_global',
    collections: [
      'character_previews',          // 角色预览数据
      'servers',                     // 区服配置
      'player_history',              // 玩家历史
      'cross_server_rankings',       // 跨服排行榜
      'global_events'                // 全局活动
    ]
  };

  // 区服数据库 - 只存储区服内业务数据
  serverDatabases: {
    [serverId: string]: {
      name: `football_manager_server_${serverId}`,
      collections: [
        'users',                     // 用户详细数据
        'clubs',                     // 俱乐部数据
        'matches',                   // 比赛数据
        'cards',                     // 卡片数据
        'economy',                   // 经济数据
        'achievements'               // 成就详细数据
      ]
    }
  };
}
```

#### **2.2 数据同步策略优化**
```typescript
// 基于独立存储的同步服务
class CharacterPreviewSyncService {
  /**
   * 从区服数据库提取关键信息到全局预览表
   */
  async syncCharacterPreview(serverId: string, characterId: string): Promise<void> {
    // 1. 从区服数据库获取完整角色数据
    const fullCharacterData = await this.getFullCharacterData(serverId, characterId);

    // 2. 提取预览所需的关键字段
    const previewData = this.extractPreviewFields(fullCharacterData, serverId);

    // 3. 更新全局预览表 (使用upsert避免重复)
    await this.upsertCharacterPreview(previewData);

    // 4. 更新缓存
    await this.updatePreviewCache(previewData);
  }

  /**
   * 提取预览字段 - 只保留展示必需的数据
   */
  private extractPreviewFields(fullData: any, serverId: string): CharacterPreviewEntity {
    return {
      id: `${fullData.playerId}_${serverId}`,
      playerId: fullData.playerId,
      serverId,
      characterId: fullData.id,

      // 基础信息
      characterName: fullData.name,
      level: fullData.level,
      avatar: fullData.avatar || 'default-avatar.png',

      // 俱乐部信息 (从关联表获取)
      clubName: fullData.club?.name,
      clubLevel: fullData.club?.level,
      clubLogo: fullData.club?.logo,

      // 经理信息
      managerTitle: fullData.title || '新手经理',
      managerRank: fullData.rank || 'Bronze',
      totalAssets: this.calculateTotalAssets(fullData.economy),

      // 游戏进度
      currentSeason: fullData.progress?.season || 1,
      currentLeague: fullData.progress?.league || '新手联赛',
      leagueRanking: fullData.progress?.ranking || 0,
      matchesPlayed: fullData.stats?.matches || 0,
      winRate: fullData.stats?.winRate || 0,

      // 成就摘要 (只取前3个最重要的)
      topAchievements: this.getTopAchievements(fullData.achievements),
      totalAchievements: fullData.achievements?.length || 0,

      // 活跃状态
      lastLoginTime: fullData.lastLoginTime,
      totalPlayTime: Math.round(fullData.totalPlayTime / 3600), // 转换为小时
      isActive: this.calculateActiveStatus(fullData.lastLoginTime),

      // 同步信息
      syncedAt: new Date(),
      dataVersion: (fullData.version || 0) + 1,
      syncPriority: this.determineSyncPriority(fullData),

      updatedAt: new Date()
    };
  }

  /**
   * 计算总资产 (简化显示)
   */
  private calculateTotalAssets(economy: any): number {
    if (!economy) return 0;

    return (economy.coins || 0) +
           (economy.gems || 0) * 100 + // 宝石按1:100换算
           (economy.items?.reduce((sum, item) => sum + (item.value || 0), 0) || 0);
  }

  /**
   * 获取顶级成就 (用于展示)
   */
  private getTopAchievements(achievements: any[]): AchievementPreview[] {
    if (!achievements || achievements.length === 0) return [];

    return achievements
      .filter(ach => ach.rarity === 'legendary' || ach.rarity === 'epic')
      .sort((a, b) => this.getAchievementWeight(b) - this.getAchievementWeight(a))
      .slice(0, 3)
      .map(ach => ({
        id: ach.id,
        name: ach.name,
        icon: ach.icon,
        rarity: ach.rarity
      }));
  }

  /**
   * 判断角色活跃状态
   */
  private calculateActiveStatus(lastLoginTime: Date): boolean {
    const daysSinceLogin = (Date.now() - lastLoginTime.getTime()) / (1000 * 60 * 60 * 24);
    return daysSinceLogin <= 7; // 7天内登录算活跃
  }

  /**
   * 确定同步优先级
   */
  private determineSyncPriority(characterData: any): 'low' | 'normal' | 'high' {
    const lastLogin = new Date(characterData.lastLoginTime);
    const hoursAgo = (Date.now() - lastLogin.getTime()) / (1000 * 60 * 60);

    if (hoursAgo <= 1) return 'high';      // 1小时内登录过
    if (hoursAgo <= 24) return 'normal';   // 24小时内登录过
    return 'low';                          // 超过24小时
  }
}
```

## 📊 独立存储架构评估分析

### **1. 可行性评估** ✅ **完全可行**

#### **技术可行性**
- ✅ **数据结构简单**: 角色预览数据字段固定，易于建模
- ✅ **同步机制成熟**: 基于事件驱动的数据同步技术成熟
- ✅ **查询性能优秀**: 单表查询，索引优化，性能可预期
- ✅ **扩展性良好**: 新增区服只需要增加同步配置

#### **业务可行性**
- ✅ **数据一致性**: 通过版本号和时间戳保证数据一致性
- ✅ **实时性要求**: 预览数据不需要强实时性，延迟同步可接受
- ✅ **容错能力**: 单个区服故障不影响其他区服数据展示
- ✅ **维护成本**: 独立表结构，维护和备份更简单

### **2. 性能优势分析**

#### **查询性能对比**
```typescript
// 传统方案 - 跨区服查询 (性能差)
async getPlayerCharactersTraditional(playerId: string): Promise<CharacterPreview[]> {
  const servers = await this.getAvailableServers();
  const characters = [];

  // 需要查询每个区服数据库 - O(n) 复杂度
  for (const server of servers) {
    try {
      const character = await this.queryServerDatabase(server.id, playerId);
      if (character) characters.push(character);
    } catch (error) {
      // 单个区服故障影响整体查询
      console.error(`Failed to query server ${server.id}`, error);
    }
  }

  return characters; // 查询时间: n * 平均查询时间
}

// 独立存储方案 - 单表查询 (性能优)
async getPlayerCharactersOptimized(playerId: string): Promise<CharacterPreview[]> {
  // 单次查询获取所有角色 - O(1) 复杂度
  return this.characterPreviewRepository.findByPlayerId(playerId);
  // 查询时间: 固定的单表查询时间
}
```

#### **性能指标对比**

| 指标 | 传统跨服查询 | 独立存储方案 | 性能提升 |
|------|-------------|-------------|----------|
| **查询延迟** | 500-2000ms | 10-50ms | **10-40倍** |
| **并发能力** | 受区服数据库限制 | 单表高并发 | **5-10倍** |
| **故障影响** | 单区服故障影响全局 | 完全隔离 | **100%可用性** |
| **缓存效率** | 复杂的多级缓存 | 简单的单表缓存 | **缓存命中率+30%** |

### **3. 数据一致性保障**

#### **最终一致性模型**
```typescript
// 数据一致性策略
interface DataConsistencyStrategy {
  // 1. 写入时一致性 (区服内操作)
  writeConsistency: {
    location: '区服数据库',
    requirement: '强一致性',
    mechanism: '事务保证'
  };

  // 2. 读取时一致性 (预览数据)
  readConsistency: {
    location: '全局预览表',
    requirement: '最终一致性',
    mechanism: '异步同步 + 版本控制',
    acceptableDelay: '5-10分钟'
  };

  // 3. 冲突解决策略
  conflictResolution: {
    strategy: '时间戳 + 版本号',
    rule: '区服数据为准，预览数据为辅'
  };
}

// 数据同步保障机制
class DataConsistencyGuard {
  /**
   * 检测数据不一致并自动修复
   */
  async detectAndRepairInconsistency(): Promise<void> {
    // 1. 定期检查预览数据与区服数据的一致性
    const inconsistentRecords = await this.findInconsistentRecords();

    // 2. 自动修复不一致的数据
    for (const record of inconsistentRecords) {
      await this.repairInconsistentRecord(record);
    }

    // 3. 记录修复日志
    await this.logRepairActions(inconsistentRecords);
  }

  private async findInconsistentRecords(): Promise<InconsistentRecord[]> {
    // 比较预览表和区服数据的关键字段
    // 如: 等级、俱乐部名称、最后登录时间等
  }
}
```

### **4. 存储成本分析**

#### **存储空间对比**
```typescript
// 数据量估算
interface StorageEstimation {
  // 单个角色预览记录大小
  singleRecordSize: {
    basicFields: '200 bytes',      // 基础字段
    jsonFields: '300 bytes',       // 成就等JSON字段
    indexes: '100 bytes',          // 索引开销
    total: '600 bytes'             // 总计
  };

  // 100万玩家，平均每人3个区服角色
  totalStorage: {
    records: '3,000,000 条',
    dataSize: '1.8 GB',           // 纯数据
    indexSize: '0.3 GB',          // 索引
    totalSize: '2.1 GB'           // 总存储
  };

  // 与区服数据对比
  comparison: {
    previewData: '2.1 GB',        // 预览数据
    fullGameData: '500+ GB',      // 完整游戏数据
    ratio: '< 0.5%'               // 预览数据占比极小
  };
}
```

### **5. 维护优势分析**

#### **运维简化**
- ✅ **备份策略**: 预览数据独立备份，不影响区服数据备份
- ✅ **数据迁移**: 预览数据迁移不需要停服
- ✅ **性能调优**: 可以独立优化预览数据查询性能
- ✅ **监控告警**: 独立的监控指标，问题定位更精确

#### **开发效率**
- ✅ **API简化**: 区服列表API不需要复杂的跨服查询逻辑
- ✅ **测试简化**: 预览功能测试不依赖区服环境
- ✅ **部署独立**: 预览服务可以独立部署和扩展
- ✅ **故障隔离**: 预览功能故障不影响游戏内功能

### **6. 推荐实施方案**

#### **数据库设计**
```sql
-- 全局数据库中的角色预览表
CREATE TABLE character_previews (
  id VARCHAR(64) PRIMARY KEY,           -- playerId_serverId
  player_id VARCHAR(32) NOT NULL,      -- 玩家ID
  server_id VARCHAR(16) NOT NULL,      -- 区服ID
  character_id VARCHAR(32) NOT NULL,   -- 角色ID

  -- 展示数据
  character_name VARCHAR(50) NOT NULL,
  level INT DEFAULT 1,
  avatar VARCHAR(200),
  club_name VARCHAR(50),
  club_level INT,
  manager_title VARCHAR(50),
  total_assets BIGINT DEFAULT 0,

  -- 游戏进度
  current_season INT DEFAULT 1,
  current_league VARCHAR(50),
  league_ranking INT DEFAULT 0,
  matches_played INT DEFAULT 0,
  win_rate DECIMAL(3,2) DEFAULT 0,

  -- 成就数据 (JSON)
  top_achievements JSON,
  total_achievements INT DEFAULT 0,

  -- 状态信息
  last_login_time TIMESTAMP,
  total_play_time INT DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,

  -- 同步信息
  synced_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  data_version INT DEFAULT 1,
  sync_priority ENUM('low', 'normal', 'high') DEFAULT 'normal',

  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  -- 索引
  INDEX idx_player_login (player_id, last_login_time DESC),
  INDEX idx_player_server (player_id, server_id),
  INDEX idx_server_active (server_id, is_active, level DESC),
  INDEX idx_sync_queue (synced_at, sync_priority DESC),

  UNIQUE KEY uk_player_server (player_id, server_id)
);
```

#### **实施建议**
1. **阶段1**: 创建独立的角色预览表和同步机制
2. **阶段2**: 迁移现有数据到预览表
3. **阶段3**: 切换API查询到预览表
4. **阶段4**: 优化同步频率和缓存策略

---

> **结论**: 将玩家简单角色信息独立存储到专用数据表是**完全可行且强烈推荐**的方案。这种设计能够显著提升查询性能、简化系统架构、提高系统可用性，是分区分服系统的最佳实践。

#### **2.2 高效缓存策略**

> **Redis缓存实现**: 详细的缓存键设计、TTL策略和多实例配置参考 [Redis前缀架构v3.0文档](./redis-prefix-architecture-v3.md)

```typescript
// 缓存策略概要
interface CacheStrategy {
  // 全局缓存 (global前缀)
  playerCharacters: {
    key: 'player:{playerId}:characters',
    ttl: 7200,                       // 2小时
    scope: 'global'
  };

  serverList: {
    key: 'servers:list',
    ttl: 300,                        // 5分钟
    scope: 'global'
  };

  // 区服缓存 (server前缀)
  userProfile: {
    key: 'user:profile:{userId}',
    ttl: 1800,                       // 30分钟
    scope: 'server'
  };

  // 跨服缓存 (cross前缀)
  globalRanking: {
    key: 'ranking:{type}',
    ttl: 600,                        // 10分钟
    scope: 'cross'
  };
}
```

#### **2.3 数据访问层优化**
```typescript
// 角色预览数据访问层
@Injectable()
class CharacterPreviewRepository {
  constructor(
    @InjectModel(CharacterPreviewEntity.name)
    private model: Model<CharacterPreviewEntity>
  ) {}

  /**
   * 根据玩家ID获取所有角色预览
   */
  async findByPlayerId(playerId: string): Promise<CharacterPreview[]> {
    const entities = await this.model
      .find({ playerId })
      .sort({ lastLoginTime: -1 }) // 按最后登录时间倒序
      .lean()
      .exec();

    return entities.map(this.entityToDto);
  }

  /**
   * 获取玩家最后登录的区服
   */
  async getLastPlayedServer(playerId: string): Promise<string | null> {
    const entity = await this.model
      .findOne({ playerId })
      .sort({ lastLoginTime: -1 })
      .select('serverId')
      .lean()
      .exec();

    return entity?.serverId || null;
  }

  /**
   * 批量插入或更新角色预览
   */
  async batchUpsert(previews: CharacterPreview[]): Promise<void> {
    const operations = previews.map(preview => ({
      updateOne: {
        filter: {
          playerId: preview.playerId,
          serverId: preview.serverId
        },
        update: {
          $set: {
            ...this.dtoToEntity(preview),
            updatedAt: new Date()
          }
        },
        upsert: true
      }
    }));

    await this.model.bulkWrite(operations);
  }

  /**
   * 获取需要同步的角色列表
   */
  async findStaleCharacters(olderThan: Date, limit: number = 100): Promise<CharacterPreview[]> {
    const entities = await this.model
      .find({
        $or: [
          { syncedAt: { $lt: olderThan } },
          { syncedAt: { $exists: false } }
        ]
      })
      .limit(limit)
      .lean()
      .exec();

    return entities.map(this.entityToDto);
  }

  private entityToDto(entity: CharacterPreviewEntity): CharacterPreview {
    return {
      playerId: entity.playerId,
      serverId: entity.serverId,
      characterId: entity.characterId,
      characterName: entity.characterName,
      level: entity.level,
      avatar: entity.avatar,
      clubName: entity.clubName,
      clubLevel: entity.clubLevel,
      managerTitle: entity.managerTitle,
      totalAssets: entity.totalAssets,
      gameProgress: {
        season: entity.currentSeason,
        league: entity.currentLeague,
        ranking: entity.leagueRanking,
        matchesPlayed: entity.matchesPlayed,
        winRate: entity.winRate
      },
      achievements: entity.achievements,
      lastLoginTime: entity.lastLoginTime,
      totalPlayTime: entity.totalPlayTime,
      isActive: entity.isActive,
      syncedAt: entity.syncedAt
    };
  }

  private dtoToEntity(dto: CharacterPreview): Partial<CharacterPreviewEntity> {
    return {
      playerId: dto.playerId,
      serverId: dto.serverId,
      characterId: dto.characterId,
      characterName: dto.characterName,
      level: dto.level,
      avatar: dto.avatar,
      clubName: dto.clubName,
      clubLevel: dto.clubLevel,
      managerTitle: dto.managerTitle,
      totalAssets: dto.totalAssets,
      currentSeason: dto.gameProgress.season,
      currentLeague: dto.gameProgress.league,
      leagueRanking: dto.gameProgress.ranking,
      matchesPlayed: dto.gameProgress.matchesPlayed,
      winRate: dto.gameProgress.winRate,
      achievements: dto.achievements,
      lastLoginTime: dto.lastLoginTime,
      totalPlayTime: dto.totalPlayTime,
      isActive: dto.isActive,
      syncedAt: dto.syncedAt || new Date(),
      version: 1
    };
  }
}
```

### **3. 便捷的服务端API设计**

#### **3.1 一站式区服列表接口**
```typescript
// 认证服务中的区服管理控制器
@Controller('servers')
export class ServerController {
  constructor(
    private serverService: ServerService,
    private characterPreviewService: CharacterPreviewService,
    private playerHistoryService: PlayerHistoryService
  ) {}

  /**
   * 获取完整的区服列表和玩家历史信息
   * GET /api/servers/list
   */
  @Get('list')
  @UseGuards(JwtAuthGuard)
  async getServerListWithHistory(@CurrentUser() user: JwtPayload): Promise<ServerListResponse> {
    // 并行获取所有需要的数据
    const [servers, playerCharacters, lastServerId] = await Promise.all([
      this.serverService.getAvailableServers(),
      this.characterPreviewService.getPlayerCharacters(user.userId),
      this.playerHistoryService.getLastPlayedServer(user.userId)
    ]);

    // 合并区服信息和角色信息
    const serversWithCharacters = servers.map(server => {
      const character = playerCharacters.find(char => char.serverId === server.id);
      return {
        ...server,
        hasCharacter: !!character,
        characterInfo: character || null
      };
    });

    // 生成推荐
    const recommendations = this.generateRecommendations(serversWithCharacters, playerCharacters);

    return {
      servers: serversWithCharacters,
      lastServerId,
      recommendations,
      summary: {
        totalServers: servers.length,
        playedServers: playerCharacters.length,
        activeServers: playerCharacters.filter(char => char.isActive).length
      }
    };
  }

  /**
   * 获取玩家在指定区服的详细角色信息
   * GET /api/servers/:serverId/character
   */
  @Get(':serverId/character')
  @UseGuards(JwtAuthGuard)
  async getCharacterInServer(
    @Param('serverId') serverId: string,
    @CurrentUser() user: JwtPayload
  ): Promise<CharacterPreview | null> {
    return this.characterPreviewService.getCharacterInServer(user.userId, serverId);
  }

  /**
   * 选择进入区服
   * POST /api/servers/:serverId/enter
   */
  @Post(':serverId/enter')
  @UseGuards(JwtAuthGuard)
  async enterServer(
    @Param('serverId') serverId: string,
    @CurrentUser() user: JwtPayload
  ): Promise<EnterServerResponse> {
    // 1. 验证区服状态
    const server = await this.serverService.getServerById(serverId);
    if (!server || !this.serverService.isServerAccessible(server)) {
      throw new BadRequestException('服务器不可访问');
    }

    // 2. 生成区服访问令牌
    const serverToken = await this.authService.generateServerToken(user.userId, serverId);

    // 3. 获取或创建角色信息
    let character = await this.characterPreviewService.getCharacterInServer(user.userId, serverId);
    if (!character) {
      // 新区服，需要创建角色
      character = await this.createNewCharacterPreview(user.userId, serverId);
    }

    // 4. 更新最后登录区服
    await this.playerHistoryService.updateLastPlayedServer(user.userId, serverId);

    // 5. 获取游戏服务器地址
    const gameServerUrl = await this.serverService.getGameServerUrl(serverId);

    return {
      success: true,
      serverToken,
      gameServerUrl,
      characterInfo: character,
      serverInfo: server
    };
  }

  /**
   * 生成区服推荐
   */
  private generateRecommendations(
    servers: ServerInfo[],
    playerCharacters: CharacterPreview[]
  ): ServerRecommendation[] {
    const recommendations: ServerRecommendation[] = [];

    // 推荐新服
    const newServers = servers.filter(s =>
      s.tags.includes('new') &&
      !playerCharacters.some(char => char.serverId === s.id)
    );
    newServers.slice(0, 2).forEach(server => {
      recommendations.push({
        serverId: server.id,
        reason: 'new_server',
        description: '全新区服，与其他玩家同步开始',
        priority: 8
      });
    });

    // 推荐人数适中的区服
    const balancedServers = servers.filter(s =>
      s.playerCount > s.capacity * 0.3 &&
      s.playerCount < s.capacity * 0.8 &&
      !playerCharacters.some(char => char.serverId === s.id)
    );
    balancedServers.slice(0, 1).forEach(server => {
      recommendations.push({
        serverId: server.id,
        reason: 'low_population',
        description: '人数适中，游戏体验良好',
        priority: 6
      });
    });

    return recommendations.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 为新区服创建角色预览
   */
  private async createNewCharacterPreview(
    playerId: string,
    serverId: string
  ): Promise<CharacterPreview> {
    // 这里只创建预览数据，实际角色创建在游戏服务中进行
    const preview: CharacterPreview = {
      playerId,
      serverId,
      characterId: '', // 游戏服务创建角色后会更新
      characterName: '', // 游戏服务创建角色后会更新
      level: 1,
      avatar: 'default-avatar.png',
      totalAssets: 0,
      gameProgress: {
        season: 1,
        league: '新手联赛',
        ranking: 0,
        matchesPlayed: 0,
        winRate: 0
      },
      achievements: [],
      lastLoginTime: new Date(),
      totalPlayTime: 0,
      isActive: true,
      syncedAt: new Date()
    };

    await this.characterPreviewService.updateCharacterPreview(preview);
    return preview;
  }
}
```

#### **3.2 角色预览服务**
```typescript
@Injectable()
export class CharacterPreviewService {
  constructor(
    private characterPreviewRepository: CharacterPreviewRepository,
    private cacheService: CharacterPreviewCacheService
  ) {}

  /**
   * 获取玩家所有角色预览
   */
  async getPlayerCharacters(playerId: string): Promise<CharacterPreview[]> {
    return this.cacheService.getPlayerCharacters(playerId);
  }

  /**
   * 获取玩家在指定区服的角色
   */
  async getCharacterInServer(playerId: string, serverId: string): Promise<CharacterPreview | null> {
    const characters = await this.getPlayerCharacters(playerId);
    return characters.find(char => char.serverId === serverId) || null;
  }

  /**
   * 更新角色预览数据
   */
  async updateCharacterPreview(preview: CharacterPreview): Promise<void> {
    await this.cacheService.updateCharacterPreview(preview);
  }

  /**
   * 批量更新角色预览数据
   */
  async batchUpdateCharacterPreviews(previews: CharacterPreview[]): Promise<void> {
    await this.cacheService.batchUpdateCharacterPreviews(previews);
  }

  /**
   * 从区服同步角色数据
   */
  async syncFromGameServer(serverId: string, characterId: string): Promise<void> {
    try {
      // 调用对应区服的游戏服务获取最新数据
      const gameServerData = await this.microserviceClient.call(
        `user-service-${serverId}`,
        'user.getCharacterData',
        { characterId }
      );

      if (gameServerData) {
        const preview = this.mapGameDataToPreview(gameServerData, serverId);
        await this.updateCharacterPreview(preview);
      }
    } catch (error) {
      console.error(`Failed to sync character ${serverId}:${characterId}`, error);
    }
  }

  private mapGameDataToPreview(gameData: any, serverId: string): CharacterPreview {
    return {
      playerId: gameData.playerId,
      serverId,
      characterId: gameData.id,
      characterName: gameData.name,
      level: gameData.level,
      avatar: gameData.avatar,
      clubName: gameData.club?.name,
      clubLevel: gameData.club?.level,
      managerTitle: gameData.title,
      totalAssets: gameData.economy?.totalAssets || 0,
      gameProgress: {
        season: gameData.progress?.currentSeason || 1,
        league: gameData.progress?.currentLeague || '新手联赛',
        ranking: gameData.progress?.leagueRanking || 0,
        matchesPlayed: gameData.stats?.matchesPlayed || 0,
        winRate: gameData.stats?.winRate || 0
      },
      achievements: gameData.achievements?.slice(0, 3) || [],
      lastLoginTime: gameData.lastLoginTime || new Date(),
      totalPlayTime: gameData.totalPlayTime || 0,
      isActive: true,
      syncedAt: new Date()
    };
  }
}
```

#### **3.3 玩家历史服务**
```typescript
@Injectable()
export class PlayerHistoryService {
  constructor(
    private characterPreviewRepository: CharacterPreviewRepository,
    private cacheService: CharacterPreviewCacheService
  ) {}

  /**
   * 获取玩家最后游戏的区服
   */
  async getLastPlayedServer(playerId: string): Promise<string | null> {
    // 实现细节参考Redis前缀架构v3.0文档
    return this.characterPreviewRepository.getLastPlayedServer(playerId);
  }

  /**
   * 更新最后游戏的区服
   */
  async updateLastPlayedServer(playerId: string, serverId: string): Promise<void> {
    // 实现细节参考Redis前缀架构v3.0文档
  }

  /**
   * 获取玩家的区服游戏历史统计
   */
  async getPlayerServerStats(playerId: string): Promise<PlayerServerStats> {
    const characters = await this.characterPreviewRepository.findByPlayerId(playerId);

    return {
      totalServers: characters.length,
      activeServers: characters.filter(char => char.isActive).length,
      totalPlayTime: characters.reduce((sum, char) => sum + char.totalPlayTime, 0),
      highestLevel: Math.max(...characters.map(char => char.level), 0),
      totalAchievements: characters.reduce((sum, char) => sum + char.achievements.length, 0)
    };
  }
}

interface PlayerServerStats {
  totalServers: number;
  activeServers: number;
  totalPlayTime: number;
  highestLevel: number;
  totalAchievements: number;
}
```

## 📊 性能优化总结

### **1. 同步性能优化**
- ✅ **批量同步**: 5分钟间隔，每批100个角色
- ✅ **优先级队列**: 登录/登出高优先级，其他数据普通/低优先级
- ✅ **失败重试**: 最多重试3次，避免无限重试
- ✅ **异步处理**: 不阻塞游戏内操作

### **2. 缓存性能优化**
- ✅ **分层缓存**: 玩家角色(2小时) > 区服列表(5分钟) > 最后区服(24小时)
- ✅ **一次性加载**: 登录时加载玩家所有角色，避免多次查询
- ✅ **智能失效**: 只在数据更新时清除相关缓存
- ✅ **批量操作**: 支持批量更新，减少数据库压力

### **3. 数据库性能优化**
- ✅ **复合索引**: 针对查询模式优化的索引设计
- ✅ **批量写入**: 使用bulkWrite减少数据库连接
- ✅ **数据版本**: 支持增量同步和冲突检测
- ✅ **分页查询**: 大数据量查询支持分页

### **4. API性能优化**
- ✅ **并行查询**: 同时获取区服列表和角色数据
- ✅ **数据聚合**: 一次API调用返回完整信息
- ✅ **智能推荐**: 基于算法的区服推荐
- ✅ **状态缓存**: 区服状态和容量信息缓存

## 🔧 技术实现要点

### **1. 数据一致性保障**
```typescript
// 使用数据版本号防止并发更新冲突
interface CharacterPreviewEntity {
  version: number;                 // 数据版本号
  syncedAt: Date;                  // 最后同步时间
}

// 乐观锁更新
async updateWithVersion(preview: CharacterPreview): Promise<void> {
  const result = await this.model.updateOne(
    {
      playerId: preview.playerId,
      serverId: preview.serverId,
      version: preview.version
    },
    {
      $set: { ...preview, version: preview.version + 1 },
      $currentDate: { updatedAt: true }
    }
  );

  if (result.modifiedCount === 0) {
    throw new ConflictException('数据已被其他进程更新');
  }
}
```

### **2. 容错机制**
```typescript
// 同步失败时的降级策略
class CharacterPreviewFallbackService {
  async getCharacterWithFallback(playerId: string, serverId: string): Promise<CharacterPreview | null> {
    try {
      // 1. 尝试从缓存获取
      return await this.cacheService.getCharacterPreview(playerId, serverId);
    } catch (error) {
      try {
        // 2. 缓存失败，尝试从数据库获取
        return await this.repository.findByPlayerAndServer(playerId, serverId);
      } catch (dbError) {
        // 3. 数据库也失败，返回基础信息
        console.error('Failed to get character preview, using fallback', dbError);
        return this.createFallbackPreview(playerId, serverId);
      }
    }
  }

  private createFallbackPreview(playerId: string, serverId: string): CharacterPreview {
    return {
      playerId,
      serverId,
      characterId: 'unknown',
      characterName: '加载中...',
      level: 1,
      avatar: 'default-avatar.png',
      totalAssets: 0,
      gameProgress: {
        season: 1,
        league: '未知',
        ranking: 0,
        matchesPlayed: 0,
        winRate: 0
      },
      achievements: [],
      lastLoginTime: new Date(),
      totalPlayTime: 0,
      isActive: false,
      syncedAt: new Date()
    };
  }
}
```

### **3. 监控和告警**
```typescript
// 同步任务监控
class CharacterSyncMonitor {
  private readonly metrics = {
    syncQueueSize: 0,
    syncSuccessRate: 0,
    avgSyncTime: 0,
    failedSyncs: 0
  };

  async recordSyncMetrics(success: boolean, duration: number): Promise<void> {
    // 记录同步指标
    if (success) {
      this.metrics.syncSuccessRate = (this.metrics.syncSuccessRate * 0.9) + (1 * 0.1);
    } else {
      this.metrics.failedSyncs++;
      this.metrics.syncSuccessRate = this.metrics.syncSuccessRate * 0.9;
    }

    this.metrics.avgSyncTime = (this.metrics.avgSyncTime * 0.9) + (duration * 0.1);

    // 告警检查
    if (this.metrics.syncSuccessRate < 0.8) {
      await this.sendAlert('角色同步成功率过低', this.metrics);
    }

    if (this.metrics.syncQueueSize > 1000) {
      await this.sendAlert('角色同步队列积压', this.metrics);
    }
  }
}
```

---

> **设计总结**: 本设计方案专注于服务端技术实现，通过低频批量同步、分层缓存、一站式API等策略，在保证性能的前提下为玩家提供便捷的区服选择和角色预览功能。所有技术方案都基于现有基础设施扩展，确保系统稳定性和可维护性。
```

## ⚠️ 风险和挑战

### **1. 技术风险**
- 🔴 **数据一致性**: 跨服数据同步的一致性保障
- 🔴 **性能瓶颈**: 大量玩家同时查询区服列表
- 🔴 **合服复杂性**: 数据迁移过程中的风险控制
- 🟡 **网络延迟**: 跨服功能的网络延迟问题

### **2. 业务风险**
- 🔴 **玩家流失**: 合服可能导致部分玩家流失
- 🔴 **经济平衡**: 不同区服经济系统的平衡问题
- 🟡 **社交关系**: 合服后社交关系的处理
- 🟡 **竞争公平**: 跨服战斗的公平性保障

### **3. 运维风险**
- 🔴 **数据安全**: 合服过程中的数据安全保障
- 🔴 **回滚机制**: 合服失败时的快速回滚
- 🟡 **监控告警**: 跨服功能的监控和告警
- 🟡 **容量规划**: 区服容量的动态调整

## 📋 开发计划

### **第一阶段: 基础架构 (3-4周)**
1. 设计和实现区服管理服务
2. 扩展认证服务支持区服认证
3. 设计数据库分片策略
4. 实现基础的区服列表和选择功能

### **第二阶段: 核心功能 (4-5周)**
1. 实现玩家区服历史记录
2. 完善区服状态管理
3. 实现区服容量控制和负载均衡
4. 开发客户端区服选择界面

### **第三阶段: 跨服功能 (3-4周)**
1. 实现跨服排行榜系统
2. 开发跨服战斗匹配系统
3. 实现跨服数据同步机制
4. 开发跨服活动框架

### **第四阶段: 合服功能 (4-5周)**
1. 设计合服策略和流程
2. 实现数据迁移工具
3. 开发冲突解决机制
4. 实现补偿和回滚系统

### **第五阶段: 测试和优化 (2-3周)**
1. 全面测试各项功能
2. 性能优化和调优
3. 安全测试和加固
4. 文档完善和培训

---

> **注意**: 本文档为初版设计，需要根据具体讨论和需求调整。请仔细审查每个部分的设计是否符合项目需求，我们将根据您的反馈逐步完善这个设计方案。
```
