// Auto-generated from GulfCupStrength.json
// Generated at: 2025-07-20T12:56:03.628Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface GulfCupStrengthDefinition {
  playerId: number; // 玩家ID 例: 90220, 90221 (原: PlayerId)
  id: number; // 唯一标识符 例: 1001, 1002
  level: number; // 等级 例: 1, 2 (原: Level)
  position: string; // 位置 例: GK, DC (原: Position)
}

// 字段映射：新字段名 -> 原始字段名
export const GulfCupStrengthFieldMappings = {
  playerId: 'PlayerId',
  level: 'Level',
  position: 'Position',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const GulfCupStrengthReverseFieldMappings = {
  'PlayerId': 'playerId',
  'Level': 'level',
  'Position': 'position',
} as const;

export const GulfCupStrengthMeta = {
  tableName: 'GulfCupStrength',
  dataFileName: 'GulfCupStrength.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 4,
  requiredFields: ['playerId', 'id', 'level', 'position'],
  optionalFields: [],
  renamedFieldsCount: 3,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: GulfCupStrengthFieldMappings,
  reverseFieldMappings: GulfCupStrengthReverseFieldMappings,
} as const;

export type GulfCupStrengthConfigMeta = typeof GulfCupStrengthMeta;
export type GulfCupStrengthFieldMapping = typeof GulfCupStrengthFieldMappings;
export type GulfCupStrengthReverseFieldMapping = typeof GulfCupStrengthReverseFieldMappings;
