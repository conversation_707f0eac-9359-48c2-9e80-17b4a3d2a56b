// Auto-generated from FootballerStar.json
// Generated at: 2025-07-20T12:56:03.493Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据
// 表名已规范化: FootballerStar → HeroStar

export interface HeroStarDefinition {
  id: number; // 唯一标识符 例: 1, 2
  cost: number; // 消耗 例: 1, 2 (原: Cost)
  downgrade: number; // 数值 例: 0, 1 (原: Downgrade)
  level: number; // 等级 例: 1, 2 (原: Level)
  probability: number; // 数值 例: 10000, 6000 (原: Probability)
}

// 字段映射：新字段名 -> 原始字段名
export const HeroStarFieldMappings = {
  cost: 'Cost',
  downgrade: 'Downgrade',
  level: 'Level',
  probability: 'Probability',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const HeroStarReverseFieldMappings = {
  'Cost': 'cost',
  'Downgrade': 'downgrade',
  'Level': 'level',
  'Probability': 'probability',
} as const;

export const HeroStarMeta = {
  tableName: 'HeroStar',
  originalTableName: 'FootballerStar',
  dataFileName: 'FootballerStar.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 5,
  requiredFields: ['id', 'cost', 'downgrade', 'level', 'probability'],
  optionalFields: [],
  renamedFieldsCount: 4,
  hasFieldMappings: true,
  isTableRenamed: true,
  fieldMappings: HeroStarFieldMappings,
  reverseFieldMappings: HeroStarReverseFieldMappings,
} as const;

export type HeroStarConfigMeta = typeof HeroStarMeta;
export type HeroStarFieldMapping = typeof HeroStarFieldMappings;
export type HeroStarReverseFieldMapping = typeof HeroStarReverseFieldMappings;
