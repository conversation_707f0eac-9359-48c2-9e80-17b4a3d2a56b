// Auto-generated from GroundMatchReward.json
// Generated at: 2025-07-20T12:56:03.565Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface GroundMatchRewardDefinition {
  awardid21: number; // 唯一标识符 例: 27857, 30643 (原: Awardid2_1)
  awardid2: number; // 唯一标识符 例: 1, 0 (原: Awardid2)
  awardid11: number; // 唯一标识符 例: 73420, 86668 (原: Awardid1_1)
  awardid1: number; // 唯一标识符 例: 1, 16 (原: Awardid1)
  id: number; // 唯一标识符 例: 1, 2
  level: number; // 等级 例: 1, 2 (原: Level)
  type: number; // 类型 例: 1, 2 (原: Type)
}

// 字段映射：新字段名 -> 原始字段名
export const GroundMatchRewardFieldMappings = {
  awardid21: 'Awardid2_1',
  awardid2: 'Awardid2',
  awardid11: 'Awardid1_1',
  awardid1: 'Awardid1',
  level: 'Level',
  type: 'Type',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const GroundMatchRewardReverseFieldMappings = {
  'Awardid2_1': 'awardid21',
  'Awardid2': 'awardid2',
  'Awardid1_1': 'awardid11',
  'Awardid1': 'awardid1',
  'Level': 'level',
  'Type': 'type',
} as const;

export const GroundMatchRewardMeta = {
  tableName: 'GroundMatchReward',
  dataFileName: 'GroundMatchReward.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 7,
  requiredFields: ['awardid21', 'awardid2', 'awardid11', 'awardid1', 'id', 'level', 'type'],
  optionalFields: [],
  renamedFieldsCount: 6,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: GroundMatchRewardFieldMappings,
  reverseFieldMappings: GroundMatchRewardReverseFieldMappings,
} as const;

export type GroundMatchRewardConfigMeta = typeof GroundMatchRewardMeta;
export type GroundMatchRewardFieldMapping = typeof GroundMatchRewardFieldMappings;
export type GroundMatchRewardReverseFieldMapping = typeof GroundMatchRewardReverseFieldMappings;
