{"name": "@libs/common", "version": "1.0.0", "description": "Common shared library for Football Manager services", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p tsconfig.lib.json", "build:watch": "tsc -p tsconfig.lib.json --watch"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/config": "^3.1.1", "redis": "^4.6.11", "ioredis": "^5.3.2", "rxjs": "^7.8.1", "reflect-metadata": "^0.1.13"}, "devDependencies": {"typescript": "^5.1.3"}, "peerDependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0"}}