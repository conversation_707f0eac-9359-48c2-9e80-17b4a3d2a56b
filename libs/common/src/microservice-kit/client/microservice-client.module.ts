import { Module, DynamicModule, Global } from '@nestjs/common';
import { ClientsModule } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { MicroserviceKitConfig } from '../config/microservice.config';
import { MicroserviceClientService } from './microservice-client.service';
import { MicroserviceName } from '@shared/constants';

/**
 * 微服务客户端模块
 * 自动为指定的服务创建 ClientProxy 实例
 */
@Global()
@Module({})
export class MicroserviceClientModule {

  /**
   * 创建微服务客户端模块（使用 ConfigService）
   * @param options 选项，包含要连接的服务列表
   */
  static forRoot(options?: { services?: MicroserviceName[] }): DynamicModule {
    return {
      module: MicroserviceClientModule,
      imports: [],
      providers: [
        {
          provide: 'MICROSERVICE_CONFIG',
          useFactory: (configService: ConfigService) => {
            return configService.get<MicroserviceKitConfig>('microserviceKit');
          },
          inject: [ConfigService],
        },
        {
          provide: 'CONNECTED_SERVICES',
          useFactory: (configService: ConfigService) => {
            const config = configService.get<MicroserviceKitConfig>('microserviceKit');
            return options?.services || Object.keys(config.services) as MicroserviceName[];
          },
          inject: [ConfigService],
        },

        MicroserviceClientService,
      ],
      exports: [
        MicroserviceClientService,
        'MICROSERVICE_CONFIG',
        'CONNECTED_SERVICES',
      ],
    };
  }
}
