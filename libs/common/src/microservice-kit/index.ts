// 客户端模块导出
export * from './client/microservice-client.module';
export * from './client/microservice-client.service';

// 服务端模块导出
export * from './server/microservice-server.module';
export * from './server/microservice-server.service';

// 配置导出
export * from './config';

// 工具函数导出
export * from './utils/microservice-bootstrap';

// 主模块导出
export * from './microservice-kit.module';

// 便捷的默认导出
export { MicroserviceKitModule as default } from './microservice-kit.module';
