import { Module, DynamicModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MicroserviceClientModule } from './client/microservice-client.module';
import { MicroserviceServerModule } from './server/microservice-server.module';
import { MicroserviceClientOptions } from './config';
import { MicroserviceName } from '@shared/constants';
import microserviceKitConfig from './config/default.config';

/**
 * 微服务公共库统一模块入口
 * 提供三种使用模式：客户端、服务端、混合
 */
@Module({})
export class MicroserviceKitModule {

  /**
   * 客户端模式 - 用于网关等需要调用其他微服务的应用
   * @param options 客户端选项
   * @param options.services 要连接的服务列表，不指定则连接所有服务
   */
  static forClient(options?: { services?: MicroserviceName[] }): DynamicModule {
    return {
      module: MicroserviceKitModule,
      imports: [
        ConfigModule.forFeature(microserviceKitConfig),
        MicroserviceClientModule.forRoot({
          services: options?.services
        }),
      ],
      exports: [MicroserviceClientModule],
    };
  }

  /**
   * 服务端模式 - 用于具体的微服务应用
   */
  static forServer(serviceName: MicroserviceName): DynamicModule {
    return {
      module: MicroserviceKitModule,
      imports: [
        ConfigModule.forFeature(microserviceKitConfig),
        MicroserviceServerModule.forService(serviceName),
      ],
      exports: [MicroserviceServerModule],
    };
  }

  /**
   * 混合模式 - 既是客户端又是服务端
   * @param serviceName 当前服务名称
   * @param options 混合模式选项
   * @param options.services 要连接的其他服务列表
   */
  static forHybrid(
    serviceName: MicroserviceName,
    options?: { services?: MicroserviceName[] }
  ): DynamicModule {
    return {
      module: MicroserviceKitModule,
      imports: [
        ConfigModule.forFeature(microserviceKitConfig),
        MicroserviceClientModule.forRoot({
          services: options?.services
        }),
        MicroserviceServerModule.forService(serviceName),
      ],
      exports: [MicroserviceClientModule, MicroserviceServerModule],
    };
  }
}
