/**
 * MongoDB数据库配置
 */

import { ConfigService } from '@nestjs/config';
import { MongooseModuleOptions } from '@nestjs/mongoose';

export interface DatabaseConfig {
  uri: string;
  dbName: string;
  options: any; // 使用any类型避免类型冲突
}

export const createMongoConfig = (configService: ConfigService, serviceName: string): MongooseModuleOptions => {
  const configs: Record<string, DatabaseConfig> = {
    // 认证服务数据库
    auth: {
      uri: configService.get<string>('AUTH_MONGODB_URI') || configService.get<string>('MONGODB_URI'),
      dbName: 'auth_db',
      options: {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      },
    },

    // 角色服务数据库
    character: {
      uri: configService.get<string>('CHARACTER_MONGODB_URI') || configService.get<string>('MONGODB_URI'),
      dbName: 'character_db',
      options: {
        maxPoolSize: 15,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      },
    },

    // 球员服务数据库
    hero: {
      uri: configService.get<string>('HERO_MONGODB_URI') || configService.get<string>('MONGODB_URI'),
      dbName: 'hero_db',
      options: {
        maxPoolSize: 15,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      },
    },

    // 经济服务数据库
    economy: {
      uri: configService.get<string>('ECONOMY_MONGODB_URI') || configService.get<string>('MONGODB_URI'),
      dbName: 'economy_db',
      options: {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      },
    },

    // 社交服务数据库
    social: {
      uri: configService.get<string>('SOCIAL_MONGODB_URI') || configService.get<string>('MONGODB_URI'),
      dbName: 'social_db',
      options: {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      },
    },

    // 活动服务数据库
    activity: {
      uri: configService.get<string>('ACTIVITY_MONGODB_URI') || configService.get<string>('MONGODB_URI'),
      dbName: 'activity_db',
      options: {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      },
    },

    // 比赛服务数据库
    match: {
      uri: configService.get<string>('MATCH_MONGODB_URI') || configService.get<string>('MONGODB_URI'),
      dbName: 'match_db',
      options: {
        maxPoolSize: 15,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      },
    },
  };

  const config = configs[serviceName];
  if (!config) {
    throw new Error(`Database configuration not found for service: ${serviceName}`);
  }

  return {
    uri: config.uri,
    dbName: config.dbName,
    ...config.options,
  };
};

// 数据库健康检查配置
export const createHealthCheckConfig = (configService: ConfigService, serviceName: string) => {
  const config = createMongoConfig(configService, serviceName);
  return {
    name: `${serviceName}_mongodb`,
    uri: config.uri,
    timeout: 5000,
  };
};

// 数据库连接事件处理
export const setupDatabaseEvents = (serviceName: string) => {
  return {
    connectionFactory: (connection: any) => {
      connection.on('connected', () => {
        console.log(`[${serviceName}] MongoDB connected successfully`);
      });

      connection.on('disconnected', () => {
        console.log(`[${serviceName}] MongoDB disconnected`);
      });

      connection.on('error', (error: any) => {
        console.error(`[${serviceName}] MongoDB connection error:`, error);
      });

      connection.on('reconnected', () => {
        console.log(`[${serviceName}] MongoDB reconnected`);
      });

      return connection;
    },
  };
};
