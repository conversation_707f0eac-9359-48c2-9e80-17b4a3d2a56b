#!/bin/bash

# Docker构建调试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 清理环境
cleanup() {
    log_info "清理Docker环境..."
    docker-compose -f docker-compose.test.yml down --remove-orphans 2>/dev/null || true
    docker system prune -f 2>/dev/null || true
}

# 检查文件系统
check_filesystem() {
    log_info "检查文件系统状态..."
    
    # 检查node_modules
    if [ -d "node_modules" ]; then
        log_warning "发现根目录node_modules，可能导致冲突"
        ls -la node_modules/ | head -10
    fi
    
    # 检查符号链接
    log_info "检查符号链接..."
    find . -type l -name "*football-manager*" 2>/dev/null || true
    
    # 检查磁盘空间
    log_info "检查磁盘空间..."
    df -h .
    
    # 检查Docker空间
    log_info "检查Docker空间..."
    docker system df
}

# 测试单个服务构建
test_single_build() {
    local service=$1
    log_info "测试构建单个服务: $service"
    
    # 只构建指定服务
    docker-compose -f docker-compose.test.yml build --no-cache $service
    
    if [ $? -eq 0 ]; then
        log_success "$service 构建成功"
        return 0
    else
        log_error "$service 构建失败"
        return 1
    fi
}

# 逐步构建测试
step_by_step_build() {
    log_info "开始逐步构建测试..."
    
    # 清理环境
    cleanup
    
    # 检查文件系统
    check_filesystem
    
    # 先构建数据库
    log_info "启动测试数据库..."
    docker-compose -f docker-compose.test.yml up -d redis-test mongodb-test
    
    # 等待数据库启动
    sleep 10
    
    # 测试Auth服务构建
    log_info "测试Auth服务构建..."
    if test_single_build "auth-test"; then
        log_success "Auth服务构建成功"
    else
        log_error "Auth服务构建失败，查看详细日志"
        docker-compose -f docker-compose.test.yml logs auth-test
        return 1
    fi
    
    # 测试Gateway服务构建
    log_info "测试Gateway服务构建..."
    if test_single_build "gateway-test"; then
        log_success "Gateway服务构建成功"
    else
        log_error "Gateway服务构建失败，查看详细日志"
        docker-compose -f docker-compose.test.yml logs gateway-test
        return 1
    fi
    
    log_success "所有服务构建成功！"
}

# 快速修复尝试
quick_fix() {
    log_info "尝试快速修复..."
    
    # 删除可能冲突的文件
    log_info "清理可能冲突的文件..."
    rm -rf node_modules/.cache 2>/dev/null || true
    rm -rf node_modules/@football-manager 2>/dev/null || true
    rm -rf .npm 2>/dev/null || true
    
    # 清理Docker缓存
    log_info "清理Docker缓存..."
    docker builder prune -f
    docker image prune -f
    
    # 重新安装依赖
    log_info "重新安装依赖..."
    npm cache clean --force
    rm -rf node_modules package-lock.json
    npm install
    
    log_success "快速修复完成"
}

# 主函数
main() {
    local action=${1:-"test"}
    
    case $action in
        "check")
            check_filesystem
            ;;
        "fix")
            quick_fix
            ;;
        "test")
            step_by_step_build
            ;;
        "clean")
            cleanup
            ;;
        *)
            log_error "未知操作: $action"
            log_info "支持的操作: check, fix, test, clean"
            exit 1
            ;;
    esac
}

# 捕获退出信号
trap cleanup EXIT

# 执行主函数
main "$@"
