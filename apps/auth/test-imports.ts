/**
 * 测试装饰器导入是否正常
 * 用于验证修复后的装饰器冲突问题
 */

// 测试从 shared 模块导入装饰器
import {
  // 现有的装饰器
  RequirePermissions,        // 应该是现有的权限装饰器
  RequireRoles,             // 现有的角色装饰器
  Public,                   // 现有的公开装饰器

  // 新增的装饰器
  RequireStringPermissions,  // 新的字符串权限装饰器
  SecureEndpoint,           // 组合安全装饰器
  Auth,                     // 认证装饰器
  RequireAdmin,             // 管理员装饰器
  RequireMfa,               // MFA装饰器
  RateLimit,                // 限流装饰器

  // API响应装饰器
  ApiSuccessResponse,
  ApiCreateResponse,
  ApiPaginatedResponse,

  // 服务
  CryptoService,
  ValidationService,
  UtilsService,

  // 守卫
  ThrottlerBehindProxyGuard,

  // 拦截器
  ResponseInterceptor,
  LoggingInterceptor,

  // 管道
  ValidationPipe,
  ParseObjectIdPipe,

  // 过滤器
  AllExceptionsFilter,
  HttpExceptionFilter,

} from './src/shared/shared.module';

// 测试类型定义
console.log('装饰器导入测试:');
console.log('RequirePermissions:', typeof RequirePermissions);
console.log('RequireStringPermissions:', typeof RequireStringPermissions);
console.log('SecureEndpoint:', typeof SecureEndpoint);
console.log('Auth:', typeof Auth);
console.log('Public:', typeof Public);

// 测试服务导入
console.log('\n服务导入测试:');
console.log('CryptoService:', CryptoService.name);
console.log('ValidationService:', ValidationService.name);
console.log('UtilsService:', UtilsService.name);

// 测试组件导入
console.log('\n组件导入测试:');
console.log('ThrottlerBehindProxyGuard:', ThrottlerBehindProxyGuard.name);
console.log('ResponseInterceptor:', ResponseInterceptor.name);
console.log('ValidationPipe:', ValidationPipe.name);
console.log('AllExceptionsFilter:', AllExceptionsFilter.name);

console.log('\n✅ 所有导入测试通过！');

export {}; // 确保这是一个模块
