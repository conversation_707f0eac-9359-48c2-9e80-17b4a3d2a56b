# 🛡️ 管理员模块文档

## 📋 概述

AdminModule 是足球经理认证服务的管理员功能模块，提供完整的系统管理、用户管理、监控统计和安全管理功能。该模块专为系统管理员设计，提供强大的管理工具和详细的系统洞察。

## 🏗️ 模块架构

### 核心组件

```
apps/auth/src/modules/admin/
├── admin.module.ts                     # 主模块文件
├── admin.controller.ts                 # 主控制器
├── admin.service.ts                    # 主服务
├── dto/                               # 数据传输对象
│   ├── admin-dashboard.dto.ts         # 仪表板DTO
│   ├── system-status.dto.ts           # 系统状态DTO
│   └── admin-action.dto.ts            # 管理员操作DTO
├── controllers/                       # 子控制器
│   ├── system.controller.ts           # 系统管理控制器
│   ├── user-management.controller.ts  # 用户管理控制器
│   ├── audit.controller.ts            # 审计管理控制器
│   ├── security.controller.ts         # 安全管理控制器
│   └── statistics.controller.ts       # 统计分析控制器
└── services/                          # 服务层
    ├── system.service.ts              # 系统服务
    ├── statistics.service.ts          # 统计服务
    ├── user-management.service.ts     # 用户管理服务
    ├── audit-management.service.ts    # 审计管理服务
    └── security-management.service.ts # 安全管理服务
```

## 🎯 核心功能

### 1. 管理员仪表板

提供系统整体概览和关键指标监控。

**主要特性**:
- 📊 系统概览统计
- 👥 用户指标分析
- 🔐 会话统计信息
- 🛡️ 安全指标监控
- 💻 系统性能指标
- 📝 最近活动记录
- ⚠️ 系统警告提醒

**API端点**:
```
GET /admin/dashboard
```

### 2. 系统管理

全面的系统状态监控和维护工具。

**主要特性**:
- 🏥 系统健康检查
- 📈 性能指标监控
- 🔧 系统维护操作
- 💾 备份管理
- ⚙️ 配置管理
- 📋 系统日志查看

**API端点**:
```
GET  /admin/system/status     # 系统状态
GET  /admin/system/health     # 健康检查
GET  /admin/system/metrics    # 系统指标
POST /admin/actions/maintenance # 系统维护
POST /admin/actions/backup    # 创建备份
GET  /admin/logs/system       # 系统日志
GET  /admin/settings          # 系统设置
PUT  /admin/settings          # 更新设置
```

### 3. 用户管理

完整的用户生命周期管理功能。

**主要特性**:
- 👤 用户信息查看和编辑
- 🔄 用户状态管理
- 👑 角色分配和权限管理
- 🔑 密码重置
- 🔒 账户锁定/解锁
- 📱 MFA管理
- 💻 会话管理
- 📊 批量操作

**API端点**:
```
GET    /admin/users                    # 用户列表
GET    /admin/users/:id                # 用户详情
PUT    /admin/users/:id/status         # 更新用户状态
PUT    /admin/users/:id/roles          # 分配用户角色
POST   /admin/users/:id/reset-password # 重置用户密码
POST   /admin/users/:id/lock           # 锁定用户账户
DELETE /admin/users/:id/lock           # 解锁用户账户
DELETE /admin/users/:id/mfa            # 禁用用户MFA
GET    /admin/users/:id/sessions       # 获取用户会话
DELETE /admin/users/:id/sessions/:sessionId # 终止用户会话
DELETE /admin/users/:id                # 删除用户
POST   /admin/users/:id/restore        # 恢复用户
POST   /admin/users/bulk               # 批量操作用户
```

### 4. 统计分析

深入的数据分析和趋势洞察。

**主要特性**:
- 📈 用户增长分析
- 💻 会话活动统计
- 🛡️ 安全事件分析
- ⚡ 性能指标统计
- 🎮 业务数据分析
- 📊 自定义时间范围
- 📉 趋势图表展示

**API端点**:
```
GET /admin/statistics/overview    # 统计概览
GET /admin/statistics/users       # 用户统计
GET /admin/statistics/sessions    # 会话统计
GET /admin/statistics/security    # 安全统计
GET /admin/statistics/performance # 性能统计
GET /admin/statistics/business    # 业务统计
```

### 5. 安全管理

全面的安全监控和威胁管理。

**主要特性**:
- 🚨 安全警告管理
- 🔍 安全事件监控
- 🎯 威胁检测和分析
- 🚫 IP黑名单管理
- 🔒 安全扫描
- 📊 安全趋势分析

**API端点**:
```
GET  /admin/security/alerts      # 安全警告
GET  /admin/security/events      # 安全事件
GET  /admin/security/threats     # 威胁信息
GET  /admin/security/blocked-ips # 被阻止的IP
POST /admin/security/scan        # 安全扫描
```

### 6. 审计管理

完整的操作审计和合规支持。

**主要特性**:
- 📋 审计日志查询
- 🔍 事件追踪
- 📊 操作统计
- 📤 数据导出
- 🔎 高级搜索和过滤

**API端点**:
```
GET  /admin/audit/logs    # 审计日志
GET  /admin/audit/events  # 审计事件
POST /admin/audit/export  # 导出审计数据
```

## 🔐 权限控制

### 权限级别

AdminModule 使用分层权限控制：

1. **角色要求**:
   - `admin` - 普通管理员
   - `super_admin` - 超级管理员

2. **权限装饰器**:
   - `@AdminPermissions.systemAdmin()` - 系统管理权限
   - `@AdminPermissions.userAdmin()` - 用户管理权限
   - `@AdminPermissions.auditAdmin()` - 审计管理权限
   - `@AdminPermissions.securityAdmin()` - 安全管理权限
   - `@AdminPermissions.statisticsView()` - 统计查看权限

### 权限矩阵

| 功能 | admin | super_admin |
|------|-------|-------------|
| 查看仪表板 | ✅ | ✅ |
| 系统状态查看 | ✅ | ✅ |
| 用户管理 | ✅ | ✅ |
| 统计查看 | ✅ | ✅ |
| 安全管理 | ✅ | ✅ |
| 审计查看 | ✅ | ✅ |
| 系统维护 | ❌ | ✅ |
| 系统设置 | ❌ | ✅ |
| 备份管理 | ❌ | ✅ |

## 📊 数据结构

### 仪表板数据 (AdminDashboardDto)

```typescript
interface AdminDashboardDto {
  overview: {
    totalUsers: number;
    activeUsers: number;
    newUsersToday: number;
    activeSessions: number;
    securityAlerts: number;
    systemHealth: string;
  };
  userMetrics: UserMetricsDto;
  sessionMetrics: SessionMetricsDto;
  securityMetrics: SecurityMetricsDto;
  systemMetrics: SystemMetricsDto;
  recentActivities: RecentActivityDto[];
  alerts: SystemAlertDto[];
}
```

### 系统状态数据 (SystemStatusDto)

```typescript
interface SystemStatusDto {
  overallStatus: string;
  uptime: number;
  startTime: Date;
  currentTime: Date;
  database: DatabaseStatusDto;
  redis: RedisStatusDto;
  services: ServiceStatusDto[];
  externalServices: ExternalServiceDto[];
  resources: SystemResourcesDto;
  performance: PerformanceMetricsDto;
  security: SecurityStatusDto;
  backup: BackupStatusDto;
  version: string;
  environment: string;
}
```

### 管理员操作 (AdminActionDto)

```typescript
interface AdminActionDto {
  action: AdminActionType;
  description?: string;
  parameters?: Record<string, any>;
  targets?: string[];
  force?: boolean;
  scheduledTime?: Date;
}

enum AdminActionType {
  CLEAR_CACHE = 'clear_cache',
  RESTART_SERVICE = 'restart_service',
  UPDATE_CONFIG = 'update_config',
  CLEANUP_LOGS = 'cleanup_logs',
  OPTIMIZE_DATABASE = 'optimize_database',
  REFRESH_CERTIFICATES = 'refresh_certificates',
  SYNC_DATA = 'sync_data',
  BACKUP_DATABASE = 'backup_database',
  RESTORE_DATABASE = 'restore_database',
  MAINTENANCE_MODE = 'maintenance_mode',
  SECURITY_SCAN = 'security_scan',
  PERFORMANCE_TEST = 'performance_test',
}
```

## 🛡️ 安全特性

### 操作审计

所有管理员操作都会自动记录审计日志：

```typescript
{
  type: 'admin_action',
  userId: 'admin_id',
  targetUserId?: 'target_user_id',
  details: {
    action: 'user_status_change',
    oldValue: 'active',
    newValue: 'suspended',
    reason: '违反社区规则'
  },
  ipAddress: '*************',
  userAgent: 'Mozilla/5.0...',
  timestamp: '2023-12-01T10:00:00.000Z'
}
```

### 权限验证

多层权限验证机制：

1. **JWT令牌验证** - 确保用户已认证
2. **角色检查** - 验证用户具有管理员角色
3. **权限检查** - 验证具体操作权限
4. **操作前验证** - 关键操作前的二次确认

### 敏感操作保护

对于敏感操作提供额外保护：

- 🔑 密码重置需要管理员权限验证
- 🔒 账户锁定会自动终止用户会话
- 💾 系统备份需要超级管理员权限
- ⚙️ 系统设置修改会记录详细日志

## 📈 监控和告警

### 系统监控

实时监控系统关键指标：

- **性能指标**: CPU、内存、磁盘使用率
- **服务状态**: 数据库、Redis、外部服务
- **业务指标**: 用户活动、会话数量、错误率
- **安全指标**: 失败登录、安全事件、威胁检测

### 告警机制

自动告警系统：

- 🚨 **严重告警**: 系统故障、安全威胁
- ⚠️ **警告告警**: 性能下降、异常活动
- ℹ️ **信息告警**: 系统更新、维护通知

## 🔧 使用示例

### 获取仪表板数据

```typescript
// GET /admin/dashboard
const response = await fetch('/admin/dashboard', {
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  }
});

const dashboard = await response.json();
console.log('系统概览:', dashboard.data.overview);
```

### 执行系统维护

```typescript
// POST /admin/actions/maintenance
const maintenanceAction = {
  action: 'clear_cache',
  description: '清理系统缓存以提高性能',
  parameters: {
    cacheType: 'all',
    force: true
  }
};

const response = await fetch('/admin/actions/maintenance', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(maintenanceAction)
});
```

### 管理用户状态

```typescript
// PUT /admin/users/:id/status
const statusUpdate = {
  status: 'suspended',
  reason: '违反社区规则'
};

const response = await fetch(`/admin/users/${userId}/status`, {
  method: 'PUT',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(statusUpdate)
});
```

## 🚀 最佳实践

### 1. 权限最小化原则

- 只授予必要的管理员权限
- 定期审查和更新权限分配
- 使用角色分离避免权限过度集中

### 2. 操作审计

- 记录所有管理员操作
- 定期审查审计日志
- 建立异常操作告警机制

### 3. 安全操作

- 敏感操作需要二次确认
- 使用强密码和MFA保护管理员账户
- 定期更换管理员凭据

### 4. 监控和维护

- 定期检查系统状态
- 及时响应系统告警
- 建立定期备份和维护计划

### 5. 数据保护

- 敏感数据脱敏显示
- 遵循数据保护法规
- 建立数据访问控制机制

AdminModule 为足球经理认证服务提供了完整的管理功能，确保系统的安全、稳定和高效运行。
