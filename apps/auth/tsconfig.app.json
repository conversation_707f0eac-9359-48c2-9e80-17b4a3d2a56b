{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "declaration": false,
    "outDir": "../../dist/apps/auth"
  },
  "include": ["src/**/*"],
  "exclude": [
    // 基础排除
    "node_modules",
    "dist",
    "coverage",

    // 测试相关
    "test",
    "tests",
    "**/*spec.ts",
    "**/*.e2e-spec.ts",
    "**/*.test.ts",
    "**/test-*.ts",
    "**/test-*.js",

    // 示例和脚本
    "examples",
    "scripts",
    "**/*.example.ts",
    "**/*.example.js",

    // 文档
    "docs",
    "**/*.md",

    // 临时文件和缓存
    "**/*.tmp",
    "**/*.temp",
    "**/*.cache",
    "**/*.backup",
    "**/*.bak",

    // 日志文件
    "**/*.log",
    "logs",
    "test-logs",

    // IDE和编辑器文件
    ".vscode",
    ".idea",
    "**/*.swp",
    "**/*.swo",

    // 操作系统文件
    ".DS_Store",
    "Thumbs.db",

    // 环境文件
    ".env*",
    "!.env.example"
  ]
}
