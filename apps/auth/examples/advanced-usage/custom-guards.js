/**
 * 自定义守卫示例
 * 
 * 演示如何创建和使用自定义守卫，包括：
 * - 游戏角色守卫
 * - 时间限制守卫
 * - 地理位置守卫
 * - 设备类型守卫
 * - 组合守卫
 */

const { Injectable, CanActivate, ExecutionContext, ForbiddenException } = require('@nestjs/common');
const { Reflector } = require('@nestjs/core');

/**
 * 游戏角色守卫
 * 检查用户是否具有特定的游戏角色
 */
@Injectable()
class GameRoleGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    // 获取装饰器设置的所需游戏角色
    const requiredGameRoles = this.reflector.getAllAndOverride<string[]>('gameRoles', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredGameRoles) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    
    if (!user || !user.gameProfile) {
      return false;
    }

    // 检查用户的游戏角色
    const userGameRoles = user.roles || [];
    const gameSpecificRoles = userGameRoles.filter(role => 
      ['team_manager', 'scout', 'youth_coach', 'financial_director', 'league_admin', 'vip_member'].includes(role)
    );

    return requiredGameRoles.some(role => gameSpecificRoles.includes(role));
  }
}

/**
 * 时间限制守卫
 * 检查当前时间是否在允许的时间范围内
 */
@Injectable()
class TimeRestrictionGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const timeRestriction = this.reflector.getAllAndOverride<{
      startHour: number;
      endHour: number;
      timezone?: string;
    }>('timeRestriction', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!timeRestriction) {
      return true;
    }

    const now = new Date();
    const currentHour = now.getHours();

    // 检查是否在允许的时间范围内
    if (timeRestriction.startHour <= timeRestriction.endHour) {
      // 同一天内的时间范围
      return currentHour >= timeRestriction.startHour && currentHour < timeRestriction.endHour;
    } else {
      // 跨天的时间范围
      return currentHour >= timeRestriction.startHour || currentHour < timeRestriction.endHour;
    }
  }
}

/**
 * 地理位置守卫
 * 检查用户的地理位置是否在允许的范围内
 */
@Injectable()
class GeoLocationGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const geoRestriction = this.reflector.getAllAndOverride<{
      allowedCountries?: string[];
      blockedCountries?: string[];
      allowedRegions?: string[];
    }>('geoRestriction', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!geoRestriction) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const userLocation = this.extractLocationFromRequest(request);

    if (!userLocation) {
      // 如果无法获取位置信息，默认允许
      return true;
    }

    // 检查国家限制
    if (geoRestriction.allowedCountries) {
      return geoRestriction.allowedCountries.includes(userLocation.country);
    }

    if (geoRestriction.blockedCountries) {
      return !geoRestriction.blockedCountries.includes(userLocation.country);
    }

    // 检查地区限制
    if (geoRestriction.allowedRegions) {
      return geoRestriction.allowedRegions.includes(userLocation.region);
    }

    return true;
  }

  private extractLocationFromRequest(request: any): { country: string; region: string } | null {
    // 从请求头中提取地理位置信息
    // 实际应用中可能需要使用 GeoIP 服务
    const country = request.headers['cf-ipcountry'] || 
                   request.headers['x-country-code'] ||
                   'CN'; // 默认值

    const region = request.headers['cf-region'] || 
                  request.headers['x-region-code'] ||
                  'Beijing'; // 默认值

    return { country, region };
  }
}

/**
 * 设备类型守卫
 * 检查用户的设备类型是否被允许
 */
@Injectable()
class DeviceTypeGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const deviceRestriction = this.reflector.getAllAndOverride<{
      allowedDevices?: string[];
      blockedDevices?: string[];
    }>('deviceRestriction', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!deviceRestriction) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const deviceType = this.extractDeviceType(request);

    if (deviceRestriction.allowedDevices) {
      return deviceRestriction.allowedDevices.includes(deviceType);
    }

    if (deviceRestriction.blockedDevices) {
      return !deviceRestriction.blockedDevices.includes(deviceType);
    }

    return true;
  }

  private extractDeviceType(request: any): string {
    const userAgent = request.headers['user-agent'] || '';
    
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      return 'mobile';
    } else if (/Tablet/.test(userAgent)) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }
}

/**
 * 游戏会话守卫
 * 检查用户是否有活跃的游戏会话
 */
@Injectable()
class GameSessionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    // 注入游戏会话服务
    // private gameSessionService: GameSessionService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requireActiveSession = this.reflector.getAllAndOverride<boolean>('requireActiveSession', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requireActiveSession) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    
    if (!user) {
      return false;
    }

    // 检查用户是否有活跃的游戏会话
    // const hasActiveSession = await this.gameSessionService.hasActiveSession(user.id);
    
    // 模拟检查
    const hasActiveSession = Math.random() > 0.3; // 70% 概率有活跃会话

    if (!hasActiveSession) {
      throw new ForbiddenException('需要活跃的游戏会话才能执行此操作');
    }

    return true;
  }
}

/**
 * 球队所有权守卫
 * 检查用户是否拥有指定的球队
 */
@Injectable()
class TeamOwnershipGuard implements CanActivate {
  constructor(
    // private teamService: TeamService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const { user } = request;
    const teamId = request.params.teamId || request.body.teamId;

    if (!user || !teamId) {
      return false;
    }

    // 检查用户是否拥有该球队
    // const team = await this.teamService.findById(teamId);
    // return team && team.ownerId === user.id;

    // 模拟检查
    return Math.random() > 0.5; // 50% 概率拥有球队
  }
}

/**
 * 组合守卫
 * 结合多个条件进行复杂的权限检查
 */
@Injectable()
class CompositeGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private gameRoleGuard: GameRoleGuard,
    private timeRestrictionGuard: TimeRestrictionGuard,
    private geoLocationGuard: GeoLocationGuard,
    private deviceTypeGuard: DeviceTypeGuard,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const compositeRules = this.reflector.getAllAndOverride<{
      operator: 'AND' | 'OR';
      rules: string[];
    }>('compositeRules', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!compositeRules) {
      return true;
    }

    const results: boolean[] = [];

    // 执行各个守卫检查
    for (const rule of compositeRules.rules) {
      let result = false;

      switch (rule) {
        case 'gameRole':
          result = this.gameRoleGuard.canActivate(context);
          break;
        case 'timeRestriction':
          result = this.timeRestrictionGuard.canActivate(context);
          break;
        case 'geoLocation':
          result = this.geoLocationGuard.canActivate(context);
          break;
        case 'deviceType':
          result = this.deviceTypeGuard.canActivate(context);
          break;
        default:
          result = true;
      }

      results.push(result);
    }

    // 根据操作符组合结果
    if (compositeRules.operator === 'AND') {
      return results.every(result => result);
    } else {
      return results.some(result => result);
    }
  }
}

/**
 * 装饰器定义
 */
const { SetMetadata } = require('@nestjs/common');

// 游戏角色装饰器
const GameRoles = (...roles: string[]) => SetMetadata('gameRoles', roles);

// 时间限制装饰器
const TimeRestriction = (startHour: number, endHour: number, timezone?: string) => 
  SetMetadata('timeRestriction', { startHour, endHour, timezone });

// 地理位置限制装饰器
const GeoRestriction = (options: {
  allowedCountries?: string[];
  blockedCountries?: string[];
  allowedRegions?: string[];
}) => SetMetadata('geoRestriction', options);

// 设备类型限制装饰器
const DeviceRestriction = (options: {
  allowedDevices?: string[];
  blockedDevices?: string[];
}) => SetMetadata('deviceRestriction', options);

// 需要活跃会话装饰器
const RequireActiveSession = () => SetMetadata('requireActiveSession', true);

// 组合规则装饰器
const CompositeRules = (operator: 'AND' | 'OR', rules: string[]) => 
  SetMetadata('compositeRules', { operator, rules });

/**
 * 使用示例
 */

// 示例控制器
class GameController {
  /**
   * 只有球队经理可以访问
   */
  @GameRoles('team_manager')
  @UseGuards(GameRoleGuard)
  createTeam() {
    return '创建球队';
  }

  /**
   * 只在工作时间可以访问
   */
  @TimeRestriction(9, 18) // 9:00 - 18:00
  @UseGuards(TimeRestrictionGuard)
  adminOperation() {
    return '管理员操作';
  }

  /**
   * 只允许中国用户访问
   */
  @GeoRestriction({ allowedCountries: ['CN'] })
  @UseGuards(GeoLocationGuard)
  chinaOnlyFeature() {
    return '中国专属功能';
  }

  /**
   * 只允许移动设备访问
   */
  @DeviceRestriction({ allowedDevices: ['mobile'] })
  @UseGuards(DeviceTypeGuard)
  mobileOnlyFeature() {
    return '移动端专属功能';
  }

  /**
   * 需要活跃游戏会话
   */
  @RequireActiveSession()
  @UseGuards(GameSessionGuard)
  gameAction() {
    return '游戏操作';
  }

  /**
   * 组合条件：VIP会员 AND 工作时间 AND 桌面设备
   */
  @CompositeRules('AND', ['gameRole', 'timeRestriction', 'deviceType'])
  @GameRoles('vip_member')
  @TimeRestriction(9, 18)
  @DeviceRestriction({ allowedDevices: ['desktop'] })
  @UseGuards(CompositeGuard)
  vipDesktopFeature() {
    return 'VIP桌面专属功能';
  }

  /**
   * 组合条件：管理员 OR VIP会员
   */
  @CompositeRules('OR', ['gameRole'])
  @GameRoles('admin', 'vip_member')
  @UseGuards(CompositeGuard)
  premiumFeature() {
    return '高级功能';
  }
}

/**
 * 守卫使用最佳实践
 */
function guardBestPractices() {
  console.log('\n🛡️ 自定义守卫最佳实践');
  console.log('='.repeat(50));

  console.log('1️⃣ 守卫设计原则:');
  console.log('  • 单一职责：每个守卫只负责一种检查');
  console.log('  • 可组合：支持多个守卫组合使用');
  console.log('  • 可配置：通过装饰器参数配置行为');
  console.log('  • 性能优化：避免重复的数据库查询');

  console.log('\n2️⃣ 错误处理:');
  console.log('  • 返回 false：静默拒绝访问');
  console.log('  • 抛出异常：提供详细的错误信息');
  console.log('  • 记录日志：记录安全相关的访问尝试');

  console.log('\n3️⃣ 性能考虑:');
  console.log('  • 缓存检查结果：避免重复计算');
  console.log('  • 异步操作：使用 async/await 处理数据库查询');
  console.log('  • 早期返回：优先检查简单条件');

  console.log('\n4️⃣ 安全注意事项:');
  console.log('  • 输入验证：验证所有输入参数');
  console.log('  • 权限升级：防止权限提升攻击');
  console.log('  • 审计日志：记录所有权限检查');

  console.log('\n5️⃣ 测试策略:');
  console.log('  • 单元测试：测试各种条件组合');
  console.log('  • 集成测试：测试守卫在实际场景中的表现');
  console.log('  • 安全测试：测试绕过守卫的尝试');
}

/**
 * 守卫性能监控示例
 */
@Injectable()
class PerformanceMonitoringGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const startTime = Date.now();
    
    try {
      // 执行实际的权限检查
      const result = await this.performSecurityCheck(context);
      
      const duration = Date.now() - startTime;
      
      // 记录性能指标
      console.log(`守卫执行时间: ${duration}ms`);
      
      if (duration > 100) {
        console.warn('守卫执行时间过长，可能需要优化');
      }
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`守卫执行失败 (${duration}ms):`, error.message);
      return false;
    }
  }

  private async performSecurityCheck(context: ExecutionContext): Promise<boolean> {
    // 模拟安全检查
    await new Promise(resolve => setTimeout(resolve, Math.random() * 50));
    return Math.random() > 0.1; // 90% 通过率
  }
}

// 导出所有守卫和装饰器
module.exports = {
  GameRoleGuard,
  TimeRestrictionGuard,
  GeoLocationGuard,
  DeviceTypeGuard,
  GameSessionGuard,
  TeamOwnershipGuard,
  CompositeGuard,
  PerformanceMonitoringGuard,
  
  // 装饰器
  GameRoles,
  TimeRestriction,
  GeoRestriction,
  DeviceRestriction,
  RequireActiveSession,
  CompositeRules,
  
  // 示例和最佳实践
  GameController,
  guardBestPractices,
};
