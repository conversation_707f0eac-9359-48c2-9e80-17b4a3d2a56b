#!/usr/bin/env node

/**
 * 足球经理认证服务示例运行器
 * 
 * 统一运行所有示例，展示认证服务的完整功能
 */

const readline = require('readline');

// 导入所有示例模块
const userRegistration = require('./basic-usage/user-registration');
const userLogin = require('./basic-usage/user-login');
const totpSetup = require('./mfa-integration/totp-setup');
const gameRoles = require('./role-permissions/game-roles');

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * 主菜单
 */
function showMainMenu() {
  console.clear();
  console.log('🏆 足球经理认证服务示例运行器');
  console.log('='.repeat(50));
  console.log('');
  console.log('📚 可用示例:');
  console.log('');
  console.log('🔰 基础功能:');
  console.log('  1. 用户注册示例');
  console.log('  2. 用户登录示例');
  console.log('  3. 令牌管理示例');
  console.log('  4. 密码操作示例');
  console.log('');
  console.log('🔐 多因子认证:');
  console.log('  5. TOTP设置示例');
  console.log('  6. MFA登录示例');
  console.log('  7. 备用码管理示例');
  console.log('');
  console.log('👥 角色权限:');
  console.log('  8. 游戏角色示例');
  console.log('  9. 权限检查示例');
  console.log('  10. 角色升级示例');
  console.log('');
  console.log('🛡️ 安全功能:');
  console.log('  11. 审计日志示例');
  console.log('  12. 风险评估示例');
  console.log('  13. 会话管理示例');
  console.log('');
  console.log('🔧 高级功能:');
  console.log('  14. 自定义守卫示例');
  console.log('  15. 中间件使用示例');
  console.log('  16. 事件处理示例');
  console.log('');
  console.log('🎯 综合演示:');
  console.log('  17. 完整认证流程');
  console.log('  18. 游戏场景演示');
  console.log('  19. 性能测试演示');
  console.log('');
  console.log('📖 其他选项:');
  console.log('  20. 查看API文档');
  console.log('  21. 最佳实践指南');
  console.log('  22. 故障排除指南');
  console.log('');
  console.log('  0. 退出');
  console.log('');
}

/**
 * 处理用户选择
 */
async function handleUserChoice(choice) {
  console.clear();
  
  try {
    switch (choice) {
      case '1':
        console.log('🚀 运行用户注册示例...\n');
        await userRegistration.runAllRegistrationExamples();
        break;
        
      case '2':
        console.log('🚀 运行用户登录示例...\n');
        await userLogin.runAllLoginExamples();
        break;
        
      case '3':
        console.log('🚀 运行令牌管理示例...\n');
        await runTokenManagementExample();
        break;
        
      case '4':
        console.log('🚀 运行密码操作示例...\n');
        await runPasswordOperationsExample();
        break;
        
      case '5':
        console.log('🚀 运行TOTP设置示例...\n');
        await totpSetup.runAllTotpExamples();
        break;
        
      case '6':
        console.log('🚀 运行MFA登录示例...\n');
        await runMfaLoginExample();
        break;
        
      case '7':
        console.log('🚀 运行备用码管理示例...\n');
        await runBackupCodesExample();
        break;
        
      case '8':
        console.log('🚀 运行游戏角色示例...\n');
        await gameRoles.runAllGameRoleExamples();
        break;
        
      case '9':
        console.log('🚀 运行权限检查示例...\n');
        await runPermissionCheckExample();
        break;
        
      case '10':
        console.log('🚀 运行角色升级示例...\n');
        await gameRoles.roleUpgradeExample();
        break;
        
      case '11':
        console.log('🚀 运行审计日志示例...\n');
        await runAuditLogExample();
        break;
        
      case '12':
        console.log('🚀 运行风险评估示例...\n');
        await runRiskAssessmentExample();
        break;
        
      case '13':
        console.log('🚀 运行会话管理示例...\n');
        await userLogin.sessionManagementExample();
        break;
        
      case '14':
        console.log('🚀 运行自定义守卫示例...\n');
        await runCustomGuardsExample();
        break;
        
      case '15':
        console.log('🚀 运行中间件使用示例...\n');
        await runMiddlewareExample();
        break;
        
      case '16':
        console.log('🚀 运行事件处理示例...\n');
        await runEventHandlingExample();
        break;
        
      case '17':
        console.log('🚀 运行完整认证流程...\n');
        await runCompleteAuthFlow();
        break;
        
      case '18':
        console.log('🚀 运行游戏场景演示...\n');
        await runGameScenarioDemo();
        break;
        
      case '19':
        console.log('🚀 运行性能测试演示...\n');
        await runPerformanceTestDemo();
        break;
        
      case '20':
        showApiDocumentation();
        break;
        
      case '21':
        showBestPractices();
        break;
        
      case '22':
        showTroubleshootingGuide();
        break;
        
      case '0':
        console.log('👋 感谢使用足球经理认证服务示例！');
        process.exit(0);
        break;
        
      default:
        console.log('❌ 无效选择，请重新输入');
    }
  } catch (error) {
    console.error('❌ 示例运行出错:', error.message);
    console.error('💡 请检查认证服务是否正在运行 (http://localhost:3001)');
  }
  
  console.log('\n按回车键返回主菜单...');
  await waitForEnter();
}

/**
 * 等待用户按回车键
 */
function waitForEnter() {
  return new Promise(resolve => {
    rl.question('', () => resolve());
  });
}

/**
 * 获取用户输入
 */
function getUserInput(prompt) {
  return new Promise(resolve => {
    rl.question(prompt, answer => resolve(answer));
  });
}

/**
 * 令牌管理示例
 */
async function runTokenManagementExample() {
  console.log('🔑 令牌管理示例');
  console.log('='.repeat(40));
  
  const authClient = require('./basic-usage/auth-client');
  
  try {
    // 登录获取令牌
    console.log('1️⃣ 登录获取令牌...');
    await authClient.login({
      identifier: 'admin',
      password: 'Admin123!@#',
    });
    console.log('✅ 登录成功，已获取令牌');
    
    // 验证令牌
    console.log('\n2️⃣ 验证令牌...');
    const verification = await authClient.verifyToken();
    console.log('✅ 令牌验证成功:', verification.data.user.username);
    
    // 刷新令牌
    console.log('\n3️⃣ 刷新令牌...');
    await authClient.refreshAccessToken();
    console.log('✅ 令牌刷新成功');
    
    // 获取用户信息
    console.log('\n4️⃣ 使用令牌获取用户信息...');
    const profile = await authClient.getProfile();
    console.log('✅ 用户信息获取成功:', profile.data.user.username);
    
    console.log('\n🎉 令牌管理示例完成！');
  } catch (error) {
    console.error('❌ 令牌管理示例失败:', error.message);
  }
}

/**
 * 完整认证流程演示
 */
async function runCompleteAuthFlow() {
  console.log('🔄 完整认证流程演示');
  console.log('='.repeat(40));
  
  const authClient = require('./basic-usage/auth-client');
  
  try {
    // 1. 用户注册
    console.log('1️⃣ 用户注册...');
    const userData = {
      username: `demo_user_${Date.now()}`,
      email: `demo${Date.now()}@example.com`,
      password: 'DemoPassword123!',
      profile: {
        firstName: '演示',
        lastName: '用户',
        language: 'zh',
      },
      gameProfile: {
        experienceLevel: 'beginner',
        favoriteTeam: 'Barcelona',
      },
      acceptTerms: true,
    };
    
    const registerResult = await authClient.register(userData);
    console.log('✅ 注册成功:', registerResult.data.user.username);
    
    // 2. 用户登录
    console.log('\n2️⃣ 用户登录...');
    const loginResult = await authClient.login({
      identifier: userData.username,
      password: userData.password,
    });
    console.log('✅ 登录成功');
    
    // 3. 获取用户信息
    console.log('\n3️⃣ 获取用户信息...');
    const profile = await authClient.getProfile();
    console.log('✅ 用户信息:', {
      username: profile.data.user.username,
      email: profile.data.user.email,
      roles: profile.data.user.roles,
    });
    
    // 4. 检查权限
    console.log('\n4️⃣ 检查游戏权限...');
    const canCreateTeam = await authClient.checkPermission('team', 'create');
    console.log(`✅ 创建球队权限: ${canCreateTeam ? '有' : '无'}`);
    
    // 5. 会话管理
    console.log('\n5️⃣ 会话管理...');
    const sessions = await authClient.getSessions();
    console.log(`✅ 当前活跃会话: ${sessions.data.sessions.length} 个`);
    
    // 6. 登出
    console.log('\n6️⃣ 用户登出...');
    await authClient.logout();
    console.log('✅ 登出成功');
    
    console.log('\n🎉 完整认证流程演示完成！');
  } catch (error) {
    console.error('❌ 认证流程演示失败:', error.message);
  }
}

/**
 * 游戏场景演示
 */
async function runGameScenarioDemo() {
  console.log('🎮 游戏场景演示');
  console.log('='.repeat(40));
  
  console.log('📋 足球经理游戏场景:');
  console.log('');
  
  const scenarios = [
    {
      title: '新手玩家入门',
      description: '新玩家注册 → 创建球队 → 开始游戏',
      steps: [
        '注册账户',
        '完成新手教程',
        '创建第一支球队',
        '招募初始球员',
        '参加友谊赛',
      ],
    },
    {
      title: '球队经理进阶',
      description: '经验玩家 → 球员交易 → 战术调整',
      steps: [
        '分析球队阵容',
        '在转会市场寻找球员',
        '进行球员交易',
        '调整战术设置',
        '参加联赛比赛',
      ],
    },
    {
      title: '联赛管理员',
      description: '管理员 → 创建联赛 → 管理比赛',
      steps: [
        '创建新赛季联赛',
        '邀请球队参加',
        '安排比赛日程',
        '监控比赛进行',
        '发布联赛排名',
      ],
    },
  ];
  
  scenarios.forEach((scenario, index) => {
    console.log(`${index + 1}. ${scenario.title}`);
    console.log(`   ${scenario.description}`);
    console.log('   步骤:');
    scenario.steps.forEach((step, stepIndex) => {
      console.log(`     ${stepIndex + 1}) ${step}`);
    });
    console.log('');
  });
  
  console.log('💡 这些场景展示了认证服务在实际游戏中的应用');
  console.log('🔐 每个操作都需要相应的权限验证');
  console.log('📊 所有活动都会被记录在审计日志中');
}

/**
 * API文档展示
 */
function showApiDocumentation() {
  console.log('📖 API文档');
  console.log('='.repeat(40));
  
  console.log('🌐 在线文档:');
  console.log('  Swagger UI: http://localhost:3001/api/docs');
  console.log('  API规范: http://localhost:3001/api/docs-json');
  console.log('');
  
  console.log('📚 主要API端点:');
  console.log('');
  console.log('🔐 认证相关:');
  console.log('  POST /auth/register     - 用户注册');
  console.log('  POST /auth/login        - 用户登录');
  console.log('  POST /auth/refresh      - 刷新令牌');
  console.log('  POST /auth/logout       - 用户登出');
  console.log('  POST /auth/verify-token - 验证令牌');
  console.log('');
  console.log('🔒 多因子认证:');
  console.log('  GET  /auth/mfa/status   - MFA状态');
  console.log('  POST /auth/mfa/setup    - MFA设置');
  console.log('  POST /auth/mfa/enable   - 启用MFA');
  console.log('  DELETE /auth/mfa/disable - 禁用MFA');
  console.log('');
  console.log('👤 用户管理:');
  console.log('  GET  /users/me          - 获取个人信息');
  console.log('  PUT  /users/me          - 更新个人信息');
  console.log('  PUT  /users/me/password - 修改密码');
  console.log('  GET  /users/me/sessions - 获取会话列表');
  console.log('');
  console.log('👥 角色权限:');
  console.log('  GET  /roles             - 角色列表');
  console.log('  GET  /permissions       - 权限列表');
  console.log('  POST /auth/check-permission - 检查权限');
  console.log('');
  console.log('🏥 健康检查:');
  console.log('  GET  /health            - 服务健康状态');
  console.log('  GET  /health/detailed   - 详细健康信息');
}

/**
 * 最佳实践指南
 */
function showBestPractices() {
  console.log('💡 最佳实践指南');
  console.log('='.repeat(40));
  
  console.log('🔐 安全最佳实践:');
  console.log('  • 使用强密码策略');
  console.log('  • 启用多因子认证');
  console.log('  • 定期轮换JWT密钥');
  console.log('  • 监控异常登录活动');
  console.log('  • 实施最小权限原则');
  console.log('');
  
  console.log('🚀 性能最佳实践:');
  console.log('  • 使用Redis缓存会话');
  console.log('  • 实施令牌黑名单');
  console.log('  • 优化数据库查询');
  console.log('  • 使用连接池');
  console.log('  • 监控响应时间');
  console.log('');
  
  console.log('🔧 开发最佳实践:');
  console.log('  • 使用环境变量配置');
  console.log('  • 编写完整的测试');
  console.log('  • 实施代码审查');
  console.log('  • 使用版本控制');
  console.log('  • 文档化API接口');
  console.log('');
  
  console.log('📊 监控最佳实践:');
  console.log('  • 设置健康检查');
  console.log('  • 收集性能指标');
  console.log('  • 配置告警规则');
  console.log('  • 分析审计日志');
  console.log('  • 定期安全扫描');
}

/**
 * 故障排除指南
 */
function showTroubleshootingGuide() {
  console.log('🔧 故障排除指南');
  console.log('='.repeat(40));
  
  console.log('❌ 常见问题:');
  console.log('');
  
  console.log('1. 连接失败');
  console.log('   问题: 无法连接到认证服务');
  console.log('   解决: 检查服务是否运行在 http://localhost:3001');
  console.log('   命令: curl http://localhost:3001/health');
  console.log('');
  
  console.log('2. 登录失败');
  console.log('   问题: 用户名或密码错误');
  console.log('   解决: 确认使用正确的凭据');
  console.log('   默认: admin / Admin123!@#');
  console.log('');
  
  console.log('3. 权限不足');
  console.log('   问题: 403 Forbidden 错误');
  console.log('   解决: 检查用户角色和权限');
  console.log('   命令: GET /users/me');
  console.log('');
  
  console.log('4. 令牌过期');
  console.log('   问题: 401 Unauthorized 错误');
  console.log('   解决: 使用刷新令牌获取新的访问令牌');
  console.log('   命令: POST /auth/refresh');
  console.log('');
  
  console.log('5. 数据库连接');
  console.log('   问题: MongoDB 连接失败');
  console.log('   解决: 确认 MongoDB 服务运行正常');
  console.log('   命令: mongosh --eval "db.adminCommand(\'ping\')"');
  console.log('');
  
  console.log('6. Redis 连接');
  console.log('   问题: Redis 连接失败');
  console.log('   解决: 确认 Redis 服务运行正常');
  console.log('   命令: redis-cli ping');
  console.log('');
  
  console.log('📞 获取帮助:');
  console.log('  • 查看日志: docker logs fm-auth-service');
  console.log('  • 健康检查: http://localhost:3001/health');
  console.log('  • API文档: http://localhost:3001/api/docs');
  console.log('  • GitHub Issues: https://github.com/your-repo/issues');
}

// 占位符函数
async function runPasswordOperationsExample() {
  console.log('🔑 密码操作示例开发中...');
}

async function runMfaLoginExample() {
  console.log('🔐 MFA登录示例开发中...');
}

async function runBackupCodesExample() {
  console.log('🔑 备用码管理示例开发中...');
}

async function runPermissionCheckExample() {
  console.log('🔍 权限检查示例开发中...');
}

async function runAuditLogExample() {
  console.log('📋 审计日志示例开发中...');
}

async function runRiskAssessmentExample() {
  console.log('⚠️ 风险评估示例开发中...');
}

async function runCustomGuardsExample() {
  console.log('🛡️ 自定义守卫示例开发中...');
}

async function runMiddlewareExample() {
  console.log('🔧 中间件使用示例开发中...');
}

async function runEventHandlingExample() {
  console.log('📡 事件处理示例开发中...');
}

async function runPerformanceTestDemo() {
  console.log('⚡ 性能测试演示开发中...');
}

/**
 * 主程序
 */
async function main() {
  console.log('🚀 启动足球经理认证服务示例运行器...\n');
  
  // 检查服务连接
  try {
    const authClient = require('./basic-usage/auth-client');
    const response = await authClient.api.get('/health');
    console.log('✅ 认证服务连接正常\n');
  } catch (error) {
    console.log('❌ 无法连接到认证服务');
    console.log('💡 请确保服务运行在 http://localhost:3001');
    console.log('🚀 启动命令: npm run start:dev\n');
  }
  
  while (true) {
    showMainMenu();
    const choice = await getUserInput('请选择示例 (0-22): ');
    await handleUserChoice(choice.trim());
  }
}

// 运行主程序
if (require.main === module) {
  main().catch(error => {
    console.error('程序运行出错:', error);
    process.exit(1);
  });
}

module.exports = {
  main,
  runCompleteAuthFlow,
  runGameScenarioDemo,
  showApiDocumentation,
  showBestPractices,
  showTroubleshootingGuide,
};
