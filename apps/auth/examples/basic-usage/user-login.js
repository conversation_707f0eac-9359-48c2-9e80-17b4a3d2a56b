/**
 * 用户登录示例
 * 
 * 演示如何使用认证服务进行用户登录，包括：
 * - 用户名登录
 * - 邮箱登录
 * - 记住我功能
 * - 设备管理
 * - 错误处理
 */

const authClient = require('./auth-client');

/**
 * 基础登录示例
 */
async function basicLogin() {
  console.log('\n🔑 基础登录示例');
  console.log('='.repeat(40));

  try {
    const credentials = {
      identifier: 'admin', // 可以是用户名或邮箱
      password: 'Admin123!@#',
    };

    const result = await authClient.login(credentials);
    
    console.log('✅ 登录成功!');
    console.log('用户信息:', {
      id: result.data.user.id,
      username: result.data.user.username,
      email: result.data.user.email,
      roles: result.data.user.roles,
    });
    console.log('会话信息:', {
      sessionId: result.data.session.id,
      deviceId: result.data.session.deviceId,
      expiresAt: result.data.session.expiresAt,
    });
    console.log('令牌信息:', {
      accessToken: result.data.tokens.accessToken.substring(0, 20) + '...',
      refreshToken: result.data.tokens.refreshToken.substring(0, 20) + '...',
      expiresIn: result.data.tokens.expiresIn,
    });
    
    return result.data;
  } catch (error) {
    console.error('❌ 登录失败:', error.message);
    throw error;
  }
}

/**
 * 邮箱登录示例
 */
async function emailLogin() {
  console.log('\n📧 邮箱登录示例');
  console.log('='.repeat(40));

  try {
    const credentials = {
      identifier: '<EMAIL>',
      password: 'Admin123!@#',
    };

    const result = await authClient.login(credentials);
    
    console.log('✅ 邮箱登录成功!');
    console.log('用户:', result.data.user.username);
    
    return result.data;
  } catch (error) {
    console.error('❌ 邮箱登录失败:', error.message);
    throw error;
  }
}

/**
 * 记住我功能示例
 */
async function rememberMeLogin() {
  console.log('\n💾 记住我功能示例');
  console.log('='.repeat(40));

  try {
    const credentials = {
      identifier: 'admin',
      password: 'Admin123!@#',
      rememberMe: true, // 启用记住我功能
    };

    const result = await authClient.login(credentials);
    
    console.log('✅ 记住我登录成功!');
    console.log('刷新令牌有效期更长:', result.data.tokens.refreshTokenExpiresIn);
    
    return result.data;
  } catch (error) {
    console.error('❌ 记住我登录失败:', error.message);
    throw error;
  }
}

/**
 * 设备信息登录示例
 */
async function deviceInfoLogin() {
  console.log('\n📱 设备信息登录示例');
  console.log('='.repeat(40));

  try {
    const credentials = {
      identifier: 'admin',
      password: 'Admin123!@#',
    };

    const deviceInfo = {
      type: 'mobile',
      name: 'iPhone 15 Pro',
      fingerprint: 'ios-device-12345',
      userAgent: 'FootballManager/1.0 (iOS 17.0; iPhone15,2)',
      ipAddress: '*************',
      location: {
        country: 'CN',
        city: 'Beijing',
        latitude: 39.9042,
        longitude: 116.4074,
      },
    };

    const result = await authClient.login(credentials, deviceInfo);
    
    console.log('✅ 设备信息登录成功!');
    console.log('设备信息:', {
      deviceId: result.data.session.deviceId,
      deviceType: result.data.session.deviceInfo?.type,
      deviceName: result.data.session.deviceInfo?.name,
    });
    
    return result.data;
  } catch (error) {
    console.error('❌ 设备信息登录失败:', error.message);
    throw error;
  }
}

/**
 * 多设备登录示例
 */
async function multiDeviceLogin() {
  console.log('\n🖥️📱 多设备登录示例');
  console.log('='.repeat(40));

  const credentials = {
    identifier: 'admin',
    password: 'Admin123!@#',
  };

  const devices = [
    {
      type: 'web',
      name: 'Chrome Browser',
      fingerprint: 'web-chrome-12345',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      ipAddress: '*************',
    },
    {
      type: 'mobile',
      name: 'Android Phone',
      fingerprint: 'android-device-67890',
      userAgent: 'FootballManager/1.0 (Android 14; SM-G998B)',
      ipAddress: '*************',
    },
    {
      type: 'desktop',
      name: 'Windows App',
      fingerprint: 'windows-app-54321',
      userAgent: 'FootballManager-Desktop/1.0 (Windows 11)',
      ipAddress: '*************',
    },
  ];

  const sessions = [];

  for (const device of devices) {
    try {
      // 为每个设备创建新的客户端实例
      const { AuthClient } = require('./auth-client');
      const deviceClient = new AuthClient();
      
      const result = await deviceClient.login(credentials, device);
      sessions.push({
        device: device.name,
        sessionId: result.data.session.id,
        client: deviceClient,
      });
      
      console.log(`✅ ${device.name} 登录成功`);
    } catch (error) {
      console.error(`❌ ${device.name} 登录失败:`, error.message);
    }
  }

  console.log(`\n📊 多设备登录结果: ${sessions.length}/${devices.length} 设备成功登录`);
  
  // 获取会话列表
  if (sessions.length > 0) {
    try {
      const sessionList = await sessions[0].client.getSessions();
      console.log('当前活跃会话数:', sessionList.data.sessions.length);
    } catch (error) {
      console.error('获取会话列表失败:', error.message);
    }
  }

  return sessions;
}

/**
 * 登录错误处理示例
 */
async function loginErrorHandling() {
  console.log('\n🚨 登录错误处理示例');
  console.log('='.repeat(40));

  const errorCases = [
    {
      name: '错误密码',
      credentials: {
        identifier: 'admin',
        password: 'wrongpassword',
      },
    },
    {
      name: '不存在的用户',
      credentials: {
        identifier: 'nonexistentuser',
        password: 'anypassword',
      },
    },
    {
      name: '空用户名',
      credentials: {
        identifier: '',
        password: 'password',
      },
    },
    {
      name: '空密码',
      credentials: {
        identifier: 'admin',
        password: '',
      },
    },
  ];

  for (const testCase of errorCases) {
    try {
      await authClient.login(testCase.credentials);
      console.log(`❌ ${testCase.name}: 应该失败但成功了`);
    } catch (error) {
      console.log(`✅ ${testCase.name}: ${error.message}`);
      
      // 展示错误详情
      if (error.code) {
        console.log(`   错误代码: ${error.code}`);
      }
      if (error.status) {
        console.log(`   HTTP状态: ${error.status}`);
      }
    }
  }
}

/**
 * 暴力破解防护测试
 */
async function bruteForceProtectionTest() {
  console.log('\n🛡️ 暴力破解防护测试');
  console.log('='.repeat(40));

  const credentials = {
    identifier: 'admin',
    password: 'wrongpassword',
  };

  let attemptCount = 0;
  let rateLimited = false;

  console.log('开始连续错误登录尝试...');

  for (let i = 0; i < 12; i++) {
    try {
      await authClient.login(credentials);
      console.log(`尝试 ${i + 1}: 意外成功`);
    } catch (error) {
      attemptCount++;
      
      if (error.status === 429) {
        rateLimited = true;
        console.log(`尝试 ${i + 1}: 被限流阻止 - ${error.message}`);
        break;
      } else {
        console.log(`尝试 ${i + 1}: 登录失败 - ${error.message}`);
      }
    }
    
    // 短暂延迟
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log(`\n📊 暴力破解防护测试结果:`);
  console.log(`总尝试次数: ${attemptCount}`);
  console.log(`是否触发限流: ${rateLimited ? '是' : '否'}`);
  
  if (rateLimited) {
    console.log('✅ 暴力破解防护正常工作');
  } else {
    console.log('⚠️ 未检测到暴力破解防护');
  }
}

/**
 * 会话管理示例
 */
async function sessionManagementExample() {
  console.log('\n🔄 会话管理示例');
  console.log('='.repeat(40));

  try {
    // 登录
    const credentials = {
      identifier: 'admin',
      password: 'Admin123!@#',
    };

    await authClient.login(credentials);
    console.log('✅ 登录成功');

    // 获取会话列表
    const sessions = await authClient.getSessions();
    console.log(`当前活跃会话: ${sessions.data.sessions.length} 个`);

    // 显示会话详情
    sessions.data.sessions.forEach((session, index) => {
      console.log(`会话 ${index + 1}:`);
      console.log(`  ID: ${session.id}`);
      console.log(`  设备: ${session.deviceInfo?.name || '未知'}`);
      console.log(`  IP: ${session.ipAddress}`);
      console.log(`  最后活动: ${session.lastActivity}`);
      console.log(`  状态: ${session.active ? '活跃' : '非活跃'}`);
    });

    // 终止其他会话（保留当前会话）
    const otherSessions = sessions.data.sessions.filter(
      session => session.id !== sessions.data.currentSessionId
    );

    if (otherSessions.length > 0) {
      console.log(`\n终止 ${otherSessions.length} 个其他会话...`);
      
      for (const session of otherSessions) {
        try {
          await authClient.terminateSession(session.id);
          console.log(`✅ 会话 ${session.id} 已终止`);
        } catch (error) {
          console.error(`❌ 终止会话 ${session.id} 失败:`, error.message);
        }
      }
    }

    // 验证当前会话仍然有效
    const profile = await authClient.getProfile();
    console.log(`✅ 当前会话仍然有效，用户: ${profile.data.user.username}`);

  } catch (error) {
    console.error('❌ 会话管理示例失败:', error.message);
    throw error;
  }
}

/**
 * 运行所有登录示例
 */
async function runAllLoginExamples() {
  console.log('🚀 开始运行用户登录示例...\n');

  try {
    // 基础登录
    await basicLogin();
    
    // 邮箱登录
    await emailLogin();
    
    // 记住我功能
    await rememberMeLogin();
    
    // 设备信息登录
    await deviceInfoLogin();
    
    // 多设备登录
    await multiDeviceLogin();
    
    // 错误处理
    await loginErrorHandling();
    
    // 暴力破解防护
    await bruteForceProtectionTest();
    
    // 会话管理
    await sessionManagementExample();
    
    console.log('\n🎉 所有登录示例运行完成!');
  } catch (error) {
    console.error('\n💥 示例运行出错:', error.message);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runAllLoginExamples();
}

module.exports = {
  basicLogin,
  emailLogin,
  rememberMeLogin,
  deviceInfoLogin,
  multiDeviceLogin,
  loginErrorHandling,
  bruteForceProtectionTest,
  sessionManagementExample,
  runAllLoginExamples,
};
