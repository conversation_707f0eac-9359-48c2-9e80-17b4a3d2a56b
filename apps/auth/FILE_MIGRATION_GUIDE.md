# Auth服务文件迁移指南

## 📁 目录结构已创建完成

所有新的目录结构已经创建完成，现在您可以按照以下指南手动移动文件。

## 🔄 文件迁移映射表

### **1. Auth模块 (modules/auth/)**

#### 控制器 (controllers/)
```
src/domain/auth/auth.controller.ts → modules/auth/controllers/auth.controller.ts
src/domain/auth/mfa.controller.ts → modules/auth/controllers/mfa.controller.ts
src/domain/character-auth/controllers/character-auth.controller.ts → modules/auth/controllers/character-auth.controller.ts
```

#### 服务 (services/)
```
src/domain/auth/auth.service.ts → modules/auth/services/auth.service.ts
src/domain/auth/jwt.service.ts → modules/auth/services/jwt.service.ts
src/domain/auth/mfa.service.ts → modules/auth/services/mfa.service.ts
src/domain/auth/password.service.ts → modules/auth/services/password.service.ts
src/domain/character-auth/services/character-auth.service.ts → modules/auth/services/character-auth.service.ts
src/domain/character-auth/services/character-session.service.ts → modules/auth/services/character-session.service.ts
```

#### 策略 (strategies/)
```
src/domain/auth/jwt.strategy.ts → modules/auth/strategies/jwt.strategy.ts
src/domain/auth/local.strategy.ts → modules/auth/strategies/local.strategy.ts
```

#### DTO (dto/)
```
src/domain/auth/dto/*.ts → modules/auth/dto/
src/domain/character-auth/dto/*.ts → modules/auth/dto/
```

#### 实体 (entities/)
```
src/domain/character-auth/entities/character-session.entity.ts → modules/auth/entities/character-session.entity.ts
```

#### 仓储 (repositories/)
```
src/domain/character-auth/repositories/*.ts → modules/auth/repositories/
```

### **2. RBAC模块 (modules/rbac/)**

#### 控制器 (controllers/)
```
src/domain/roles/roles.controller.ts → modules/rbac/controllers/roles.controller.ts
src/domain/permissions/permissions.controller.ts → modules/rbac/controllers/permissions.controller.ts
```

#### 服务 (services/)
```
src/domain/roles/roles.service.ts → modules/rbac/services/roles.service.ts
src/domain/permissions/permissions.service.ts → modules/rbac/services/permissions.service.ts
```

#### 实体 (entities/)
```
src/domain/roles/entities/role.entity.ts → modules/rbac/entities/role.entity.ts
src/domain/permissions/entities/permission.entity.ts → modules/rbac/entities/permission.entity.ts
```

#### 仓储 (repositories/)
```
src/domain/roles/repositories/role.repository.ts → modules/rbac/repositories/role.repository.ts
src/domain/permissions/repositories/permission.repository.ts → modules/rbac/repositories/permission.repository.ts
```

#### DTO (dto/)
```
src/domain/roles/dto/*.ts → modules/rbac/dto/
src/domain/permissions/dto/*.ts → modules/rbac/dto/
```

#### 守卫 (guards/)
```
src/infrastructure/guards/roles.guard.ts → modules/rbac/guards/roles.guard.ts
src/infrastructure/guards/permissions.guard.ts → modules/rbac/guards/permissions.guard.ts
src/common/guards/roles.guard.ts → modules/rbac/guards/roles.guard.ts (如果存在)
src/common/guards/permissions.guard.ts → modules/rbac/guards/permissions.guard.ts (如果存在)
```

### **3. 用户管理模块 (modules/user-management/)**

#### 控制器 (controllers/)
```
src/domain/users/users.controller.ts → modules/user-management/controllers/users.controller.ts
```

#### 服务 (services/)
```
src/domain/users/users.service.ts → modules/user-management/services/users.service.ts
src/domain/users/services/user-transformer.service.ts → modules/user-management/services/user-transformer.service.ts
```

#### 实体 (entities/)
```
src/domain/users/entities/user.entity.ts → modules/user-management/entities/user.entity.ts
```

#### 仓储 (repositories/)
```
src/domain/users/repositories/user.repository.ts → modules/user-management/repositories/user.repository.ts
```

#### DTO (dto/)
```
src/domain/users/dto/*.ts → modules/user-management/dto/
```

### **4. 会话管理模块 (modules/session-management/)**

#### 服务 (services/)
```
src/core/session/session.service.ts → modules/session-management/services/session.service.ts
```

#### 实体 (entities/)
```
src/core/session/entities/session.entity.ts → modules/session-management/entities/session.entity.ts
```

#### 仓储 (repositories/)
```
src/core/session/repositories/session.repository.ts → modules/session-management/repositories/session.repository.ts
```

### **5. 安全模块 (modules/security/)**

#### 服务 (services/)
```
src/core/security/security.service.ts → modules/security/services/security.service.ts
src/infrastructure/services/crypto.service.ts → modules/security/services/crypto.service.ts
src/infrastructure/services/validation.service.ts → modules/security/services/validation.service.ts
src/infrastructure/services/utils.service.ts → modules/security/services/utils.service.ts
```

#### 守卫 (guards/)
```
src/infrastructure/guards/jwt-auth.guard.ts → modules/security/guards/jwt-auth.guard.ts
src/infrastructure/guards/throttler-behind-proxy.guard.ts → modules/security/guards/throttler-behind-proxy.guard.ts
```

#### 拦截器 (interceptors/)
```
src/infrastructure/interceptors/*.ts → modules/security/interceptors/
```

#### 管道 (pipes/)
```
src/infrastructure/pipes/*.ts → modules/security/pipes/
```

#### 过滤器 (filters/)
```
src/infrastructure/filters/*.ts → modules/security/filters/
```

### **6. 管理模块 (modules/administration/)**

#### 控制器 (controllers/)
```
src/app/admin/admin.controller.ts → modules/administration/controllers/admin.controller.ts
src/app/admin/controllers/audit.controller.ts → modules/administration/controllers/audit.controller.ts
src/app/admin/controllers/security.controller.ts → modules/administration/controllers/security.controller.ts
src/app/admin/controllers/statistics.controller.ts → modules/administration/controllers/statistics.controller.ts
src/app/admin/controllers/system.controller.ts → modules/administration/controllers/system.controller.ts
src/app/admin/controllers/user-management.controller.ts → modules/administration/controllers/user-management.controller.ts
```

#### 服务 (services/)
```
src/app/admin/admin.service.ts → modules/administration/services/admin.service.ts
src/app/admin/services/audit-management.service.ts → modules/administration/services/audit-management.service.ts
src/app/admin/services/security-management.service.ts → modules/administration/services/security-management.service.ts
src/app/admin/services/statistics.service.ts → modules/administration/services/statistics.service.ts
src/app/admin/services/system.service.ts → modules/administration/services/system.service.ts
src/app/admin/services/user-management.service.ts → modules/administration/services/user-management.service.ts
```

#### DTO (dto/)
```
src/app/admin/dto/*.ts → modules/administration/dto/
```

### **7. 共享资源 (shared/)**

#### 配置 (config/)
```
src/config/app.config.ts → shared/config/app.config.ts
src/config/auth.config.ts → shared/config/auth.config.ts
src/config/database.config.ts → shared/config/database.config.ts
src/config/redis.config.ts → shared/config/redis.config.ts
src/config/security.config.ts → shared/config/security.config.ts
```

#### 常量 (constants/)
```
src/common/constants/*.ts → shared/constants/
```

#### 接口 (interfaces/)
```
src/common/interfaces/*.ts → shared/interfaces/
```

#### DTO (dto/)
```
src/common/dto/*.ts → shared/dto/
```

#### 装饰器 (decorators/)
```
src/common/decorators/*.ts → shared/decorators/
```

#### 工具 (utils/)
```
src/common/utils/*.ts → shared/utils/
```

#### 类型 (types/)
```
src/common/types/*.ts → shared/types/ (如果存在)
```

### **8. 基础设施 (infrastructure-new/)**

#### 数据库 (database/)
```
src/infrastructure/database/* → infrastructure-new/database/
```

#### 微服务 (microservices/)
```
src/infrastructure/microservices/*.ts → infrastructure-new/microservices/
```

#### 健康检查 (health/)
```
src/app/health/health.controller.ts → infrastructure-new/health/health.controller.ts
src/app/health/health.service.ts → infrastructure-new/health/health.service.ts
```

## ❌ 需要删除的文件/目录

### **用户历史模块 (需要删除)**
```
src/domain/user-history/ → 删除 (转移到profile服务)
```

### **旧的模块文件**
```
src/domain/auth/auth.module.ts → 删除 (将创建新的modules/auth/auth.module.ts)
src/domain/roles/roles.module.ts → 删除 (将创建新的modules/rbac/rbac.module.ts)
src/domain/permissions/permissions.module.ts → 删除 (合并到rbac模块)
src/domain/users/users.module.ts → 删除 (将创建新的modules/user-management/user-management.module.ts)
src/domain/character-auth/character-auth.module.ts → 删除 (合并到auth模块)
```

### **旧的共享模块**
```
src/infrastructure/shared.module.ts → 删除 (将创建新的shared/shared.module.ts)
src/core/shared/core.module.ts → 删除 (功能分散到各个模块)
```

## 📋 迁移后需要创建的新文件

### **模块文件**
- `modules/auth/auth.module.ts`
- `modules/rbac/rbac.module.ts`
- `modules/user-management/user-management.module.ts`
- `modules/session-management/session-management.module.ts`
- `modules/security/security.module.ts`
- `modules/administration/administration.module.ts`

### **共享文件**
- `shared/shared.module.ts`
- `shared/config/index.ts` (统一配置导出)

### **基础设施文件**
- `infrastructure-new/infrastructure.module.ts`
- `infrastructure-new/database/mongodb/mongodb.module.ts`
- `infrastructure-new/database/redis/redis.module.ts`
- `infrastructure-new/microservices/microservice.module.ts`
- `infrastructure-new/health/health.module.ts`

## 🔧 迁移后需要更新的文件

### **主模块**
- `src/app.module.ts` - 更新导入路径和模块引用

### **主入口**
- `src/main.ts` - 可能需要更新某些导入路径

## ⚠️ 注意事项

1. **备份原文件**: 移动文件前，建议先复制到 `backup/` 目录
2. **更新导入路径**: 移动文件后需要更新所有相关的导入路径
3. **模块依赖**: 注意模块间的依赖关系，按照依赖倒置原则进行重构
4. **测试文件**: 相应的测试文件也需要移动到对应的目录结构中

---

*按照此指南完成文件迁移后，您将拥有一个清晰、模块化的auth服务架构。*

## 📁 已创建的目录结构
```
apps/auth/src/
├── modules/                     # ✅ 功能模块
│   ├── auth/                   # ✅ 认证模块 (包含character-auth)
│   │   ├── controllers/        # ✅ 控制器
│   │   ├── services/           # ✅ 服务
│   │   ├── strategies/         # ✅ 认证策略
│   │   ├── dto/               # ✅ 数据传输对象
│   │   ├── entities/          # ✅ 实体
│   │   └── repositories/      # ✅ 仓储
│   │
│   ├── rbac/                   # ✅ 授权模块 (简化命名)
│   │   ├── controllers/        # ✅ 角色权限控制器
│   │   ├── services/           # ✅ 角色权限服务
│   │   ├── entities/          # ✅ 角色权限实体
│   │   ├── repositories/      # ✅ 角色权限仓储
│   │   ├── dto/               # ✅ 角色权限DTO
│   │   └── guards/            # ✅ 角色权限守卫
│   │
│   ├── user-management/        # ✅ 用户管理模块
│   │   ├── controllers/        # ✅ 用户控制器
│   │   ├── services/           # ✅ 用户服务
│   │   ├── entities/          # ✅ 用户实体
│   │   ├── repositories/      # ✅ 用户仓储
│   │   └── dto/               # ✅ 用户DTO
│   │
│   ├── session-management/     # ✅ 会话管理模块
│   │   ├── services/           # ✅ 会话服务
│   │   ├── entities/          # ✅ 会话实体
│   │   └── repositories/      # ✅ 会话仓储
│   │
│   ├── security/               # ✅ 安全模块
│   │   ├── services/           # ✅ 安全服务
│   │   ├── guards/            # ✅ 安全守卫
│   │   ├── interceptors/      # ✅ 拦截器
│   │   ├── pipes/             # ✅ 管道
│   │   └── filters/           # ✅ 过滤器
│   │
│   └── administration/         # ✅ 管理模块
│       ├── controllers/        # ✅ 管理控制器
│       ├── services/           # ✅ 管理服务
│       └── dto/               # ✅ 管理DTO
│
├── shared/                     # ✅ 共享资源
│   ├── config/                # ✅ 统一配置
│   ├── constants/             # ✅ 常量定义
│   ├── interfaces/            # ✅ 接口定义
│   ├── dto/                   # ✅ 共享DTO
│   ├── decorators/            # ✅ 装饰器
│   ├── utils/                 # ✅ 工具函数
│   └── types/                 # ✅ 类型定义
│
├── infrastructure-new/         # ✅ 基础设施 (新)
│   ├── database/              # ✅ 数据库配置
│   │   ├── mongodb/           # ✅ MongoDB配置
│   │   └── redis/             # ✅ Redis配置
│   ├── microservices/         # ✅ 微服务通信
│   └── health/                # ✅ 健康检查
│
└── backup/                     # ✅ 备份目录

```