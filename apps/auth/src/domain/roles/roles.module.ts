import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { RolesController } from '../../modules/rbac/controllers/roles.controller';
import { RolesService } from '../../modules/rbac/services/roles.service';
import { Role, RoleSchema } from '../../modules/rbac/entities/role.entity';
import { PermissionsModule } from '../permissions/permissions.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Role.name, schema: RoleSchema },
    ]),
    forwardRef(() => PermissionsModule), // 使用 forwardRef 解决循环依赖
  ],
  controllers: [RolesController],
  providers: [RolesService],
  exports: [RolesService],
})
export class RolesModule {}
