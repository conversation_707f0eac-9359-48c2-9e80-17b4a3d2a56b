import { ApiProperty } from '@nestjs/swagger';
import { 
  IsString, 
  IsOptional, 
  IsArray, 
  IsBoolean,
  IsNumber,
  IsEnum,
  MinLength, 
  <PERSON><PERSON>ength, 
  <PERSON>,
  Min,
  Max,
  ValidateNested
} from 'class-validator';
import { Type } from 'class-transformer';

// 角色约束DTO
export class RoleConstraintsDto {
  @ApiProperty({ 
    description: '最大用户数',
    example: 100,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxUsers?: number;

  @ApiProperty({ 
    description: '有效开始时间',
    example: '2024-01-01T00:00:00.000Z',
    required: false
  })
  @IsOptional()
  @IsString()
  validFrom?: string;

  @ApiProperty({ 
    description: '有效结束时间',
    example: '2024-12-31T23:59:59.999Z',
    required: false
  })
  @IsOptional()
  @IsString()
  validUntil?: string;

  @ApiProperty({ 
    description: '其他条件',
    example: { department: 'IT' },
    required: false
  })
  @IsOptional()
  conditions?: Record<string, any>;
}

// 创建角色DTO
export class CreateRoleDto {
  @ApiProperty({ 
    description: '角色名称',
    example: 'player',
    minLength: 2,
    maxLength: 50
  })
  @IsString()
  @MinLength(2, { message: '角色名称至少需要2个字符' })
  @MaxLength(50, { message: '角色名称不能超过50个字符' })
  @Matches(/^[a-zA-Z0-9_-]+$/, { 
    message: '角色名称只能包含字母、数字、下划线和连字符' 
  })
  name: string;

  @ApiProperty({ 
    description: '显示名称',
    example: '球员',
    minLength: 2,
    maxLength: 100
  })
  @IsString()
  @MinLength(2, { message: '显示名称至少需要2个字符' })
  @MaxLength(100, { message: '显示名称不能超过100个字符' })
  displayName: string;

  @ApiProperty({ 
    description: '角色描述',
    example: '游戏中的球员角色，可以参与比赛和训练',
    maxLength: 500,
    required: false
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: '角色描述不能超过500个字符' })
  description?: string;

  @ApiProperty({ 
    description: '角色权限',
    example: ['game:play', 'team:join'],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  permissions?: string[];

  @ApiProperty({ 
    description: '继承的角色',
    example: ['user'],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  inherits?: string[];

  @ApiProperty({ 
    description: '角色约束',
    type: RoleConstraintsDto,
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => RoleConstraintsDto)
  constraints?: RoleConstraintsDto;

  @ApiProperty({ 
    description: '角色分类',
    example: 'game',
    default: 'custom'
  })
  @IsOptional()
  @IsString()
  category?: string = 'custom';

  @ApiProperty({ 
    description: '优先级',
    example: 10,
    minimum: 0,
    maximum: 100,
    default: 0
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  priority?: number = 0;

  @ApiProperty({ 
    description: '角色颜色',
    example: '#FF5722',
    required: false
  })
  @IsOptional()
  @IsString()
  @Matches(/^#[0-9A-Fa-f]{6}$/, { message: '颜色必须是有效的十六进制颜色代码' })
  color?: string;

  @ApiProperty({
    description: '角色图标',
    example: 'sports_soccer',
    required: false
  })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiProperty({
    description: '是否系统角色',
    example: false,
    default: false,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  system?: boolean = false;

  @ApiProperty({
    description: '是否启用',
    example: true,
    default: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  enabled?: boolean = true;
}
