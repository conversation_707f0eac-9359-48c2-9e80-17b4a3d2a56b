import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';

// 核心模块
import { CoreModule } from '../../core/shared/core.module';
import { UsersModule } from '../users/users.module';
import { SessionModule } from '../../core/session/session.module';

// 用户历史模块
import { UserHistoryModule } from '../user-history/user-history.module';

// JWT服务
import { JwtService as AuthJwtService } from '../../modules/auth/services/jwt.service';



// 服务
import { CharacterAuthService } from '../../modules/auth/services/character-auth.service';
import { CharacterSessionService } from '../../modules/auth/services/character-session.service';

// 控制器
import { CharacterAuthController } from '../../modules/auth/controllers/character-auth.controller';

// 实体和Schema
import { CharacterSession, CharacterSessionSchema } from '../../modules/auth/entities/character-session.entity';

/**
 * 角色认证模块
 * 负责角色登录、角色登出、角色会话管理等功能
 */
@Module({
  imports: [
    ConfigModule,
    CoreModule,
    UsersModule,
    SessionModule,
    UserHistoryModule,

    // JWT模块 - 用于角色Token生成
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('auth.jwt.secret'),
        signOptions: {
          expiresIn: configService.get<string>('auth.jwt.accessTokenTTL'),
          issuer: configService.get<string>('auth.jwt.issuer'),
          audience: configService.get<string>('auth.jwt.audience'),
          algorithm: configService.get<string>('auth.jwt.algorithm') as any,
        },
      }),
      inject: [ConfigService],
    }),

    // MongoDB Schema注册
    MongooseModule.forFeature([
      { name: CharacterSession.name, schema: CharacterSessionSchema },
    ]),
  ],
  controllers: [
    CharacterAuthController,
  ],
  providers: [
    CharacterAuthService,
    CharacterSessionService,
  ],
  exports: [
    CharacterAuthService,
    CharacterSessionService,
  ],
})
export class CharacterAuthModule {}
