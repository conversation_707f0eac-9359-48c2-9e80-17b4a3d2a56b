import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { UserRepository } from './repositories/user.repository';
import { UserTransformerService } from './services/user-transformer.service';
import { User, UserSchema } from './entities/user.entity';
import { CoreModule } from '../../core/shared/core.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema }
    ]),
    CoreModule,
  ],
  controllers: [UsersController],
  providers: [
    UsersService,
    UserRepository,
    UserTransformerService,
  ],
  exports: [
    UsersService,
    UserRepository,
  ],
})
export class UsersModule {}
