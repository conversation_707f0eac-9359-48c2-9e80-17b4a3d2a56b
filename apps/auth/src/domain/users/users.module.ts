import { <PERSON>du<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UsersController } from '../../modules/user-management/controllers/users.controller';
import { UsersService } from '../../modules/user-management/services/users.service';
import { UserRepository } from '../../modules/user-management/repositories/user.repository';
import { UserTransformerService } from '../../modules/user-management/services/user-transformer.service';
import { User, UserSchema } from '../../modules/user-management/entities/user.entity';
import { CoreModule } from '../../core/shared/core.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema }
    ]),
    CoreModule,
  ],
  controllers: [UsersController],
  providers: [
    UsersService,
    UserRepository,
    UserTransformerService,
  ],
  exports: [
    UsersService,
    UserRepository,
  ],
})
export class UsersModule {}
