import { ApiProperty, PartialType, OmitType } from '@nestjs/swagger';
import { 
  IsString, 
  IsEmail, 
  IsOptional, 
  IsEnum, 
  IsDateString, 
  IsBoolean,
  MinLength, 
  MaxLength, 
  Matches,
  IsPhoneNumber,
  ValidateNested,
  IsArray,
  IsNumber,
  Min,
  Max
} from 'class-validator';
import { Type } from 'class-transformer';
import { CreateUserDto, CreateUserProfileDto, GamePreferencesDto } from './create-user.dto';
import { Gender, UserStatus } from '../entities/user.entity';

// 更新用户个人信息DTO
export class UpdateUserProfileDto extends PartialType(CreateUserProfileDto) {}

// 更新游戏个人信息DTO
export class UpdateGameProfileDto {
  @ApiProperty({ 
    description: '用户等级',
    example: 15,
    minimum: 1,
    maximum: 100,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  level?: number;

  @ApiProperty({ 
    description: '经验值',
    example: 12500,
    minimum: 0,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  experience?: number;

  @ApiProperty({ 
    description: '游戏币',
    example: 5000,
    minimum: 0,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  coins?: number;

  @ApiProperty({ 
    description: '会员到期时间',
    example: '2024-01-01T00:00:00.000Z',
    required: false
  })
  @IsOptional()
  @IsDateString()
  premiumUntil?: string;

  @ApiProperty({ 
    description: '成就列表',
    example: ['first_goal', 'champion'],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  achievements?: string[];

  @ApiProperty({ 
    description: '游戏偏好设置',
    type: GamePreferencesDto,
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => GamePreferencesDto)
  preferences?: GamePreferencesDto;
}

// 更新用户基本信息DTO
export class UpdateUserDto {
  @ApiProperty({
    description: '用户名',
    example: 'john_doe',
    required: false
  })
  @IsOptional()
  @IsString()
  @MinLength(3)
  @MaxLength(30)
  @Matches(/^[a-zA-Z0-9_-]+$/)
  username?: string;

  @ApiProperty({
    description: '邮箱地址',
    example: '<EMAIL>',
    required: false
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({
    description: '手机号码',
    example: '+1234567890',
    required: false
  })
  @IsOptional()
  @IsPhoneNumber()
  phone?: string;
  @ApiProperty({ 
    description: '用户个人信息',
    type: UpdateUserProfileDto,
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateUserProfileDto)
  profile?: UpdateUserProfileDto;

  @ApiProperty({ 
    description: '游戏个人信息',
    type: UpdateGameProfileDto,
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateGameProfileDto)
  gameProfile?: UpdateGameProfileDto;

  @ApiProperty({
    description: '邮箱验证状态',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  emailVerified?: boolean;

  @ApiProperty({
    description: '手机验证状态',
    example: false,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  phoneVerified?: boolean;
}

// 更新密码DTO
export class UpdatePasswordDto {
  @ApiProperty({ 
    description: '当前密码',
    example: 'OldPassword123!'
  })
  @IsString()
  @MinLength(1, { message: '请输入当前密码' })
  currentPassword: string;

  @ApiProperty({ 
    description: '新密码',
    example: 'NewSecurePass456!',
    minLength: 8,
    maxLength: 128
  })
  @IsString()
  @MinLength(8, { message: '新密码至少需要8个字符' })
  @MaxLength(128, { message: '新密码不能超过128个字符' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
    message: '新密码必须包含至少一个大写字母、一个小写字母、一个数字和一个特殊字符'
  })
  newPassword: string;

  @ApiProperty({ 
    description: '确认新密码',
    example: 'NewSecurePass456!'
  })
  @IsString()
  confirmNewPassword: string;

  @ApiProperty({ 
    description: 'MFA验证码（如果启用了MFA）',
    example: '123456',
    required: false
  })
  @IsOptional()
  @IsString()
  @Matches(/^\d{6}$/, { message: 'MFA验证码必须是6位数字' })
  mfaCode?: string;
}

// 管理员更新用户DTO
export class AdminUpdateUserDto extends UpdateUserDto {
  @ApiProperty({ 
    description: '用户状态',
    enum: UserStatus,
    example: UserStatus.ACTIVE,
    required: false
  })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;

  @ApiProperty({ 
    description: '用户角色',
    example: ['user', 'player'],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  roles?: string[];

  @ApiProperty({ 
    description: '直接权限',
    example: ['game:play', 'profile:read'],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  permissions?: string[];

  @ApiProperty({ 
    description: '邮箱验证状态',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  emailVerified?: boolean;

  @ApiProperty({ 
    description: '手机验证状态',
    example: false,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  phoneVerified?: boolean;

  @ApiProperty({ 
    description: '重置登录尝试次数',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  resetLoginAttempts?: boolean;

  @ApiProperty({ 
    description: '解锁账户',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  unlockAccount?: boolean;
}

// 批量更新用户DTO
export class BulkUpdateUsersDto {
  @ApiProperty({ 
    description: '用户ID列表',
    example: ['user1', 'user2', 'user3'],
    type: [String]
  })
  @IsArray()
  @IsString({ each: true })
  userIds: string[];

  @ApiProperty({ 
    description: '更新操作',
    example: {
      status: UserStatus.SUSPENDED,
      roles: ['user']
    }
  })
  @ValidateNested()
  @Type(() => AdminUpdateUserDto)
  updates: Partial<AdminUpdateUserDto>;
}

// 用户搜索DTO
export class SearchUsersDto {
  @ApiProperty({ 
    description: '搜索关键词',
    example: 'john',
    required: false
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  search?: string;

  @ApiProperty({ 
    description: '用户状态过滤',
    enum: UserStatus,
    required: false
  })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;

  @ApiProperty({ 
    description: '角色过滤',
    example: 'player',
    required: false
  })
  @IsOptional()
  @IsString()
  role?: string;

  @ApiProperty({ 
    description: '邮箱验证状态过滤',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  emailVerified?: boolean;

  @ApiProperty({ 
    description: '手机验证状态过滤',
    example: false,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  phoneVerified?: boolean;

  @ApiProperty({ 
    description: '注册开始日期',
    example: '2023-01-01',
    required: false
  })
  @IsOptional()
  @IsDateString()
  registeredAfter?: string;

  @ApiProperty({ 
    description: '注册结束日期',
    example: '2023-12-31',
    required: false
  })
  @IsOptional()
  @IsDateString()
  registeredBefore?: string;

  @ApiProperty({ 
    description: '最后登录开始日期',
    example: '2023-11-01',
    required: false
  })
  @IsOptional()
  @IsDateString()
  lastLoginAfter?: string;

  @ApiProperty({ 
    description: '最后登录结束日期',
    example: '2023-12-01',
    required: false
  })
  @IsOptional()
  @IsDateString()
  lastLoginBefore?: string;

  @ApiProperty({ 
    description: '页码',
    example: 1,
    minimum: 1,
    default: 1,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ 
    description: '每页数量',
    example: 20,
    minimum: 1,
    maximum: 100,
    default: 20,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiProperty({ 
    description: '排序字段',
    example: 'createdAt',
    default: 'createdAt',
    required: false
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiProperty({ 
    description: '排序方向',
    example: 'desc',
    enum: ['asc', 'desc'],
    default: 'desc',
    required: false
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';
}
