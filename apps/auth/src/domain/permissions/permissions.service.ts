import {
  Injectable,
  Logger,
  ConflictException,
  NotFoundException,
  BadRequestException
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Permission, PermissionDocument } from './entities/permission.entity';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { UpdatePermissionDto } from './dto/update-permission.dto';
import { Cacheable, CacheEvict, CachePut } from '@common/redis';

@Injectable()
export class PermissionsService {
  private readonly logger = new Logger(PermissionsService.name);

  constructor(
    @InjectModel(Permission.name) private permissionModel: Model<PermissionDocument>,
  ) {}

  /**
   * 创建权限
   */
  async create(createPermissionDto: CreatePermissionDto): Promise<PermissionDocument> {
    this.logger.log(`创建权限: ${createPermissionDto.resource}:${createPermissionDto.action}`);

    // 检查权限是否已存在
    const existingPermission = await this.permissionModel.findOne({
      resource: createPermissionDto.resource.toLowerCase(),
      action: createPermissionDto.action.toLowerCase()
    }).exec();

    if (existingPermission) {
      throw new ConflictException('权限已存在');
    }

    // 验证依赖权限是否存在
    if (createPermissionDto.dependencies && createPermissionDto.dependencies.length > 0) {
      await this.validatePermissions(createPermissionDto.dependencies);
    }

    // 验证冲突权限是否存在
    if (createPermissionDto.conflicts && createPermissionDto.conflicts.length > 0) {
      await this.validatePermissions(createPermissionDto.conflicts);
    }

    try {
      const permission = new this.permissionModel(createPermissionDto);
      const savedPermission = await permission.save();
      
      this.logger.log(`权限创建成功: ${savedPermission.name} (${savedPermission.id})`);
      return savedPermission;
    } catch (error) {
      this.logger.error('创建权限失败', error);
      throw new BadRequestException('创建权限失败');
    }
  }

  /**
   * 获取所有权限
   */
  async findAll(): Promise<PermissionDocument[]> {
    return await this.permissionModel.find({ enabled: true })
      .sort({ category: 1, level: 1, name: 1 })
      .exec();
  }

  /**
   * 根据ID获取权限
   */
  async findById(id: string): Promise<PermissionDocument> {
    const permission = await this.permissionModel.findById(id).exec();
    if (!permission) {
      throw new NotFoundException('权限不存在');
    }
    return permission;
  }

  /**
   * 根据名称获取权限
   */
  async findByName(name: string): Promise<PermissionDocument> {
    const permission = await this.permissionModel.findOne({ 
      name: name.toLowerCase(),
      enabled: true 
    }).exec();
    
    if (!permission) {
      throw new NotFoundException('权限不存在');
    }
    return permission;
  }

  /**
   * 根据资源获取权限
   */
  async findByResource(resource: string): Promise<PermissionDocument[]> {
    return await this.permissionModel.find({
      resource: resource.toLowerCase(),
      enabled: true
    }).sort({ action: 1 }).exec();
  }

  /**
   * 根据分类获取权限
   */
  async findByCategory(category: string): Promise<PermissionDocument[]> {
    return await this.permissionModel.find({
      category,
      enabled: true
    }).sort({ level: 1, name: 1 }).exec();
  }

  /**
   * 搜索权限
   */
  async search(query: string): Promise<PermissionDocument[]> {
    return await this.permissionModel.find({
      $text: { $search: query },
      enabled: true
    }).sort({ score: { $meta: 'textScore' } }).exec();
  }

  /**
   * 更新权限
   */
  async update(id: string, updatePermissionDto: UpdatePermissionDto): Promise<PermissionDocument> {
    this.logger.log(`更新权限: ${id}`);

    const permission = await this.findById(id);

    // 检查是否尝试修改系统权限
    if (permission.system && updatePermissionDto.system === false) {
      throw new BadRequestException('不能修改系统权限的系统标识');
    }

    // 检查资源和操作组合是否被其他权限使用
    if (updatePermissionDto.resource || updatePermissionDto.action) {
      const resource = updatePermissionDto.resource || permission.resource;
      const action = updatePermissionDto.action || permission.action;
      
      const existingPermission = await this.permissionModel.findOne({
        resource: resource.toLowerCase(),
        action: action.toLowerCase(),
        _id: { $ne: id }
      }).exec();
      
      if (existingPermission) {
        throw new ConflictException('资源和操作组合已被其他权限使用');
      }
    }

    // 验证依赖权限是否存在
    if (updatePermissionDto.dependencies) {
      await this.validatePermissions(updatePermissionDto.dependencies);
    }

    // 验证冲突权限是否存在
    if (updatePermissionDto.conflicts) {
      await this.validatePermissions(updatePermissionDto.conflicts);
    }

    try {
      const updatedPermission = await this.permissionModel.findByIdAndUpdate(
        id,
        { $set: updatePermissionDto },
        { new: true, runValidators: true }
      ).exec();

      if (!updatedPermission) {
        throw new NotFoundException('权限不存在');
      }

      this.logger.log(`权限更新成功: ${updatedPermission.name} (${updatedPermission.id})`);
      return updatedPermission;
    } catch (error) {
      this.logger.error('更新权限失败', error);
      throw new BadRequestException('更新权限失败');
    }
  }

  /**
   * 删除权限
   */
  async remove(id: string): Promise<void> {
    this.logger.log(`删除权限: ${id}`);

    const permission = await this.findById(id);

    // 检查是否为系统权限
    if (permission.system) {
      throw new BadRequestException('不能删除系统权限');
    }

    // 检查是否有角色使用此权限
    // TODO: 实现角色权限检查

    await this.permissionModel.findByIdAndDelete(id).exec();
    this.logger.log(`权限删除成功: ${permission.name} (${permission.id})`);
  }

  /**
   * 获取系统权限
   */
  async getSystemPermissions(): Promise<PermissionDocument[]> {
    return await this.permissionModel.find({ 
      system: true, 
      enabled: true 
    }).sort({ category: 1, level: 1 }).exec();
  }

  /**
   * 获取危险权限
   */
  async getDangerousPermissions(): Promise<PermissionDocument[]> {
    return await this.permissionModel.find({ 
      dangerous: true, 
      enabled: true 
    }).sort({ level: -1 }).exec();
  }

  /**
   * 获取权限统计信息
   */
  async getStatistics(): Promise<any> {
    const [
      totalPermissions,
      systemPermissions,
      dangerousPermissions,
      categoryCounts,
      levelCounts
    ] = await Promise.all([
      // 总权限数
      this.permissionModel.countDocuments({ enabled: true }),
      
      // 系统权限数
      this.permissionModel.countDocuments({ system: true, enabled: true }),
      
      // 危险权限数
      this.permissionModel.countDocuments({ dangerous: true, enabled: true }),
      
      // 按分类统计
      this.permissionModel.aggregate([
        { $match: { enabled: true } },
        { $group: { _id: '$category', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]),
      
      // 按级别统计
      this.permissionModel.aggregate([
        { $match: { enabled: true } },
        { $group: { _id: '$level', count: { $sum: 1 } } },
        { $sort: { _id: 1 } }
      ])
    ]);

    return {
      totalPermissions,
      systemPermissions,
      dangerousPermissions,
      categoryCounts,
      levelCounts,
      timestamp: new Date()
    };
  }

  /**
   * 批量创建权限
   */
  async createBatch(permissions: CreatePermissionDto[]): Promise<PermissionDocument[]> {
    this.logger.log(`批量创建权限: ${permissions.length}个`);

    const createdPermissions: PermissionDocument[] = [];
    const errors: string[] = [];

    for (const permissionDto of permissions) {
      try {
        const permission = await this.create(permissionDto);
        createdPermissions.push(permission);
      } catch (error) {
        errors.push(`${permissionDto.resource}:${permissionDto.action} - ${error.message}`);
      }
    }

    if (errors.length > 0) {
      this.logger.warn(`批量创建权限部分失败: ${errors.join('; ')}`);
    }

    this.logger.log(`批量创建权限完成: 成功 ${createdPermissions.length}个, 失败 ${errors.length}个`);
    return createdPermissions;
  }

  /**
   * 增加权限使用次数
   */
  async incrementUsage(permissionName: string): Promise<void> {
    await this.permissionModel.updateOne(
      { name: permissionName.toLowerCase() },
      { $inc: { usageCount: 1 } }
    ).exec();
  }

  /**
   * 检查权限依赖
   */
  async checkDependencies(permissionNames: string[]): Promise<string[]> {
    const permissions = await this.permissionModel.find({
      name: { $in: permissionNames },
      enabled: true
    }).exec();

    const allRequiredPermissions = new Set<string>();
    
    for (const permission of permissions) {
      allRequiredPermissions.add(permission.name);
      
      // 添加依赖权限
      for (const dependency of permission.dependencies) {
        allRequiredPermissions.add(dependency);
      }
    }

    return Array.from(allRequiredPermissions);
  }

  /**
   * 检查权限冲突
   */
  async checkConflicts(permissionNames: string[]): Promise<string[]> {
    const permissions = await this.permissionModel.find({
      name: { $in: permissionNames },
      enabled: true
    }).exec();

    const conflicts: string[] = [];
    
    for (const permission of permissions) {
      for (const conflict of permission.conflicts) {
        if (permissionNames.includes(conflict)) {
          conflicts.push(`${permission.name} 与 ${conflict} 冲突`);
        }
      }
    }

    return conflicts;
  }

  /**
   * 验证权限是否存在
   */
  private async validatePermissions(permissions: string[]): Promise<void> {
    const existingPermissions = await this.permissionModel.find({
      name: { $in: permissions },
      enabled: true
    }).exec();

    const existingPermissionNames = existingPermissions.map(p => p.name);
    const invalidPermissions = permissions.filter(p => !existingPermissionNames.includes(p));

    if (invalidPermissions.length > 0) {
      throw new BadRequestException(`以下权限不存在: ${invalidPermissions.join(', ')}`);
    }
  }
}
