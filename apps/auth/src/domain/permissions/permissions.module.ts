import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PermissionsController } from '../../modules/rbac/controllers/permissions.controller';
import { PermissionsService } from '../../modules/rbac/services/permissions.service';
import { Permission, PermissionSchema } from '../../modules/rbac/entities/permission.entity';
import { RolesModule } from '../roles/roles.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Permission.name, schema: PermissionSchema }
    ]),
    forwardRef(() => RolesModule), // 使用 forwardRef 解决循环依赖
  ],
  controllers: [PermissionsController],
  providers: [PermissionsService],
  exports: [PermissionsService],
})
export class PermissionsModule {}
