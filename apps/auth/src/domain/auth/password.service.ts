import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';

export interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
  strength: 'weak' | 'medium' | 'strong' | 'very_strong';
  score: number;
}

export interface PasswordHashResult {
  hash: string;
  salt: string;
}

@Injectable()
export class PasswordService {
  private readonly logger = new Logger(PasswordService.name);
  private readonly saltRounds: number;
  private readonly minLength: number;
  private readonly maxLength: number;
  private readonly requireUppercase: boolean;
  private readonly requireLowercase: boolean;
  private readonly requireNumbers: boolean;
  private readonly requireSpecialChars: boolean;
  private readonly forbiddenPatterns: string[];
  private readonly historyCount: number;

  constructor(private configService: ConfigService) {
    this.saltRounds = this.configService.get<number>('auth.password.saltRounds', 12);
    this.minLength = this.configService.get<number>('auth.password.minLength', 8);
    this.maxLength = this.configService.get<number>('auth.password.maxLength', 128);
    this.requireUppercase = this.configService.get<boolean>('auth.password.requireUppercase', true);
    this.requireLowercase = this.configService.get<boolean>('auth.password.requireLowercase', true);
    this.requireNumbers = this.configService.get<boolean>('auth.password.requireNumbers', true);
    this.requireSpecialChars = this.configService.get<boolean>('auth.password.requireSpecialChars', true);
    this.forbiddenPatterns = this.configService.get<string[]>('auth.password.forbiddenPatterns', []);
    this.historyCount = this.configService.get<number>('auth.password.historyCount', 5);
  }

  /**
   * 验证密码强度和规则
   */
  validatePassword(password: string, userInfo?: any): PasswordValidationResult {
    const errors: string[] = [];
    let score = 0;

    // 长度检查
    if (password.length < this.minLength) {
      errors.push(`密码长度至少需要${this.minLength}个字符`);
    } else if (password.length >= this.minLength) {
      score += 10;
    }

    if (password.length > this.maxLength) {
      errors.push(`密码长度不能超过${this.maxLength}个字符`);
    }

    // 大写字母检查
    if (this.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('密码必须包含至少一个大写字母');
    } else if (/[A-Z]/.test(password)) {
      score += 15;
    }

    // 小写字母检查
    if (this.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('密码必须包含至少一个小写字母');
    } else if (/[a-z]/.test(password)) {
      score += 15;
    }

    // 数字检查
    if (this.requireNumbers && !/\d/.test(password)) {
      errors.push('密码必须包含至少一个数字');
    } else if (/\d/.test(password)) {
      score += 15;
    }

    // 特殊字符检查
    if (this.requireSpecialChars && !/[@$!%*?&]/.test(password)) {
      errors.push('密码必须包含至少一个特殊字符 (@$!%*?&)');
    } else if (/[@$!%*?&]/.test(password)) {
      score += 15;
    }

    // 禁止模式检查
    const lowerPassword = password.toLowerCase();
    for (const pattern of this.forbiddenPatterns) {
      if (lowerPassword.includes(pattern.toLowerCase())) {
        errors.push(`密码不能包含常见模式: ${pattern}`);
      }
    }

    // 用户信息检查
    if (userInfo) {
      const userFields = [
        userInfo.username,
        userInfo.email?.split('@')[0],
        userInfo.firstName,
        userInfo.lastName
      ].filter(Boolean);

      for (const field of userFields) {
        if (field && lowerPassword.includes(field.toLowerCase())) {
          errors.push('密码不能包含用户信息');
          break;
        }
      }
    }

    // 复杂度加分
    if (password.length >= 12) score += 10;
    if (password.length >= 16) score += 10;
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 10;
    if (/[0-9].*[0-9]/.test(password)) score += 5;
    if (/[A-Z].*[A-Z]/.test(password)) score += 5;

    // 重复字符检查
    if (/(.)\1{2,}/.test(password)) {
      errors.push('密码不能包含连续重复的字符');
      score -= 10;
    }

    // 连续字符检查
    if (this.hasSequentialChars(password)) {
      errors.push('密码不能包含连续的字符序列');
      score -= 10;
    }

    // 确定强度等级
    let strength: 'weak' | 'medium' | 'strong' | 'very_strong';
    if (score < 30) {
      strength = 'weak';
    } else if (score < 60) {
      strength = 'medium';
    } else if (score < 80) {
      strength = 'strong';
    } else {
      strength = 'very_strong';
    }

    return {
      isValid: errors.length === 0,
      errors,
      strength,
      score: Math.min(100, Math.max(0, score))
    };
  }

  /**
   * 哈希密码
   */
  async hashPassword(password: string): Promise<PasswordHashResult> {
    try {
      const salt = await bcrypt.genSalt(this.saltRounds);
      const hash = await bcrypt.hash(password, salt);
      
      return { hash, salt };
    } catch (error) {
      this.logger.error('密码哈希失败', error);
      throw new Error('密码处理失败');
    }
  }

  /**
   * 验证密码
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      this.logger.error('密码验证失败', error);
      return false;
    }
  }

  /**
   * 检查密码是否在历史记录中
   */
  async isPasswordInHistory(password: string, passwordHistory: string[]): Promise<boolean> {
    try {
      for (const historicalHash of passwordHistory) {
        if (await this.verifyPassword(password, historicalHash)) {
          return true;
        }
      }
      return false;
    } catch (error) {
      this.logger.error('检查密码历史失败', error);
      return false;
    }
  }

  /**
   * 生成随机密码
   */
  generateRandomPassword(length: number = 16): string {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const symbols = '@$!%*?&';
    
    let charset = '';
    let password = '';
    
    // 确保包含所有必需的字符类型
    if (this.requireUppercase) {
      charset += uppercase;
      password += uppercase[Math.floor(Math.random() * uppercase.length)];
    }
    
    if (this.requireLowercase) {
      charset += lowercase;
      password += lowercase[Math.floor(Math.random() * lowercase.length)];
    }
    
    if (this.requireNumbers) {
      charset += numbers;
      password += numbers[Math.floor(Math.random() * numbers.length)];
    }
    
    if (this.requireSpecialChars) {
      charset += symbols;
      password += symbols[Math.floor(Math.random() * symbols.length)];
    }
    
    // 填充剩余长度
    for (let i = password.length; i < length; i++) {
      password += charset[Math.floor(Math.random() * charset.length)];
    }
    
    // 打乱密码字符顺序
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  /**
   * 生成密码重置令牌
   */
  generateResetToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 生成邮箱验证令牌
   */
  generateVerificationToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 生成短验证码
   */
  generateShortCode(length: number = 6): string {
    const digits = '0123456789';
    let code = '';
    
    for (let i = 0; i < length; i++) {
      code += digits[Math.floor(Math.random() * digits.length)];
    }
    
    return code;
  }

  /**
   * 检查密码是否过期
   */
  isPasswordExpired(lastPasswordChange: Date): boolean {
    const maxAge = this.configService.get<number>('auth.password.maxAge', 90); // 90天
    const daysSinceChange = Math.floor(
      (Date.now() - lastPasswordChange.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    return daysSinceChange > maxAge;
  }

  /**
   * 获取密码策略信息
   */
  getPasswordPolicy(): any {
    return {
      minLength: this.minLength,
      maxLength: this.maxLength,
      requireUppercase: this.requireUppercase,
      requireLowercase: this.requireLowercase,
      requireNumbers: this.requireNumbers,
      requireSpecialChars: this.requireSpecialChars,
      historyCount: this.historyCount,
      maxAge: this.configService.get<number>('auth.password.maxAge', 90),
      forbiddenPatterns: this.forbiddenPatterns
    };
  }

  /**
   * 检查是否有连续字符
   */
  private hasSequentialChars(password: string): boolean {
    const sequences = [
      'abcdefghijklmnopqrstuvwxyz',
      'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      '0123456789',
      'qwertyuiopasdfghjklzxcvbnm',
      'QWERTYUIOPASDFGHJKLZXCVBNM'
    ];
    
    for (const sequence of sequences) {
      for (let i = 0; i <= sequence.length - 3; i++) {
        const subseq = sequence.substring(i, i + 3);
        if (password.includes(subseq)) {
          return true;
        }
      }
    }
    
    return false;
  }

  /**
   * 更新密码历史
   */
  updatePasswordHistory(currentHistory: string[], newPasswordHash: string): string[] {
    const updatedHistory = [newPasswordHash, ...currentHistory];
    return updatedHistory.slice(0, this.historyCount);
  }
}
