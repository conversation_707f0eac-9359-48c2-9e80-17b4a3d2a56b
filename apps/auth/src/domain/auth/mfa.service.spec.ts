import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { MfaService } from './mfa.service';
import * as speakeasy from 'speakeasy';
import {UsersService} from "../users/users.service";
import {SecurityService} from "../../core/security/security.service";

// Mock speakeasy
jest.mock('speakeasy');
jest.mock('qrcode');

describe('MfaService', () => {
  let service: MfaService;
  let usersService: jest.Mocked<UsersService>;
  let securityService: jest.Mocked<SecurityService>;
  let configService: jest.Mocked<ConfigService>;

  const mockUser = {
    id: 'user-id',
    email: '<EMAIL>',
    security: {
      mfaEnabled: false,
      mfaSecret: null,
      backupCodes: [],
    },
  };

  const mockSecret = {
    base32: 'JBSWY3DPEHPK3PXP',
    otpauth_url: 'otpauth://totp/Test%20App:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=Test%20App',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MfaService,
        {
          provide: UsersService,
          useValue: {
            findById: jest.fn(),
            update: jest.fn(),
            validatePassword: jest.fn(),
          },
        },
        {
          provide: SecurityService,
          useValue: {
            logSecurityEvent: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key: string, defaultValue?: any) => {
              const config = {
                'app.name': 'Test App',
                'sms.enabled': false,
                'auth.mfa.totpWindow': 2,
                'auth.mfa.backupCodeCount': 10,
              };
              return config[key] || defaultValue;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<MfaService>(MfaService);
    usersService = module.get(UsersService);
    securityService = module.get(SecurityService);
    configService = module.get(ConfigService);

    // Reset mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateTotpSecret', () => {
    it('should generate TOTP secret successfully', async () => {
      // Arrange
      usersService.findById.mockResolvedValue(mockUser as any);
      (speakeasy.generateSecret as jest.Mock).mockReturnValue(mockSecret);
      securityService.logSecurityEvent.mockResolvedValue(undefined);

      // Mock qrcode
      const qrcode = require('qrcode');
      qrcode.toDataURL = jest.fn().mockResolvedValue('data:image/png;base64,mock-qr-code');

      // Act
      const result = await service.generateTotpSecret('user-id');

      // Assert
      expect(result).toHaveProperty('secret');
      expect(result).toHaveProperty('qrCodeUrl');
      expect(result).toHaveProperty('backupCodes');
      expect(result).toHaveProperty('manualEntryKey');
      expect(result.secret).toBe('JBSWY3DPEHPK3PXP');
      expect(result.backupCodes).toHaveLength(10);
      expect(usersService.findById).toHaveBeenCalledWith('user-id');
      expect(securityService.logSecurityEvent).toHaveBeenCalled();
    });
  });

  describe('enableMfa', () => {
    it('should enable MFA successfully with valid code', async () => {
      // Arrange
      const secret = 'JBSWY3DPEHPK3PXP';
      const code = '123456';
      
      (speakeasy.totp.verify as jest.Mock).mockReturnValue(true);
      usersService.update.mockResolvedValue(undefined);
      securityService.logSecurityEvent.mockResolvedValue(undefined);

      // Act
      const result = await service.enableMfa('user-id', secret, code);

      // Assert
      expect(result.enabled).toBe(true);
      expect(result.backupCodes).toHaveLength(10);
      expect(speakeasy.totp.verify).toHaveBeenCalledWith({
        secret,
        encoding: 'base32',
        token: code,
        window: 2,
      });
      expect(usersService.update).toHaveBeenCalled();
      expect(securityService.logSecurityEvent).toHaveBeenCalled();
    });

    it('should throw BadRequestException for invalid code', async () => {
      // Arrange
      const secret = 'JBSWY3DPEHPK3PXP';
      const code = '000000';
      
      (speakeasy.totp.verify as jest.Mock).mockReturnValue(false);

      // Act & Assert
      await expect(service.enableMfa('user-id', secret, code)).rejects.toThrow(BadRequestException);
      expect(usersService.update).not.toHaveBeenCalled();
    });
  });

  describe('disableMfa', () => {
    it('should disable MFA successfully', async () => {
      // Arrange
      const mfaUser = {
        ...mockUser,
        security: {
          mfaEnabled: true,
          mfaSecret: 'JBSWY3DPEHPK3PXP',
          backupCodes: ['hashedcode1', 'hashedcode2'],
        },
      };
      
      usersService.findById.mockResolvedValue(mfaUser as any);
      usersService.validatePassword.mockResolvedValue(true);
      (speakeasy.totp.verify as jest.Mock).mockReturnValue(true);
      usersService.update.mockResolvedValue(undefined);
      securityService.logSecurityEvent.mockResolvedValue(undefined);

      // Act
      await service.disableMfa('user-id', 'password123', '123456');

      // Assert
      expect(usersService.validatePassword).toHaveBeenCalledWith(mfaUser, 'password123');
      expect(speakeasy.totp.verify).toHaveBeenCalled();
      expect(usersService.update).toHaveBeenCalledWith('user-id', {
        security: {
          mfaEnabled: false,
          mfaSecret: null,
          backupCodes: [],
        }
      });
      expect(securityService.logSecurityEvent).toHaveBeenCalled();
    });

    it('should throw UnauthorizedException for invalid password', async () => {
      // Arrange
      usersService.findById.mockResolvedValue(mockUser as any);
      usersService.validatePassword.mockResolvedValue(false);

      // Act & Assert
      await expect(service.disableMfa('user-id', 'wrongpassword', '123456')).rejects.toThrow(UnauthorizedException);
      expect(usersService.update).not.toHaveBeenCalled();
    });
  });

  describe('verifyTotpCode', () => {
    it('should verify TOTP code successfully', async () => {
      // Arrange
      const mfaUser = {
        ...mockUser,
        security: {
          mfaEnabled: true,
          mfaSecret: 'JBSWY3DPEHPK3PXP',
          backupCodes: [],
        },
      };
      
      usersService.findById.mockResolvedValue(mfaUser as any);
      (speakeasy.totp.verify as jest.Mock).mockReturnValue(true);
      securityService.logSecurityEvent.mockResolvedValue(undefined);

      // Act
      const result = await service.verifyTotpCode('user-id', '123456');

      // Assert
      expect(result).toBe(true);
      expect(speakeasy.totp.verify).toHaveBeenCalledWith({
        secret: 'JBSWY3DPEHPK3PXP',
        encoding: 'base32',
        token: '123456',
        window: 2,
      });
      expect(securityService.logSecurityEvent).toHaveBeenCalled();
    });

    it('should return false for invalid code', async () => {
      // Arrange
      const mfaUser = {
        ...mockUser,
        security: {
          mfaEnabled: true,
          mfaSecret: 'JBSWY3DPEHPK3PXP',
          backupCodes: [],
        },
      };
      
      usersService.findById.mockResolvedValue(mfaUser as any);
      (speakeasy.totp.verify as jest.Mock).mockReturnValue(false);

      // Act
      const result = await service.verifyTotpCode('user-id', '000000');

      // Assert
      expect(result).toBe(false);
    });

    it('should return false when MFA is not enabled', async () => {
      // Arrange
      usersService.findById.mockResolvedValue(mockUser as any);

      // Act
      const result = await service.verifyTotpCode('user-id', '123456');

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('verifyBackupCode', () => {
    it('should verify backup code successfully', async () => {
      // Arrange
      const hashedCode = require('crypto').createHash('sha256').update('ABCD1234').digest('hex');
      const mfaUser = {
        ...mockUser,
        security: {
          mfaEnabled: true,
          mfaSecret: 'JBSWY3DPEHPK3PXP',
          backupCodes: [hashedCode, 'othercode'],
        },
      };
      
      usersService.findById.mockResolvedValue(mfaUser as any);
      usersService.update.mockResolvedValue(undefined);
      securityService.logSecurityEvent.mockResolvedValue(undefined);

      // Act
      const result = await service.verifyBackupCode('user-id', 'ABCD1234');

      // Assert
      expect(result).toBe(true);
      expect(usersService.update).toHaveBeenCalledWith('user-id', {
        security: {
          ...mfaUser.security,
          backupCodes: ['othercode'],
        }
      });
      expect(securityService.logSecurityEvent).toHaveBeenCalled();
    });

    it('should return false for invalid backup code', async () => {
      // Arrange
      const mfaUser = {
        ...mockUser,
        security: {
          mfaEnabled: true,
          mfaSecret: 'JBSWY3DPEHPK3PXP',
          backupCodes: ['hashedcode1', 'hashedcode2'],
        },
      };
      
      usersService.findById.mockResolvedValue(mfaUser as any);

      // Act
      const result = await service.verifyBackupCode('user-id', 'INVALID');

      // Assert
      expect(result).toBe(false);
      expect(usersService.update).not.toHaveBeenCalled();
    });
  });

  describe('getMfaStatus', () => {
    it('should return MFA status', async () => {
      // Arrange
      const mfaUser = {
        ...mockUser,
        security: {
          mfaEnabled: true,
          backupCodes: ['code1', 'code2'],
          trustedDevices: ['device1'],
        },
      };
      
      usersService.findById.mockResolvedValue(mfaUser as any);

      // Act
      const result = await service.getMfaStatus('user-id');

      // Assert
      expect(result).toEqual({
        enabled: true,
        backupCodesCount: 2,
        trustedDevicesCount: 1,
      });
    });
  });
});
