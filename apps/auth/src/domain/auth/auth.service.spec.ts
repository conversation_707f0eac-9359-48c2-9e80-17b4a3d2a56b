import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { UnauthorizedException, BadRequestException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UsersService } from '../../modules/users/users.service';
import { PasswordService } from './password.service';
import { JwtService } from './jwt.service';
import { SessionService } from '../session/session.service';
import { SecurityService } from '../security/security.service';
import { MfaService } from './mfa.service';

describe('AuthService', () => {
  let service: AuthService;
  let usersService: jest.Mocked<UsersService>;
  let passwordService: jest.Mocked<PasswordService>;
  let jwtService: jest.Mocked<JwtService>;
  let sessionService: jest.Mocked<SessionService>;
  let securityService: jest.Mocked<SecurityService>;
  let mfaService: jest.Mocked<MfaService>;

  const mockUser = {
    id: 'user-id',
    username: 'testuser',
    email: '<EMAIL>',
    passwordHash: 'hashed-password',
    security: {
      mfaEnabled: false,
      loginAttempts: 0,
      lockedUntil: null,
    },
    toObject: jest.fn().mockReturnThis(),
  };

  const mockSession = {
    id: 'session-id',
    deviceId: 'device-id',
    lastActivity: new Date(),
  };

  const mockTokens = {
    accessToken: 'access-token',
    refreshToken: 'refresh-token',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: {
            findByUsernameOrEmail: jest.fn(),
            validateUserStatus: jest.fn(),
            incrementLoginAttempts: jest.fn(),
            resetLoginAttempts: jest.fn(),
            updateLastLogin: jest.fn(),
            findById: jest.fn(),
          },
        },
        {
          provide: PasswordService,
          useValue: {
            verifyPassword: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            generateTokenPair: jest.fn(),
            verifyRefreshToken: jest.fn(),
            revokeToken: jest.fn(),
            revokeAllUserTokens: jest.fn(),
          },
        },
        {
          provide: SessionService,
          useValue: {
            createSession: jest.fn(),
            findById: jest.fn(),
            updateSessionTokens: jest.fn(),
            updateLastActivity: jest.fn(),
            terminateSession: jest.fn(),
            findByUserId: jest.fn(),
          },
        },
        {
          provide: SecurityService,
          useValue: {
            logSecurityEvent: jest.fn(),
          },
        },
        {
          provide: MfaService,
          useValue: {
            verifyTotpCode: jest.fn(),
            verifyBackupCode: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    usersService = module.get(UsersService);
    passwordService = module.get(PasswordService);
    jwtService = module.get(JwtService);
    sessionService = module.get(SessionService);
    securityService = module.get(SecurityService);
    mfaService = module.get(MfaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('login', () => {
    const loginDto = {
      identifier: 'testuser',
      password: 'password123',
      deviceInfo: {
        type: 'web',
        name: 'Chrome',
        fingerprint: 'device-fingerprint',
        userAgent: 'Mozilla/5.0...',
        ipAddress: '***********',
      },
    };

    it('should login successfully with valid credentials', async () => {
      // Arrange
      usersService.findByUsernameOrEmail.mockResolvedValue(mockUser as any);
      usersService.validateUserStatus.mockResolvedValue(undefined);
      passwordService.verifyPassword.mockResolvedValue(true);
      sessionService.createSession.mockResolvedValue(mockSession as any);
      jwtService.generateTokenPair.mockReturnValue(mockTokens as any);
      sessionService.updateSessionTokens.mockResolvedValue(undefined);
      usersService.resetLoginAttempts.mockResolvedValue(undefined);
      usersService.updateLastLogin.mockResolvedValue(undefined);
      securityService.logSecurityEvent.mockResolvedValue(undefined);

      // Act
      const result = await service.login(loginDto);

      // Assert
      expect(result).toHaveProperty('user');
      expect(result).toHaveProperty('tokens');
      expect(result).toHaveProperty('session');
      expect(usersService.findByUsernameOrEmail).toHaveBeenCalledWith('testuser', true);
      expect(passwordService.verifyPassword).toHaveBeenCalledWith('password123', 'hashed-password');
      expect(sessionService.createSession).toHaveBeenCalled();
      expect(jwtService.generateTokenPair).toHaveBeenCalled();
    });

    it('should throw UnauthorizedException for invalid password', async () => {
      // Arrange
      usersService.findByUsernameOrEmail.mockResolvedValue(mockUser as any);
      usersService.validateUserStatus.mockResolvedValue(undefined);
      passwordService.verifyPassword.mockResolvedValue(false);
      usersService.incrementLoginAttempts.mockResolvedValue(undefined);
      securityService.logSecurityEvent.mockResolvedValue(undefined);

      // Act & Assert
      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
      expect(usersService.incrementLoginAttempts).toHaveBeenCalledWith(mockUser);
    });

    it('should require MFA code when MFA is enabled', async () => {
      // Arrange
      const mfaUser = { ...mockUser, security: { ...mockUser.security, mfaEnabled: true } };
      usersService.findByUsernameOrEmail.mockResolvedValue(mfaUser as any);
      usersService.validateUserStatus.mockResolvedValue(undefined);
      passwordService.verifyPassword.mockResolvedValue(true);

      // Act & Assert
      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });

    it('should login successfully with valid MFA code', async () => {
      // Arrange
      const mfaUser = { ...mockUser, security: { ...mockUser.security, mfaEnabled: true } };
      const loginWithMfa = { ...loginDto, mfaCode: '123456' };
      
      usersService.findByUsernameOrEmail.mockResolvedValue(mfaUser as any);
      usersService.validateUserStatus.mockResolvedValue(undefined);
      passwordService.verifyPassword.mockResolvedValue(true);
      mfaService.verifyTotpCode.mockResolvedValue(true);
      sessionService.createSession.mockResolvedValue(mockSession as any);
      jwtService.generateTokenPair.mockReturnValue(mockTokens as any);
      sessionService.updateSessionTokens.mockResolvedValue(undefined);
      usersService.resetLoginAttempts.mockResolvedValue(undefined);
      usersService.updateLastLogin.mockResolvedValue(undefined);
      securityService.logSecurityEvent.mockResolvedValue(undefined);

      // Act
      const result = await service.login(loginWithMfa);

      // Assert
      expect(result).toHaveProperty('user');
      expect(result).toHaveProperty('tokens');
      expect(result).toHaveProperty('session');
      expect(mfaService.verifyTotpCode).toHaveBeenCalledWith('user-id', '123456');
    });
  });

  describe('refreshToken', () => {
    const refreshToken = 'refresh-token';
    const mockPayload = {
      sub: 'user-id',
      sessionId: 'session-id',
    };

    it('should refresh token successfully', async () => {
      // Arrange
      jwtService.verifyRefreshToken.mockResolvedValue(mockPayload as any);
      sessionService.findById.mockResolvedValue({ ...mockSession, active: true } as any);
      usersService.findById.mockResolvedValue(mockUser as any);
      usersService.validateUserStatus.mockResolvedValue(undefined);
      jwtService.generateTokenPair.mockReturnValue(mockTokens as any);
      sessionService.updateSessionTokens.mockResolvedValue(undefined);
      sessionService.updateLastActivity.mockResolvedValue(undefined);

      // Act
      const result = await service.refreshToken(refreshToken);

      // Assert
      expect(result).toHaveProperty('tokens');
      expect(jwtService.verifyRefreshToken).toHaveBeenCalledWith(refreshToken);
      expect(sessionService.findById).toHaveBeenCalledWith('session-id');
      expect(usersService.findById).toHaveBeenCalledWith('user-id');
    });

    it('should throw UnauthorizedException for inactive session', async () => {
      // Arrange
      jwtService.verifyRefreshToken.mockResolvedValue(mockPayload as any);
      sessionService.findById.mockResolvedValue({ ...mockSession, active: false } as any);

      // Act & Assert
      await expect(service.refreshToken(refreshToken)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('logout', () => {
    const userId = 'user-id';
    const sessionId = 'session-id';

    it('should logout single session successfully', async () => {
      // Arrange
      sessionService.terminateSession.mockResolvedValue(undefined);

      // Act
      await service.logout(userId, sessionId);

      // Assert
      expect(sessionService.terminateSession).toHaveBeenCalledWith(sessionId);
    });

    it('should logout all sessions when allDevices is true', async () => {
      // Arrange
      const sessions = [mockSession, { ...mockSession, id: 'session-2' }];
      sessionService.findByUserId.mockResolvedValue(sessions as any);
      sessionService.terminateSession.mockResolvedValue(undefined);
      jwtService.revokeAllUserTokens.mockResolvedValue(undefined);

      // Act
      await service.logout(userId, sessionId, true);

      // Assert
      expect(sessionService.findByUserId).toHaveBeenCalledWith(userId);
      expect(sessionService.terminateSession).toHaveBeenCalledTimes(2);
      expect(jwtService.revokeAllUserTokens).toHaveBeenCalledWith(userId);
    });
  });

  describe('validateToken', () => {
    it('should validate token and return user', async () => {
      // Arrange
      usersService.findById.mockResolvedValue(mockUser as any);
      usersService.validateUserStatus.mockResolvedValue(undefined);

      // Act
      const result = await service.validateToken('user-id');

      // Assert
      expect(result).toBe(mockUser);
      expect(usersService.findById).toHaveBeenCalledWith('user-id');
      expect(usersService.validateUserStatus).toHaveBeenCalledWith(mockUser);
    });
  });
});
