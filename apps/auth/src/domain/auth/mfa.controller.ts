import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
  ValidationPipe,
  UseInterceptors,
  ClassSerializerInterceptor,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { MfaService } from './mfa.service';
import { JwtAuthGuard } from '../../infrastructure/guards/jwt-auth.guard';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../../domain/users/entities/user.entity';
import { ApiResponseDto } from '../../common/dto/response.dto';
import { IsString, IsPhoneNumber, Length } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

// MFA设置DTO
export class EnableMfaDto {
  @ApiProperty({ 
    description: 'TOTP密钥',
    example: 'JBSWY3DPEHPK3PXP'
  })
  @IsString()
  secret: string;

  @ApiProperty({ 
    description: '验证码',
    example: '123456'
  })
  @IsString()
  @Length(6, 6, { message: '验证码必须是6位数字' })
  code: string;
}

// MFA禁用DTO
export class DisableMfaDto {
  @ApiProperty({ 
    description: '当前密码',
    example: 'CurrentPassword123!'
  })
  @IsString()
  password: string;

  @ApiProperty({ 
    description: 'MFA验证码',
    example: '123456'
  })
  @IsString()
  @Length(6, 6, { message: '验证码必须是6位数字' })
  code: string;
}

// 短信验证码DTO
export class SendSmsCodeDto {
  @ApiProperty({ 
    description: '手机号码',
    example: '+8613800138000'
  })
  @IsPhoneNumber('CN', { message: '请输入有效的手机号码' })
  phone: string;
}

// 验证短信码DTO
export class VerifySmsCodeDto {
  @ApiProperty({ 
    description: '短信验证码',
    example: '123456'
  })
  @IsString()
  @Length(6, 6, { message: '验证码必须是6位数字' })
  code: string;
}

@ApiTags('多因子认证')
@Controller('auth/mfa')
@UseGuards(JwtAuthGuard)
@UseInterceptors(ClassSerializerInterceptor)
@ApiBearerAuth('JWT-auth')
export class MfaController {
  constructor(private readonly mfaService: MfaService) {}

  @Get('status')
  @ApiOperation({ summary: '获取MFA状态' })
  @ApiResponse({
    status: 200,
    description: '获取MFA状态成功',
  })
  async getMfaStatus(
    @CurrentUser() user: User,
  ): Promise<ApiResponseDto<any>> {
    const status = await this.mfaService.getMfaStatus(user.id);
    
    return {
      success: true,
      data: status,
      message: '获取MFA状态成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('setup')
  @UseGuards(ThrottlerGuard)
  @ApiOperation({ summary: '生成MFA设置信息' })
  @ApiResponse({
    status: 200,
    description: 'MFA设置信息生成成功',
  })
  @ApiResponse({
    status: 429,
    description: '请求过于频繁',
  })
  async setupMfa(
    @CurrentUser() user: User,
  ): Promise<ApiResponseDto<any>> {
    const setupResult = await this.mfaService.generateTotpSecret(user.id);
    
    return {
      success: true,
      data: setupResult,
      message: 'MFA设置信息生成成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('enable')
  @UseGuards(ThrottlerGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '启用MFA' })
  @ApiResponse({
    status: 200,
    description: 'MFA启用成功',
  })
  @ApiResponse({
    status: 400,
    description: '验证码错误',
  })
  @ApiResponse({
    status: 429,
    description: '请求过于频繁',
  })
  async enableMfa(
    @CurrentUser() user: User,
    @Body(ValidationPipe) enableMfaDto: EnableMfaDto,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.mfaService.enableMfa(
      user.id,
      enableMfaDto.secret,
      enableMfaDto.code
    );
    
    return {
      success: true,
      data: result,
      message: 'MFA启用成功，请妥善保存备用码',
      timestamp: new Date().toISOString(),
    };
  }

  @Delete('disable')
  @UseGuards(ThrottlerGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '禁用MFA' })
  @ApiResponse({
    status: 204,
    description: 'MFA禁用成功',
  })
  @ApiResponse({
    status: 400,
    description: '密码或验证码错误',
  })
  @ApiResponse({
    status: 429,
    description: '请求过于频繁',
  })
  async disableMfa(
    @CurrentUser() user: User,
    @Body(ValidationPipe) disableMfaDto: DisableMfaDto,
  ): Promise<ApiResponseDto<null>> {
    await this.mfaService.disableMfa(
      user.id,
      disableMfaDto.password,
      disableMfaDto.code
    );
    
    return {
      success: true,
      data: null,
      message: 'MFA禁用成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('backup-codes/regenerate')
  @UseGuards(ThrottlerGuard)
  @ApiOperation({ summary: '重新生成备用码' })
  @ApiResponse({
    status: 200,
    description: '备用码重新生成成功',
  })
  @ApiResponse({
    status: 429,
    description: '请求过于频繁',
  })
  async regenerateBackupCodes(
    @CurrentUser() user: User,
  ): Promise<ApiResponseDto<string[]>> {
    const backupCodes = await this.mfaService.generateNewBackupCodes(user.id);
    
    return {
      success: true,
      data: backupCodes,
      message: '备用码重新生成成功，请妥善保存',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('sms/send')
  @UseGuards(ThrottlerGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '发送短信验证码' })
  @ApiResponse({
    status: 200,
    description: '短信验证码发送成功',
  })
  @ApiResponse({
    status: 400,
    description: '手机号码无效',
  })
  @ApiResponse({
    status: 429,
    description: '请求过于频繁',
  })
  async sendSmsCode(
    @CurrentUser() user: User,
    @Body(ValidationPipe) sendSmsCodeDto: SendSmsCodeDto,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.mfaService.sendSmsCode(user.id, sendSmsCodeDto.phone);
    
    return {
      success: true,
      data: result,
      message: '短信验证码发送成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('sms/verify')
  @UseGuards(ThrottlerGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '验证短信验证码' })
  @ApiResponse({
    status: 200,
    description: '短信验证码验证成功',
  })
  @ApiResponse({
    status: 400,
    description: '验证码错误',
  })
  @ApiResponse({
    status: 429,
    description: '请求过于频繁',
  })
  async verifySmsCode(
    @CurrentUser() user: User,
    @Body(ValidationPipe) verifySmsCodeDto: VerifySmsCodeDto,
  ): Promise<ApiResponseDto<boolean>> {
    const isValid = await this.mfaService.verifySmsCode(user.id, verifySmsCodeDto.code);
    
    return {
      success: true,
      data: isValid,
      message: isValid ? '验证码验证成功' : '验证码错误',
      timestamp: new Date().toISOString(),
    };
  }
}
