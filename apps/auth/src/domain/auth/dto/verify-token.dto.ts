import { IsString, IsNotEmpty, Matches, Length } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

/**
 * 验证令牌请求 DTO
 */
export class VerifyTokenDto {
  @ApiProperty({
    description: 'JWT访问令牌',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    minLength: 10,
    maxLength: 2048
  })
  @IsString({ message: '令牌必须是字符串' })
  @IsNotEmpty({ message: '令牌不能为空' })
  @Length(10, 2048, { message: '令牌长度必须在10-2048字符之间' })
  @Matches(/^[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]*$/, {
    message: '令牌格式不正确，必须是有效的JWT格式'
  })
  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  token: string;
}

/**
 * 验证令牌响应 DTO
 */
export class VerifyTokenResponseDto {
  @ApiProperty({
    description: '令牌是否有效',
    example: true
  })
  valid: boolean;

  @ApiProperty({
    description: '用户信息',
    required: false,
    type: 'object'
  })
  user?: {
    id: string;
    username: string;
    email: string;
    roles: string[];
    permissions: string[];
    status: string;
    emailVerified: boolean;
    profile?: {
      firstName: string;
      lastName: string;
      language: string;
    };
    security: {
      mfaEnabled: boolean;
      lastPasswordChange?: Date;
    };
  };

  @ApiProperty({
    description: '令牌信息',
    required: false,
    type: 'object'
  })
  token?: {
    jti: string;
    iat: number;
    exp: number;
    expiresIn: number;
    type: string;
  };

  @ApiProperty({
    description: '错误信息',
    required: false,
    example: '令牌已过期'
  })
  error?: string;

  @ApiProperty({
    description: '错误代码',
    required: false,
    example: 'TOKEN_EXPIRED'
  })
  errorCode?: string;
}
