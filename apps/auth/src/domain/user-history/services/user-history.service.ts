import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

// 实体
import { 
  UserHistory, 
  UserHistoryDocument, 
  ServerHistoryItem, 
  CharacterSummary,
  GlobalUserStats,
  ServerUserStats
} from '../entities/user-history.entity';

// 核心服务
import { RedisService } from '@common/redis';
import { UsersService } from '../../users/users.service';

/**
 * 用户历史服务
 * 负责用户历史记录的创建、查询、更新等核心功能
 */
@Injectable()
export class UserHistoryService {
  private readonly logger = new Logger(UserHistoryService.name);

  constructor(
    @InjectModel(UserHistory.name)
    private readonly userHistoryModel: Model<UserHistoryDocument>,
    private readonly redisService: RedisService,
    private readonly usersService: UsersService,
  ) {}

  /**
   * 获取用户历史记录
   */
  async getUserHistory(userId: string): Promise<UserHistoryDocument> {
    this.logger.log(`获取用户历史记录: ${userId}`);

    try {
      // 1. 先从缓存获取
      const cached = await this.getUserHistoryFromCache(userId);
      if (cached) {
        return cached;
      }

      // 2. 从数据库获取
      let userHistory = await this.userHistoryModel.findOne({ userId }).exec();

      // 3. 如果不存在则创建
      if (!userHistory) {
        userHistory = await this.createUserHistory(userId);
      }

      // 4. 缓存结果
      await this.cacheUserHistory(userHistory);

      return userHistory;

    } catch (error) {
      this.logger.error(`获取用户历史记录失败: ${userId}`, error);
      throw error;
    }
  }

  /**
   * 获取用户区服历史
   */
  async getUserServerHistory(userId: string): Promise<ServerHistoryItem[]> {
    const userHistory = await this.getUserHistory(userId);
    return userHistory.serverHistory || [];
  }

  /**
   * 获取用户在指定区服的历史
   */
  async getUserServerHistoryById(userId: string, serverId: string): Promise<ServerHistoryItem | null> {
    const userHistory = await this.getUserHistory(userId);
    return (userHistory as any).getServerHistory(serverId) || null;
  }

  /**
   * 获取用户角色摘要
   */
  async getUserCharacters(userId: string, serverId?: string): Promise<CharacterSummary[]> {
    const userHistory = await this.getUserHistory(userId);
    
    if (serverId) {
      const serverHistory = (userHistory as any).getServerHistory(serverId);
      return serverHistory?.characters || [];
    }

    // 返回所有区服的角色
    const allCharacters: CharacterSummary[] = [];
    userHistory.serverHistory.forEach(server => {
      server.characters.forEach(character => {
        allCharacters.push({
          ...character,
          serverId: server.serverId,
        } as CharacterSummary & { serverId: string });
      });
    });

    return allCharacters;
  }

  /**
   * 获取用户全局统计
   */
  async getUserGlobalStats(userId: string): Promise<GlobalUserStats> {
    const userHistory = await this.getUserHistory(userId);
    return userHistory.globalStats || this.getDefaultGlobalStats();
  }

  /**
   * 更新用户最后登录信息
   */
  async updateLastLogin(userId: string, serverId: string, characterId: string): Promise<void> {
    this.logger.log(`更新用户最后登录信息: ${userId}@${serverId}:${characterId}`);

    try {
      const userHistory = await this.getUserHistory(userId);
      
      // 更新基本信息
      userHistory.lastServerId = serverId;
      userHistory.lastCharacterId = characterId;
      
      // 更新全局统计
      if (!userHistory.globalStats) {
        userHistory.globalStats = this.getDefaultGlobalStats();
      }
      userHistory.globalStats.lastLoginTime = new Date();
      userHistory.globalStats.totalLoginDays += 1; // 简化处理，实际应该按天去重

      await userHistory.save();
      
      // 更新缓存
      await this.cacheUserHistory(userHistory);

    } catch (error) {
      this.logger.error(`更新用户最后登录信息失败: ${userId}`, error);
      throw error;
    }
  }

  /**
   * 更新区服历史
   */
  async updateServerHistory(
    userId: string, 
    serverId: string, 
    serverName: string,
    updates: Partial<ServerHistoryItem> = {}
  ): Promise<void> {
    this.logger.log(`更新用户区服历史: ${userId}@${serverId}`);

    try {
      const userHistory = await this.getUserHistory(userId);
      
      // 使用实例方法更新区服历史
      (userHistory as any).updateServerHistory(serverId, {
        serverName,
        lastLoginTime: new Date(),
        loginCount: ((userHistory as any).getServerHistory(serverId)?.loginCount || 0) + 1,
        ...updates,
      });

      // 更新全局统计
      this.updateGlobalStatsFromServerHistory(userHistory);

      await userHistory.save();
      
      // 更新缓存
      await this.cacheUserHistory(userHistory);

    } catch (error) {
      this.logger.error(`更新用户区服历史失败: ${userId}@${serverId}`, error);
      throw error;
    }
  }

  /**
   * 更新角色摘要
   */
  async updateCharacterSummary(
    userId: string,
    serverId: string,
    characterId: string,
    updates: Partial<CharacterSummary>
  ): Promise<void> {
    this.logger.log(`更新角色摘要: ${userId}@${serverId}:${characterId}`);

    try {
      const userHistory = await this.getUserHistory(userId);
      
      // 使用实例方法更新角色摘要
      (userHistory as any).updateCharacterSummary(serverId, characterId, {
        lastActiveAt: new Date(),
        ...updates,
      });

      // 更新区服统计
      this.updateServerStatsFromCharacters(userHistory, serverId);

      await userHistory.save();
      
      // 更新缓存
      await this.cacheUserHistory(userHistory);

    } catch (error) {
      this.logger.error(`更新角色摘要失败: ${userId}@${serverId}:${characterId}`, error);
      throw error;
    }
  }

  /**
   * 获取推荐区服
   */
  async getRecommendedServers(userId: string, availableServers: any[]): Promise<any[]> {
    const userHistory = await this.getUserHistory(userId);
    
    return availableServers.map(server => {
      const serverHistory = (userHistory as any).getServerHistory(server.id);
      const recommendationScore = this.calculateRecommendationScore(server, serverHistory);
      
      return {
        ...server,
        hasCharacter: !!serverHistory,
        lastPlayTime: serverHistory?.lastLoginTime,
        totalPlayTime: serverHistory?.totalPlayTime || 0,
        characterCount: serverHistory?.characters?.length || 0,
        recommendationScore,
      };
    }).sort((a, b) => b.recommendationScore - a.recommendationScore);
  }

  /**
   * 私有方法：创建用户历史记录
   */
  private async createUserHistory(userId: string): Promise<any> {
    this.logger.log(`创建用户历史记录: ${userId}`);

    try {
      // 获取用户基本信息
      const user = await this.usersService.findById(userId);
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      const userHistory = new this.userHistoryModel({
        userId,
        username: user.username,
        email: user.email,
        serverHistory: [],
        globalStats: this.getDefaultGlobalStats(),
        globalAchievements: [],
        preferences: {},
      });

      return await userHistory.save();

    } catch (error) {
      this.logger.error(`创建用户历史记录失败: ${userId}`, error);
      throw error;
    }
  }

  /**
   * 私有方法：从缓存获取用户历史
   */
  private async getUserHistoryFromCache(userId: string): Promise<UserHistoryDocument | null> {
    try {
      const cacheKey = `user_history:${userId}`;
      const cached = await this.redisService.get(cacheKey, 'global');
      
      if (cached) {
        const historyData = JSON.parse(cached as string);
        // 创建临时文档对象
        return new this.userHistoryModel(historyData);
      }

      return null;
    } catch (error) {
      this.logger.warn(`从缓存获取用户历史失败: ${userId}`, error);
      return null;
    }
  }

  /**
   * 私有方法：缓存用户历史
   */
  private async cacheUserHistory(userHistory: UserHistoryDocument): Promise<void> {
    try {
      const cacheKey = `user_history:${userHistory.userId}`;
      const ttl = 3600; // 1小时
      
      await this.redisService.set(
        cacheKey, 
        JSON.stringify(userHistory.toJSON()), 
        ttl, 
        'global'
      );
    } catch (error) {
      this.logger.warn(`缓存用户历史失败: ${userHistory.userId}`, error);
    }
  }

  /**
   * 私有方法：获取默认全局统计
   */
  private getDefaultGlobalStats(): GlobalUserStats {
    return {
      totalPlayTime: 0,
      totalLoginDays: 0,
      totalCharacters: 0,
      totalServersPlayed: 0,
      firstGameTime: new Date(),
      totalRecharge: 0,
      vipLevel: 0,
      lastLoginTime: new Date(),
    };
  }

  /**
   * 私有方法：从区服历史更新全局统计
   */
  private updateGlobalStatsFromServerHistory(userHistory: UserHistoryDocument): void {
    if (!userHistory.globalStats) {
      userHistory.globalStats = this.getDefaultGlobalStats();
    }

    const stats = userHistory.globalStats;
    
    // 计算总游戏时长
    stats.totalPlayTime = userHistory.serverHistory.reduce((total, server) => 
      total + (server.totalPlayTime || 0), 0
    );

    // 计算总角色数
    stats.totalCharacters = userHistory.serverHistory.reduce((total, server) => 
      total + (server.characters?.length || 0), 0
    );

    // 计算游戏过的区服数
    stats.totalServersPlayed = userHistory.serverHistory.filter(server => 
      server.status === 'active' && server.loginCount > 0
    ).length;

    // 更新首次游戏时间
    const firstLoginTimes = userHistory.serverHistory
      .map(server => server.firstLoginTime)
      .filter(time => time)
      .sort((a, b) => a.getTime() - b.getTime());
    
    if (firstLoginTimes.length > 0) {
      stats.firstGameTime = firstLoginTimes[0];
    }
  }

  /**
   * 私有方法：从角色数据更新区服统计
   */
  private updateServerStatsFromCharacters(userHistory: UserHistoryDocument, serverId: string): void {
    const serverHistory = (userHistory as any).getServerHistory(serverId);
    if (!serverHistory) return;

    if (!serverHistory.serverStats) {
      serverHistory.serverStats = {
        playTime: 0,
        loginDays: 0,
        characterCount: 0,
        maxLevel: 0,
        maxPower: 0,
        rechargeAmount: 0,
        totalBattles: 0,
        winRate: 0,
      };
    }

    const stats = serverHistory.serverStats;
    const characters = serverHistory.characters || [];

    // 更新角色数量
    stats.characterCount = characters.filter(char => char.status === 'active').length;

    // 更新最高等级和战力
    stats.maxLevel = Math.max(...characters.map(char => char.level || 0), 0);
    stats.maxPower = Math.max(...characters.map(char => char.power || 0), 0);

    // 更新总游戏时长
    stats.playTime = characters.reduce((total, char) => total + (char.totalPlayTime || 0), 0);
  }

  /**
   * 私有方法：计算推荐分数
   */
  private calculateRecommendationScore(server: any, serverHistory?: ServerHistoryItem): number {
    let score = 0;

    if (serverHistory) {
      // 有历史记录的区服基础分数
      score += 50;

      // 最近游戏时间加分
      const daysSinceLastPlay = (Date.now() - serverHistory.lastLoginTime.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceLastPlay <= 1) score += 30;
      else if (daysSinceLastPlay <= 7) score += 20;
      else if (daysSinceLastPlay <= 30) score += 10;

      // 角色数量加分
      const characterCount = serverHistory.characters?.length || 0;
      score += Math.min(characterCount * 5, 20);

      // 游戏时长加分
      const playTimeHours = (serverHistory.totalPlayTime || 0) / 3600;
      score += Math.min(playTimeHours / 10, 15);
    }

    // 服务器负载加分
    if (server.currentPlayers && server.maxPlayers) {
      const loadRatio = server.currentPlayers / server.maxPlayers;
      if (loadRatio < 0.3) score += 20;
      else if (loadRatio < 0.7) score += 15;
      else if (loadRatio < 0.9) score += 5;
    }

    // 新服加分
    if (server.openTime) {
      const daysSinceOpen = (Date.now() - new Date(server.openTime).getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceOpen <= 7) score += 25;
    }

    return Math.min(score, 100);
  }
}
