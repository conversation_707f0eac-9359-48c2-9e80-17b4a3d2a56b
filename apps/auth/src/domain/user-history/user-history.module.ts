import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

// 核心模块
import { CoreModule } from '../../core/shared/core.module';
import { UsersModule } from '../users/users.module';

// 服务
import { UserHistoryService } from './services/user-history.service';
import { UserHistorySyncService } from './services/user-history-sync.service';

// 控制器
import { UserHistoryController } from './controllers/user-history.controller';

// 实体和Schema
import { UserHistory, UserHistorySchema } from './entities/user-history.entity';

/**
 * 用户历史模块
 * 负责用户全局历史记录、区服历史、角色摘要等功能
 */
@Module({
  imports: [
    ConfigModule,
    CoreModule,
    UsersModule,
    
    // MongoDB Schema注册
    MongooseModule.forFeature([
      { name: UserHistory.name, schema: UserHistorySchema },
    ]),
  ],
  controllers: [
    UserHistoryController,
  ],
  providers: [
    UserHistoryService,
    UserHistorySyncService,
  ],
  exports: [
    UserHistoryService,
    UserHistorySyncService,
  ],
})
export class UserHistoryModule {}
