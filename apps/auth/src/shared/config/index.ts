import { ConfigFactory } from '@nestjs/config';

/**
 * 应用配置接口
 */
export interface AppConfiguration {
  app: AppConfig;
  database: DatabaseConfig;
  auth: AuthConfig;
  security: SecurityConfig;
}

export interface AppConfig {
  name: string;
  version: string;
  port: number;
  environment: string;
}

export interface DatabaseConfig {
  mongodb: {
    uri: string;
    options: {
      maxPoolSize: number;
      serverSelectionTimeoutMS: number;
    };
  };
  redis: {
    host: string;
    port: number;
    password?: string;
    db: number;
  };
}

export interface AuthConfig {
  jwt: {
    secret: string;
    accessTokenTTL: string;
    refreshTokenTTL: string;
    algorithm: string;
    issuer: string;
    audience: string;
  };
  password: {
    saltRounds: number;
    minLength: number;
    maxLength: number;
  };
  mfa: {
    enabled: boolean;
    issuer: string;
  };
  session: {
    maxConcurrentSessions: number;
    sessionTimeout: number;
    absoluteTimeout: number;
  };
}

export interface SecurityConfig {
  rateLimit: {
    global: {
      ttl: number;
      limit: number;
    };
    auth: {
      ttl: number;
      limit: number;
    };
  };
  encryption: {
    algorithm: string;
    keyLength: number;
  };
  audit: {
    enabled: boolean;
    retention: number;
  };
}

/**
 * 统一配置工厂
 * 
 * 整合了原来分散在5个配置文件中的所有配置：
 * - app.config.ts
 * - database.config.ts  
 * - auth.config.ts
 * - security.config.ts
 * - redis.config.ts
 */
export const createConfiguration = (): ConfigFactory<AppConfiguration> => () => ({
  app: {
    name: process.env.APP_NAME || 'football-manager-auth',
    version: process.env.APP_VERSION || '1.0.0',
    port: parseInt(process.env.AUTH_PORT || '3001', 10),
    environment: process.env.NODE_ENV || 'development',
  },
  
  database: {
    mongodb: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/auth',
      options: {
        maxPoolSize: parseInt(process.env.MONGODB_MAX_POOL_SIZE || '10', 10),
        serverSelectionTimeoutMS: parseInt(process.env.MONGODB_TIMEOUT || '5000', 10),
      },
    },
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379', 10),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0', 10),
    },
  },
  
  auth: {
    jwt: {
      secret: process.env.JWT_SECRET || 'change-me-in-production',
      accessTokenTTL: process.env.JWT_ACCESS_TOKEN_TTL || '15m',
      refreshTokenTTL: process.env.JWT_REFRESH_TOKEN_TTL || '7d',
      algorithm: process.env.JWT_ALGORITHM || 'HS256',
      issuer: process.env.JWT_ISSUER || 'football-manager-auth',
      audience: process.env.JWT_AUDIENCE || 'football-manager-app',
    },
    password: {
      saltRounds: parseInt(process.env.PASSWORD_SALT_ROUNDS || '12', 10),
      minLength: parseInt(process.env.PASSWORD_MIN_LENGTH || '8', 10),
      maxLength: parseInt(process.env.PASSWORD_MAX_LENGTH || '128', 10),
    },
    mfa: {
      enabled: process.env.MFA_ENABLED === 'true',
      issuer: process.env.MFA_ISSUER || '足球经理',
    },
    session: {
      maxConcurrentSessions: parseInt(process.env.MAX_CONCURRENT_SESSIONS || '5', 10),
      sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '3600', 10),
      absoluteTimeout: parseInt(process.env.ABSOLUTE_TIMEOUT || '86400', 10),
    },
  },
  
  security: {
    rateLimit: {
      global: {
        ttl: parseInt(process.env.RATE_LIMIT_TTL || '60', 10),
        limit: parseInt(process.env.RATE_LIMIT_MAX || '100', 10),
      },
      auth: {
        ttl: parseInt(process.env.AUTH_RATE_LIMIT_TTL || '300', 10),
        limit: parseInt(process.env.AUTH_RATE_LIMIT_MAX || '5', 10),
      },
    },
    encryption: {
      algorithm: process.env.ENCRYPTION_ALGORITHM || 'aes-256-gcm',
      keyLength: parseInt(process.env.ENCRYPTION_KEY_LENGTH || '32', 10),
    },
    audit: {
      enabled: process.env.AUDIT_ENABLED !== 'false',
      retention: parseInt(process.env.AUDIT_RETENTION_DAYS || '365', 10),
    },
  },
});

// 导出配置类型，供其他模块使用
export type { AppConfiguration, AppConfig, DatabaseConfig, AuthConfig, SecurityConfig };
