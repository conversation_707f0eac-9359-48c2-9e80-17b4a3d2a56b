import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';

export const authConfig = registerAs('auth', () => {
  const config = {
    // JWT配置
    jwt: {
      secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
      algorithm: process.env.JWT_ALGORITHM || 'HS256',
      issuer: process.env.JWT_ISSUER || 'football-manager-auth',
      audience: process.env.JWT_AUDIENCE || 'football-manager-app',
      
      // 令牌有效期
      accessTokenTTL: process.env.JWT_ACCESS_TOKEN_TTL || '15m',
      refreshTokenTTL: process.env.JWT_REFRESH_TOKEN_TTL || '7d',

      // 角色级Token配置（符合设计文档规范）
      character: {
        secret: process.env.CHARACTER_JWT_SECRET || 'your-character-jwt-secret-change-in-production',
        expiresIn: process.env.CHARACTER_JWT_EXPIRES_IN || '4h',
        algorithm: process.env.CHARACTER_JWT_ALGORITHM || 'HS256',
        issuer: process.env.CHARACTER_JWT_ISSUER || 'football-manager-game',
        audience: process.env.CHARACTER_JWT_AUDIENCE || 'football-manager-app',
      },
      
      // 高级选项
      clockTolerance: parseInt(process.env.JWT_CLOCK_TOLERANCE || '60', 10),
      ignoreExpiration: process.env.JWT_IGNORE_EXPIRATION === 'true',
      ignoreNotBefore: process.env.JWT_IGNORE_NOT_BEFORE === 'true',
      
      // 令牌轮换
      enableTokenRotation: process.env.JWT_ENABLE_TOKEN_ROTATION !== 'false',
      rotationThreshold: parseInt(process.env.JWT_ROTATION_THRESHOLD || '300', 10), // 5分钟
      
      // 黑名单
      blacklist: {
        enabled: process.env.JWT_BLACKLIST_ENABLED !== 'false',
        ttl: parseInt(process.env.JWT_BLACKLIST_TTL || '86400', 10), // 24小时
        keyPrefix: process.env.JWT_BLACKLIST_KEY_PREFIX || 'jwt:blacklist:',
      },
    },

    // 密码策略
    password: {
      minLength: parseInt(process.env.PASSWORD_MIN_LENGTH || '8', 10),
      maxLength: parseInt(process.env.PASSWORD_MAX_LENGTH || '128', 10),
      requireUppercase: process.env.PASSWORD_REQUIRE_UPPERCASE !== 'false',
      requireLowercase: process.env.PASSWORD_REQUIRE_LOWERCASE !== 'false',
      requireNumbers: process.env.PASSWORD_REQUIRE_NUMBERS !== 'false',
      requireSpecialChars: process.env.PASSWORD_REQUIRE_SPECIAL_CHARS !== 'false',
      forbiddenPatterns: process.env.PASSWORD_FORBIDDEN_PATTERNS?.split(',') || [
        'password', '123456', 'qwerty', 'admin', 'root'
      ],
      historyCount: parseInt(process.env.PASSWORD_HISTORY_COUNT || '5', 10),
      maxAge: parseInt(process.env.PASSWORD_MAX_AGE || '90', 10), // 90天
      minAge: parseInt(process.env.PASSWORD_MIN_AGE || '1', 10), // 1天
      
      // bcrypt配置
      saltRounds: parseInt(process.env.PASSWORD_SALT_ROUNDS || '12', 10),
    },

    // 多因子认证 (MFA)
    mfa: {
      // TOTP配置
      totp: {
        algorithm: process.env.MFA_TOTP_ALGORITHM || 'SHA1',
        digits: parseInt(process.env.MFA_TOTP_DIGITS || '6', 10),
        period: parseInt(process.env.MFA_TOTP_PERIOD || '30', 10),
        window: parseInt(process.env.MFA_TOTP_WINDOW || '2', 10),
        issuer: process.env.MFA_TOTP_ISSUER || '足球经理',
      },
      
      // SMS配置
      sms: {
        enabled: process.env.MFA_SMS_ENABLED === 'true',
        provider: process.env.MFA_SMS_PROVIDER || 'twilio',
        codeLength: parseInt(process.env.MFA_SMS_CODE_LENGTH || '6', 10),
        expiryTime: parseInt(process.env.MFA_SMS_EXPIRY_TIME || '300', 10), // 5分钟
        maxAttempts: parseInt(process.env.MFA_SMS_MAX_ATTEMPTS || '3', 10),
        cooldownPeriod: parseInt(process.env.MFA_SMS_COOLDOWN_PERIOD || '60', 10), // 1分钟
        rateLimit: {
          maxSMS: parseInt(process.env.MFA_SMS_RATE_LIMIT_MAX || '5', 10),
          window: parseInt(process.env.MFA_SMS_RATE_LIMIT_WINDOW || '3600', 10), // 1小时
        },
      },
      
      // 邮件配置
      email: {
        enabled: process.env.MFA_EMAIL_ENABLED === 'true',
        codeLength: parseInt(process.env.MFA_EMAIL_CODE_LENGTH || '6', 10),
        expiryTime: parseInt(process.env.MFA_EMAIL_EXPIRY_TIME || '300', 10), // 5分钟
        maxAttempts: parseInt(process.env.MFA_EMAIL_MAX_ATTEMPTS || '3', 10),
        cooldownPeriod: parseInt(process.env.MFA_EMAIL_COOLDOWN_PERIOD || '60', 10), // 1分钟
      },
      
      // 备用码配置
      backupCodes: {
        count: parseInt(process.env.MFA_BACKUP_CODES_COUNT || '10', 10),
        length: parseInt(process.env.MFA_BACKUP_CODES_LENGTH || '8', 10),
        format: process.env.MFA_BACKUP_CODES_FORMAT || 'numeric', // numeric, alphanumeric
      },
    },

    // 会话配置
    session: {
      maxConcurrentSessions: parseInt(process.env.SESSION_MAX_CONCURRENT || '5', 10),
      sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '3600', 10), // 1小时
      absoluteTimeout: parseInt(process.env.SESSION_ABSOLUTE_TIMEOUT || '86400', 10), // 24小时
      renewalThreshold: parseInt(process.env.SESSION_RENEWAL_THRESHOLD || '300', 10), // 5分钟
      
      // 会话安全
      secureTransport: process.env.SESSION_SECURE_TRANSPORT !== 'false',
      httpOnly: process.env.SESSION_HTTP_ONLY !== 'false',
      sameSite: process.env.SESSION_SAME_SITE || 'strict',
      
      // 设备管理
      deviceTracking: process.env.SESSION_DEVICE_TRACKING !== 'false',
      maxDevices: parseInt(process.env.SESSION_MAX_DEVICES || '10', 10),
      deviceTrustDuration: parseInt(process.env.SESSION_DEVICE_TRUST_DURATION || '2592000', 10), // 30天
    },

    // OAuth配置
    oauth: {
      // Google OAuth
      google: {
        enabled: process.env.OAUTH_GOOGLE_ENABLED === 'true',
        clientId: process.env.OAUTH_GOOGLE_CLIENT_ID,
        clientSecret: process.env.OAUTH_GOOGLE_CLIENT_SECRET,
        callbackURL: process.env.OAUTH_GOOGLE_CALLBACK_URL || '/auth/google/callback',
        scope: process.env.OAUTH_GOOGLE_SCOPE?.split(',') || ['profile', 'email'],
      },
      
      // Facebook OAuth
      facebook: {
        enabled: process.env.OAUTH_FACEBOOK_ENABLED === 'true',
        clientId: process.env.OAUTH_FACEBOOK_CLIENT_ID,
        clientSecret: process.env.OAUTH_FACEBOOK_CLIENT_SECRET,
        callbackURL: process.env.OAUTH_FACEBOOK_CALLBACK_URL || '/auth/facebook/callback',
        scope: process.env.OAUTH_FACEBOOK_SCOPE?.split(',') || ['email'],
      },
      
      // 微信OAuth
      wechat: {
        enabled: process.env.OAUTH_WECHAT_ENABLED === 'true',
        appId: process.env.OAUTH_WECHAT_APP_ID,
        appSecret: process.env.OAUTH_WECHAT_APP_SECRET,
        callbackURL: process.env.OAUTH_WECHAT_CALLBACK_URL || '/auth/wechat/callback',
        scope: process.env.OAUTH_WECHAT_SCOPE || 'snsapi_userinfo',
      },
    },

    // API密钥配置
    apiKey: {
      enabled: process.env.API_KEY_ENABLED === 'true',
      header: process.env.API_KEY_HEADER || 'X-API-Key',
      queryParam: process.env.API_KEY_QUERY_PARAM || 'api_key',
      keyLength: parseInt(process.env.API_KEY_LENGTH || '32', 10),
      expiryDays: parseInt(process.env.API_KEY_EXPIRY_DAYS || '365', 10), // 1年
      
      // 速率限制
      rateLimit: {
        enabled: process.env.API_KEY_RATE_LIMIT_ENABLED !== 'false',
        windowMs: parseInt(process.env.API_KEY_RATE_LIMIT_WINDOW_MS || '60000', 10), // 1分钟
        max: parseInt(process.env.API_KEY_RATE_LIMIT_MAX || '1000', 10),
      },
    },

    // 多服务器配置（新增）
    multiServer: {
      enabled: process.env.MULTI_SERVER_ENABLED === 'true',
      defaultServerId: process.env.DEFAULT_SERVER_ID || 'server_001',
      maxServers: parseInt(process.env.MAX_SERVERS || '10', 10),
      sessionTimeout: parseInt(process.env.CHARACTER_SESSION_TIMEOUT || '14400', 10), // 4小时（秒）

      // 区服配置
      servers: [
        {
          id: process.env.SERVER_001_ID || 'server_001',
          name: process.env.SERVER_001_NAME || '新手村',
          status: process.env.SERVER_001_STATUS || 'active',
          maxPlayers: parseInt(process.env.SERVER_001_MAX_PLAYERS || '10000', 10),
        },
        {
          id: process.env.SERVER_002_ID || 'server_002',
          name: process.env.SERVER_002_NAME || '勇者大陆',
          status: process.env.SERVER_002_STATUS || 'active',
          maxPlayers: parseInt(process.env.SERVER_002_MAX_PLAYERS || '10000', 10),
        },
      ].filter(server => server.id), // 过滤掉空的服务器配置
    },
  };

  // 配置验证
  const schema = Joi.object({
    jwt: Joi.object({
      secret: Joi.string().min(32).required(),
      algorithm: Joi.string().valid('HS256', 'HS384', 'HS512', 'RS256', 'RS384', 'RS512').required(),
      issuer: Joi.string().required(),
      audience: Joi.string().required(),
      accessTokenTTL: Joi.string().required(),
      refreshTokenTTL: Joi.string().required(),

      // 角色级Token配置验证
      character: Joi.object({
        secret: Joi.string().min(32).required(),
        expiresIn: Joi.string().required(),
        algorithm: Joi.string().valid('HS256', 'HS384', 'HS512', 'RS256', 'RS384', 'RS512'),
        issuer: Joi.string().required(),
        audience: Joi.string().required(),
      }).required(),
      clockTolerance: Joi.number().min(0),
      ignoreExpiration: Joi.boolean(),
      ignoreNotBefore: Joi.boolean(),
      enableTokenRotation: Joi.boolean(),
      rotationThreshold: Joi.number().min(0),
      blacklist: Joi.object({
        enabled: Joi.boolean(),
        ttl: Joi.number().min(1),
        keyPrefix: Joi.string(),
      }),
    }).required(),

    password: Joi.object({
      minLength: Joi.number().min(6).max(20),
      maxLength: Joi.number().min(20).max(256),
      requireUppercase: Joi.boolean(),
      requireLowercase: Joi.boolean(),
      requireNumbers: Joi.boolean(),
      requireSpecialChars: Joi.boolean(),
      forbiddenPatterns: Joi.array().items(Joi.string()),
      historyCount: Joi.number().min(0),
      maxAge: Joi.number().min(1),
      minAge: Joi.number().min(0),
      saltRounds: Joi.number().min(10).max(15),
    }).required(),

    mfa: Joi.object({
      totp: Joi.object({
        algorithm: Joi.string().valid('SHA1', 'SHA256', 'SHA512'),
        digits: Joi.number().valid(6, 8),
        period: Joi.number().min(15).max(300),
        window: Joi.number().min(0).max(10),
        issuer: Joi.string(),
      }),
      sms: Joi.object({
        enabled: Joi.boolean(),
        provider: Joi.string(),
        codeLength: Joi.number().min(4).max(8),
        expiryTime: Joi.number().min(60).max(3600),
        maxAttempts: Joi.number().min(1).max(10),
        cooldownPeriod: Joi.number().min(30).max(3600),
        rateLimit: Joi.object({
          maxSMS: Joi.number().min(1),
          window: Joi.number().min(300),
        }),
      }),
      email: Joi.object({
        enabled: Joi.boolean(),
        codeLength: Joi.number().min(4).max(8),
        expiryTime: Joi.number().min(60).max(3600),
        maxAttempts: Joi.number().min(1).max(10),
        cooldownPeriod: Joi.number().min(30).max(3600),
      }),
      backupCodes: Joi.object({
        count: Joi.number().min(5).max(20),
        length: Joi.number().min(6).max(12),
        format: Joi.string().valid('numeric', 'alphanumeric'),
      }),
    }),

    session: Joi.object({
      maxConcurrentSessions: Joi.number().min(1).max(20),
      sessionTimeout: Joi.number().min(300).max(86400),
      absoluteTimeout: Joi.number().min(3600).max(604800),
      renewalThreshold: Joi.number().min(60).max(3600),
      secureTransport: Joi.boolean(),
      httpOnly: Joi.boolean(),
      sameSite: Joi.string().valid('strict', 'lax', 'none'),
      deviceTracking: Joi.boolean(),
      maxDevices: Joi.number().min(1).max(50),
      deviceTrustDuration: Joi.number().min(86400),
    }),

    oauth: Joi.object({
      google: Joi.object({
        enabled: Joi.boolean(),
        clientId: Joi.string().allow('', null),
        clientSecret: Joi.string().allow('', null),
        callbackURL: Joi.string(),
        scope: Joi.array().items(Joi.string()),
      }),
      facebook: Joi.object({
        enabled: Joi.boolean(),
        clientId: Joi.string().allow('', null),
        clientSecret: Joi.string().allow('', null),
        callbackURL: Joi.string(),
        scope: Joi.array().items(Joi.string()),
      }),
      wechat: Joi.object({
        enabled: Joi.boolean(),
        appId: Joi.string().allow('', null),
        appSecret: Joi.string().allow('', null),
        callbackURL: Joi.string(),
        scope: Joi.string(),
      }),
    }),

    apiKey: Joi.object({
      enabled: Joi.boolean(),
      header: Joi.string(),
      queryParam: Joi.string(),
      keyLength: Joi.number().min(16).max(64),
      expiryDays: Joi.number().min(1).max(3650),
      rateLimit: Joi.object({
        enabled: Joi.boolean(),
        windowMs: Joi.number().min(1000),
        max: Joi.number().min(1),
      }),
    }),

    multiServer: Joi.object({
      enabled: Joi.boolean(),
      defaultServerId: Joi.string(),
      maxServers: Joi.number().min(1).max(100),
      sessionTimeout: Joi.number().min(300).max(86400), // 5分钟到24小时
      servers: Joi.array().items(
        Joi.object({
          id: Joi.string().required(),
          name: Joi.string().required(),
          status: Joi.string().valid('active', 'maintenance', 'closed'),
          maxPlayers: Joi.number().min(1),
        })
      ),
    }),
  });

  const { error } = schema.validate(config);
  if (error) {
    throw new Error(`认证配置验证失败: ${error.message}`);
  }

  return config;
});
