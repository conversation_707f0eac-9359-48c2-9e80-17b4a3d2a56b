import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';

export const databaseConfig = registerAs('database', () => {
  const config = {
    mongodb: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/football-manager-auth',
      options: {
        // 连接选项
        maxPoolSize: parseInt(process.env.MONGODB_MAX_POOL_SIZE || '10', 10),
        serverSelectionTimeoutMS: parseInt(process.env.MONGODB_SERVER_SELECTION_TIMEOUT || '5000', 10),
        socketTimeoutMS: parseInt(process.env.MONGODB_SOCKET_TIMEOUT || '45000', 10),
        connectTimeoutMS: parseInt(process.env.MONGODB_CONNECT_TIMEOUT || '10000', 10),
        
        // 缓冲配置（bufferMaxEntries 在新版本中已废弃）
        // bufferMaxEntries: parseInt(process.env.MONGODB_BUFFER_MAX_ENTRIES || '0', 10),
        bufferCommands: process.env.MONGODB_BUFFER_COMMANDS === 'true',
        
        // 认证配置
        // authSource: process.env.MONGODB_AUTH_SOURCE || 'admin',
        ...(process.env.MONGODB_AUTH_SOURCE && !process.env.MONGODB_URI?.includes('authSource') && {
          authSource: process.env.MONGODB_AUTH_SOURCE
        }),
        
        // SSL/TLS配置（使用新的 TLS 选项替代废弃的 SSL 选项）
        tls: process.env.MONGODB_TLS === 'true' || process.env.MONGODB_SSL === 'true',
        tlsAllowInvalidCertificates: process.env.MONGODB_TLS_ALLOW_INVALID_CERTIFICATES === 'true' || process.env.MONGODB_SSL_VALIDATE === 'false',
        tlsCAFile: process.env.MONGODB_TLS_CA_FILE || process.env.MONGODB_SSL_CA,
        tlsCertificateKeyFile: process.env.MONGODB_TLS_CERT_KEY_FILE || process.env.MONGODB_SSL_CERT,
        // 移除废弃的 SSL 选项
        // ssl: process.env.MONGODB_SSL === 'true',
        // sslValidate: process.env.MONGODB_SSL_VALIDATE === 'true',
        // sslCA: process.env.MONGODB_SSL_CA,
        // sslCert: process.env.MONGODB_SSL_CERT,
        // sslKey: process.env.MONGODB_SSL_KEY,
        
        // 副本集配置
        replicaSet: process.env.MONGODB_REPLICA_SET,
        
        // 读写偏好
        readPreference: process.env.MONGODB_READ_PREFERENCE || 'primary',
        readConcern: {
          level: process.env.MONGODB_READ_CONCERN_LEVEL || 'majority',
        },
        writeConcern: {
          w: process.env.MONGODB_WRITE_CONCERN_W || 'majority',
          j: process.env.MONGODB_WRITE_CONCERN_J === 'true',
          wtimeout: parseInt(process.env.MONGODB_WRITE_CONCERN_TIMEOUT || '1000', 10),
        },
        
        // 其他选项
        retryWrites: process.env.MONGODB_RETRY_WRITES !== 'false',
        retryReads: process.env.MONGODB_RETRY_READS !== 'false',
        compressors: process.env.MONGODB_COMPRESSORS?.split(',') || ['snappy', 'zlib'],
        zlibCompressionLevel: parseInt(process.env.MONGODB_ZLIB_COMPRESSION_LEVEL || '6', 10),
      },
    },

    // 数据库性能配置
    performance: {
      // 索引配置
      autoIndex: process.env.MONGODB_AUTO_INDEX !== 'false',
      autoCreate: process.env.MONGODB_AUTO_CREATE !== 'false',
      
      // 查询优化
      maxTimeMS: parseInt(process.env.MONGODB_MAX_TIME_MS || '30000', 10),
      
      // 批量操作
      batchSize: parseInt(process.env.MONGODB_BATCH_SIZE || '1000', 10),
    },

    // 监控配置
    monitoring: {
      enableCommandMonitoring: process.env.MONGODB_ENABLE_COMMAND_MONITORING === 'true',
      enableConnectionMonitoring: process.env.MONGODB_ENABLE_CONNECTION_MONITORING === 'true',
      enableServerMonitoring: process.env.MONGODB_ENABLE_SERVER_MONITORING === 'true',
      enableTopologyMonitoring: process.env.MONGODB_ENABLE_TOPOLOGY_MONITORING === 'true',
    },

    // 备份配置
    backup: {
      enabled: process.env.MONGODB_BACKUP_ENABLED === 'true',
      schedule: process.env.MONGODB_BACKUP_SCHEDULE || '0 2 * * *', // 每天凌晨2点
      retention: parseInt(process.env.MONGODB_BACKUP_RETENTION || '30', 10), // 保留30天
      compression: process.env.MONGODB_BACKUP_COMPRESSION !== 'false',
    },
  };

  // 配置验证
  const schema = Joi.object({
    mongodb: Joi.object({
      uri: Joi.string().uri().required(),
      options: Joi.object({
        maxPoolSize: Joi.number().min(1).max(100),
        serverSelectionTimeoutMS: Joi.number().min(1000),
        socketTimeoutMS: Joi.number().min(1000),
        connectTimeoutMS: Joi.number().min(1000),
        // bufferMaxEntries: Joi.number().min(0), // 已废弃
        bufferCommands: Joi.boolean(),

        // 认证源配置 - 可选，因为可能在 URI 中指定
        authSource: Joi.string().optional(),
        // 新的 TLS 选项（替代废弃的 SSL 选项）
        tls: Joi.boolean(),
        tlsAllowInvalidCertificates: Joi.boolean(),
        tlsCAFile: Joi.string().allow('', null),
        tlsCertificateKeyFile: Joi.string().allow('', null),
        // 废弃的 SSL 选项（保留以兼容旧配置）
        // ssl: Joi.boolean(),
        // sslValidate: Joi.boolean(),
        // sslCA: Joi.string().allow('', null),
        // sslCert: Joi.string().allow('', null),
        // sslKey: Joi.string().allow('', null),
        replicaSet: Joi.string().allow('', null),
        readPreference: Joi.string().valid('primary', 'primaryPreferred', 'secondary', 'secondaryPreferred', 'nearest'),
        readConcern: Joi.object({
          level: Joi.string().valid('local', 'available', 'majority', 'linearizable', 'snapshot'),
        }),
        writeConcern: Joi.object({
          w: Joi.alternatives().try(Joi.string(), Joi.number()),
          j: Joi.boolean(),
          wtimeout: Joi.number().min(0),
        }),
        retryWrites: Joi.boolean(),
        retryReads: Joi.boolean(),
        compressors: Joi.array().items(Joi.string()),
        zlibCompressionLevel: Joi.number().min(1).max(9),
      }),
    }).required(),

    performance: Joi.object({
      autoIndex: Joi.boolean(),
      autoCreate: Joi.boolean(),
      maxTimeMS: Joi.number().min(1000),
      batchSize: Joi.number().min(1),
    }),

    monitoring: Joi.object({
      enableCommandMonitoring: Joi.boolean(),
      enableConnectionMonitoring: Joi.boolean(),
      enableServerMonitoring: Joi.boolean(),
      enableTopologyMonitoring: Joi.boolean(),
    }),

    backup: Joi.object({
      enabled: Joi.boolean(),
      schedule: Joi.string(),
      retention: Joi.number().min(1),
      compression: Joi.boolean(),
    }),
  });

  const { error } = schema.validate(config);
  if (error) {
    throw new Error(`数据库配置验证失败: ${error.message}`);
  }

  return config;
});
