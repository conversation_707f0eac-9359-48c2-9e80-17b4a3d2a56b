/**
 * 错误相关常量
 */

// 错误代码常量
export const ERROR_CODES = {
  // 认证错误 (1000-1999)
  AUTH_INVALID_CREDENTIALS: 'AUTH_1001',
  AUTH_TOKEN_EXPIRED: 'AUTH_1002',
  AUTH_TOKEN_INVALID: 'AUTH_1003',
  AUTH_TOKEN_MISSING: 'AUTH_1004',
  AUTH_TOKEN_REVOKED: 'AUTH_1005',
  AUTH_INSUFFICIENT_PERMISSIONS: 'AUTH_1006',
  AUTH_ACCOUNT_LOCKED: 'AUTH_1007',
  AUTH_ACCOUNT_SUSPENDED: 'AUTH_1008',
  AUTH_ACCOUNT_INACTIVE: 'AUTH_1009',
  AUTH_ACCOUNT_DELETED: 'AUTH_1010',
  AUTH_MFA_REQUIRED: 'AUTH_1011',
  AUTH_MFA_INVALID: 'AUTH_1012',
  AUTH_SESSION_EXPIRED: 'AUTH_1013',
  AUTH_SESSION_INVALID: 'AUTH_1014',
  AUTH_DEVICE_NOT_TRUSTED: 'AUTH_1015',
  AUTH_IP_BLOCKED: 'AUTH_1016',
  AUTH_RATE_LIMITED: 'AUTH_1017',
  AUTH_BRUTE_FORCE_DETECTED: 'AUTH_1018',

  // 用户错误 (2000-2999)
  USER_NOT_FOUND: 'USER_2001',
  USER_ALREADY_EXISTS: 'USER_2002',
  USER_EMAIL_EXISTS: 'USER_2003',
  USER_USERNAME_EXISTS: 'USER_2004',
  USER_PHONE_EXISTS: 'USER_2005',
  USER_INVALID_EMAIL: 'USER_2006',
  USER_INVALID_PHONE: 'USER_2007',
  USER_WEAK_PASSWORD: 'USER_2008',
  USER_PASSWORD_REUSED: 'USER_2009',
  USER_EMAIL_NOT_VERIFIED: 'USER_2010',
  USER_PHONE_NOT_VERIFIED: 'USER_2011',
  USER_PROFILE_INCOMPLETE: 'USER_2012',

  // 角色权限错误 (3000-3999)
  ROLE_NOT_FOUND: 'ROLE_3001',
  ROLE_ALREADY_EXISTS: 'ROLE_3002',
  ROLE_IN_USE: 'ROLE_3003',
  ROLE_SYSTEM_PROTECTED: 'ROLE_3004',
  PERMISSION_NOT_FOUND: 'PERM_3005',
  PERMISSION_ALREADY_EXISTS: 'PERM_3006',
  PERMISSION_DENIED: 'PERM_3007',
  PERMISSION_SYSTEM_PROTECTED: 'PERM_3008',

  // 验证错误 (4000-4999)
  VALIDATION_FAILED: 'VALID_4001',
  VALIDATION_REQUIRED_FIELD: 'VALID_4002',
  VALIDATION_INVALID_FORMAT: 'VALID_4003',
  VALIDATION_OUT_OF_RANGE: 'VALID_4004',
  VALIDATION_INVALID_TYPE: 'VALID_4005',
  VALIDATION_CONSTRAINT_VIOLATION: 'VALID_4006',

  // 系统错误 (5000-5999)
  SYSTEM_INTERNAL_ERROR: 'SYS_5001',
  SYSTEM_DATABASE_ERROR: 'SYS_5002',
  SYSTEM_REDIS_ERROR: 'SYS_5003',
  SYSTEM_NETWORK_ERROR: 'SYS_5004',
  SYSTEM_TIMEOUT: 'SYS_5005',
  SYSTEM_MAINTENANCE: 'SYS_5006',
  SYSTEM_OVERLOADED: 'SYS_5007',
  SYSTEM_CONFIG_ERROR: 'SYS_5008',
  SYSTEM_EXTERNAL_SERVICE_ERROR: 'SYS_5009',

  // 业务错误 (6000-6999)
  BUSINESS_OPERATION_NOT_ALLOWED: 'BIZ_6001',
  BUSINESS_RESOURCE_CONFLICT: 'BIZ_6002',
  BUSINESS_QUOTA_EXCEEDED: 'BIZ_6003',
  BUSINESS_FEATURE_DISABLED: 'BIZ_6004',
  BUSINESS_INVALID_STATE: 'BIZ_6005',
} as const;

// 错误消息常量
export const ERROR_MESSAGES = {
  // 认证错误消息
  [ERROR_CODES.AUTH_INVALID_CREDENTIALS]: '用户名或密码错误',
  [ERROR_CODES.AUTH_TOKEN_EXPIRED]: '令牌已过期，请重新登录',
  [ERROR_CODES.AUTH_TOKEN_INVALID]: '无效的令牌',
  [ERROR_CODES.AUTH_TOKEN_MISSING]: '缺少认证令牌',
  [ERROR_CODES.AUTH_TOKEN_REVOKED]: '令牌已被撤销',
  [ERROR_CODES.AUTH_INSUFFICIENT_PERMISSIONS]: '权限不足',
  [ERROR_CODES.AUTH_ACCOUNT_LOCKED]: '账户已被锁定',
  [ERROR_CODES.AUTH_ACCOUNT_SUSPENDED]: '账户已被暂停',
  [ERROR_CODES.AUTH_ACCOUNT_INACTIVE]: '账户未激活',
  [ERROR_CODES.AUTH_ACCOUNT_DELETED]: '账户不存在',
  [ERROR_CODES.AUTH_MFA_REQUIRED]: '需要多因子认证',
  [ERROR_CODES.AUTH_MFA_INVALID]: '多因子认证码无效',
  [ERROR_CODES.AUTH_SESSION_EXPIRED]: '会话已过期',
  [ERROR_CODES.AUTH_SESSION_INVALID]: '无效的会话',
  [ERROR_CODES.AUTH_DEVICE_NOT_TRUSTED]: '设备未受信任',
  [ERROR_CODES.AUTH_IP_BLOCKED]: 'IP地址已被阻止',
  [ERROR_CODES.AUTH_RATE_LIMITED]: '请求过于频繁，请稍后重试',
  [ERROR_CODES.AUTH_BRUTE_FORCE_DETECTED]: '检测到暴力破解攻击',

  // 用户错误消息
  [ERROR_CODES.USER_NOT_FOUND]: '用户不存在',
  [ERROR_CODES.USER_ALREADY_EXISTS]: '用户已存在',
  [ERROR_CODES.USER_EMAIL_EXISTS]: '邮箱地址已被使用',
  [ERROR_CODES.USER_USERNAME_EXISTS]: '用户名已被使用',
  [ERROR_CODES.USER_PHONE_EXISTS]: '手机号码已被使用',
  [ERROR_CODES.USER_INVALID_EMAIL]: '邮箱地址格式无效',
  [ERROR_CODES.USER_INVALID_PHONE]: '手机号码格式无效',
  [ERROR_CODES.USER_WEAK_PASSWORD]: '密码强度不足',
  [ERROR_CODES.USER_PASSWORD_REUSED]: '不能使用最近使用过的密码',
  [ERROR_CODES.USER_EMAIL_NOT_VERIFIED]: '邮箱地址未验证',
  [ERROR_CODES.USER_PHONE_NOT_VERIFIED]: '手机号码未验证',
  [ERROR_CODES.USER_PROFILE_INCOMPLETE]: '用户资料不完整',

  // 角色权限错误消息
  [ERROR_CODES.ROLE_NOT_FOUND]: '角色不存在',
  [ERROR_CODES.ROLE_ALREADY_EXISTS]: '角色已存在',
  [ERROR_CODES.ROLE_IN_USE]: '角色正在使用中，无法删除',
  [ERROR_CODES.ROLE_SYSTEM_PROTECTED]: '系统角色受保护，无法修改',
  [ERROR_CODES.PERMISSION_NOT_FOUND]: '权限不存在',
  [ERROR_CODES.PERMISSION_ALREADY_EXISTS]: '权限已存在',
  [ERROR_CODES.PERMISSION_DENIED]: '权限被拒绝',
  [ERROR_CODES.PERMISSION_SYSTEM_PROTECTED]: '系统权限受保护，无法修改',

  // 验证错误消息
  [ERROR_CODES.VALIDATION_FAILED]: '数据验证失败',
  [ERROR_CODES.VALIDATION_REQUIRED_FIELD]: '必填字段不能为空',
  [ERROR_CODES.VALIDATION_INVALID_FORMAT]: '数据格式无效',
  [ERROR_CODES.VALIDATION_OUT_OF_RANGE]: '数据超出有效范围',
  [ERROR_CODES.VALIDATION_INVALID_TYPE]: '数据类型无效',
  [ERROR_CODES.VALIDATION_CONSTRAINT_VIOLATION]: '违反约束条件',

  // 系统错误消息
  [ERROR_CODES.SYSTEM_INTERNAL_ERROR]: '系统内部错误',
  [ERROR_CODES.SYSTEM_DATABASE_ERROR]: '数据库错误',
  [ERROR_CODES.SYSTEM_REDIS_ERROR]: '缓存服务错误',
  [ERROR_CODES.SYSTEM_NETWORK_ERROR]: '网络错误',
  [ERROR_CODES.SYSTEM_TIMEOUT]: '请求超时',
  [ERROR_CODES.SYSTEM_MAINTENANCE]: '系统维护中',
  [ERROR_CODES.SYSTEM_OVERLOADED]: '系统负载过高',
  [ERROR_CODES.SYSTEM_CONFIG_ERROR]: '系统配置错误',
  [ERROR_CODES.SYSTEM_EXTERNAL_SERVICE_ERROR]: '外部服务错误',

  // 业务错误消息
  [ERROR_CODES.BUSINESS_OPERATION_NOT_ALLOWED]: '操作不被允许',
  [ERROR_CODES.BUSINESS_RESOURCE_CONFLICT]: '资源冲突',
  [ERROR_CODES.BUSINESS_QUOTA_EXCEEDED]: '配额已超出',
  [ERROR_CODES.BUSINESS_FEATURE_DISABLED]: '功能已禁用',
  [ERROR_CODES.BUSINESS_INVALID_STATE]: '无效的状态',
} as const;

// HTTP状态码映射
export const ERROR_HTTP_STATUS = {
  // 认证错误 -> 401 Unauthorized
  [ERROR_CODES.AUTH_INVALID_CREDENTIALS]: 401,
  [ERROR_CODES.AUTH_TOKEN_EXPIRED]: 401,
  [ERROR_CODES.AUTH_TOKEN_INVALID]: 401,
  [ERROR_CODES.AUTH_TOKEN_MISSING]: 401,
  [ERROR_CODES.AUTH_TOKEN_REVOKED]: 401,
  [ERROR_CODES.AUTH_MFA_REQUIRED]: 401,
  [ERROR_CODES.AUTH_MFA_INVALID]: 401,
  [ERROR_CODES.AUTH_SESSION_EXPIRED]: 401,
  [ERROR_CODES.AUTH_SESSION_INVALID]: 401,

  // 权限错误 -> 403 Forbidden
  [ERROR_CODES.AUTH_INSUFFICIENT_PERMISSIONS]: 403,
  [ERROR_CODES.AUTH_ACCOUNT_LOCKED]: 403,
  [ERROR_CODES.AUTH_ACCOUNT_SUSPENDED]: 403,
  [ERROR_CODES.AUTH_ACCOUNT_INACTIVE]: 403,
  [ERROR_CODES.AUTH_ACCOUNT_DELETED]: 403,
  [ERROR_CODES.AUTH_DEVICE_NOT_TRUSTED]: 403,
  [ERROR_CODES.AUTH_IP_BLOCKED]: 403,
  [ERROR_CODES.PERMISSION_DENIED]: 403,

  // 资源不存在 -> 404 Not Found
  [ERROR_CODES.USER_NOT_FOUND]: 404,
  [ERROR_CODES.ROLE_NOT_FOUND]: 404,
  [ERROR_CODES.PERMISSION_NOT_FOUND]: 404,

  // 资源冲突 -> 409 Conflict
  [ERROR_CODES.USER_ALREADY_EXISTS]: 409,
  [ERROR_CODES.USER_EMAIL_EXISTS]: 409,
  [ERROR_CODES.USER_USERNAME_EXISTS]: 409,
  [ERROR_CODES.USER_PHONE_EXISTS]: 409,
  [ERROR_CODES.ROLE_ALREADY_EXISTS]: 409,
  [ERROR_CODES.PERMISSION_ALREADY_EXISTS]: 409,
  [ERROR_CODES.BUSINESS_RESOURCE_CONFLICT]: 409,

  // 验证错误 -> 400 Bad Request
  [ERROR_CODES.VALIDATION_FAILED]: 400,
  [ERROR_CODES.VALIDATION_REQUIRED_FIELD]: 400,
  [ERROR_CODES.VALIDATION_INVALID_FORMAT]: 400,
  [ERROR_CODES.VALIDATION_OUT_OF_RANGE]: 400,
  [ERROR_CODES.VALIDATION_INVALID_TYPE]: 400,
  [ERROR_CODES.VALIDATION_CONSTRAINT_VIOLATION]: 400,
  [ERROR_CODES.USER_INVALID_EMAIL]: 400,
  [ERROR_CODES.USER_INVALID_PHONE]: 400,
  [ERROR_CODES.USER_WEAK_PASSWORD]: 400,

  // 速率限制 -> 429 Too Many Requests
  [ERROR_CODES.AUTH_RATE_LIMITED]: 429,
  [ERROR_CODES.AUTH_BRUTE_FORCE_DETECTED]: 429,

  // 业务逻辑错误 -> 422 Unprocessable Entity
  [ERROR_CODES.USER_PASSWORD_REUSED]: 422,
  [ERROR_CODES.USER_EMAIL_NOT_VERIFIED]: 422,
  [ERROR_CODES.USER_PHONE_NOT_VERIFIED]: 422,
  [ERROR_CODES.USER_PROFILE_INCOMPLETE]: 422,
  [ERROR_CODES.ROLE_IN_USE]: 422,
  [ERROR_CODES.ROLE_SYSTEM_PROTECTED]: 422,
  [ERROR_CODES.PERMISSION_SYSTEM_PROTECTED]: 422,
  [ERROR_CODES.BUSINESS_OPERATION_NOT_ALLOWED]: 422,
  [ERROR_CODES.BUSINESS_QUOTA_EXCEEDED]: 422,
  [ERROR_CODES.BUSINESS_FEATURE_DISABLED]: 422,
  [ERROR_CODES.BUSINESS_INVALID_STATE]: 422,

  // 系统错误 -> 500 Internal Server Error
  [ERROR_CODES.SYSTEM_INTERNAL_ERROR]: 500,
  [ERROR_CODES.SYSTEM_DATABASE_ERROR]: 500,
  [ERROR_CODES.SYSTEM_REDIS_ERROR]: 500,
  [ERROR_CODES.SYSTEM_CONFIG_ERROR]: 500,

  // 网络错误 -> 502 Bad Gateway
  [ERROR_CODES.SYSTEM_NETWORK_ERROR]: 502,
  [ERROR_CODES.SYSTEM_EXTERNAL_SERVICE_ERROR]: 502,

  // 服务不可用 -> 503 Service Unavailable
  [ERROR_CODES.SYSTEM_MAINTENANCE]: 503,
  [ERROR_CODES.SYSTEM_OVERLOADED]: 503,

  // 超时 -> 504 Gateway Timeout
  [ERROR_CODES.SYSTEM_TIMEOUT]: 504,
} as const;

// 错误分类
export const ERROR_CATEGORIES = {
  AUTHENTICATION: 'authentication',
  AUTHORIZATION: 'authorization',
  VALIDATION: 'validation',
  BUSINESS: 'business',
  SYSTEM: 'system',
  NETWORK: 'network',
} as const;

// 错误严重级别
export const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
} as const;

// 导出类型
export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];
export type ErrorCategory = typeof ERROR_CATEGORIES[keyof typeof ERROR_CATEGORIES];
export type ErrorSeverity = typeof ERROR_SEVERITY[keyof typeof ERROR_SEVERITY];
