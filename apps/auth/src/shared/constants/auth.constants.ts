/**
 * 认证相关常量
 */

// JWT 相关常量
export const JWT_CONSTANTS = {
  // 令牌类型
  TOKEN_TYPES: {
    ACCESS: 'access',
    REFRESH: 'refresh',
    RESET: 'reset',
    VERIFICATION: 'verification',
  },
  
  // 令牌过期时间
  EXPIRATION: {
    ACCESS_TOKEN: '15m',
    REFRESH_TOKEN: '7d',
    RESET_TOKEN: '1h',
    VERIFICATION_TOKEN: '24h',
  },
  
  // 令牌前缀
  TOKEN_PREFIX: 'Bearer ',
  
  // 头部字段
  HEADERS: {
    AUTHORIZATION: 'authorization',
    API_KEY: 'x-api-key',
    DEVICE_ID: 'x-device-id',
    USER_AGENT: 'user-agent',
    FORWARDED_FOR: 'x-forwarded-for',
    REAL_IP: 'x-real-ip',
  },
} as const;

// 认证策略常量
export const AUTH_STRATEGIES = {
  JWT: 'jwt',
  LOCAL: 'local',
  GOOGLE: 'google',
  API_KEY: 'api-key',
} as const;

// 用户状态常量
export const USER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  SUSPENDED: 'suspended',
  DELETED: 'deleted',
} as const;

// 会话状态常量
export const SESSION_STATUS = {
  ACTIVE: 'active',
  EXPIRED: 'expired',
  REVOKED: 'revoked',
  INVALID: 'invalid',
} as const;

// MFA 类型常量
export const MFA_TYPES = {
  TOTP: 'totp',
  SMS: 'sms',
  EMAIL: 'email',
  BACKUP_CODE: 'backup_code',
} as const;

// 设备类型常量
export const DEVICE_TYPES = {
  WEB: 'web',
  MOBILE: 'mobile',
  TABLET: 'tablet',
  DESKTOP: 'desktop',
  API: 'api',
} as const;

// 登录方式常量
export const LOGIN_METHODS = {
  PASSWORD: 'password',
  GOOGLE: 'google',
  MFA: 'mfa',
  API_KEY: 'api_key',
} as const;

// 安全事件类型常量
export const SECURITY_EVENT_TYPES = {
  LOGIN_SUCCESS: 'login_success',
  LOGIN_FAILED: 'login_failed',
  LOGOUT: 'logout',
  PASSWORD_CHANGED: 'password_changed',
  MFA_ENABLED: 'mfa_enabled',
  MFA_DISABLED: 'mfa_disabled',
  ACCOUNT_LOCKED: 'account_locked',
  ACCOUNT_UNLOCKED: 'account_unlocked',
  SUSPICIOUS_ACTIVITY: 'suspicious_activity',
  BRUTE_FORCE_ATTEMPT: 'brute_force_attempt',
  TOKEN_REVOKED: 'token_revoked',
  PERMISSION_DENIED: 'permission_denied',
  ADMIN_DASHBOARD_ACCESS: 'admin_dashboard_access',
  SYSTEM_MAINTENANCE: 'system_maintenance',
  SYSTEM_BACKUP: 'system_backup',
  SYSTEM_SETTINGS_UPDATE: 'system_settings_update',
} as const;

// 权限操作常量
export const PERMISSION_ACTIONS = {
  CREATE: 'create',
  READ: 'read',
  UPDATE: 'update',
  DELETE: 'delete',
  MANAGE: 'manage',
  EXECUTE: 'execute',
} as const;

// 资源类型常量
export const RESOURCE_TYPES = {
  USER: 'user',
  ROLE: 'role',
  PERMISSION: 'permission',
  SESSION: 'session',
  AUDIT_LOG: 'audit_log',
  SYSTEM: 'system',
  ADMIN: 'admin',
} as const;

// 缓存键前缀
export const CACHE_KEYS = {
  USER: 'user:',
  SESSION: 'session:',
  PERMISSION: 'permission:',
  ROLE: 'role:',
  BLACKLIST: 'blacklist:',
  RATE_LIMIT: 'rate_limit:',
  MFA_CODE: 'mfa_code:',
  RESET_TOKEN: 'reset_token:',
  VERIFICATION_TOKEN: 'verification_token:',
} as const;

// 速率限制常量 - 支持环境变量覆盖
export const RATE_LIMIT = {
  LOGIN: {
    WINDOW: parseInt(process.env.RATE_LIMIT_LOGIN_TTL || '900', 10) * 1000, // 默认15分钟
    MAX_ATTEMPTS: parseInt(process.env.RATE_LIMIT_LOGIN_LIMIT || '5', 10),
  },
  REGISTER: {
    WINDOW: parseInt(process.env.RATE_LIMIT_REGISTER_TTL || '3600', 10) * 1000, // 默认1小时
    MAX_ATTEMPTS: parseInt(process.env.RATE_LIMIT_REGISTER_LIMIT || '3', 10),
  },
  PASSWORD_RESET: {
    WINDOW: parseInt(process.env.RATE_LIMIT_PASSWORD_RESET_TTL || '3600', 10) * 1000, // 默认1小时
    MAX_ATTEMPTS: parseInt(process.env.RATE_LIMIT_PASSWORD_RESET_LIMIT || '3', 10),
  },
  MFA_VERIFY: {
    WINDOW: 5 * 60 * 1000, // 5分钟
    MAX_ATTEMPTS: 5,
  },
} as const;

// 密码策略常量
export const PASSWORD_POLICY = {
  MIN_LENGTH: 8,
  MAX_LENGTH: 128,
  REQUIRE_UPPERCASE: true,
  REQUIRE_LOWERCASE: true,
  REQUIRE_NUMBERS: true,
  REQUIRE_SPECIAL_CHARS: true,
  SPECIAL_CHARS: '!@#$%^&*()_+-=[]{}|;:,.<>?',
  HISTORY_COUNT: 5, // 记住最近5个密码
} as const;

// 账户保护常量
export const ACCOUNT_PROTECTION = {
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 30 * 60 * 1000, // 30分钟
  PROGRESSIVE_DELAY: true,
  DELAY_MULTIPLIER: 2,
} as const;

// 会话配置常量
export const SESSION_CONFIG = {
  MAX_CONCURRENT_SESSIONS: 5,
  IDLE_TIMEOUT: 30 * 60 * 1000, // 30分钟
  ABSOLUTE_TIMEOUT: 8 * 60 * 60 * 1000, // 8小时
  REMEMBER_ME_DURATION: 30 * 24 * 60 * 60 * 1000, // 30天
} as const;

// 审计日志常量
export const AUDIT_LOG = {
  RETENTION_DAYS: 90,
  BATCH_SIZE: 1000,
  SENSITIVE_FIELDS: ['password', 'token', 'secret', 'key'],
} as const;

// 加密常量
export const ENCRYPTION = {
  ALGORITHM: 'aes-256-gcm',
  KEY_LENGTH: 32,
  IV_LENGTH: 16,
  SALT_LENGTH: 32,
  TAG_LENGTH: 16,
  ITERATIONS: 100000,
} as const;

// API 版本常量
export const API_VERSIONS = {
  V1: 'v1',
  V2: 'v2',
} as const;

// 环境常量
export const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  STAGING: 'staging',
  PRODUCTION: 'production',
  TEST: 'test',
} as const;

// 日志级别常量
export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug',
  VERBOSE: 'verbose',
} as const;

// 健康检查常量
export const HEALTH_CHECK = {
  TIMEOUT: 5000,
  INTERVAL: 30000,
  RETRIES: 3,
} as const;

// 导出所有常量的类型
export type JwtTokenType = typeof JWT_CONSTANTS.TOKEN_TYPES[keyof typeof JWT_CONSTANTS.TOKEN_TYPES];
export type AuthStrategy = typeof AUTH_STRATEGIES[keyof typeof AUTH_STRATEGIES];
export type UserStatus = typeof USER_STATUS[keyof typeof USER_STATUS];
export type SessionStatus = typeof SESSION_STATUS[keyof typeof SESSION_STATUS];
export type MfaType = typeof MFA_TYPES[keyof typeof MFA_TYPES];
export type DeviceType = typeof DEVICE_TYPES[keyof typeof DEVICE_TYPES];
export type LoginMethod = typeof LOGIN_METHODS[keyof typeof LOGIN_METHODS];
export type SecurityEventType = typeof SECURITY_EVENT_TYPES[keyof typeof SECURITY_EVENT_TYPES];
export type PermissionAction = typeof PERMISSION_ACTIONS[keyof typeof PERMISSION_ACTIONS];
export type ResourceType = typeof RESOURCE_TYPES[keyof typeof RESOURCE_TYPES];
export type ApiVersion = typeof API_VERSIONS[keyof typeof API_VERSIONS];
export type Environment = typeof ENVIRONMENTS[keyof typeof ENVIRONMENTS];
export type LogLevel = typeof LOG_LEVELS[keyof typeof LOG_LEVELS];
