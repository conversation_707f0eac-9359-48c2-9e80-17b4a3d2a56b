/**
 * 认证相关接口定义
 */

import { 
  JwtTokenType, 
  UserStatus, 
  SessionStatus, 
  MfaType, 
  DeviceType, 
  LoginMethod,
  SecurityEventType 
} from '../constants/auth.constants';

// JWT 载荷接口
export interface JwtPayload {
  sub: string;                    // 用户ID
  username: string;               // 用户名
  email: string;                  // 邮箱
  roles: string[];                // 角色列表
  permissions: string[];          // 权限列表
  sessionId: string;              // 会话ID
  deviceId?: string;              // 设备ID
  type: JwtTokenType;             // 令牌类型
  iat: number;                    // 签发时间
  exp: number;                    // 过期时间
  jti: string;                    // 令牌ID
}

// 刷新令牌载荷接口
export interface RefreshPayload {
  sub: string;                    // 用户ID
  sessionId: string;              // 会话ID
  deviceId?: string;              // 设备ID
  type: JwtTokenType;             // 令牌类型
  iat: number;                    // 签发时间
  exp: number;                    // 过期时间
  jti: string;                    // 令牌ID
}

// 认证结果接口
export interface AuthResult {
  user: {
    id: string;
    username: string;
    email: string;
    roles: string[];
    permissions: string[];
    profile: any;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    tokenType: string;
  };
  session: {
    sessionId: string;
    deviceId?: string;
    expiresAt: Date;
  };
  mfaRequired?: boolean;
  mfaToken?: string;
}

// 令牌结果接口
export interface TokenResult {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

// 登录请求接口
export interface LoginRequest {
  identifier: string;             // 用户名或邮箱
  password: string;               // 密码
  mfaCode?: string;               // MFA验证码
  rememberMe?: boolean;           // 记住我
  deviceInfo?: DeviceInfo;        // 设备信息
}

// 注册请求接口
export interface RegisterRequest {
  username: string;               // 用户名
  email: string;                  // 邮箱
  password: string;               // 密码
  confirmPassword: string;        // 确认密码
  profile: {
    firstName: string;            // 名
    lastName: string;             // 姓
    dateOfBirth?: Date;           // 出生日期
    gender?: 'male' | 'female' | 'other'; // 性别
    country?: string;             // 国家
    language?: string;            // 语言
  };
  acceptTerms: boolean;           // 接受条款
  deviceInfo?: DeviceInfo;        // 设备信息
}

// 设备信息接口
export interface DeviceInfo {
  type: DeviceType;               // 设备类型
  name: string;                   // 设备名称
  fingerprint: string;            // 设备指纹
  userAgent: string;              // 用户代理
  os: string;                     // 操作系统
  browser: string;                // 浏览器
  version: string;                // 版本
  screen: {
    width: number;                // 屏幕宽度
    height: number;               // 屏幕高度
    colorDepth: number;           // 颜色深度
  };
  timezone: string;               // 时区
  language: string;               // 语言
}

// 会话信息接口
export interface SessionInfo {
  sessionId: string;              // 会话ID
  userId: string;                 // 用户ID
  deviceId?: string;              // 设备ID
  deviceInfo: DeviceInfo;         // 设备信息
  ipAddress: string;              // IP地址
  location?: {
    country: string;              // 国家
    city: string;                 // 城市
    latitude: number;             // 纬度
    longitude: number;            // 经度
  };
  status: SessionStatus;          // 会话状态
  trusted: boolean;               // 是否可信
  createdAt: Date;                // 创建时间
  lastActivity: Date;             // 最后活动时间
  expiresAt: Date;                // 过期时间
}

// MFA 设置结果接口
export interface MfaSetupResult {
  secret: string;                 // MFA密钥
  qrCode: string;                 // 二维码
  backupCodes: string[];          // 备用码
  type: MfaType;                  // MFA类型
}

// MFA 验证结果接口
export interface MfaResult {
  verified: boolean;              // 是否验证成功
  remainingAttempts?: number;     // 剩余尝试次数
  nextAttemptAt?: Date;           // 下次尝试时间
}

// 密码重置请求接口
export interface PasswordResetRequest {
  email: string;                  // 邮箱
  captcha?: string;               // 验证码
}

// 密码重置确认接口
export interface PasswordResetConfirm {
  token: string;                  // 重置令牌
  newPassword: string;            // 新密码
  confirmPassword: string;        // 确认密码
}

// 密码修改请求接口
export interface PasswordChangeRequest {
  currentPassword: string;        // 当前密码
  newPassword: string;            // 新密码
  confirmPassword: string;        // 确认密码
}

// 安全事件接口
export interface SecurityEvent {
  id: string;                     // 事件ID
  type: SecurityEventType;        // 事件类型
  userId?: string;                // 用户ID
  sessionId?: string;             // 会话ID
  ipAddress: string;              // IP地址
  userAgent?: string;             // 用户代理
  location?: {
    country: string;              // 国家
    city: string;                 // 城市
  };
  details: any;                   // 事件详情
  severity: 'low' | 'medium' | 'high' | 'critical'; // 严重程度
  timestamp: Date;                // 时间戳
  resolved: boolean;              // 是否已解决
  resolvedAt?: Date;              // 解决时间
  resolvedBy?: string;            // 解决人
}

// 权限检查请求接口
export interface PermissionCheckRequest {
  userId: string;                 // 用户ID
  resource: string;               // 资源
  action: string;                 // 操作
  context?: any;                  // 上下文
}

// 权限检查结果接口
export interface PermissionCheckResult {
  allowed: boolean;               // 是否允许
  reason?: string;                // 原因
  requiredPermissions?: string[]; // 需要的权限
  missingPermissions?: string[];  // 缺少的权限
}

// 登录历史接口
export interface LoginHistory {
  id: string;                     // 记录ID
  userId: string;                 // 用户ID
  method: LoginMethod;            // 登录方式
  ipAddress: string;              // IP地址
  userAgent: string;              // 用户代理
  deviceInfo: DeviceInfo;         // 设备信息
  location?: {
    country: string;              // 国家
    city: string;                 // 城市
  };
  success: boolean;               // 是否成功
  failureReason?: string;         // 失败原因
  mfaUsed: boolean;               // 是否使用MFA
  timestamp: Date;                // 时间戳
}

// 账户锁定信息接口
export interface AccountLockInfo {
  locked: boolean;                // 是否锁定
  lockedAt?: Date;                // 锁定时间
  lockedUntil?: Date;             // 锁定到期时间
  reason?: string;                // 锁定原因
  attempts: number;               // 尝试次数
  maxAttempts: number;            // 最大尝试次数
  remainingTime?: number;         // 剩余时间（毫秒）
}

// 风险评估结果接口
export interface RiskAssessment {
  score: number;                  // 风险分数 (0-100)
  level: 'low' | 'medium' | 'high' | 'critical'; // 风险等级
  factors: {
    name: string;                 // 因素名称
    score: number;                // 因素分数
    weight: number;               // 权重
    description: string;          // 描述
  }[];
  recommendations: string[];      // 建议
  requiresAction: boolean;        // 是否需要采取行动
  actions: string[];              // 建议的行动
}

// API 密钥接口
export interface ApiKey {
  id: string;                     // 密钥ID
  name: string;                   // 密钥名称
  key: string;                    // 密钥值
  userId: string;                 // 用户ID
  permissions: string[];          // 权限列表
  ipWhitelist?: string[];         // IP白名单
  active: boolean;                // 是否激活
  expiresAt?: Date;               // 过期时间
  lastUsedAt?: Date;              // 最后使用时间
  createdAt: Date;                // 创建时间
  updatedAt: Date;                // 更新时间
}

// OAuth 提供商接口
export interface OAuthProvider {
  name: string;                   // 提供商名称
  clientId: string;               // 客户端ID
  clientSecret: string;           // 客户端密钥
  redirectUri: string;            // 重定向URI
  scope: string[];                // 权限范围
  enabled: boolean;               // 是否启用
}

// OAuth 用户信息接口
export interface OAuthUserInfo {
  id: string;                     // 外部用户ID
  email: string;                  // 邮箱
  name: string;                   // 姓名
  avatar?: string;                // 头像
  provider: string;               // 提供商
  verified: boolean;              // 是否验证
}

// 令牌黑名单接口
export interface TokenBlacklist {
  tokenId: string;                // 令牌ID
  userId: string;                 // 用户ID
  type: JwtTokenType;             // 令牌类型
  reason: string;                 // 撤销原因
  revokedAt: Date;                // 撤销时间
  revokedBy: string;              // 撤销人
  expiresAt: Date;                // 原过期时间
}

// 验证码接口
export interface VerificationCode {
  id: string;                     // 验证码ID
  code: string;                   // 验证码
  type: 'email' | 'sms' | 'totp'; // 类型
  target: string;                 // 目标（邮箱或手机）
  userId?: string;                // 用户ID
  attempts: number;               // 尝试次数
  maxAttempts: number;            // 最大尝试次数
  expiresAt: Date;                // 过期时间
  usedAt?: Date;                  // 使用时间
  createdAt: Date;                // 创建时间
}
