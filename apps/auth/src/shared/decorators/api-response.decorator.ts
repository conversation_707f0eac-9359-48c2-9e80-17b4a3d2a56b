/**
 * API响应装饰器
 * 提供标准化的API响应文档装饰器
 */

import { applyDecorators, Type } from '@nestjs/common';
import {
  ApiResponse,
  ApiOkResponse,
  ApiCreatedResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiUnprocessableEntityResponse,
  ApiTooManyRequestsResponse,
  ApiInternalServerErrorResponse,
  ApiServiceUnavailableResponse,
  getSchemaPath,
} from '@nestjs/swagger';

// 响应选项接口
export interface ApiResponseOptions {
  description?: string;
  type?: Type<any> | Function | [Function] | string;
  isArray?: boolean;
  status?: number;
  headers?: Record<string, any>;
  links?: Record<string, any>;
  example?: any;
  examples?: Record<string, any>;
}

// 分页响应选项接口
export interface ApiPaginatedResponseOptions extends ApiResponseOptions {
  itemType: Type<any> | Function;
}

// 错误响应选项接口
export interface ApiErrorResponseOptions {
  description?: string;
  errorCode?: string;
  errorMessage?: string;
  example?: any;
}

/**
 * 标准成功响应装饰器
 */
export const ApiSuccessResponse = (options: ApiResponseOptions = {}) => {
  const schema = options.type ? {
    allOf: [
      { $ref: getSchemaPath('SuccessResponse') },
      {
        properties: {
          data: options.isArray 
            ? { type: 'array', items: { $ref: getSchemaPath(options.type as any) } }
            : { $ref: getSchemaPath(options.type as any) }
        }
      }
    ]
  } : {
    $ref: getSchemaPath('SuccessResponse')
  };

  return ApiOkResponse({
    description: options.description || '操作成功',
    schema,
    headers: {
      'X-Request-ID': {
        description: '请求唯一标识',
        schema: { type: 'string' }
      },
      'X-Timestamp': {
        description: '响应时间戳',
        schema: { type: 'string' }
      },
      ...options.headers,
    },
  });
};

/**
 * 创建成功响应装饰器
 */
export const ApiCreateResponse = (options: ApiResponseOptions = {}) => {
  const schema = options.type ? {
    allOf: [
      { $ref: getSchemaPath('SuccessResponse') },
      {
        properties: {
          data: { $ref: getSchemaPath(options.type as any) }
        }
      }
    ]
  } : {
    $ref: getSchemaPath('SuccessResponse')
  };

  return ApiCreatedResponse({
    description: options.description || '创建成功',
    schema,
    headers: {
      'X-Request-ID': {
        description: '请求唯一标识',
        schema: { type: 'string' }
      },
      'Location': {
        description: '新创建资源的位置',
        schema: { type: 'string' }
      },
      ...options.headers,
    },
  });
};

/**
 * 分页响应装饰器
 */
export const ApiPaginatedResponse = (options: ApiPaginatedResponseOptions) => {
  return ApiOkResponse({
    description: options.description || '分页查询成功',
    schema: {
      allOf: [
        { $ref: getSchemaPath('SuccessResponse') },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(options.itemType as any) }
                },
                pagination: {
                  type: 'object',
                  properties: {
                    page: { type: 'number', example: 1 },
                    limit: { type: 'number', example: 10 },
                    total: { type: 'number', example: 100 },
                    totalPages: { type: 'number', example: 10 },
                    hasNext: { type: 'boolean', example: true },
                    hasPrev: { type: 'boolean', example: false },
                    nextPage: { type: 'number', example: 2 },
                    prevPage: { type: 'number', nullable: true, example: null },
                  }
                },
                meta: {
                  type: 'object',
                  properties: {
                    sortBy: { type: 'string', example: 'createdAt' },
                    sortOrder: { type: 'string', enum: ['asc', 'desc'], example: 'desc' },
                    filters: { type: 'object' },
                    search: { type: 'string' },
                  }
                }
              }
            }
          }
        }
      ]
    },
    headers: {
      'X-Total-Count': {
        description: '总记录数',
        schema: { type: 'number' }
      },
      'X-Page': {
        description: '当前页码',
        schema: { type: 'number' }
      },
      'X-Per-Page': {
        description: '每页记录数',
        schema: { type: 'number' }
      },
      'X-Total-Pages': {
        description: '总页数',
        schema: { type: 'number' }
      },
    },
  });
};

/**
 * 文件响应装饰器
 */
export const ApiFileResponse = (options: ApiResponseOptions = {}) => {
  return ApiOkResponse({
    description: options.description || '文件操作成功',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: '507f1f77bcf86cd799439011' },
        filename: { type: 'string', example: 'document.pdf' },
        originalName: { type: 'string', example: 'my-document.pdf' },
        mimetype: { type: 'string', example: 'application/pdf' },
        size: { type: 'number', example: 1024000 },
        url: { type: 'string', example: 'https://example.com/files/document.pdf' },
        uploadedAt: { type: 'string', format: 'date-time' },
      }
    },
    headers: {
      'Content-Type': {
        description: '文件MIME类型',
        schema: { type: 'string' }
      },
      'Content-Length': {
        description: '文件大小',
        schema: { type: 'number' }
      },
      'Content-Disposition': {
        description: '文件处理方式',
        schema: { type: 'string' }
      },
    },
  });
};

/**
 * 标准错误响应装饰器
 */
export const ApiErrorResponse = (status: number, options: ApiErrorResponseOptions = {}) => {
  const errorSchema = {
    type: 'object',
    properties: {
      success: { type: 'boolean', example: false },
      error: {
        type: 'object',
        properties: {
          code: { type: 'string', example: options.errorCode || 'ERROR_CODE' },
          message: { type: 'string', example: options.errorMessage || '操作失败' },
          details: { type: 'object' },
          timestamp: { type: 'string', format: 'date-time' },
          path: { type: 'string', example: '/api/v1/users' },
        }
      },
      timestamp: { type: 'string', format: 'date-time' },
      requestId: { type: 'string', example: 'req_1234567890' },
      version: { type: 'string', example: 'v1' },
    }
  };

  return ApiResponse({
    status,
    description: options.description || '操作失败',
    schema: errorSchema,
  });
};

/**
 * 验证错误响应装饰器
 */
export const ApiValidationErrorResponse = (options: ApiErrorResponseOptions = {}) => {
  return ApiBadRequestResponse({
    description: options.description || '数据验证失败',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        error: {
          type: 'object',
          properties: {
            code: { type: 'string', example: 'VALID_4001' },
            message: { type: 'string', example: '数据验证失败' },
            details: {
              type: 'object',
              properties: {
                errors: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      field: { type: 'string', example: 'email' },
                      value: { type: 'string', example: 'invalid-email' },
                      constraints: {
                        type: 'object',
                        example: { isEmail: 'email must be an email' }
                      }
                    }
                  }
                },
                totalErrors: { type: 'number', example: 1 },
                fields: { type: 'array', items: { type: 'string' }, example: ['email'] }
              }
            },
            timestamp: { type: 'string', format: 'date-time' },
            path: { type: 'string', example: '/api/v1/users' },
          }
        },
        timestamp: { type: 'string', format: 'date-time' },
        requestId: { type: 'string', example: 'req_1234567890' },
        version: { type: 'string', example: 'v1' },
      }
    },
  });
};

/**
 * 认证错误响应装饰器
 */
export const ApiAuthErrorResponse = (options: ApiErrorResponseOptions = {}) => {
  return ApiUnauthorizedResponse({
    description: options.description || '认证失败',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        error: {
          type: 'object',
          properties: {
            code: { type: 'string', example: 'AUTH_1003' },
            message: { type: 'string', example: '无效的令牌' },
            timestamp: { type: 'string', format: 'date-time' },
            path: { type: 'string', example: '/api/v1/users/me' },
          }
        },
        timestamp: { type: 'string', format: 'date-time' },
        requestId: { type: 'string', example: 'req_1234567890' },
        version: { type: 'string', example: 'v1' },
      }
    },
  });
};

/**
 * 权限错误响应装饰器
 */
export const ApiPermissionErrorResponse = (options: ApiErrorResponseOptions = {}) => {
  return ApiForbiddenResponse({
    description: options.description || '权限不足',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        error: {
          type: 'object',
          properties: {
            code: { type: 'string', example: 'AUTH_1006' },
            message: { type: 'string', example: '权限不足' },
            details: {
              type: 'object',
              properties: {
                requiredRoles: { type: 'array', items: { type: 'string' } },
                requiredPermissions: { type: 'array', items: { type: 'string' } },
                userRoles: { type: 'array', items: { type: 'string' } },
                userPermissions: { type: 'array', items: { type: 'string' } },
              }
            },
            timestamp: { type: 'string', format: 'date-time' },
            path: { type: 'string', example: '/api/v1/admin/users' },
          }
        },
        timestamp: { type: 'string', format: 'date-time' },
        requestId: { type: 'string', example: 'req_1234567890' },
        version: { type: 'string', example: 'v1' },
      }
    },
  });
};

/**
 * 资源不存在响应装饰器
 */
export const ApiNotFoundErrorResponse = (resource: string = '资源') => {
  return ApiNotFoundResponse({
    description: `${resource}不存在`,
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        error: {
          type: 'object',
          properties: {
            code: { type: 'string', example: 'USER_2001' },
            message: { type: 'string', example: `${resource}不存在` },
            timestamp: { type: 'string', format: 'date-time' },
            path: { type: 'string', example: '/api/v1/users/123' },
          }
        },
        timestamp: { type: 'string', format: 'date-time' },
        requestId: { type: 'string', example: 'req_1234567890' },
        version: { type: 'string', example: 'v1' },
      }
    },
  });
};

/**
 * 资源冲突响应装饰器
 */
export const ApiConflictErrorResponse = (resource: string = '资源') => {
  return ApiConflictResponse({
    description: `${resource}已存在`,
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        error: {
          type: 'object',
          properties: {
            code: { type: 'string', example: 'USER_2002' },
            message: { type: 'string', example: `${resource}已存在` },
            timestamp: { type: 'string', format: 'date-time' },
            path: { type: 'string', example: '/api/v1/users' },
          }
        },
        timestamp: { type: 'string', format: 'date-time' },
        requestId: { type: 'string', example: 'req_1234567890' },
        version: { type: 'string', example: 'v1' },
      }
    },
  });
};

/**
 * 限流错误响应装饰器
 */
export const ApiRateLimitErrorResponse = () => {
  return ApiTooManyRequestsResponse({
    description: '请求过于频繁',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        error: {
          type: 'object',
          properties: {
            code: { type: 'string', example: 'AUTH_1017' },
            message: { type: 'string', example: '请求过于频繁，请稍后重试' },
            timestamp: { type: 'string', format: 'date-time' },
            path: { type: 'string', example: '/api/v1/auth/login' },
          }
        },
        timestamp: { type: 'string', format: 'date-time' },
        requestId: { type: 'string', example: 'req_1234567890' },
        version: { type: 'string', example: 'v1' },
      }
    },
    headers: {
      'X-RateLimit-Limit': {
        description: '速率限制',
        schema: { type: 'number' }
      },
      'X-RateLimit-Remaining': {
        description: '剩余请求数',
        schema: { type: 'number' }
      },
      'X-RateLimit-Reset': {
        description: '重置时间',
        schema: { type: 'string' }
      },
    },
  });
};

/**
 * 标准API响应装饰器组合
 */
export const ApiStandardResponses = (options: {
  success?: ApiResponseOptions;
  notFound?: string;
  conflict?: string;
  includeAuth?: boolean;
  includeValidation?: boolean;
  includeRateLimit?: boolean;
} = {}) => {
  const decorators = [];

  // 成功响应
  if (options.success) {
    decorators.push(ApiSuccessResponse(options.success));
  }

  // 验证错误
  if (options.includeValidation) {
    decorators.push(ApiValidationErrorResponse());
  }

  // 认证错误
  if (options.includeAuth) {
    decorators.push(ApiAuthErrorResponse());
    decorators.push(ApiPermissionErrorResponse());
  }

  // 资源不存在
  if (options.notFound) {
    decorators.push(ApiNotFoundErrorResponse(options.notFound));
  }

  // 资源冲突
  if (options.conflict) {
    decorators.push(ApiConflictErrorResponse(options.conflict));
  }

  // 限流错误
  if (options.includeRateLimit) {
    decorators.push(ApiRateLimitErrorResponse());
  }

  // 服务器错误
  decorators.push(ApiInternalServerErrorResponse({
    description: '服务器内部错误'
  }));

  return applyDecorators(...decorators);
};
