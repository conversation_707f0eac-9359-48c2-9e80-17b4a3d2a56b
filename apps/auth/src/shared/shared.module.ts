import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// 统一配置导入
import { createConfiguration } from '../config';

// 装饰器导出 - 避免命名冲突
export * from '@auth/common/decorators/roles.decorator';
export * from '@auth/common/decorators/permissions.decorator';
export * from '@auth/common/decorators/current-user.decorator';
export * from '@auth/common/decorators/api-response.decorator';

// 从 auth.decorator 中选择性导出，避免与现有装饰器冲突
export {
  Public,
  Auth,
  RequireRoles,
  RequireAdmin,
  RequireSuperAdmin,
  RequireMfa,
  RequireEmailVerified,
  RequirePhoneVerified,
  AllowInactive,
  RateLimit,
  SecureEndpoint,
  ApiKeyAuth,
  RequireGameRoles,
  RequireTeamOwner,
  RequireCoach,
  RequireManagement,
  DevOnly,
  TestOnly,
  MaintenanceAllowed,
  InternalApi,
  Experimental,
  Deprecated,
  RequireStringPermissions,
} from '@auth/common/decorators/auth.decorator';

// 常量导出
export * from './constants/auth.constants';
export * from './constants/error.constants';
export * from './constants/role.constants';

// 接口导出
export * from '@auth/common/interfaces/auth.interface';
export * from '@auth/common/interfaces/user.interface';
export * from '@auth/common/interfaces/response.interface';

// DTO导出
export * from '@auth/common/dto/pagination.dto';
export * from '@auth/common/dto/response.dto';

// 类型导出 - 暂时注释，文件不存在
// export * from './types';

// 工具函数导出 - 暂时注释，文件不存在
// export * from './utils/validation.util';
// export * from './utils/crypto.util';

/**
 * 共享模块
 * 
 * 提供全局共享的资源和配置，包括：
 * - 统一配置管理
 * - 通用装饰器和工具
 * - 共享接口和类型定义
 * - 常量和枚举定义
 * - 通用DTO和响应格式
 * 
 * 职责范围：
 * - 全局配置和常量管理
 * - 通用工具和装饰器提供
 * - 接口和类型定义
 * - 跨模块的共享资源
 * 
 * 设计原则：
 * - 全局可用：使用@Global装饰器
 * - 最小依赖：只依赖基础模块
 * - 纯净导出：只导出接口、类型、工具
 * - 无业务逻辑：不包含具体业务实现
 */
@Global()
@Module({
  imports: [
    // 配置模块 - 使用统一配置
    ConfigModule.forRoot({
      isGlobal: true,
      load: [createConfiguration],
      envFilePath: ['.env', `.env.${process.env.NODE_ENV}`],
    }),
  ],
  providers: [
    // 共享模块不注册具体的业务服务
    // 只提供配置和工具函数
  ],
  exports: [
    // 导出配置模块
    ConfigModule,
  ],
})
export class SharedModule {
  constructor() {
    console.log('✅ 共享模块已初始化 - 提供全局配置和通用资源');
  }
}
