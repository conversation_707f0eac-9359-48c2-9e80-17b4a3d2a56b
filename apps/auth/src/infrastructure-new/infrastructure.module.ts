import { Module } from '@nestjs/common';

// 数据库模块
import { MongodbModule } from './database/mongodb/mongodb.module';
import { RedisModule } from './database/redis/redis.module';

// 微服务模块
import { MicroserviceModule } from './microservices/microservice.module';

// 健康检查模块
import { HealthModule } from './health/health.module';

/**
 * 基础设施模块
 * 
 * 整合了所有基础设施相关的配置和服务，包括：
 * - 数据库连接和配置（MongoDB、Redis）
 * - 微服务通信配置
 * - 健康检查和监控
 * - 外部服务集成
 * 
 * 职责范围：
 * - 数据库连接管理
 * - 缓存服务配置
 * - 微服务通信设置
 * - 系统健康监控
 * - 基础设施服务注册
 * 
 * 设计原则：
 * - 基础优先：最先初始化的模块
 * - 无业务逻辑：只提供基础设施服务
 * - 高可用：确保基础服务的稳定性
 * - 可监控：提供完整的健康检查
 */
@Module({
  imports: [
    // 数据库模块
    MongodbModule,
    RedisModule,
    
    // 微服务通信模块
    MicroserviceModule,
    
    // 健康检查模块
    HealthModule,
  ],
  exports: [
    // 导出所有基础设施模块
    MongodbModule,
    RedisModule,
    MicroserviceModule,
    HealthModule,
  ],
})
export class InfrastructureModule {
  constructor() {
    console.log('✅ 基础设施模块已初始化 - 提供数据库、缓存、微服务等基础服务');
  }
}
