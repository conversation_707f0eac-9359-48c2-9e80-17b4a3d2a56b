import { Module } from '@nestjs/common';
import { MicroserviceKitModule } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@common/microservice-kit';

/**
 * 微服务模块
 * 
 * 提供微服务通信配置
 */
@Module({
  imports: [
    MicroserviceKitModule.forServer(MICROSERVICE_NAMES.AUTH_SERVICE),
  ],
  exports: [MicroserviceKitModule],
})
export class MicroserviceModule {
  constructor() {
    console.log('✅ 微服务模块已初始化');
  }
}
