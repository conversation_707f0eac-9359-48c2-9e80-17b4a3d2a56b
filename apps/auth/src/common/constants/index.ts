/**
 * Auth服务常量统一导出
 * 
 * 提供认证相关的所有常量定义，便于其他模块导入使用
 */

// 认证相关常量
export * from './auth.constants';

// 错误相关常量
export * from './error.constants';

// 角色相关常量
export * from './role.constants';

// 便捷的重新导出（常用常量）
export {
  // 从auth.constants.ts导出的常用常量
  JWT_STRATEGY,
  API_KEY_STRATEGY,
  REFRESH_TOKEN_STRATEGY,
  
  // 从role.constants.ts导出的常用常量
  ROLES,
  PERMISSIONS,
  
  // 从error.constants.ts导出的常用常量
  AUTH_ERRORS,
} from './auth.constants';
