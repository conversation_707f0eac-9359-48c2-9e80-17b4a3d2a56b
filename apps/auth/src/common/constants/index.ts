/**
 * Auth服务常量统一导出
 * 
 * 提供认证相关的所有常量定义，便于其他模块导入使用
 */

// 认证相关常量
export * from './auth.constants';

// 错误相关常量
export * from './error.constants';

// 角色相关常量
export * from './role.constants';

// 便捷的重新导出（常用常量）
export {
  // 从auth.constants.ts导出的常用常量
  JWT_CONSTANTS,
  AUTH_STRATEGIES,
  USER_STATUS,
  SESSION_STATUS,
  RATE_LIMIT,
  PASSWORD_POLICY,
  ENCRYPTION,

  // 类型导出
  AuthStrategy,
  UserStatus,
  Environment,
  LogLevel,
} from './auth.constants';
