/**
 * Auth服务通用模块统一导出
 * 
 * 提供所有通用类、接口、类型、常量等的统一入口
 * 便于其他模块快速导入所需内容
 */

// 常量定义
export * from './constants';

// 装饰器
export * from './decorators';

// DTO定义
export * from './dto';

// 接口定义
export * from './interfaces';

// 类型定义
export * from './types';

// 工具函数（直接导出，不通过桶文件）
export * from './utils/logger.util';

// 最常用的快捷导出
export {
  // 常量
  ROLES,
  PERMISSIONS,
  AUTH_ERRORS,
  
  // 装饰器
  Auth,
  Public,
  CurrentUser,
  Roles,
  Permissions,
  
  // DTO
  PaginationDto,
  ResponseDto,
  
  // 接口
  AuthPayload,
  JwtPayload,
  UserProfile,
  
  // 类型
  UserStatus,
  UserRole,
  Permission,
  TokenType,
} from './constants';
