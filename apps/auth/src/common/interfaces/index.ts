/**
 * Auth服务接口统一导出
 * 
 * 提供认证、用户、响应等接口定义，便于类型检查和开发
 */

// 认证接口
export * from './auth.interface';

// 响应接口
export * from './response.interface';

// 用户接口
export * from './user.interface';

// 便捷的重新导出（最常用的接口）
export {
  // 认证相关接口
  AuthPayload,
  JwtPayload,
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  
  // 用户相关接口
  UserProfile,
  UserSession,
  UserPermissions,
  
  // 响应相关接口
  ApiResponse,
  PaginatedResponse,
  ErrorResponse,
} from './auth.interface';
