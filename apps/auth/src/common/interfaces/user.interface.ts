/**
 * 用户相关接口定义
 */

import { UserStatus } from '../constants/auth.constants';
import { AllRoles, AllPermissions } from '../constants/role.constants';

// 用户基础信息接口
export interface UserProfile {
  firstName: string;              // 名
  lastName: string;               // 姓
  avatar?: string;                // 头像URL
  dateOfBirth?: Date;             // 出生日期
  gender?: 'male' | 'female' | 'other'; // 性别
  country?: string;               // 国家
  city?: string;                  // 城市
  timezone?: string;              // 时区
  language: string;               // 首选语言
  bio?: string;                   // 个人简介
  website?: string;               // 个人网站
  socialLinks?: {
    twitter?: string;             // Twitter
    facebook?: string;            // Facebook
    instagram?: string;           // Instagram
    linkedin?: string;            // LinkedIn
  };
}

// 游戏资料接口
export interface GameProfile {
  level: number;                  // 用户等级
  experience: number;             // 经验值
  coins: number;                  // 游戏币
  premiumUntil?: Date;            // 会员到期时间
  achievements: string[];         // 成就列表
  statistics: {
    gamesPlayed: number;          // 游戏场次
    wins: number;                 // 胜利次数
    losses: number;               // 失败次数
    draws: number;                // 平局次数
    winRate: number;              // 胜率
    totalPlayTime: number;        // 总游戏时间（分钟）
    favoritePosition?: string;    // 喜欢的位置
    favoriteTeam?: string;        // 喜欢的球队
  };
  preferences: {
    difficulty: 'easy' | 'medium' | 'hard' | 'expert'; // 难度偏好
    gameSpeed: 'slow' | 'normal' | 'fast'; // 游戏速度
    notifications: {
      matchResults: boolean;      // 比赛结果通知
      transfers: boolean;         // 转会通知
      injuries: boolean;          // 伤病通知
      contracts: boolean;         // 合同通知
    };
    privacy: {
      showProfile: boolean;       // 显示资料
      showStatistics: boolean;    // 显示统计
      allowMessages: boolean;     // 允许消息
    };
  };
}

// 安全设置接口
export interface SecuritySettings {
  mfaEnabled: boolean;            // 是否启用MFA
  mfaSecret?: string;             // MFA密钥
  mfaBackupCodes?: string[];      // MFA备用码
  trustedDevices: string[];       // 可信设备列表
  lastPasswordChange: Date;       // 最后密码修改时间
  passwordHistory: string[];      // 历史密码哈希
  loginAttempts: number;          // 登录尝试次数
  lockedUntil?: Date;             // 锁定到期时间
  securityQuestions?: {
    question: string;             // 安全问题
    answerHash: string;           // 答案哈希
  }[];
  loginNotifications: boolean;    // 登录通知
  suspiciousActivityAlerts: boolean; // 可疑活动警报
  sessionTimeout: number;         // 会话超时时间（分钟）
}

// 通知设置接口
export interface NotificationSettings {
  email: {
    enabled: boolean;             // 是否启用邮件通知
    security: boolean;            // 安全通知
    marketing: boolean;           // 营销通知
    updates: boolean;             // 更新通知
    newsletter: boolean;          // 新闻通讯
  };
  sms: {
    enabled: boolean;             // 是否启用短信通知
    security: boolean;            // 安全通知
    important: boolean;           // 重要通知
  };
  push: {
    enabled: boolean;             // 是否启用推送通知
    security: boolean;            // 安全通知
    game: boolean;                // 游戏通知
    social: boolean;              // 社交通知
  };
  inApp: {
    enabled: boolean;             // 是否启用应用内通知
    sound: boolean;               // 声音
    vibration: boolean;           // 震动
  };
}

// 隐私设置接口
export interface PrivacySettings {
  profileVisibility: 'public' | 'friends' | 'private'; // 资料可见性
  showOnlineStatus: boolean;      // 显示在线状态
  showLastSeen: boolean;          // 显示最后在线时间
  allowFriendRequests: boolean;   // 允许好友请求
  allowMessages: boolean;         // 允许消息
  showGameHistory: boolean;       // 显示游戏历史
  showAchievements: boolean;      // 显示成就
  dataSharing: {
    analytics: boolean;           // 分析数据
    marketing: boolean;           // 营销数据
    research: boolean;            // 研究数据
  };
}

// 用户偏好设置接口
export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'; // 主题
  language: string;               // 语言
  timezone: string;               // 时区
  dateFormat: string;             // 日期格式
  timeFormat: '12h' | '24h';      // 时间格式
  currency: string;               // 货币
  notifications: NotificationSettings; // 通知设置
  privacy: PrivacySettings;       // 隐私设置
  accessibility: {
    highContrast: boolean;        // 高对比度
    largeText: boolean;           // 大字体
    screenReader: boolean;        // 屏幕阅读器
    keyboardNavigation: boolean;  // 键盘导航
  };
}

// 用户统计接口
export interface UserStatistics {
  loginCount: number;             // 登录次数
  lastLoginAt?: Date;             // 最后登录时间
  lastActiveAt?: Date;            // 最后活跃时间
  totalPlayTime: number;          // 总游戏时间（分钟）
  accountAge: number;             // 账户年龄（天）
  friendsCount: number;           // 好友数量
  achievementsCount: number;      // 成就数量
  gamesPlayed: number;            // 游戏场次
  averageSessionTime: number;     // 平均会话时间（分钟）
  deviceCount: number;            // 设备数量
  securityScore: number;          // 安全分数
}

// 用户完整接口
export interface User {
  id: string;                     // 用户唯一标识
  username: string;               // 用户名
  email: string;                  // 邮箱地址
  phone?: string;                 // 手机号码
  passwordHash: string;           // 密码哈希
  salt: string;                   // 密码盐值
  
  // 个人信息
  profile: UserProfile;           // 个人资料
  
  // 游戏相关
  gameProfile: GameProfile;       // 游戏资料
  
  // 安全设置
  security: SecuritySettings;     // 安全设置
  
  // 用户偏好
  preferences: UserPreferences;   // 用户偏好
  
  // 状态信息
  status: UserStatus;             // 用户状态
  emailVerified: boolean;         // 邮箱是否验证
  phoneVerified: boolean;         // 手机是否验证
  roles: AllRoles[];              // 用户角色
  permissions: AllPermissions[];  // 直接权限
  
  // 统计信息
  statistics: UserStatistics;     // 用户统计
  
  // 审计字段
  createdAt: Date;                // 创建时间
  updatedAt: Date;                // 更新时间
  createdBy?: string;             // 创建者
  updatedBy?: string;             // 更新者
  
  // 软删除
  deletedAt?: Date;               // 删除时间
  deletedBy?: string;             // 删除者
}

// 用户创建请求接口
export interface CreateUserRequest {
  username: string;               // 用户名
  email: string;                  // 邮箱
  password: string;               // 密码
  phone?: string;                 // 手机号
  profile: Partial<UserProfile>;  // 个人资料
  roles?: AllRoles[];             // 角色
  permissions?: AllPermissions[]; // 权限
  sendWelcomeEmail?: boolean;     // 发送欢迎邮件
}

// 用户更新请求接口
export interface UpdateUserRequest {
  username?: string;              // 用户名
  email?: string;                 // 邮箱
  phone?: string;                 // 手机号
  profile?: Partial<UserProfile>; // 个人资料
  gameProfile?: Partial<GameProfile>; // 游戏资料
  preferences?: Partial<UserPreferences>; // 用户偏好
  status?: UserStatus;            // 用户状态
  roles?: AllRoles[];             // 角色
  permissions?: AllPermissions[]; // 权限
}

// 用户查询选项接口
export interface UserQueryOptions {
  search?: string;                // 搜索关键词
  status?: UserStatus[];          // 状态过滤
  roles?: AllRoles[];             // 角色过滤
  emailVerified?: boolean;        // 邮箱验证状态
  phoneVerified?: boolean;        // 手机验证状态
  createdAfter?: Date;            // 创建时间之后
  createdBefore?: Date;           // 创建时间之前
  lastLoginAfter?: Date;          // 最后登录之后
  lastLoginBefore?: Date;         // 最后登录之前
  sortBy?: 'createdAt' | 'updatedAt' | 'lastLoginAt' | 'username' | 'email'; // 排序字段
  sortOrder?: 'asc' | 'desc';     // 排序方向
  page?: number;                  // 页码
  limit?: number;                 // 每页数量
  includeDeleted?: boolean;       // 包含已删除
}

// 用户列表响应接口
export interface UserListResponse {
  users: User[];                  // 用户列表
  total: number;                  // 总数
  page: number;                   // 当前页
  limit: number;                  // 每页数量
  totalPages: number;             // 总页数
  hasNext: boolean;               // 是否有下一页
  hasPrev: boolean;               // 是否有上一页
}

// 用户简要信息接口
export interface UserSummary {
  id: string;                     // 用户ID
  username: string;               // 用户名
  email: string;                  // 邮箱
  avatar?: string;                // 头像
  status: UserStatus;             // 状态
  roles: AllRoles[];              // 角色
  lastLoginAt?: Date;             // 最后登录时间
  createdAt: Date;                // 创建时间
}

// 用户验证接口
export interface UserVerification {
  userId: string;                 // 用户ID
  type: 'email' | 'phone';        // 验证类型
  token: string;                  // 验证令牌
  expiresAt: Date;                // 过期时间
  attempts: number;               // 尝试次数
  maxAttempts: number;            // 最大尝试次数
  verified: boolean;              // 是否已验证
  verifiedAt?: Date;              // 验证时间
}

// 用户活动接口
export interface UserActivity {
  id: string;                     // 活动ID
  userId: string;                 // 用户ID
  type: string;                   // 活动类型
  description: string;            // 活动描述
  metadata: any;                  // 元数据
  ipAddress: string;              // IP地址
  userAgent: string;              // 用户代理
  timestamp: Date;                // 时间戳
}

// 用户关系接口
export interface UserRelationship {
  id: string;                     // 关系ID
  fromUserId: string;             // 发起用户ID
  toUserId: string;               // 目标用户ID
  type: 'friend' | 'blocked' | 'following' | 'follower'; // 关系类型
  status: 'pending' | 'accepted' | 'rejected'; // 状态
  createdAt: Date;                // 创建时间
  updatedAt: Date;                // 更新时间
}
