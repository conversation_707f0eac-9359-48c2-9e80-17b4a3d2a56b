import { WinstonModuleOptions } from 'nest-winston';
import * as winston from 'winston';
import * as path from 'path';
import { MICROSERVICE_NAMES } from '@shared/constants';

/**
 * 创建Winston日志配置
 */
export function createWinstonLogger(): WinstonModuleOptions {
  const environment = process.env.NODE_ENV || 'development';
  const logLevel = process.env.LOG_LEVEL || 'info';
  const logFormat = process.env.LOG_FORMAT || 'json';

  // 自定义格式化器
  const customFormat = winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss.SSS',
    }),
    winston.format.errors({ stack: true }),
    winston.format.metadata({
      fillExcept: ['message', 'level', 'timestamp', 'label'],
    }),
  );

  // 开发环境格式
  const developmentFormat = winston.format.combine(
    customFormat,
    winston.format.colorize({ all: true }),
    winston.format.printf(({ timestamp, level, message, metadata, stack }) => {
      let log = `${timestamp} [${level}] ${message}`;
      
      if (Object.keys(metadata).length > 0) {
        log += `\n${JSON.stringify(metadata, null, 2)}`;
      }
      
      if (stack) {
        log += `\n${stack}`;
      }
      
      return log;
    }),
  );

  // 生产环境格式
  const productionFormat = winston.format.combine(
    customFormat,
    winston.format.json(),
  );

  // 选择格式
  const format = environment === 'development' ? developmentFormat : productionFormat;

  // 传输器配置
  const transports: winston.transport[] = [];

  // 控制台传输器
  if (process.env.LOG_CONSOLE_ENABLED !== 'false') {
    transports.push(
      new winston.transports.Console({
        level: environment === 'development' ? 'debug' : logLevel,
        format: environment === 'development' ? developmentFormat : productionFormat,
        handleExceptions: true,
        handleRejections: true,
      }),
    );
  }

  // 文件传输器
  if (process.env.LOG_FILE_ENABLED !== 'false') {
    const logDir = process.env.LOG_DIR || 'logs';
    
    // 确保日志目录存在
    const fs = require('fs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // 应用日志
    transports.push(
      new winston.transports.File({
        filename: path.join(logDir, 'app.log'),
        level: logLevel,
        format: productionFormat,
        maxsize: parseInt(process.env.LOG_FILE_MAX_SIZE || '20971520', 10), // 20MB
        maxFiles: parseInt(process.env.LOG_FILE_MAX_FILES || '14', 10), // 14个文件
        tailable: true,
        zippedArchive: true,
      }),
    );

    // 错误日志
    transports.push(
      new winston.transports.File({
        filename: path.join(logDir, 'error.log'),
        level: 'error',
        format: productionFormat,
        maxsize: parseInt(process.env.LOG_ERROR_FILE_MAX_SIZE || '20971520', 10), // 20MB
        maxFiles: parseInt(process.env.LOG_ERROR_FILE_MAX_FILES || '30', 10), // 30个文件
        tailable: true,
        zippedArchive: true,
      }),
    );

    // 访问日志
    transports.push(
      new winston.transports.File({
        filename: path.join(logDir, 'access.log'),
        level: 'http',
        format: productionFormat,
        maxsize: parseInt(process.env.LOG_ACCESS_FILE_MAX_SIZE || '50971520', 10), // 50MB
        maxFiles: parseInt(process.env.LOG_ACCESS_FILE_MAX_FILES || '7', 10), // 7个文件
        tailable: true,
        zippedArchive: true,
      }),
    );
  }

  // 远程日志传输器 (可选)
  if (process.env.LOG_REMOTE_ENABLED === 'true') {
    // 这里可以添加远程日志服务，如 Elasticsearch、Logstash 等
    // 示例：Elasticsearch传输器
    if (process.env.LOG_ELASTICSEARCH_ENABLED === 'true') {
      const ElasticsearchTransport = require('winston-elasticsearch'); // 暂时禁用
      
      transports.push(
        new ElasticsearchTransport({
          level: logLevel,
          clientOpts: {
            node: process.env.ELASTICSEARCH_URL || 'http://localhost:9200',
            auth: {
              username: process.env.ELASTICSEARCH_USERNAME,
              password: process.env.ELASTICSEARCH_PASSWORD,
            },
          },
          index: process.env.ELASTICSEARCH_INDEX || 'auth-service-logs',
          indexTemplate: {
            name: 'auth-service-logs-template',
            pattern: 'auth-service-logs-*',
            settings: {
              number_of_shards: 1,
              number_of_replicas: 0,
            },
            mappings: {
              properties: {
                '@timestamp': { type: 'date' },
                level: { type: 'keyword' },
                message: { type: 'text' },
                service: { type: 'keyword' },
                environment: { type: 'keyword' },
              },
            },
          },
          transformer: (logData) => {
            const transformed = {
              '@timestamp': logData.timestamp,
              level: logData.level,
              message: logData.message,
              service: MICROSERVICE_NAMES.AUTH_SERVICE,
              environment: process.env.NODE_ENV,
              ...logData.metadata,
            };
            
            // 移除敏感信息
            if (transformed.password) delete transformed.password;
            if (transformed.token) delete transformed.token;
            if (transformed.authorization) delete transformed.authorization;
            
            return transformed;
          },
        }),
      );
    }
  }

  return {
    level: logLevel,
    format,
    defaultMeta: {
      service: MICROSERVICE_NAMES.AUTH_SERVICE,
      environment,
      version: process.env.APP_VERSION || '1.0.0',
      hostname: process.env.HOSTNAME || require('os').hostname(),
      pid: process.pid,
    },
    transports,
    exitOnError: false,
    silent: process.env.LOG_SILENT === 'true',
    
    // 异常处理
    exceptionHandlers: [
      new winston.transports.File({
        filename: path.join(process.env.LOG_DIR || 'logs', 'exceptions.log'),
        format: productionFormat,
        maxsize: parseInt(process.env.LOG_EXCEPTION_FILE_MAX_SIZE || '20971520', 10),
        maxFiles: parseInt(process.env.LOG_EXCEPTION_FILE_MAX_FILES || '5', 10),
        tailable: true,
        zippedArchive: true,
      }),
    ],
    
    // 拒绝处理
    rejectionHandlers: [
      new winston.transports.File({
        filename: path.join(process.env.LOG_DIR || 'logs', 'rejections.log'),
        format: productionFormat,
        maxsize: parseInt(process.env.LOG_REJECTION_FILE_MAX_SIZE || '20971520', 10),
        maxFiles: parseInt(process.env.LOG_REJECTION_FILE_MAX_FILES || '5', 10),
        tailable: true,
        zippedArchive: true,
      }),
    ],
  };
}

/**
 * 创建请求日志中间件
 */
export function createRequestLogger() {
  return winston.createLogger({
    level: 'http',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json(),
    ),
    transports: [
      new winston.transports.File({
        filename: path.join(process.env.LOG_DIR || 'logs', 'requests.log'),
        maxsize: parseInt(process.env.LOG_REQUEST_FILE_MAX_SIZE || '50971520', 10),
        maxFiles: parseInt(process.env.LOG_REQUEST_FILE_MAX_FILES || '7', 10),
        tailable: true,
        zippedArchive: true,
      }),
    ],
  });
}

/**
 * 安全日志记录器
 */
export function createSecurityLogger() {
  return winston.createLogger({
    level: 'info',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json(),
      winston.format.metadata(),
    ),
    defaultMeta: {
      type: 'security',
      service: MICROSERVICE_NAMES.AUTH_SERVICE,
    },
    transports: [
      new winston.transports.File({
        filename: path.join(process.env.LOG_DIR || 'logs', 'security.log'),
        maxsize: parseInt(process.env.LOG_SECURITY_FILE_MAX_SIZE || '50971520', 10),
        maxFiles: parseInt(process.env.LOG_SECURITY_FILE_MAX_FILES || '30', 10),
        tailable: true,
        zippedArchive: true,
      }),
    ],
  });
}

/**
 * 审计日志记录器
 */
export function createAuditLogger() {
  return winston.createLogger({
    level: 'info',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json(),
      winston.format.metadata(),
    ),
    defaultMeta: {
      type: 'audit',
      service: MICROSERVICE_NAMES.AUTH_SERVICE,
    },
    transports: [
      new winston.transports.File({
        filename: path.join(process.env.LOG_DIR || 'logs', 'audit.log'),
        maxsize: parseInt(process.env.LOG_AUDIT_FILE_MAX_SIZE || '100971520', 10), // 100MB
        maxFiles: parseInt(process.env.LOG_AUDIT_FILE_MAX_FILES || '365', 10), // 365个文件
        tailable: true,
        zippedArchive: true,
      }),
    ],
  });
}
