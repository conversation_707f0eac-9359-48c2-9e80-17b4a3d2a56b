/**
 * 认证装饰器
 * 提供各种认证相关的装饰器
 */

import { applyDecorators, SetMetadata, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiUnauthorizedResponse, ApiForbiddenResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../infrastructure/guards/jwt-auth.guard';
import { RolesGuard } from '../../infrastructure/guards/roles.guard';
import { PermissionsGuard } from '../../infrastructure/guards/permissions.guard';
import { ThrottlerBehindProxyGuard } from '../../infrastructure/guards/throttler-behind-proxy.guard';
import { AllRoles, AllPermissions } from '../constants/role.constants';

// 使用现有的元数据键，避免冲突
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';
import { ROLES_KEY } from '../decorators/roles.decorator';
import { PERMISSIONS_KEY } from '../decorators/permissions.decorator';

// 新增的元数据键
export const REQUIRE_MFA_KEY = 'requireMfa';
export const ALLOW_INACTIVE_KEY = 'allowInactive';
export const REQUIRE_EMAIL_VERIFIED_KEY = 'requireEmailVerified';
export const REQUIRE_PHONE_VERIFIED_KEY = 'requirePhoneVerified';

/**
 * 公开端点装饰器
 * 标记端点为公开访问，不需要认证
 * 注意：使用现有的 Public 装饰器，避免重复定义
 */
// export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);
// 使用现有的 Public 装饰器
export { Public } from '../decorators/public.decorator';

/**
 * 认证装饰器
 * 要求用户必须登录
 */
export const Auth = () => applyDecorators(
  UseGuards(JwtAuthGuard),
  ApiBearerAuth('JWT-auth'),
  ApiUnauthorizedResponse({
    description: '未授权访问',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        error: {
          type: 'object',
          properties: {
            code: { type: 'string', example: 'AUTH_1003' },
            message: { type: 'string', example: '无效的令牌' },
            timestamp: { type: 'string', example: '2023-12-01T10:00:00.000Z' },
          },
        },
      },
    },
  }),
);

/**
 * 角色认证装饰器
 * 要求用户具有指定角色
 */
export const RequireRoles = (...roles: AllRoles[]) => applyDecorators(
  SetMetadata(ROLES_KEY, roles),
  UseGuards(JwtAuthGuard, RolesGuard),
  ApiBearerAuth('JWT-auth'),
  ApiUnauthorizedResponse({ description: '未授权访问' }),
  ApiForbiddenResponse({
    description: '权限不足',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        error: {
          type: 'object',
          properties: {
            code: { type: 'string', example: 'AUTH_1006' },
            message: { type: 'string', example: '权限不足' },
            requiredRoles: { type: 'array', items: { type: 'string' }, example: roles },
            timestamp: { type: 'string', example: '2023-12-01T10:00:00.000Z' },
          },
        },
      },
    },
  }),
);

/**
 * 权限认证装饰器
 * 重新导出现有的权限装饰器，避免冲突
 */
export { RequirePermissions } from '../decorators/permissions.decorator';

/**
 * 简化的权限装饰器
 * 支持字符串格式的权限定义
 */
export const RequireStringPermissions = (...permissions: string[]) => {
  // 将字符串权限转换为现有的 PermissionRequirement 格式
  const permissionRequirements = permissions.map(permission => {
    const [resource, action] = permission.split(':');
    return { resource: resource || permission, action: action || 'access' };
  });

  return applyDecorators(
    SetMetadata(PERMISSIONS_KEY, permissionRequirements),
    UseGuards(JwtAuthGuard, PermissionsGuard),
    ApiBearerAuth('JWT-auth'),
    ApiUnauthorizedResponse({ description: '未授权访问' }),
    ApiForbiddenResponse({
      description: '权限不足',
      schema: {
        type: 'object',
        properties: {
          success: { type: 'boolean', example: false },
          error: {
            type: 'object',
            properties: {
              code: { type: 'string', example: 'PERM_3007' },
              message: { type: 'string', example: '权限被拒绝' },
              requiredPermissions: { type: 'array', items: { type: 'string' }, example: permissions },
              timestamp: { type: 'string', example: '2023-12-01T10:00:00.000Z' },
            },
          },
        },
      },
    }),
  );
};

/**
 * 管理员认证装饰器
 * 要求用户具有管理员权限
 */
export const RequireAdmin = () => RequireRoles('super_admin', 'system_admin');

/**
 * 超级管理员认证装饰器
 * 要求用户具有超级管理员权限
 */
export const RequireSuperAdmin = () => RequireRoles('super_admin');

/**
 * MFA认证装饰器
 * 要求用户启用并通过多因子认证
 */
export const RequireMfa = () => applyDecorators(
  SetMetadata(REQUIRE_MFA_KEY, true),
  UseGuards(JwtAuthGuard),
  ApiBearerAuth('JWT-auth'),
  ApiUnauthorizedResponse({
    description: '需要多因子认证',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        error: {
          type: 'object',
          properties: {
            code: { type: 'string', example: 'AUTH_1011' },
            message: { type: 'string', example: '需要多因子认证' },
            timestamp: { type: 'string', example: '2023-12-01T10:00:00.000Z' },
          },
        },
      },
    },
  }),
);

/**
 * 邮箱验证装饰器
 * 要求用户邮箱已验证
 */
export const RequireEmailVerified = () => applyDecorators(
  SetMetadata(REQUIRE_EMAIL_VERIFIED_KEY, true),
  UseGuards(JwtAuthGuard),
  ApiBearerAuth('JWT-auth'),
  ApiUnauthorizedResponse({
    description: '邮箱未验证',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        error: {
          type: 'object',
          properties: {
            code: { type: 'string', example: 'USER_2010' },
            message: { type: 'string', example: '邮箱地址未验证' },
            timestamp: { type: 'string', example: '2023-12-01T10:00:00.000Z' },
          },
        },
      },
    },
  }),
);

/**
 * 手机验证装饰器
 * 要求用户手机号已验证
 */
export const RequirePhoneVerified = () => applyDecorators(
  SetMetadata(REQUIRE_PHONE_VERIFIED_KEY, true),
  UseGuards(JwtAuthGuard),
  ApiBearerAuth('JWT-auth'),
  ApiUnauthorizedResponse({
    description: '手机号未验证',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        error: {
          type: 'object',
          properties: {
            code: { type: 'string', example: 'USER_2011' },
            message: { type: 'string', example: '手机号码未验证' },
            timestamp: { type: 'string', example: '2023-12-01T10:00:00.000Z' },
          },
        },
      },
    },
  }),
);

/**
 * 允许非活跃用户装饰器
 * 允许状态为非活跃的用户访问
 */
export const AllowInactive = () => SetMetadata(ALLOW_INACTIVE_KEY, true);

/**
 * 限流装饰器
 * 为端点设置速率限制
 */
export const RateLimit = (limit: number, ttl: number = 60) => applyDecorators(
  UseGuards(ThrottlerBehindProxyGuard),
  SetMetadata('throttler', { limit, ttl }),
);

/**
 * 组合认证装饰器
 * 组合多种认证要求
 */
export const SecureEndpoint = (options: {
  roles?: AllRoles[];
  permissions?: string[]; // 改为字符串数组，更灵活
  requireMfa?: boolean;
  requireEmailVerified?: boolean;
  requirePhoneVerified?: boolean;
  rateLimit?: { limit: number; ttl?: number };
}) => {
  const decorators = [UseGuards(JwtAuthGuard), ApiBearerAuth('JWT-auth')];

  // 添加角色守卫
  if (options.roles && options.roles.length > 0) {
    decorators.push(SetMetadata(ROLES_KEY, options.roles));
    decorators.push(UseGuards(RolesGuard));
  }

  // 添加权限守卫
  if (options.permissions && options.permissions.length > 0) {
    // 将字符串权限转换为 PermissionRequirement 格式
    const permissionRequirements = options.permissions.map(permission => {
      const [resource, action] = permission.split(':');
      return { resource: resource || permission, action: action || 'access' };
    });
    decorators.push(SetMetadata(PERMISSIONS_KEY, permissionRequirements));
    decorators.push(UseGuards(PermissionsGuard));
  }

  // 添加MFA要求
  if (options.requireMfa) {
    decorators.push(SetMetadata(REQUIRE_MFA_KEY, true));
  }

  // 添加邮箱验证要求
  if (options.requireEmailVerified) {
    decorators.push(SetMetadata(REQUIRE_EMAIL_VERIFIED_KEY, true));
  }

  // 添加手机验证要求
  if (options.requirePhoneVerified) {
    decorators.push(SetMetadata(REQUIRE_PHONE_VERIFIED_KEY, true));
  }

  // 添加限流
  if (options.rateLimit) {
    decorators.push(UseGuards(ThrottlerBehindProxyGuard));
    decorators.push(SetMetadata('throttler', {
      limit: options.rateLimit.limit,
      ttl: options.rateLimit.ttl || 60,
    }));
  }

  // 添加API文档
  decorators.push(
    ApiUnauthorizedResponse({ description: '未授权访问' }),
    ApiForbiddenResponse({ description: '权限不足' }),
  );

  return applyDecorators(...decorators);
};

/**
 * API密钥认证装饰器
 * 使用API密钥进行认证
 */
export const ApiKeyAuth = () => applyDecorators(
  // 这里需要实现API密钥守卫
  // UseGuards(ApiKeyGuard),
  ApiBearerAuth('API-Key'),
  ApiUnauthorizedResponse({
    description: 'API密钥无效',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        error: {
          type: 'object',
          properties: {
            code: { type: 'string', example: 'AUTH_1004' },
            message: { type: 'string', example: '缺少认证令牌' },
            timestamp: { type: 'string', example: '2023-12-01T10:00:00.000Z' },
          },
        },
      },
    },
  }),
);

/**
 * 游戏角色认证装饰器
 * 要求用户具有特定的游戏角色
 */
export const RequireGameRoles = (...roles: string[]) => RequireRoles(...(roles as AllRoles[]));

/**
 * 球队所有者认证装饰器
 * 要求用户是球队所有者
 */
export const RequireTeamOwner = () => RequireGameRoles('team_owner');

/**
 * 教练认证装饰器
 * 要求用户是教练（主教练或助理教练）
 */
export const RequireCoach = () => RequireGameRoles('head_coach', 'assistant_coach');

/**
 * 管理层认证装饰器
 * 要求用户是管理层（所有者或总经理）
 */
export const RequireManagement = () => RequireGameRoles('team_owner', 'general_manager');

/**
 * 开发环境装饰器
 * 只在开发环境中可用的端点
 */
export const DevOnly = () => {
  // 在生产环境中，这个装饰器应该阻止访问
  return applyDecorators(
    // 可以添加环境检查守卫
  );
};

/**
 * 测试环境装饰器
 * 只在测试环境中可用的端点
 */
export const TestOnly = () => {
  return applyDecorators(
    // 可以添加环境检查守卫
  );
};

/**
 * 维护模式装饰器
 * 在维护模式下仍可访问的端点
 */
export const MaintenanceAllowed = () => SetMetadata('maintenanceAllowed', true);

/**
 * 内部API装饰器
 * 标记为内部服务间调用的API
 */
export const InternalApi = () => applyDecorators(
  // 可以添加内部服务验证守卫
  SetMetadata('internalApi', true),
);

/**
 * 实验性功能装饰器
 * 标记实验性功能
 */
export const Experimental = () => SetMetadata('experimental', true);

/**
 * 已弃用装饰器
 * 标记已弃用的端点
 */
export const Deprecated = (version?: string, alternative?: string) => 
  SetMetadata('deprecated', { version, alternative });
