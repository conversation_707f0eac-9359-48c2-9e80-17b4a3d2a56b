import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '../../domain/auth/jwt.service';

/**
 * 角色认证守卫
 * 专门用于验证角色级Token的权限边界
 */
@Injectable()
export class CharacterAuthGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException('缺少角色认证Token');
    }

    try {
      // 验证角色Token
      const payload = await this.jwtService.verifyCharacterToken(token);
      
      // 将载荷信息附加到请求对象
      request.user = payload;
      request.character = {
        characterId: payload.characterId,
        serverId: payload.serverId,
        sessionId: payload.sessionId,
      };

      return true;
    } catch (error) {
      throw new UnauthorizedException('角色认证失败');
    }
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
