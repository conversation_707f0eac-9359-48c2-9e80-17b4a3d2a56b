/**
 * 分页相关DTO
 */

import { IsOptional, IsInt, Min, <PERSON>, IsString, IsIn, IsBoolean } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';

// 分页查询DTO
export class PaginationDto {
  @ApiPropertyOptional({
    description: '页码，从1开始',
    minimum: 1,
    default: 1,
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '页码必须是整数' })
  @Min(1, { message: '页码不能小于1' })
  page?: number = 1;

  @ApiPropertyOptional({
    description: '每页数量',
    minimum: 1,
    maximum: 100,
    default: 10,
    example: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '每页数量必须是整数' })
  @Min(1, { message: '每页数量不能小于1' })
  @Max(100, { message: '每页数量不能超过100' })
  limit?: number = 10;

  @ApiPropertyOptional({
    description: '排序字段',
    example: 'createdAt',
  })
  @IsOptional()
  @IsString({ message: '排序字段必须是字符串' })
  sortBy?: string;

  @ApiPropertyOptional({
    description: '排序方向',
    enum: ['asc', 'desc'],
    default: 'desc',
    example: 'desc',
  })
  @IsOptional()
  @IsIn(['asc', 'desc'], { message: '排序方向只能是asc或desc' })
  sortOrder?: 'asc' | 'desc' = 'desc';

  @ApiPropertyOptional({
    description: '搜索关键词',
    example: 'john',
  })
  @IsOptional()
  @IsString({ message: '搜索关键词必须是字符串' })
  @Transform(({ value }) => value?.trim())
  search?: string;

  // 计算偏移量
  get offset(): number {
    return (this.page - 1) * this.limit;
  }

  // 获取排序对象
  get sort(): Record<string, 1 | -1> {
    if (!this.sortBy) return {};
    return { [this.sortBy]: this.sortOrder === 'asc' ? 1 : -1 };
  }
}

// 高级分页查询DTO
export class AdvancedPaginationDto extends PaginationDto {
  @ApiPropertyOptional({
    description: '过滤条件（JSON字符串）',
    example: '{"status":"active","role":"user"}',
  })
  @IsOptional()
  @IsString({ message: '过滤条件必须是字符串' })
  @Transform(({ value }) => {
    if (!value) return undefined;
    try {
      return JSON.parse(value);
    } catch {
      return undefined;
    }
  })
  filters?: Record<string, any>;

  @ApiPropertyOptional({
    description: '包含已删除的记录',
    default: false,
    example: false,
  })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean({ message: '包含已删除记录必须是布尔值' })
  includeDeleted?: boolean = false;

  @ApiPropertyOptional({
    description: '选择字段（逗号分隔）',
    example: 'id,username,email,createdAt',
  })
  @IsOptional()
  @IsString({ message: '选择字段必须是字符串' })
  @Transform(({ value }) => value?.split(',').map((field: string) => field.trim()).filter(Boolean))
  select?: string[];

  @ApiPropertyOptional({
    description: '关联查询（逗号分隔）',
    example: 'profile,roles,permissions',
  })
  @IsOptional()
  @IsString({ message: '关联查询必须是字符串' })
  @Transform(({ value }) => value?.split(',').map((relation: string) => relation.trim()).filter(Boolean))
  populate?: string[];
}

// 分页响应DTO
export class PaginationResponseDto<T> {
  @ApiPropertyOptional({
    description: '数据项',
    type: 'array',
  })
  items: T[];

  @ApiPropertyOptional({
    description: '分页信息',
    type: 'object',
  })
  pagination: {
    page: number;                 // 当前页
    limit: number;                // 每页数量
    total: number;                // 总数
    totalPages: number;           // 总页数
    hasNext: boolean;             // 是否有下一页
    hasPrev: boolean;             // 是否有上一页
    nextPage?: number;            // 下一页
    prevPage?: number;            // 上一页
  };

  @ApiPropertyOptional({
    description: '元数据',
    type: 'object',
  })
  meta?: {
    sortBy?: string;              // 排序字段
    sortOrder?: 'asc' | 'desc';   // 排序方向
    filters?: any;                // 过滤条件
    search?: string;              // 搜索关键词
    select?: string[];            // 选择字段
    populate?: string[];          // 关联查询
  };

  constructor(
    items: T[],
    total: number,
    page: number,
    limit: number,
    meta?: any
  ) {
    this.items = items;
    this.pagination = {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: page < Math.ceil(total / limit),
      hasPrev: page > 1,
      nextPage: page < Math.ceil(total / limit) ? page + 1 : undefined,
      prevPage: page > 1 ? page - 1 : undefined,
    };
    this.meta = meta;
  }
}

// 游标分页DTO
export class CursorPaginationDto {
  @ApiPropertyOptional({
    description: '游标（上次查询的最后一个ID）',
    example: '507f1f77bcf86cd799439011',
  })
  @IsOptional()
  @IsString({ message: '游标必须是字符串' })
  cursor?: string;

  @ApiPropertyOptional({
    description: '每页数量',
    minimum: 1,
    maximum: 100,
    default: 10,
    example: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '每页数量必须是整数' })
  @Min(1, { message: '每页数量不能小于1' })
  @Max(100, { message: '每页数量不能超过100' })
  limit?: number = 10;

  @ApiPropertyOptional({
    description: '排序方向',
    enum: ['asc', 'desc'],
    default: 'desc',
    example: 'desc',
  })
  @IsOptional()
  @IsIn(['asc', 'desc'], { message: '排序方向只能是asc或desc' })
  direction?: 'asc' | 'desc' = 'desc';
}

// 游标分页响应DTO
export class CursorPaginationResponseDto<T> {
  @ApiPropertyOptional({
    description: '数据项',
    type: 'array',
  })
  items: T[];

  @ApiPropertyOptional({
    description: '分页信息',
    type: 'object',
  })
  pagination: {
    limit: number;                // 每页数量
    hasNext: boolean;             // 是否有下一页
    hasPrev: boolean;             // 是否有上一页
    nextCursor?: string;          // 下一页游标
    prevCursor?: string;          // 上一页游标
  };

  @ApiPropertyOptional({
    description: '元数据',
    type: 'object',
  })
  meta?: {
    direction?: 'asc' | 'desc';   // 排序方向
    filters?: any;                // 过滤条件
    search?: string;              // 搜索关键词
  };

  constructor(
    items: T[],
    limit: number,
    hasNext: boolean,
    hasPrev: boolean,
    nextCursor?: string,
    prevCursor?: string,
    meta?: any
  ) {
    this.items = items;
    this.pagination = {
      limit,
      hasNext,
      hasPrev,
      nextCursor,
      prevCursor,
    };
    this.meta = meta;
  }
}

// 分页工具类
export class PaginationHelper {
  /**
   * 创建分页响应
   */
  static createResponse<T>(
    items: T[],
    total: number,
    page: number,
    limit: number,
    meta?: any
  ): PaginationResponseDto<T> {
    return new PaginationResponseDto(items, total, page, limit, meta);
  }

  /**
   * 创建游标分页响应
   */
  static createCursorResponse<T>(
    items: T[],
    limit: number,
    hasNext: boolean,
    hasPrev: boolean,
    nextCursor?: string,
    prevCursor?: string,
    meta?: any
  ): CursorPaginationResponseDto<T> {
    return new CursorPaginationResponseDto(
      items,
      limit,
      hasNext,
      hasPrev,
      nextCursor,
      prevCursor,
      meta
    );
  }

  /**
   * 计算总页数
   */
  static calculateTotalPages(total: number, limit: number): number {
    return Math.ceil(total / limit);
  }

  /**
   * 计算偏移量
   */
  static calculateOffset(page: number, limit: number): number {
    return (page - 1) * limit;
  }

  /**
   * 验证页码
   */
  static validatePage(page: number, totalPages: number): boolean {
    return page >= 1 && page <= totalPages;
  }

  /**
   * 获取安全的页码
   */
  static getSafePage(page: number, totalPages: number): number {
    if (page < 1) return 1;
    if (page > totalPages) return totalPages;
    return page;
  }

  /**
   * 获取安全的限制数量
   */
  static getSafeLimit(limit: number, maxLimit: number = 100): number {
    if (limit < 1) return 1;
    if (limit > maxLimit) return maxLimit;
    return limit;
  }
}
