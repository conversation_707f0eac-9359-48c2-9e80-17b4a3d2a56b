import { ApiProperty } from '@nestjs/swagger';

export class ApiResponseDto<T> {
  @ApiProperty({ description: '请求是否成功' })
  success: boolean;

  @ApiProperty({ description: '响应数据' })
  data: T;

  @ApiProperty({ description: '响应消息' })
  message: string;

  @ApiProperty({ description: '时间戳' })
  timestamp: string;

  @ApiProperty({ description: '错误代码', required: false })
  errorCode?: string;

  @ApiProperty({ description: '错误详情', required: false })
  errors?: any;
}
