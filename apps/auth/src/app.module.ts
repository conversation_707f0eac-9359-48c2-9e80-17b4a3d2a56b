import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { ThrottlerModule } from '@nestjs/throttler';
import { WinstonModule } from 'nest-winston';
import { RedisModule } from '@common/redis';

// 配置模块
import { appConfig } from './shared/config/app.config';
import { databaseConfig } from './shared/config/database.config';
import { redisConfig } from './shared/config/redis.config';
import { authConfig } from './shared/config/auth.config';
import { securityConfig } from './shared/config/security.config';

// 新架构模块导入
import { AuthModule } from './modules/auth/auth.module';
import { RbacModule } from './modules/rbac/rbac.module';
import { UserManagementModule } from './modules/user-management/user-management.module';
import { SessionManagementModule } from './modules/session-management/session-management.module';
import { SecurityModule } from './modules/security/security.module';
import { AdministrationModule } from './modules/administration/administration.module';

// 共享和基础设施
import { SharedModule } from './shared/shared.module';
import { InfrastructureModule } from './infrastructure-new/infrastructure.module';

// 通用组件
import { MicroserviceKitModule } from '@common/microservice-kit';
import { NetworkSecurityModule, IPWhitelistMiddleware, ServiceAuthMiddleware } from '@common/network-security';
import { MICROSERVICE_NAMES } from '@shared/constants';

// 服务注册与发现
import { ServiceRegistryModule } from '@libs/service-registry';

// 工具
import { createWinstonLogger } from './shared/utils/logger.util';
import { EnvDebugUtil } from '@common/utils/env-debug.util';

@Module({
  imports: [
    // 配置模块 - 必须首先加载
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      load: [
        appConfig,
        databaseConfig,
        redisConfig,
        authConfig,
        securityConfig,
      ],
      envFilePath: [
        '.env',                                           // 1. 基础配置
        `.env.${process.env.NODE_ENV || 'development'}`, // 2. 环境特定配置
        '.env.local',                                     // 3. 本地覆盖

        // 业务模块特定配置
        'apps/auth/.env.redis',                          // 4. Redis业务配置
        'apps/auth/.env.security',                       // 5. 安全业务配置

        // 环境特定的业务配置（如果存在）
        `apps/auth/.env.redis.${process.env.NODE_ENV || 'development'}`,
        `apps/auth/.env.security.${process.env.NODE_ENV || 'development'}`,

        // 最终覆盖
        'apps/auth/.env.local',                          // 6. 服务本地覆盖
      ],
      expandVariables: true,                              // 启用变量展开
      validationSchema: null, // 将在配置文件中定义验证
      validationOptions: {
        allowUnknown: true,
        abortEarly: false,
      },
    }),

    // 日志模块
    WinstonModule.forRootAsync({
      useFactory: () => createWinstonLogger(),
    }),

    // 数据库连接
    MongooseModule.forRootAsync({
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('database.mongodb.uri'),
        ...configService.get('database.mongodb.options'),
      }),
      inject: [ConfigService],
    }),

    // Redis模块 - 使用新的v3.0架构，统一前缀隔离，移除数据库分离
    RedisModule.forRootAsync({
      service: MICROSERVICE_NAMES.AUTH_SERVICE,
      // 🔥 移除database参数，使用统一的Redis数据库0 + 前缀隔离
      useFactory: (configService: ConfigService) => ({
        host: configService.get('REDIS_HOST'),
        port: configService.get('REDIS_PORT'),
        password: configService.get('REDIS_PASSWORD'),
      }),
      inject: [ConfigService],
    }),

    // 限流模块
    ThrottlerModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        throttlers: [{
          ttl: configService.get<number>('security.rateLimit.ttl', 60),
          limit: configService.get<number>('security.rateLimit.limit', 100),
        }],
        ignoreUserAgents: [
          /googlebot/gi,
          /bingbot/gi,
        ],
      }),
      inject: [ConfigService],
    }),

    // 基础设施层 - 新架构
    InfrastructureModule,

    // 共享资源
    SharedModule,

    // 通用安全模块 - IP白名单和服务间认证
    NetworkSecurityModule,

    // 服务注册与发现模块
    ServiceRegistryModule,

    // 功能模块 (新架构 - 单向依赖)
    SecurityModule,           // 安全模块 - 无依赖
    UserManagementModule,     // 用户管理 - 无依赖
    SessionManagementModule,  // 会话管理 - 无依赖
    RbacModule,              // 权限管理 - 无依赖
    AuthModule,              // 认证模块 - 依赖上述模块
    AdministrationModule,    // 管理模块 - 依赖所有模块
  ],
  controllers: [],
  providers: [],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(IPWhitelistMiddleware, ServiceAuthMiddleware)
      .forRoutes('*');
  }

  constructor(private configService: ConfigService) {
    // 1. 首先输出环境变量调试信息
    const authConfig = EnvDebugUtil.createServiceConfig('auth');
    EnvDebugUtil.logEnvironmentDetails(authConfig);
    EnvDebugUtil.validateEnvironmentConsistency(authConfig);

    // 2. 然后输出服务启动信息
    const environment = this.configService.get<string>('app.environment');
    const serviceName = this.configService.get<string>('app.name');
    const version = this.configService.get<string>('app.version');

    console.log(`
    ⚽ 足球经理认证服务
    📦 服务: ${serviceName}
    🏷️ 版本: ${version}
    🌍 环境: ${environment}
    🕐 启动时间: ${new Date().toISOString()}
    `);

    // 3. 验证关键配置的一致性
    this.validateConfigConsistency();
  }

  /**
   * 验证配置一致性
   */
  private validateConfigConsistency(): void {
    console.log('\n🔍 ConfigService 配置验证:');

    const processNodeEnv = process.env.NODE_ENV;
    const configNodeEnv = this.configService.get<string>('NODE_ENV');
    const appEnvironment = this.configService.get<string>('app.environment');

    console.log(`   process.env.NODE_ENV: "${processNodeEnv}"`);
    console.log(`   configService.get('NODE_ENV'): "${configNodeEnv}"`);
    console.log(`   configService.get('app.environment'): "${appEnvironment}"`);

    // 检查一致性
    if (processNodeEnv !== configNodeEnv) {
      console.log('   🚨 警告: process.env.NODE_ENV 与 configService.get("NODE_ENV") 不一致！');
      console.log('   💡 建议: 使用 cross-env NODE_ENV=xxx 启动服务');
    } else {
      console.log('   ✅ 环境变量一致性检查通过');
    }

    console.log('');
  }
}
