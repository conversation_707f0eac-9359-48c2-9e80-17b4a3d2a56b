import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';
import { MICROSERVICE_NAMES } from '@shared/constants';

export const appConfig = registerAs('app', () => {
  const config = {
    name: process.env.APP_NAME || 'football-manager-auth',
    version: process.env.APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.AUTH_PORT || '3001', 10),
    host: process.env.AUTH_HOST || '0.0.0.0',
    timezone: process.env.TZ || 'UTC',
    
    // 服务发现配置
    service: {
      name: MICROSERVICE_NAMES.AUTH_SERVICE,
      version: '1.0.0',
      description: '足球经理认证服务',
      tags: ['auth', 'security', 'users'],
    },

    // 应用特性开关
    features: {
      swagger: process.env.ENABLE_SWAGGER !== 'false',
      metrics: process.env.ENABLE_METRICS !== 'false',
      healthCheck: process.env.ENABLE_HEALTH_CHECK !== 'false',
      gracefulShutdown: process.env.ENABLE_GRACEFUL_SHUTDOWN !== 'false',
    },

    // 性能配置
    performance: {
      maxRequestSize: process.env.MAX_REQUEST_SIZE || '10mb',
      keepAliveTimeout: parseInt(process.env.KEEP_ALIVE_TIMEOUT || '5000', 10),
      headersTimeout: parseInt(process.env.HEADERS_TIMEOUT || '60000', 10),
      bodyParserLimit: process.env.BODY_PARSER_LIMIT || '10mb',
    },

    // 监控配置
    monitoring: {
      enablePrometheus: process.env.ENABLE_PROMETHEUS === 'true',
      enableJaeger: process.env.ENABLE_JAEGER === 'true',
      metricsPath: process.env.METRICS_PATH || '/metrics',
      healthPath: process.env.HEALTH_PATH || '/health',
    },
  };

  // 配置验证
  const schema = Joi.object({
    name: Joi.string().required(),
    version: Joi.string().required(),
    environment: Joi.string().valid('development', 'staging', 'production').required(),
    port: Joi.number().port().required(),
    host: Joi.string().required(),
    timezone: Joi.string().required(),

    // 服务发现配置验证
    service: Joi.object({
      name: Joi.string().required(),
      version: Joi.string().required(),
      description: Joi.string().required(),
      tags: Joi.array().items(Joi.string()).required(),
    }).required(),

    // 应用特性开关验证
    features: Joi.object({
      swagger: Joi.boolean().required(),
      metrics: Joi.boolean().required(),
      healthCheck: Joi.boolean().required(),
      gracefulShutdown: Joi.boolean().required(),
    }).required(),

    // 性能配置验证
    performance: Joi.object({
      maxRequestSize: Joi.string().required(),
      keepAliveTimeout: Joi.number().required(),
      headersTimeout: Joi.number().required(),
      bodyParserLimit: Joi.string().required(),
    }).required(),

    // 监控配置验证
    monitoring: Joi.object({
      enablePrometheus: Joi.boolean().required(),
      enableJaeger: Joi.boolean().required(),
      metricsPath: Joi.string().required(),
      healthPath: Joi.string().required(),
    }).required(),
  });

  const { error } = schema.validate(config);
  if (error) {
    throw new Error(`应用配置验证失败: ${error.message}`);
  }

  return config;
});
