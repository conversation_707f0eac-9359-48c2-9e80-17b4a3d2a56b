import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';

export const securityConfig = registerAs('security', () => {
  const config = {
    // CORS配置
    cors: {
      origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
      methods: process.env.CORS_METHODS?.split(',') || ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
      allowedHeaders: process.env.CORS_ALLOWED_HEADERS?.split(',') || [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-API-Key',
        'X-Request-ID',
      ],
      exposedHeaders: process.env.CORS_EXPOSED_HEADERS?.split(',') || [
        'X-RateLimit-Limit',
        'X-RateLimit-Remaining',
        'X-RateLimit-Reset',
        'X-Response-Time',
      ],
      credentials: process.env.CORS_CREDENTIALS !== 'false',
      maxAge: parseInt(process.env.CORS_MAX_AGE || '86400', 10), // 24小时
      optionsSuccessStatus: parseInt(process.env.CORS_OPTIONS_SUCCESS_STATUS || '200', 10),
    },

    // Helmet安全头配置
    helmet: {
      contentSecurityPolicy: {
        enabled: process.env.HELMET_CSP_ENABLED !== 'false',
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "wss:", "https:"],
          fontSrc: ["'self'", "https://fonts.gstatic.com"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
          childSrc: ["'none'"],
          workerSrc: ["'self'"],
          manifestSrc: ["'self'"],
        },
        reportOnly: process.env.NODE_ENV === 'development',
      },
      
      hsts: {
        enabled: process.env.HELMET_HSTS_ENABLED !== 'false',
        maxAge: parseInt(process.env.HELMET_HSTS_MAX_AGE || '31536000', 10), // 1年
        includeSubDomains: process.env.HELMET_HSTS_INCLUDE_SUBDOMAINS !== 'false',
        preload: process.env.HELMET_HSTS_PRELOAD !== 'false',
      },
      
      noSniff: process.env.HELMET_NO_SNIFF !== 'false',
      frameguard: {
        action: process.env.HELMET_FRAMEGUARD_ACTION || 'deny',
      },
      xssFilter: process.env.HELMET_XSS_FILTER !== 'false',
      referrerPolicy: {
        policy: process.env.HELMET_REFERRER_POLICY || 'same-origin',
      },
      hidePoweredBy: process.env.HELMET_HIDE_POWERED_BY !== 'false',
    },

    // 限流配置
    rateLimit: {
      // 全局限流
      global: {
        enabled: process.env.RATE_LIMIT_GLOBAL_ENABLED !== 'false' && process.env.NODE_ENV !== 'development' && process.env.NODE_ENV !== 'test',
        ttl: parseInt(process.env.RATE_LIMIT_GLOBAL_TTL || '60', 10), // 60秒
        limit: parseInt(process.env.RATE_LIMIT_GLOBAL_LIMIT || '100', 10),
        skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESSFUL === 'true',
        skipFailedRequests: process.env.RATE_LIMIT_SKIP_FAILED === 'true',
      },

      // 登录限流
      login: {
        enabled: process.env.RATE_LIMIT_LOGIN_ENABLED !== 'false' && process.env.NODE_ENV !== 'development' && process.env.NODE_ENV !== 'test',
        ttl: parseInt(process.env.RATE_LIMIT_LOGIN_TTL || '300', 10), // 5分钟
        limit: parseInt(process.env.RATE_LIMIT_LOGIN_LIMIT || '5', 10),
        blockDuration: parseInt(process.env.RATE_LIMIT_LOGIN_BLOCK_DURATION || '900', 10), // 15分钟
      },
      
      // 注册限流
      register: {
        enabled: process.env.RATE_LIMIT_REGISTER_ENABLED !== 'false',
        ttl: parseInt(process.env.RATE_LIMIT_REGISTER_TTL || '3600', 10), // 1小时
        limit: parseInt(process.env.RATE_LIMIT_REGISTER_LIMIT || '3', 10),
      },
      
      // 密码重置限流
      passwordReset: {
        enabled: process.env.RATE_LIMIT_PASSWORD_RESET_ENABLED !== 'false',
        ttl: parseInt(process.env.RATE_LIMIT_PASSWORD_RESET_TTL || '3600', 10), // 1小时
        limit: parseInt(process.env.RATE_LIMIT_PASSWORD_RESET_LIMIT || '3', 10),
      },
    },

    // 账户保护
    accountProtection: {
      // 登录保护
      loginProtection: {
        enabled: process.env.ACCOUNT_PROTECTION_LOGIN_ENABLED !== 'false',
        maxAttempts: parseInt(process.env.ACCOUNT_PROTECTION_MAX_ATTEMPTS || '5', 10),
        lockoutDuration: parseInt(process.env.ACCOUNT_PROTECTION_LOCKOUT_DURATION || '900', 10), // 15分钟
        progressiveLockout: process.env.ACCOUNT_PROTECTION_PROGRESSIVE_LOCKOUT === 'true',
        captchaThreshold: parseInt(process.env.ACCOUNT_PROTECTION_CAPTCHA_THRESHOLD || '3', 10),
        ipBasedLockout: process.env.ACCOUNT_PROTECTION_IP_BASED_LOCKOUT === 'true',
        deviceBasedLockout: process.env.ACCOUNT_PROTECTION_DEVICE_BASED_LOCKOUT === 'true',
      },
      
      // 异常检测
      anomalyDetection: {
        enabled: process.env.ANOMALY_DETECTION_ENABLED === 'true',
        geoLocation: {
          enabled: process.env.ANOMALY_DETECTION_GEO_ENABLED === 'true',
          maxDistance: parseInt(process.env.ANOMALY_DETECTION_MAX_DISTANCE || '1000', 10), // 1000公里
          timeThreshold: parseInt(process.env.ANOMALY_DETECTION_TIME_THRESHOLD || '3600', 10), // 1小时
        },
        deviceFingerprint: {
          enabled: process.env.ANOMALY_DETECTION_DEVICE_ENABLED === 'true',
          strictMode: process.env.ANOMALY_DETECTION_DEVICE_STRICT === 'true',
        },
        behaviorAnalysis: {
          enabled: process.env.ANOMALY_DETECTION_BEHAVIOR_ENABLED === 'true',
          learningPeriod: parseInt(process.env.ANOMALY_DETECTION_LEARNING_PERIOD || '30', 10), // 30天
        },
      },
    },

    // 数据保护
    dataProtection: {
      // 加密配置
      encryption: {
        algorithm: process.env.ENCRYPTION_ALGORITHM || 'aes-256-gcm',
        keyDerivation: process.env.ENCRYPTION_KEY_DERIVATION || 'pbkdf2',
        iterations: parseInt(process.env.ENCRYPTION_ITERATIONS || '100000', 10),
        saltLength: parseInt(process.env.ENCRYPTION_SALT_LENGTH || '32', 10),
        ivLength: parseInt(process.env.ENCRYPTION_IV_LENGTH || '16', 10),
        tagLength: parseInt(process.env.ENCRYPTION_TAG_LENGTH || '16', 10),
        masterKey: process.env.ENCRYPTION_MASTER_KEY || 'your-master-encryption-key-change-in-production',
      },
      
      // 数据脱敏
      masking: {
        enabled: process.env.DATA_MASKING_ENABLED !== 'false',
        email: {
          pattern: process.env.DATA_MASKING_EMAIL_PATTERN || 'prefix',
          visibleChars: parseInt(process.env.DATA_MASKING_EMAIL_VISIBLE_CHARS || '2', 10),
        },
        phone: {
          pattern: process.env.DATA_MASKING_PHONE_PATTERN || 'middle',
          visibleChars: parseInt(process.env.DATA_MASKING_PHONE_VISIBLE_CHARS || '3', 10),
        },
        idCard: {
          pattern: process.env.DATA_MASKING_ID_CARD_PATTERN || 'both',
          visibleChars: parseInt(process.env.DATA_MASKING_ID_CARD_VISIBLE_CHARS || '4', 10),
        },
      },
    },

    // 审计配置
    audit: {
      enabled: process.env.AUDIT_ENABLED !== 'false',
      
      // 事件类型
      events: {
        authentication: process.env.AUDIT_EVENTS_AUTH !== 'false',
        authorization: process.env.AUDIT_EVENTS_AUTHZ !== 'false',
        dataAccess: process.env.AUDIT_EVENTS_DATA_ACCESS !== 'false',
        adminActions: process.env.AUDIT_EVENTS_ADMIN !== 'false',
        securityViolations: process.env.AUDIT_EVENTS_SECURITY !== 'false',
      },
      
      // 存储配置
      storage: {
        type: process.env.AUDIT_STORAGE_TYPE || 'mongodb',
        retention: parseInt(process.env.AUDIT_RETENTION_DAYS || '365', 10), // 1年
        compression: process.env.AUDIT_COMPRESSION === 'true',
        encryption: process.env.AUDIT_ENCRYPTION === 'true',
      },
      
      // 告警配置
      alerting: {
        enabled: process.env.AUDIT_ALERTING_ENABLED === 'true',
        thresholds: {
          failedLogins: parseInt(process.env.AUDIT_ALERT_FAILED_LOGINS || '10', 10),
          suspiciousActivity: parseInt(process.env.AUDIT_ALERT_SUSPICIOUS_ACTIVITY || '5', 10),
          dataBreaches: parseInt(process.env.AUDIT_ALERT_DATA_BREACHES || '1', 10),
        },
      },
    },

    // 风险评分
    riskScoring: {
      enabled: process.env.RISK_SCORING_ENABLED === 'true',
      
      // 风险因子权重
      factors: {
        location: parseFloat(process.env.RISK_FACTOR_LOCATION || '0.3'),
        device: parseFloat(process.env.RISK_FACTOR_DEVICE || '0.2'),
        behavior: parseFloat(process.env.RISK_FACTOR_BEHAVIOR || '0.3'),
        time: parseFloat(process.env.RISK_FACTOR_TIME || '0.1'),
        network: parseFloat(process.env.RISK_FACTOR_NETWORK || '0.1'),
      },
      
      // 风险阈值
      thresholds: {
        low: parseInt(process.env.RISK_THRESHOLD_LOW || '30', 10),
        medium: parseInt(process.env.RISK_THRESHOLD_MEDIUM || '60', 10),
        high: parseInt(process.env.RISK_THRESHOLD_HIGH || '80', 10),
      },
      
      // 风险响应动作
      actions: {
        low: process.env.RISK_ACTION_LOW || 'allow',
        medium: process.env.RISK_ACTION_MEDIUM || 'challenge',
        high: process.env.RISK_ACTION_HIGH || 'block',
      },
    },

    // 输入验证
    inputValidation: {
      enabled: process.env.INPUT_VALIDATION_ENABLED !== 'false',
      
      // SQL注入防护
      sqlInjection: {
        enabled: process.env.INPUT_VALIDATION_SQL_INJECTION_ENABLED !== 'false',
        patterns: process.env.INPUT_VALIDATION_SQL_INJECTION_PATTERNS?.split(',') || [
          'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE', 'ALTER', 'EXEC', 'UNION'
        ],
      },
      
      // XSS防护
      xss: {
        enabled: process.env.INPUT_VALIDATION_XSS_ENABLED !== 'false',
        patterns: process.env.INPUT_VALIDATION_XSS_PATTERNS?.split(',') || [
          '<script', 'javascript:', 'onload=', 'onerror=', 'onclick='
        ],
      },
      
      // 文件上传验证
      fileUpload: {
        enabled: process.env.INPUT_VALIDATION_FILE_UPLOAD_ENABLED !== 'false',
        maxSize: parseInt(process.env.INPUT_VALIDATION_FILE_MAX_SIZE || '10485760', 10), // 10MB
        allowedTypes: process.env.INPUT_VALIDATION_FILE_ALLOWED_TYPES?.split(',') || [
          'image/jpeg', 'image/png', 'image/gif', 'image/webp'
        ],
        scanForMalware: process.env.INPUT_VALIDATION_FILE_SCAN_MALWARE === 'true',
      },
    },
  };

  // 配置验证
  const schema = Joi.object({
    cors: Joi.object({
      origin: Joi.array().items(Joi.string()).required(),
      methods: Joi.array().items(Joi.string()).required(),
      allowedHeaders: Joi.array().items(Joi.string()),
      exposedHeaders: Joi.array().items(Joi.string()),
      credentials: Joi.boolean(),
      maxAge: Joi.number().min(0),
      optionsSuccessStatus: Joi.number(),
    }).required(),

    helmet: Joi.object({
      contentSecurityPolicy: Joi.object({
        enabled: Joi.boolean(),
        directives: Joi.object().unknown(true),
        reportOnly: Joi.boolean(),
      }),
      hsts: Joi.object({
        enabled: Joi.boolean(),
        maxAge: Joi.number().min(0),
        includeSubDomains: Joi.boolean(),
        preload: Joi.boolean(),
      }),
      noSniff: Joi.boolean(),
      frameguard: Joi.object({
        action: Joi.string(),
      }),
      xssFilter: Joi.boolean(),
      referrerPolicy: Joi.object({
        policy: Joi.string(),
      }),
      hidePoweredBy: Joi.boolean(),
    }),

    rateLimit: Joi.object({
      global: Joi.object({
        enabled: Joi.boolean(),
        ttl: Joi.number().min(1),
        limit: Joi.number().min(1),
        skipSuccessfulRequests: Joi.boolean(),
        skipFailedRequests: Joi.boolean(),
      }),
      login: Joi.object({
        enabled: Joi.boolean(),
        ttl: Joi.number().min(1),
        limit: Joi.number().min(1),
        blockDuration: Joi.number().min(1),
      }),
      register: Joi.object({
        enabled: Joi.boolean(),
        ttl: Joi.number().min(1),
        limit: Joi.number().min(1),
      }),
      passwordReset: Joi.object({
        enabled: Joi.boolean(),
        ttl: Joi.number().min(1),
        limit: Joi.number().min(1),
      }),
    }).required(),

    bruteForce: Joi.object({
      enabled: Joi.boolean(),
      maxAttempts: Joi.number().min(1),
      blockDuration: Joi.number().min(1),
      progressiveDelay: Joi.boolean(),
      whitelistIPs: Joi.array().items(Joi.string()),
      skipSuccessfulRequests: Joi.boolean(),
    }),

    riskScoring: Joi.object({
      enabled: Joi.boolean(),
      factors: Joi.object({
        location: Joi.number().min(0).max(1),
        device: Joi.number().min(0).max(1),
        behavior: Joi.number().min(0).max(1),
        time: Joi.number().min(0).max(1),
        network: Joi.number().min(0).max(1),
      }),
      thresholds: Joi.object({
        low: Joi.number().min(0).max(100),
        medium: Joi.number().min(0).max(100),
        high: Joi.number().min(0).max(100),
      }),
      actions: Joi.object({
        low: Joi.string(),
        medium: Joi.string(),
        high: Joi.string(),
      }),
    }),

    inputValidation: Joi.object({
      enabled: Joi.boolean(),
      sqlInjection: Joi.object({
        enabled: Joi.boolean(),
        patterns: Joi.array().items(Joi.string()),
      }),
      xss: Joi.object({
        enabled: Joi.boolean(),
        patterns: Joi.array().items(Joi.string()),
      }),
      fileUpload: Joi.object({
        enabled: Joi.boolean(),
        maxSize: Joi.number().min(1),
        allowedTypes: Joi.array().items(Joi.string()),
        scanForMalware: Joi.boolean(),
      }),
    }),

    accountProtection: Joi.object({
      loginProtection: Joi.object({
        enabled: Joi.boolean(),
        maxAttempts: Joi.number().min(1),
        lockoutDuration: Joi.number().min(1),
        progressiveLockout: Joi.boolean(),
        captchaThreshold: Joi.number().min(1),
        ipBasedLockout: Joi.boolean(),
        deviceBasedLockout: Joi.boolean(),
      }),
      anomalyDetection: Joi.object({
        enabled: Joi.boolean(),
        geoLocation: Joi.object({
          enabled: Joi.boolean(),
          maxDistance: Joi.number().min(0),
          timeThreshold: Joi.number().min(1),
        }),
        deviceFingerprint: Joi.object({
          enabled: Joi.boolean(),
          strictMode: Joi.boolean(),
        }),
        behaviorAnalysis: Joi.object({
          enabled: Joi.boolean(),
          learningPeriod: Joi.number().min(1),
        }),
      }),
    }),

    dataProtection: Joi.object({
      encryption: Joi.object({
        algorithm: Joi.string(),
        keyDerivation: Joi.string(),
        iterations: Joi.number().min(1000),
        saltLength: Joi.number().min(16),
        ivLength: Joi.number().min(12),
        tagLength: Joi.number().min(12),
        masterKey: Joi.string().min(32),
      }),
      masking: Joi.object({
        enabled: Joi.boolean(),
        email: Joi.object({
          pattern: Joi.string(),
          visibleChars: Joi.number().min(1),
        }),
        phone: Joi.object({
          pattern: Joi.string(),
          visibleChars: Joi.number().min(1),
        }),
        idCard: Joi.object({
          pattern: Joi.string(),
          visibleChars: Joi.number().min(1),
        }),
      }),
    }),

    audit: Joi.object({
      enabled: Joi.boolean(),
      events: Joi.object({
        authentication: Joi.boolean(),
        authorization: Joi.boolean(),
        dataAccess: Joi.boolean(),
        adminActions: Joi.boolean(),
        securityViolations: Joi.boolean(),
      }),
      storage: Joi.object({
        type: Joi.string(),
        retention: Joi.number().min(1),
        compression: Joi.boolean(),
        encryption: Joi.boolean(),
      }),
      alerting: Joi.object({
        enabled: Joi.boolean(),
        thresholds: Joi.object({
          failedLogins: Joi.number().min(1),
          suspiciousActivity: Joi.number().min(1),
          dataBreaches: Joi.number().min(1),
        }),
      }),
    }),
  });

  const { error } = schema.validate(config);
  if (error) {
    throw new Error(`安全配置验证失败: ${error.message}`);
  }

  return config;
});
