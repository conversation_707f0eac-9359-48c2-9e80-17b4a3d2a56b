import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';

export const redisConfig = registerAs('redis', () => {
  const config = {
    // 基础连接配置
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0', 10),
    
    // 连接选项
    options: {
      connectTimeout: parseInt(process.env.REDIS_CONNECT_TIMEOUT || '10000', 10),
      commandTimeout: parseInt(process.env.REDIS_COMMAND_TIMEOUT || '5000', 10),
      retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY_ON_FAILOVER || '100', 10),
      maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES_PER_REQUEST || '3', 10),
      lazyConnect: process.env.REDIS_LAZY_CONNECT === 'true',
      keepAlive: parseInt(process.env.REDIS_KEEP_ALIVE || '30000', 10),
      family: parseInt(process.env.REDIS_FAMILY || '4', 10),
      // keyPrefix 将在连接时动态设置为 ${env}:${project}:${service}:
    },

    // 集群配置
    cluster: {
      enabled: process.env.REDIS_CLUSTER_ENABLED === 'true',
      nodes: process.env.REDIS_CLUSTER_NODES?.split(',').map(node => {
        const [host, port] = node.split(':');
        return { host, port: parseInt(port, 10) };
      }) || [],
      options: {
        redisOptions: {
          password: process.env.REDIS_PASSWORD,
        },
        enableOfflineQueue: process.env.REDIS_CLUSTER_ENABLE_OFFLINE_QUEUE !== 'false',
        retryDelayOnFailover: parseInt(process.env.REDIS_CLUSTER_RETRY_DELAY || '100', 10),
        maxRetriesPerRequest: parseInt(process.env.REDIS_CLUSTER_MAX_RETRIES || '3', 10),
        scaleReads: process.env.REDIS_CLUSTER_SCALE_READS || 'master',
      },
    },

    // 哨兵配置
    sentinel: {
      enabled: process.env.REDIS_SENTINEL_ENABLED === 'true',
      sentinels: process.env.REDIS_SENTINEL_HOSTS?.split(',').map(host => {
        const [hostname, port] = host.split(':');
        return { host: hostname, port: parseInt(port, 10) };
      }) || [],
      name: process.env.REDIS_SENTINEL_MASTER_NAME || 'mymaster',
      options: {
        password: process.env.REDIS_SENTINEL_PASSWORD,
        sentinelPassword: process.env.REDIS_SENTINEL_AUTH_PASSWORD,
        role: process.env.REDIS_SENTINEL_ROLE || 'master',
        enableTLSForSentinelMode: process.env.REDIS_SENTINEL_TLS === 'true',
      },
    },

    // SSL/TLS配置
    tls: {
      enabled: process.env.REDIS_TLS_ENABLED === 'true',
      cert: process.env.REDIS_TLS_CERT,
      key: process.env.REDIS_TLS_KEY,
      ca: process.env.REDIS_TLS_CA,
      rejectUnauthorized: process.env.REDIS_TLS_REJECT_UNAUTHORIZED !== 'false',
    },

    // 监控配置
    monitoring: {
      enableLatencyMonitoring: process.env.REDIS_ENABLE_LATENCY_MONITORING === 'true',
      enableReadyCheck: process.env.REDIS_ENABLE_READY_CHECK !== 'false',
      enableAutoPipelining: process.env.REDIS_ENABLE_AUTO_PIPELINING === 'true',
      maxRetriesPerRequest: parseInt(process.env.REDIS_MONITORING_MAX_RETRIES || '3', 10),
    },

    // 性能优化
    performance: {
      enableOfflineQueue: process.env.REDIS_ENABLE_OFFLINE_QUEUE !== 'false',
      lazyConnect: process.env.REDIS_LAZY_CONNECT === 'true',
      maxMemoryPolicy: process.env.REDIS_MAX_MEMORY_POLICY || 'allkeys-lru',
      compression: process.env.REDIS_COMPRESSION === 'true',
    },
  };

  // 配置验证
  const schema = Joi.object({
    host: Joi.string().required(),
    port: Joi.number().port().required(),
    password: Joi.string().allow('', null),
    db: Joi.number().min(0).max(15),

    options: Joi.object({
      connectTimeout: Joi.number().min(1000),
      commandTimeout: Joi.number().min(1000),
      retryDelayOnFailover: Joi.number().min(0),
      maxRetriesPerRequest: Joi.number().min(0),
      lazyConnect: Joi.boolean(),
      keepAlive: Joi.number().min(0),
      family: Joi.number().valid(4, 6),
      keyPrefix: Joi.string(),
    }),

    cache: Joi.object({
      ttl: Joi.number().min(1),
      max: Joi.number().min(1),
      refreshThreshold: Joi.number().min(1),
    }),

    // 移除了session、blacklist、rateLimit配置的验证
    // 这些配置已不再使用，统一使用连接级前缀管理

    cluster: Joi.object({
      enabled: Joi.boolean(),
      nodes: Joi.array().items(Joi.object({
        host: Joi.string(),
        port: Joi.number().port(),
      })),
      options: Joi.object({
        redisOptions: Joi.object({
          password: Joi.string().allow('', null),
        }),
        enableOfflineQueue: Joi.boolean(),
        retryDelayOnFailover: Joi.number().min(0),
        maxRetriesPerRequest: Joi.number().min(0),
        scaleReads: Joi.string(),
      }),
    }),

    sentinel: Joi.object({
      enabled: Joi.boolean(),
      sentinels: Joi.array().items(Joi.object({
        host: Joi.string(),
        port: Joi.number().port(),
      })),
      name: Joi.string(),
      options: Joi.object({
        password: Joi.string().allow('', null),
        sentinelPassword: Joi.string().allow('', null),
        role: Joi.string(),
        enableTLSForSentinelMode: Joi.boolean(),
      }),
    }),

    tls: Joi.object({
      enabled: Joi.boolean(),
      cert: Joi.string().allow('', null),
      key: Joi.string().allow('', null),
      ca: Joi.string().allow('', null),
      rejectUnauthorized: Joi.boolean(),
    }),

    monitoring: Joi.object({
      enableLatencyMonitoring: Joi.boolean(),
      enableReadyCheck: Joi.boolean(),
      enableAutoPipelining: Joi.boolean(),
      maxRetriesPerRequest: Joi.number().min(0),
    }),

    performance: Joi.object({
      enableOfflineQueue: Joi.boolean(),
      lazyConnect: Joi.boolean(),
      maxMemoryPolicy: Joi.string(),
      compression: Joi.boolean(),
    }),
  });

  const { error } = schema.validate(config);
  if (error) {
    throw new Error(`Redis配置验证失败: ${error.message}`);
  }

  return config;
});
