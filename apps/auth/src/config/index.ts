import { ConfigFactory, registerAs } from '@nestjs/config';
import * as Jo<PERSON> from 'joi';

/**
 * 应用配置接口
 */
export interface AppConfiguration {
  app: AppConfig;
  database: DatabaseConfig;
  auth: AuthConfig;
  security: SecurityConfig;
}

export interface AppConfig {
  name: string;
  version: string;
  port: number;
  environment: string;
}

export interface DatabaseConfig {
  mongodb: {
    uri: string;
    options: {
      maxPoolSize: number;
      serverSelectionTimeoutMS: number;
    };
  };
  redis: {
    host: string;
    port: number;
    password?: string;
    db: number;
  };
}

export interface AuthConfig {
  jwt: {
    secret: string;
    accessTokenTTL: string;
    refreshTokenTTL: string;
    algorithm: string;
    issuer: string;
    audience: string;
  };
  password: {
    saltRounds: number;
    minLength: number;
    maxLength: number;
  };
  mfa: {
    enabled: boolean;
    issuer: string;
  };
  session: {
    maxConcurrentSessions: number;
    sessionTimeout: number;
    absoluteTimeout: number;
  };
}

export interface SecurityConfig {
  rateLimit: {
    global: {
      ttl: number;
      limit: number;
    };
    auth: {
      ttl: number;
      limit: number;
    };
  };
  encryption: {
    algorithm: string;
    keyLength: number;
  };
  audit: {
    enabled: boolean;
    retention: number;
  };
}

// 配置验证Schema
const configValidationSchema = Joi.object({
  // App配置验证
  APP_NAME: Joi.string().default('football-manager-auth'),
  APP_VERSION: Joi.string().default('1.0.0'),
  NODE_ENV: Joi.string().valid('development', 'production', 'test').default('development'),
  AUTH_PORT: Joi.number().port().default(3001),

  // 数据库配置验证
  MONGODB_URI: Joi.string().required(),
  MONGODB_MAX_POOL_SIZE: Joi.number().default(10),
  MONGODB_TIMEOUT: Joi.number().default(5000),

  // Redis配置验证
  REDIS_HOST: Joi.string().default('localhost'),
  REDIS_PORT: Joi.number().port().default(6379),
  REDIS_PASSWORD: Joi.string().optional(),
  REDIS_DB: Joi.number().default(0),

  // JWT配置验证
  JWT_SECRET: Joi.string().min(32).required(),
  JWT_ACCESS_TOKEN_TTL: Joi.string().default('15m'),
  JWT_REFRESH_TOKEN_TTL: Joi.string().default('7d'),
  JWT_ALGORITHM: Joi.string().default('HS256'),
  JWT_ISSUER: Joi.string().default('football-manager-auth'),
  JWT_AUDIENCE: Joi.string().default('football-manager-app'),

  // 密码配置验证
  PASSWORD_SALT_ROUNDS: Joi.number().min(10).max(15).default(12),
  PASSWORD_MIN_LENGTH: Joi.number().min(6).default(8),
  PASSWORD_MAX_LENGTH: Joi.number().max(256).default(128),

  // MFA配置验证
  MFA_ENABLED: Joi.boolean().default(false),
  MFA_ISSUER: Joi.string().default('足球经理'),

  // 会话配置验证
  MAX_CONCURRENT_SESSIONS: Joi.number().default(5),
  SESSION_TIMEOUT: Joi.number().default(3600),
  ABSOLUTE_TIMEOUT: Joi.number().default(86400),

  // 限流配置验证
  RATE_LIMIT_TTL: Joi.number().default(60),
  RATE_LIMIT_MAX: Joi.number().default(100),
  AUTH_RATE_LIMIT_TTL: Joi.number().default(300),
  AUTH_RATE_LIMIT_MAX: Joi.number().default(5),

  // 加密配置验证
  ENCRYPTION_ALGORITHM: Joi.string().default('aes-256-gcm'),
  ENCRYPTION_KEY_LENGTH: Joi.number().default(32),

  // 审计配置验证
  AUDIT_ENABLED: Joi.boolean().default(true),
  AUDIT_RETENTION_DAYS: Joi.number().default(365),
});

/**
 * 统一配置工厂
 *
 * 整合了原来分散在5个配置文件中的所有配置：
 * - app.config.ts
 * - database.config.ts
 * - auth.config.ts
 * - security.config.ts
 * - redis.config.ts
 */
export const createConfiguration = registerAs('auth', () => {
  // 验证环境变量
  const { error, value: validatedEnv } = configValidationSchema.validate(process.env, {
    allowUnknown: true,
    abortEarly: false,
  });

  if (error) {
    throw new Error(`Auth服务配置验证失败: ${error.message}`);
  }

  return {
    app: {
      name: validatedEnv.APP_NAME,
      version: validatedEnv.APP_VERSION,
      port: validatedEnv.AUTH_PORT,
      environment: validatedEnv.NODE_ENV,
    },
  
  database: {
    mongodb: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/auth',
      options: {
        maxPoolSize: parseInt(process.env.MONGODB_MAX_POOL_SIZE || '10', 10),
        serverSelectionTimeoutMS: parseInt(process.env.MONGODB_TIMEOUT || '5000', 10),
      },
    },
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379', 10),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0', 10),
    },
  },
  
  auth: {
    jwt: {
      secret: process.env.JWT_SECRET || 'change-me-in-production',
      accessTokenTTL: process.env.JWT_ACCESS_TOKEN_TTL || '15m',
      refreshTokenTTL: process.env.JWT_REFRESH_TOKEN_TTL || '7d',
      algorithm: process.env.JWT_ALGORITHM || 'HS256',
      issuer: process.env.JWT_ISSUER || 'football-manager-auth',
      audience: process.env.JWT_AUDIENCE || 'football-manager-app',
    },
    password: {
      saltRounds: parseInt(process.env.PASSWORD_SALT_ROUNDS || '12', 10),
      minLength: parseInt(process.env.PASSWORD_MIN_LENGTH || '8', 10),
      maxLength: parseInt(process.env.PASSWORD_MAX_LENGTH || '128', 10),
    },
    mfa: {
      enabled: process.env.MFA_ENABLED === 'true',
      issuer: process.env.MFA_ISSUER || '足球经理',
    },
    session: {
      maxConcurrentSessions: parseInt(process.env.MAX_CONCURRENT_SESSIONS || '5', 10),
      sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '3600', 10),
      absoluteTimeout: parseInt(process.env.ABSOLUTE_TIMEOUT || '86400', 10),
    },
  },
  
  security: {
    rateLimit: {
      global: {
        ttl: parseInt(process.env.RATE_LIMIT_TTL || '60', 10),
        limit: parseInt(process.env.RATE_LIMIT_MAX || '100', 10),
      },
      auth: {
        ttl: parseInt(process.env.AUTH_RATE_LIMIT_TTL || '300', 10),
        limit: parseInt(process.env.AUTH_RATE_LIMIT_MAX || '5', 10),
      },
    },
    encryption: {
      algorithm: process.env.ENCRYPTION_ALGORITHM || 'aes-256-gcm',
      keyLength: parseInt(process.env.ENCRYPTION_KEY_LENGTH || '32', 10),
    },
    audit: {
      enabled: process.env.AUDIT_ENABLED !== 'false',
      retention: parseInt(process.env.AUDIT_RETENTION_DAYS || '365', 10),
    },
  },
});

// 配置类型已在上面定义，无需重复导出
