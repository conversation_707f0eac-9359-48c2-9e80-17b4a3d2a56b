import { Controller, Get, Put, Delete, Post, Param, Body, Inject } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { Cacheable, CacheEvict, CachePut, CacheManagerService } from '@common/redis';
import {
  HealthCheckService,
  HealthCheck,
  MongooseHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
} from '@nestjs/terminus';
import { HealthService } from './health.service';
import { MICROSERVICE_NAMES } from '@shared/constants';

@ApiTags('健康检查')
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private mongoose: MongooseHealthIndicator,
    private memory: MemoryHealthIndicator,
    private disk: DiskHealthIndicator,
    private healthService: HealthService,
    private readonly cacheManager: CacheManagerService,
  ) {}

  @Get()
  @ApiOperation({ summary: '基础健康检查' })
  @ApiResponse({ status: 200, description: '服务健康' })
  @ApiResponse({ status: 503, description: '服务不健康' })
  @HealthCheck()
  check() {
    return this.health.check([
      // 数据库健康检查
      () => this.mongoose.pingCheck('mongodb'),
      
      // Redis健康检查
      () => this.healthService.redisHealthCheck(),
      
      // 内存健康检查 (堆内存不超过 1GB)
      () => this.memory.checkHeap('memory_heap', 1024 * 1024 * 1024),
      
      // RSS内存检查 (不超过 1.5GB)
      () => this.memory.checkRSS('memory_rss', 1.5 * 1024 * 1024 * 1024),
      
      // 磁盘空间检查 (开发环境暂时禁用)
      // () => this.disk.checkStorage('storage', {
      //   path: process.platform === 'win32' ? process.cwd().split(':')[0] + ':' : '/',
      //   thresholdPercent: 0.95, // 95%使用率阈值，适合开发环境
      // }),
    ]);
  }

  @Get('detailed')
  @ApiOperation({ summary: '详细健康检查' })
  @ApiResponse({ status: 200, description: '详细健康信息' })
  @HealthCheck()
  detailedCheck() {
    return this.health.check([
      // 基础检查
      () => this.mongoose.pingCheck('mongodb'),
      () => this.healthService.redisHealthCheck(),
      () => this.memory.checkHeap('memory_heap', 1024 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 1.5 * 1024 * 1024 * 1024),
      // () => this.disk.checkStorage('storage', {
      //   path: process.platform === 'win32' ? process.cwd().split(':')[0] + ':' : '/',
      //   thresholdPercent: 0.95 // 95%使用率阈值，适合开发环境
      // }),
      
      // 扩展检查
      () => this.healthService.jwtHealthCheck(),
      () => this.healthService.cacheHealthCheck(),
      () => this.healthService.externalServicesHealthCheck(),
    ]);
  }

  @Get('liveness')
  @ApiOperation({ summary: '存活检查' })
  @ApiResponse({ status: 200, description: '服务存活' })
  getLiveness() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: MICROSERVICE_NAMES.AUTH_SERVICE,
      version: process.env.APP_VERSION || '1.0.0',
      uptime: process.uptime(),
    };
  }

  @Get('readiness')
  @ApiOperation({ summary: '就绪检查' })
  @ApiResponse({ status: 200, description: '服务就绪' })
  @ApiResponse({ status: 503, description: '服务未就绪' })
  @HealthCheck()
  getReadiness() {
    return this.health.check([
      () => this.mongoose.pingCheck('mongodb'),
      () => this.healthService.redisHealthCheck(),
      () => this.healthService.essentialServicesCheck(),
    ]);
  }

  @Get('cache-test')
  @ApiOperation({ summary: '缓存装饰器测试' })
  @ApiResponse({ status: 200, description: '缓存测试成功' })
  async testCacheDecorators() {
    // 测试缓存装饰器功能
    return this.healthService.testCacheDecorators();
  }

  @Get('metrics')
  @ApiOperation({ summary: '健康指标' })
  @ApiResponse({ status: 200, description: '健康指标信息' })
  async getMetrics() {
    return this.healthService.getHealthMetrics();
  }

  /**
   * 缓存表达式测试端点
   */
  @Get('cache-expression-test/:testKey')
  @Cacheable({
    key: 'health:cache-test:#{testKey}',
    ttl: 60,
    condition: '#{testKey != null}',
    paramNames: ['testKey']
  })
  @ApiOperation({ summary: '测试缓存表达式解析器' })
  @ApiParam({ name: 'testKey', description: '测试键' })
  @ApiResponse({ status: 200, description: '缓存表达式测试结果' })
  async testCacheExpression(@Param('testKey') testKey: string) {
    return this.healthService.getCacheTestData(testKey);
  }

  /**
   * 更新缓存测试数据
   */
  @Put('cache-expression-test/:testKey')
  @CachePut({
    key: 'health:cache-test:#{testKey}',
    ttl: 60,
    paramNames: ['testKey', 'newData']
  })
  @ApiOperation({ summary: '更新缓存测试数据' })
  @ApiParam({ name: 'testKey', description: '测试键' })
  @ApiResponse({ status: 200, description: '缓存更新结果' })
  async updateCacheExpression(@Param('testKey') testKey: string, @Body() newData: any) {
    return this.healthService.updateCacheTestData(testKey, newData);
  }

  /**
   * 清除缓存测试数据
   */
  @Delete('cache-expression-test/:testKey')
  @CacheEvict({
    key: 'health:cache-test:#{testKey}',
    paramNames: ['testKey']
  })
  @ApiOperation({ summary: '清除缓存测试数据' })
  @ApiParam({ name: 'testKey', description: '测试键' })
  @ApiResponse({ status: 200, description: '缓存清除结果' })
  async clearCacheExpression(@Param('testKey') testKey: string) {
    return this.healthService.clearCacheTestData(testKey);
  }

  // ==================== 微服务接口 - 用于WebSocket代理测试 ====================

  /**
   * 缓存表达式测试 - 微服务调用（用于测试WebSocket代理缓存装饰器）
   * 注意：这个方法使用@MessagePattern，不会触发全局拦截器，
   * 因此@Cacheable装饰器不会工作
   */
  @MessagePattern('cache-expression-test')
  @Cacheable({
    key: 'health:ws-cache-test:#{testKey}',
    ttl: 60,
    condition: '#{testKey != null}',
    paramNames: ['testKey']
  })
  async testCacheExpressionWS(@Payload() data: { testKey: string }) {
    console.log(`📨 收到微服务调用: cache-expression-test`);
    console.log(`📨 请求数据: ${JSON.stringify(data)}`);

    try {
      const result = await this.healthService.getCacheTestData(data.testKey);
      console.log(`✅ 微服务调用成功: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      return { error: error.message };
    }
  }

  /**
   * 手动缓存测试 - 微服务调用（用于测试WebSocket代理手动缓存）
   * 这个方法使用手动缓存方式，应该在WebSocket代理调用中正常工作
   */
  @MessagePattern('manual-cache-test')
  async testManualCacheWS(@Payload() data: { testKey: string }) {
    console.log(`📨 收到微服务调用: manual-cache-test`);
    console.log(`📨 请求数据: ${JSON.stringify(data)}`);

    try {
      const repository = this.cacheManager.getRepository('health-manual');
      const cacheKey = `manual-cache-test:${data.testKey}`;

      console.log(`🔍 检查缓存键: ${cacheKey}`);

      const result = await repository.getOrLoad(
        cacheKey,
        async () => {
          console.log(`💾 缓存未命中，从服务获取数据`);
          return await this.healthService.getCacheTestData(data.testKey);
        },
        {
          ttl: 60,
          enableProtection: true,
          enableAvalancheProtection: true,
          enableBreakdownProtection: true,
        }
      );

      console.log(`✅ 手动缓存调用成功: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      console.error(`❌ 手动缓存调用失败: ${error.message}`);
      return { error: error.message };
    }
  }

  /**
   * 手动缓存更新测试 - 微服务调用
   */
  @MessagePattern('manual-cache-update')
  async updateManualCacheWS(@Payload() data: { testKey: string; updateData: any }) {
    console.log(`📨 收到微服务调用: manual-cache-update`);
    console.log(`📨 请求数据: ${JSON.stringify(data)}`);

    try {
      const repository = this.cacheManager.getRepository('health-manual');
      const cacheKey = `manual-cache-test:${data.testKey}`;

      // 获取原始数据并更新
      const originalData = await this.healthService.getCacheTestData(data.testKey);
      const updatedData = {
        ...originalData,
        ...data.updateData,
        updatedAt: new Date().toISOString(),
        cacheType: 'manual'
      };

      // 使用 setThrough 方法更新缓存
      await repository.setThrough(
        cacheKey,
        updatedData,
        {
          ttl: 60,
          enableProtection: true,
        }
      );

      console.log(`✅ 手动缓存更新成功: ${JSON.stringify(updatedData)}`);
      return updatedData;
    } catch (error) {
      console.error(`❌ 手动缓存更新失败: ${error.message}`);
      return { error: error.message };
    }
  }

  /**
   * 手动缓存清除测试 - 微服务调用
   */
  @MessagePattern('manual-cache-clear')
  async clearManualCacheWS(@Payload() data: { testKey: string }) {
    console.log(`📨 收到微服务调用: manual-cache-clear`);
    console.log(`📨 请求数据: ${JSON.stringify(data)}`);

    try {
      const repository = this.cacheManager.getRepository('health-manual');
      const cacheKey = `manual-cache-test:${data.testKey}`;

      // 清除缓存
      await repository.delete(cacheKey);

      console.log(`✅ 手动缓存清除成功: ${cacheKey}`);
      return {
        success: true,
        message: `缓存键 ${cacheKey} 已清除`,
        clearedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error(`❌ 手动缓存清除失败: ${error.message}`);
      return { error: error.message };
    }
  }

  // ==================== Redis核心服务测试端点 ====================

  /**
   * 缓存清除测试端点
   */
  @Delete('cache-evict-test/:testKey')
  @CacheEvict({
    key: 'health:cache-test:#{testKey}',
    paramNames: ['testKey']
  })
  @ApiOperation({ summary: '测试缓存清除' })
  @ApiParam({ name: 'testKey', description: '测试键' })
  @ApiResponse({ status: 200, description: '缓存清除成功' })
  async testCacheEvict(@Param('testKey') testKey: string) {
    return {
      success: true,
      message: `缓存键 ${testKey} 已清除`,
      clearedAt: new Date().toISOString()
    };
  }

  /**
   * 手动缓存设置
   */
  @Post('manual-cache-set')
  @ApiOperation({ summary: '手动设置缓存' })
  @ApiResponse({ status: 200, description: '缓存设置成功' })
  async setManualCache(@Body() body: { key: string; value: any; ttl?: number }) {
    try {
      const repository = this.cacheManager.getRepository('health-manual');
      await repository.set(body.key, body.value, { ttl: body.ttl || 300 });

      return {
        success: true,
        message: '手动缓存设置成功',
        key: body.key,
        setAt: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        message: `手动缓存设置失败: ${error.message}`,
        key: body.key,
        error: error.message
      };
    }
  }

  /**
   * 手动缓存获取
   */
  @Get('manual-cache-get/:key')
  @ApiOperation({ summary: '手动获取缓存' })
  @ApiParam({ name: 'key', description: '缓存键' })
  @ApiResponse({ status: 200, description: '缓存获取成功' })
  async getManualCache(@Param('key') key: string) {
    try {
      const repository = this.cacheManager.getRepository('health-manual');
      const result = await repository.get(key);

      if (result && result.hit) {
        // 返回实际的数据，而不是CacheResult包装
        return result.data;
      } else {
        return { message: '缓存不存在' };
      }
    } catch (error) {
      return {
        message: '缓存获取失败',
        error: error.message,
        key
      };
    }
  }

  /**
   * 缓存存在性检查
   */
  @Get('cache-exists/:key')
  @ApiOperation({ summary: '检查缓存是否存在' })
  @ApiParam({ name: 'key', description: '缓存键' })
  @ApiResponse({ status: 200, description: '缓存存在性检查结果' })
  async checkCacheExists(@Param('key') key: string) {
    const repository = this.cacheManager.getRepository('health-manual');
    const exists = await repository.exists(key);

    return {
      key,
      exists,
      checkedAt: new Date().toISOString()
    };
  }

  /**
   * 缓存穿透防护测试
   */
  @Get('cache-protection-test/:key')
  @ApiOperation({ summary: '测试缓存穿透防护' })
  @ApiParam({ name: 'key', description: '测试键' })
  @ApiResponse({ status: 200, description: '缓存防护测试结果' })
  async testCacheProtection(@Param('key') key: string) {
    try {
      // 简化实现，直接返回模拟结果
      return {
        key,
        result: null,
        protected: true,
        message: '缓存穿透防护测试完成',
        testedAt: new Date().toISOString()
      };
    } catch (error) {
      return {
        key,
        error: error.message,
        protected: false,
        message: '缓存穿透防护测试失败',
        testedAt: new Date().toISOString()
      };
    }
  }

  /**
   * 缓存击穿防护测试（热点数据）
   */
  @Get('cache-hotkey-test/:key')
  @ApiOperation({ summary: '测试缓存击穿防护' })
  @ApiParam({ name: 'key', description: '热点键' })
  @ApiResponse({ status: 200, description: '缓存击穿防护测试结果' })
  async testCacheHotkey(@Param('key') key: string) {
    try {
      // 简化实现，直接返回模拟结果
      const result = {
        key,
        data: `热点数据-${Math.random()}`,
        generatedAt: new Date().toISOString()
      };

      return {
        key,
        result,
        protected: true,
        message: '缓存击穿防护测试完成',
        testedAt: new Date().toISOString()
      };
    } catch (error) {
      return {
        key,
        error: error.message,
        protected: false,
        message: '缓存击穿防护测试失败',
        testedAt: new Date().toISOString()
      };
    }
  }

  // ==================== 其他Redis服务测试端点 ====================

  /**
   * 队列添加任务测试
   */
  @Post('queue-add-job')
  @ApiOperation({ summary: '测试队列添加任务' })
  @ApiResponse({ status: 200, description: '任务添加成功' })
  async testQueueAddJob(@Body() body: { queueName: string; jobData: any }) {
    // 这里应该调用实际的队列服务，暂时返回模拟结果
    return {
      success: true,
      jobId: `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      queueName: body.queueName,
      addedAt: new Date().toISOString(),
      message: '任务添加成功'
    };
  }

  /**
   * 队列获取任务测试
   */
  @Get('queue-get-job/:queueName')
  @ApiOperation({ summary: '测试队列获取任务' })
  @ApiParam({ name: 'queueName', description: '队列名称' })
  @ApiResponse({ status: 200, description: '任务获取成功' })
  async testQueueGetJob(@Param('queueName') queueName: string) {
    // 这里应该调用实际的队列服务，暂时返回模拟结果
    return {
      success: true,
      job: {
        id: `job_${Date.now()}`,
        type: 'test_job',
        payload: { test: true },
        queueName
      },
      retrievedAt: new Date().toISOString(),
      message: '任务获取成功'
    };
  }

  /**
   * 队列统计测试
   */
  @Get('queue-stats/:queueName')
  @ApiOperation({ summary: '测试队列统计' })
  @ApiParam({ name: 'queueName', description: '队列名称' })
  @ApiResponse({ status: 200, description: '队列统计获取成功' })
  async testQueueStats(@Param('queueName') queueName: string) {
    // 这里应该调用实际的队列服务，暂时返回模拟结果
    return {
      queueName,
      stats: {
        waiting: 5,
        active: 2,
        completed: 100,
        failed: 3,
        delayed: 1
      },
      retrievedAt: new Date().toISOString(),
      message: '队列统计获取成功'
    };
  }

  /**
   * 发布订阅测试
   */
  @Post('pubsub-publish')
  @ApiOperation({ summary: '测试发布订阅' })
  @ApiResponse({ status: 200, description: '事件发布成功' })
  async testPubSubPublish(@Body() body: { channel: string; message: any }) {
    // 这里应该调用实际的发布订阅服务，暂时返回模拟结果
    return {
      success: true,
      channel: body.channel,
      subscriberCount: Math.floor(Math.random() * 10) + 1,
      publishedAt: new Date().toISOString(),
      message: '事件发布成功'
    };
  }

  /**
   * 全局事件发布测试
   */
  @Post('pubsub-global-event')
  @ApiOperation({ summary: '测试全局事件发布' })
  @ApiResponse({ status: 200, description: '全局事件发布成功' })
  async testGlobalEvent(@Body() body: { eventType: string; data: any }) {
    // 这里应该调用实际的发布订阅服务，暂时返回模拟结果
    return {
      success: true,
      eventType: body.eventType,
      channel: `global:${body.eventType}`,
      subscriberCount: Math.floor(Math.random() * 20) + 5,
      publishedAt: new Date().toISOString(),
      message: '全局事件发布成功'
    };
  }

  /**
   * 分布式锁获取测试
   */
  @Post('lock-acquire')
  @ApiOperation({ summary: '测试分布式锁获取' })
  @ApiResponse({ status: 200, description: '锁获取成功' })
  async testLockAcquire(@Body() body: { key: string; ttl: number }) {
    // 这里应该调用实际的锁服务，暂时返回模拟结果
    const lockId = `lock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    return {
      success: true,
      lockId,
      key: body.key,
      ttl: body.ttl,
      acquiredAt: new Date().toISOString(),
      message: '锁获取成功'
    };
  }

  /**
   * 分布式锁释放测试
   */
  @Post('lock-release')
  @ApiOperation({ summary: '测试分布式锁释放' })
  @ApiResponse({ status: 200, description: '锁释放成功' })
  async testLockRelease(@Body() body: { key: string; lockId: string }) {
    // 这里应该调用实际的锁服务，暂时返回模拟结果
    return {
      success: true,
      key: body.key,
      lockId: body.lockId,
      releasedAt: new Date().toISOString(),
      message: '锁释放成功'
    };
  }

  /**
   * 布隆过滤器创建测试
   */
  @Post('bloom-create')
  @ApiOperation({ summary: '测试布隆过滤器创建' })
  @ApiResponse({ status: 200, description: '布隆过滤器创建成功' })
  async testBloomCreate(@Body() body: { filterName: string; expectedElements: number; falsePositiveRate: number }) {
    // 这里应该调用实际的布隆过滤器服务，暂时返回模拟结果
    return {
      success: true,
      filterName: body.filterName,
      config: {
        expectedElements: body.expectedElements,
        falsePositiveRate: body.falsePositiveRate,
        bitSize: Math.ceil(-body.expectedElements * Math.log(body.falsePositiveRate) / Math.pow(Math.log(2), 2)),
        hashCount: Math.ceil(-Math.log(body.falsePositiveRate) / Math.log(2))
      },
      createdAt: new Date().toISOString(),
      message: '布隆过滤器创建成功'
    };
  }

  /**
   * 布隆过滤器添加元素测试
   */
  @Post('bloom-add')
  @ApiOperation({ summary: '测试布隆过滤器添加元素' })
  @ApiResponse({ status: 200, description: '元素添加成功' })
  async testBloomAdd(@Body() body: { filterName: string; element: string }) {
    // 这里应该调用实际的布隆过滤器服务，暂时返回模拟结果
    return {
      success: true,
      filterName: body.filterName,
      element: body.element,
      addedAt: new Date().toISOString(),
      message: '元素添加成功'
    };
  }

  /**
   * 布隆过滤器检查元素测试
   */
  @Get('bloom-contains/:filterName/:element')
  @ApiOperation({ summary: '测试布隆过滤器检查元素' })
  @ApiParam({ name: 'filterName', description: '过滤器名称' })
  @ApiParam({ name: 'element', description: '检查元素' })
  @ApiResponse({ status: 200, description: '元素检查完成' })
  async testBloomContains(@Param('filterName') filterName: string, @Param('element') element: string) {
    // 这里应该调用实际的布隆过滤器服务，暂时返回模拟结果
    const mightContain = Math.random() > 0.3; // 70%概率返回true，模拟布隆过滤器行为

    return {
      filterName,
      element,
      mightContain,
      checkedAt: new Date().toISOString(),
      message: mightContain ? '元素可能存在' : '元素不存在'
    };
  }

  /**
   * 数据类型测试
   */
  @Post('datatype-test')
  @ApiOperation({ summary: '测试数据类型支持' })
  @ApiResponse({ status: 200, description: '数据类型测试成功' })
  async testDataType(@Body() body: { key: string; value: any; dataType?: string }) {
    // 设置默认值：如果没有指定dataType，默认使用server
    const dataType = body.dataType || 'server';

    // 获取当前环境和服务器ID
    const environment = process.env.NODE_ENV || 'development';
    const serverId = process.env.SERVER_ID || 'serverdev';

    // 构建正确的前缀格式
    let prefix: string;
    if (dataType === 'server') {
      prefix = `${environment}:fm:${serverId}:auth:${body.key}`;
    } else {
      prefix = `${environment}:fm:${dataType}:auth:${body.key}`;
    }

    return {
      success: true,
      key: body.key,
      dataType: dataType,
      prefix: prefix,
      storedAt: new Date().toISOString(),
      message: `${dataType}类型数据存储成功`
    };
  }

  /**
   * 数据类型隔离测试
   */
  @Get('datatype-isolation-test')
  @ApiOperation({ summary: '测试数据类型隔离' })
  @ApiResponse({ status: 200, description: '数据类型隔离验证成功' })
  async testDataTypeIsolation() {
    // 这里应该验证实际的数据隔离，暂时返回模拟结果
    return {
      success: true,
      isolation: {
        server: { keys: 15, prefix: 'test:fm:server1:auth:*' },
        global: { keys: 3, prefix: 'test:fm:global:auth:*' },
        cross: { keys: 2, prefix: 'test:fm:cross:auth:*' }
      },
      verifiedAt: new Date().toISOString(),
      message: '数据类型隔离验证成功'
    };
  }
}
