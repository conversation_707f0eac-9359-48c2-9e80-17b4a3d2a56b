import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  UseInterceptors,
  ClassSerializerInterceptor,
  HttpCode,
  HttpStatus,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';

import { AdminService } from './admin.service';
import { JwtAuthGuard } from '../../infrastructure/guards/jwt-auth.guard';
import { RolesGuard } from '../../infrastructure/guards/roles.guard';
import { PermissionsGuard } from '../../infrastructure/guards/permissions.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { AdminPermissions } from '../../common/decorators/permissions.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { ApiResponseDto } from '../../common/dto/response.dto';
import { User } from '../../domain/users/entities/user.entity';

// DTOs
import { AdminDashboardDto } from './dto/admin-dashboard.dto';
import { SystemStatusDto } from './dto/system-status.dto';
import { AdminActionDto } from './dto/admin-action.dto';

@ApiTags('管理员')
@Controller('admin')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
@UseInterceptors(ClassSerializerInterceptor)
@ApiBearerAuth('JWT-auth')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Get('dashboard')
  @Roles('admin', 'super_admin')
  @AdminPermissions.systemAdmin()
  @ApiOperation({ summary: '获取管理员仪表板' })
  @ApiResponse({
    status: 200,
    description: '获取仪表板数据成功',
    type: AdminDashboardDto,
  })
  async getDashboard(
    @CurrentUser() user: User,
  ): Promise<ApiResponseDto<AdminDashboardDto>> {
    const dashboard = await this.adminService.getDashboard(user.id);
    
    return {
      success: true,
      data: dashboard,
      message: '获取仪表板数据成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('system/status')
  @Roles('admin', 'super_admin')
  @AdminPermissions.systemAdmin()
  @ApiOperation({ summary: '获取系统状态' })
  @ApiResponse({
    status: 200,
    description: '获取系统状态成功',
    type: SystemStatusDto,
  })
  async getSystemStatus(): Promise<ApiResponseDto<SystemStatusDto>> {
    const status = await this.adminService.getSystemStatus();
    
    return {
      success: true,
      data: status,
      message: '获取系统状态成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('system/health')
  @Roles('admin', 'super_admin')
  @AdminPermissions.systemAdmin()
  @ApiOperation({ summary: '获取系统健康状态' })
  @ApiResponse({
    status: 200,
    description: '获取系统健康状态成功',
  })
  async getSystemHealth(): Promise<ApiResponseDto<any>> {
    const health = await this.adminService.getSystemHealth();
    
    return {
      success: true,
      data: health,
      message: '获取系统健康状态成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('system/metrics')
  @Roles('admin', 'super_admin')
  @AdminPermissions.systemAdmin()
  @ApiOperation({ summary: '获取系统指标' })
  @ApiQuery({ name: 'timeRange', required: false, description: '时间范围' })
  @ApiResponse({
    status: 200,
    description: '获取系统指标成功',
  })
  async getSystemMetrics(
    @Query('timeRange') timeRange?: string,
  ): Promise<ApiResponseDto<any>> {
    const metrics = await this.adminService.getSystemMetrics(timeRange);
    
    return {
      success: true,
      data: metrics,
      message: '获取系统指标成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('actions/maintenance')
  @HttpCode(HttpStatus.OK)
  @Roles('super_admin')
  @AdminPermissions.systemAdmin()
  @ApiOperation({ summary: '执行系统维护' })
  @ApiResponse({
    status: 200,
    description: '系统维护执行成功',
  })
  async performMaintenance(
    @CurrentUser() user: User,
    @Body(ValidationPipe) actionDto: AdminActionDto,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.adminService.performMaintenance(user.id, actionDto);
    
    return {
      success: true,
      data: result,
      message: '系统维护执行成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('actions/backup')
  @HttpCode(HttpStatus.OK)
  @Roles('super_admin')
  @AdminPermissions.systemAdmin()
  @ApiOperation({ summary: '创建系统备份' })
  @ApiResponse({
    status: 200,
    description: '系统备份创建成功',
  })
  async createBackup(
    @CurrentUser() user: User,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.adminService.createBackup(user.id);
    
    return {
      success: true,
      data: result,
      message: '系统备份创建成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('logs/system')
  @Roles('admin', 'super_admin')
  @AdminPermissions.auditAdmin()
  @ApiOperation({ summary: '获取系统日志' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'level', required: false, description: '日志级别' })
  @ApiQuery({ name: 'startDate', required: false, description: '开始日期' })
  @ApiQuery({ name: 'endDate', required: false, description: '结束日期' })
  @ApiResponse({
    status: 200,
    description: '获取系统日志成功',
  })
  async getSystemLogs(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
    @Query('level') level?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<ApiResponseDto<any>> {
    const logs = await this.adminService.getSystemLogs({
      page,
      limit,
      level,
      startDate,
      endDate,
    });
    
    return {
      success: true,
      data: logs,
      message: '获取系统日志成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('settings')
  @Roles('admin', 'super_admin')
  @AdminPermissions.systemAdmin()
  @ApiOperation({ summary: '获取系统设置' })
  @ApiResponse({
    status: 200,
    description: '获取系统设置成功',
  })
  async getSystemSettings(): Promise<ApiResponseDto<any>> {
    const settings = await this.adminService.getSystemSettings();
    
    return {
      success: true,
      data: settings,
      message: '获取系统设置成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Put('settings')
  @Roles('super_admin')
  @AdminPermissions.systemAdmin()
  @ApiOperation({ summary: '更新系统设置' })
  @ApiResponse({
    status: 200,
    description: '系统设置更新成功',
  })
  async updateSystemSettings(
    @CurrentUser() user: User,
    @Body() settings: any,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.adminService.updateSystemSettings(user.id, settings);
    
    return {
      success: true,
      data: result,
      message: '系统设置更新成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('notifications')
  @Roles('admin', 'super_admin')
  @AdminPermissions.systemAdmin()
  @ApiOperation({ summary: '获取管理员通知' })
  @ApiResponse({
    status: 200,
    description: '获取管理员通知成功',
  })
  async getAdminNotifications(
    @CurrentUser() user: User,
  ): Promise<ApiResponseDto<any>> {
    const notifications = await this.adminService.getAdminNotifications(user.id);
    
    return {
      success: true,
      data: notifications,
      message: '获取管理员通知成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('notifications/:id/read')
  @HttpCode(HttpStatus.OK)
  @Roles('admin', 'super_admin')
  @AdminPermissions.systemAdmin()
  @ApiOperation({ summary: '标记通知为已读' })
  @ApiParam({ name: 'id', description: '通知ID' })
  @ApiResponse({
    status: 200,
    description: '通知标记成功',
  })
  async markNotificationAsRead(
    @CurrentUser() user: User,
    @Param('id') notificationId: string,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.adminService.markNotificationAsRead(user.id, notificationId);
    
    return {
      success: true,
      data: result,
      message: '通知标记成功',
      timestamp: new Date().toISOString(),
    };
  }
}
