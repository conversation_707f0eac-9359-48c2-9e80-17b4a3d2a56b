import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsBoolean, IsObject, IsArray, IsOptional } from 'class-validator';

export class DatabaseStatusDto {
  @ApiProperty({ description: '数据库状态' })
  @IsString()
  status: string;

  @ApiProperty({ description: '连接数' })
  @IsNumber()
  connections: number;

  @ApiProperty({ description: '响应时间(ms)' })
  @IsNumber()
  responseTime: number;

  @ApiProperty({ description: '数据库大小(MB)' })
  @IsNumber()
  size: number;

  @ApiProperty({ description: '最后检查时间' })
  lastCheck: Date;
}

export class RedisStatusDto {
  @ApiProperty({ description: 'Redis状态' })
  @IsString()
  status: string;

  @ApiProperty({ description: '已用内存(MB)' })
  @IsNumber()
  usedMemory: number;

  @ApiProperty({ description: '连接的客户端数' })
  @IsNumber()
  connectedClients: number;

  @ApiProperty({ description: '命中率(%)' })
  @IsNumber()
  hitRate: number;

  @ApiProperty({ description: '最后检查时间' })
  lastCheck: Date;
}

export class ServiceStatusDto {
  @ApiProperty({ description: '服务名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '服务状态' })
  @IsString()
  status: string;

  @ApiProperty({ description: '版本' })
  @IsString()
  version: string;

  @ApiProperty({ description: '启动时间' })
  startTime: Date;

  @ApiProperty({ description: '运行时间(秒)' })
  @IsNumber()
  uptime: number;

  @ApiProperty({ description: 'CPU使用率(%)' })
  @IsNumber()
  cpuUsage: number;

  @ApiProperty({ description: '内存使用(MB)' })
  @IsNumber()
  memoryUsage: number;
}

export class ExternalServiceDto {
  @ApiProperty({ description: '服务名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '服务URL' })
  @IsString()
  url: string;

  @ApiProperty({ description: '状态' })
  @IsString()
  status: string;

  @ApiProperty({ description: '响应时间(ms)' })
  @IsNumber()
  responseTime: number;

  @ApiProperty({ description: '最后检查时间' })
  lastCheck: Date;

  @ApiProperty({ description: '错误信息', required: false })
  @IsOptional()
  @IsString()
  error?: string;
}

export class SystemResourcesDto {
  @ApiProperty({ description: 'CPU使用率(%)' })
  @IsNumber()
  cpuUsage: number;

  @ApiProperty({ description: '内存使用率(%)' })
  @IsNumber()
  memoryUsage: number;

  @ApiProperty({ description: '可用内存(MB)' })
  @IsNumber()
  availableMemory: number;

  @ApiProperty({ description: '总内存(MB)' })
  @IsNumber()
  totalMemory: number;

  @ApiProperty({ description: '磁盘使用率(%)' })
  @IsNumber()
  diskUsage: number;

  @ApiProperty({ description: '可用磁盘空间(GB)' })
  @IsNumber()
  availableDisk: number;

  @ApiProperty({ description: '总磁盘空间(GB)' })
  @IsNumber()
  totalDisk: number;

  @ApiProperty({ description: '网络入流量(MB/s)' })
  @IsNumber()
  networkIn: number;

  @ApiProperty({ description: '网络出流量(MB/s)' })
  @IsNumber()
  networkOut: number;

  @ApiProperty({ description: '负载平均值' })
  @IsArray()
  loadAverage: number[];
}

export class PerformanceMetricsDto {
  @ApiProperty({ description: '平均响应时间(ms)' })
  @IsNumber()
  averageResponseTime: number;

  @ApiProperty({ description: '每秒请求数' })
  @IsNumber()
  requestsPerSecond: number;

  @ApiProperty({ description: '错误率(%)' })
  @IsNumber()
  errorRate: number;

  @ApiProperty({ description: '吞吐量(MB/s)' })
  @IsNumber()
  throughput: number;

  @ApiProperty({ description: '并发用户数' })
  @IsNumber()
  concurrentUsers: number;

  @ApiProperty({ description: '活跃连接数' })
  @IsNumber()
  activeConnections: number;
}

export class SecurityStatusDto {
  @ApiProperty({ description: '安全状态' })
  @IsString()
  status: string;

  @ApiProperty({ description: '今日安全事件数' })
  @IsNumber()
  securityEventsToday: number;

  @ApiProperty({ description: '被阻止的IP数' })
  @IsNumber()
  blockedIPs: number;

  @ApiProperty({ description: '失败登录尝试数' })
  @IsNumber()
  failedLoginAttempts: number;

  @ApiProperty({ description: '可疑活动数' })
  @IsNumber()
  suspiciousActivities: number;

  @ApiProperty({ description: 'SSL证书状态' })
  @IsString()
  sslStatus: string;

  @ApiProperty({ description: 'SSL证书过期时间' })
  @IsOptional()
  sslExpiry?: Date;
}

export class BackupStatusDto {
  @ApiProperty({ description: '最后备份时间' })
  @IsOptional()
  lastBackup?: Date;

  @ApiProperty({ description: '备份状态' })
  @IsString()
  status: string;

  @ApiProperty({ description: '备份大小(MB)' })
  @IsOptional()
  @IsNumber()
  backupSize?: number;

  @ApiProperty({ description: '下次计划备份时间' })
  @IsOptional()
  nextScheduledBackup?: Date;

  @ApiProperty({ description: '备份保留天数' })
  @IsNumber()
  retentionDays: number;

  @ApiProperty({ description: '可用备份数' })
  @IsNumber()
  availableBackups: number;
}

export class SystemStatusDto {
  @ApiProperty({ description: '整体系统状态' })
  @IsString()
  overallStatus: string;

  @ApiProperty({ description: '系统正常运行时间(%)' })
  @IsNumber()
  uptime: number;

  @ApiProperty({ description: '服务启动时间' })
  startTime: Date;

  @ApiProperty({ description: '当前时间' })
  currentTime: Date;

  @ApiProperty({ description: '数据库状态', type: DatabaseStatusDto })
  database: DatabaseStatusDto;

  @ApiProperty({ description: 'Redis状态', type: RedisStatusDto })
  redis: RedisStatusDto;

  @ApiProperty({ description: '服务状态列表', type: [ServiceStatusDto] })
  @IsArray()
  services: ServiceStatusDto[];

  @ApiProperty({ description: '外部服务状态', type: [ExternalServiceDto] })
  @IsArray()
  externalServices: ExternalServiceDto[];

  @ApiProperty({ description: '系统资源', type: SystemResourcesDto })
  resources: SystemResourcesDto;

  @ApiProperty({ description: '性能指标', type: PerformanceMetricsDto })
  performance: PerformanceMetricsDto;

  @ApiProperty({ description: '安全状态', type: SecurityStatusDto })
  security: SecurityStatusDto;

  @ApiProperty({ description: '备份状态', type: BackupStatusDto })
  backup: BackupStatusDto;

  @ApiProperty({ description: '系统版本' })
  @IsString()
  version: string;

  @ApiProperty({ description: '环境' })
  @IsString()
  environment: string;

  @ApiProperty({ description: '配置状态' })
  @IsString()
  configStatus: string;

  @ApiProperty({ description: '最后健康检查时间' })
  lastHealthCheck: Date;
}
