import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString, IsArray, IsObject, IsOptional } from 'class-validator';

export class OverviewDto {
  @ApiProperty({ description: '总用户数' })
  @IsNumber()
  totalUsers: number;

  @ApiProperty({ description: '活跃用户数' })
  @IsNumber()
  activeUsers: number;

  @ApiProperty({ description: '今日新用户数' })
  @IsNumber()
  newUsersToday: number;

  @ApiProperty({ description: '活跃会话数' })
  @IsNumber()
  activeSessions: number;

  @ApiProperty({ description: '安全警告数' })
  @IsNumber()
  securityAlerts: number;

  @ApiProperty({ description: '系统健康状态' })
  @IsString()
  systemHealth: string;
}

export class UserMetricsDto {
  @ApiProperty({ description: '总用户数' })
  @IsNumber()
  totalUsers: number;

  @ApiProperty({ description: '活跃用户数' })
  @IsNumber()
  activeUsers: number;

  @ApiProperty({ description: '非活跃用户数' })
  @IsNumber()
  inactiveUsers: number;

  @ApiProperty({ description: '今日新用户数' })
  @IsNumber()
  newUsersToday: number;

  @ApiProperty({ description: '本周新用户数' })
  @IsNumber()
  newUsersThisWeek: number;

  @ApiProperty({ description: '本月新用户数' })
  @IsNumber()
  newUsersThisMonth: number;

  @ApiProperty({ description: '用户增长率(%)' })
  @IsNumber()
  userGrowthRate: number;
}

export class SessionMetricsDto {
  @ApiProperty({ description: '活跃会话数' })
  @IsNumber()
  activeSessions: number;

  @ApiProperty({ description: '总会话数' })
  @IsNumber()
  totalSessions: number;

  @ApiProperty({ description: '平均会话时长(秒)' })
  @IsNumber()
  averageSessionDuration: number;

  @ApiProperty({ description: '今日会话数' })
  @IsNumber()
  sessionsToday: number;

  @ApiProperty({ description: '设备类型分布' })
  @IsObject()
  deviceBreakdown: {
    web: number;
    mobile: number;
    desktop: number;
  };
}

export class SecurityMetricsDto {
  @ApiProperty({ description: '总警告数' })
  @IsNumber()
  totalAlerts: number;

  @ApiProperty({ description: '严重警告数' })
  @IsNumber()
  criticalAlerts: number;

  @ApiProperty({ description: '已解决警告数' })
  @IsNumber()
  resolvedAlerts: number;

  @ApiProperty({ description: '失败登录尝试数' })
  @IsNumber()
  failedLoginAttempts: number;

  @ApiProperty({ description: '被阻止的IP数' })
  @IsNumber()
  blockedIPs: number;

  @ApiProperty({ description: 'MFA采用率(%)' })
  @IsNumber()
  mfaAdoptionRate: number;
}

export class SystemMetricsDto {
  @ApiProperty({ description: 'CPU使用率(%)' })
  @IsNumber()
  cpuUsage: number;

  @ApiProperty({ description: '内存使用率(%)' })
  @IsNumber()
  memoryUsage: number;

  @ApiProperty({ description: '磁盘使用率(%)' })
  @IsNumber()
  diskUsage: number;

  @ApiProperty({ description: '网络流量(MB/s)' })
  @IsNumber()
  networkTraffic: number;

  @ApiProperty({ description: '响应时间(ms)' })
  @IsNumber()
  responseTime: number;

  @ApiProperty({ description: '系统正常运行时间(%)' })
  @IsNumber()
  uptime: number;
}

export class RecentActivityDto {
  @ApiProperty({ description: '活动ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: '活动类型' })
  @IsString()
  type: string;

  @ApiProperty({ description: '用户信息' })
  @IsOptional()
  @IsObject()
  user?: any;

  @ApiProperty({ description: '活动描述' })
  @IsString()
  description: string;

  @ApiProperty({ description: '时间戳' })
  timestamp: Date;

  @ApiProperty({ description: 'IP地址' })
  @IsOptional()
  @IsString()
  ipAddress?: string;
}

export class SystemAlertDto {
  @ApiProperty({ description: '警告ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: '警告类型' })
  @IsString()
  type: string;

  @ApiProperty({ description: '严重程度' })
  @IsString()
  severity: string;

  @ApiProperty({ description: '警告消息' })
  @IsString()
  message: string;

  @ApiProperty({ description: '时间戳' })
  timestamp: Date;
}

export class AdminDashboardDto {
  @ApiProperty({ description: '概览数据', type: OverviewDto })
  overview: OverviewDto;

  @ApiProperty({ description: '用户指标', type: UserMetricsDto })
  userMetrics: UserMetricsDto;

  @ApiProperty({ description: '会话指标', type: SessionMetricsDto })
  sessionMetrics: SessionMetricsDto;

  @ApiProperty({ description: '安全指标', type: SecurityMetricsDto })
  securityMetrics: SecurityMetricsDto;

  @ApiProperty({ description: '系统指标', type: SystemMetricsDto })
  systemMetrics: SystemMetricsDto;

  @ApiProperty({ description: '最近活动', type: [RecentActivityDto] })
  @IsArray()
  recentActivities: RecentActivityDto[];

  @ApiProperty({ description: '系统警告', type: [SystemAlertDto] })
  @IsArray()
  alerts: SystemAlertDto[];
}
