import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// 控制器
import { AdminController } from '../../modules/administration/controllers/admin.controller';
import { SystemController } from '../../modules/administration/controllers/system.controller';
import { UserManagementController } from '../../modules/administration/controllers/user-management.controller';
import { AuditController } from '../../modules/administration/controllers/audit.controller';
import { SecurityController } from '../../modules/administration/controllers/security.controller';
import { StatisticsController } from '../../modules/administration/controllers/statistics.controller';

// 服务
import { AdminService } from '../../modules/administration/services/admin.service';
import { SystemService } from '../../modules/administration/services/system.service';
import { UserManagementService } from '../../modules/administration/services/user-management.service';
import { AuditManagementService } from '../../modules/administration/services/audit-management.service';
import { SecurityManagementService } from '../../modules/administration/services/security-management.service';
import { StatisticsService } from '../../modules/administration/services/statistics.service';

// 实体
import { User, UserSchema } from '../../modules/user-management/entities/user.entity';
import { Role, RoleSchema } from '../../modules/rbac/entities/role.entity';
import { Permission, PermissionSchema } from '../../modules/rbac/entities/permission.entity';
import { Session, SessionSchema } from '../../modules/session-management/entities/session.entity';
import { AuditLog, AuditLogSchema } from '../../modules/security/entities/audit-log.entity';

// 依赖模块
import { UsersModule } from '../../domain/users/users.module';
import { RolesModule } from '../../domain/roles/roles.module';
import { PermissionsModule } from '../../domain/permissions/permissions.module';
import { SecurityModule } from '../../core/security/security.module';
import { SessionModule } from '../../core/session/session.module';
import { CoreModule } from '../../core/shared/core.module';

@Module({
  imports: [
    // 数据库模型 (AdminService 需要直接访问多个模型)
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: Role.name, schema: RoleSchema },
      { name: Permission.name, schema: PermissionSchema },
      { name: Session.name, schema: SessionSchema },
      { name: AuditLog.name, schema: AuditLogSchema },
    ]),

    // 依赖模块
    CoreModule,
    UsersModule,
    RolesModule,
    PermissionsModule,
    SecurityModule,
    SessionModule,
  ],
  controllers: [
    AdminController,
    SystemController,
    UserManagementController,
    AuditController,
    SecurityController,
    StatisticsController,
  ],
  providers: [
    AdminService,
    SystemService,
    UserManagementService,
    AuditManagementService,
    SecurityManagementService,
    StatisticsService,
  ],
  exports: [
    AdminService,
    SystemService,
    UserManagementService,
    AuditManagementService,
    SecurityManagementService,
    StatisticsService,
  ],
})
export class AdminModule {}
