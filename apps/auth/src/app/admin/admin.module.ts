import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// 控制器
import { AdminController } from './admin.controller';
import { SystemController } from './controllers/system.controller';
import { UserManagementController } from './controllers/user-management.controller';
import { AuditController } from './controllers/audit.controller';
import { SecurityController } from './controllers/security.controller';
import { StatisticsController } from './controllers/statistics.controller';

// 服务
import { AdminService } from './admin.service';
import { SystemService } from './services/system.service';
import { UserManagementService } from './services/user-management.service';
import { AuditManagementService } from './services/audit-management.service';
import { SecurityManagementService } from './services/security-management.service';
import { StatisticsService } from './services/statistics.service';

// 实体
import { User, UserSchema } from '../../domain/users/entities/user.entity';
import { Role, RoleSchema } from '../../domain/roles/entities/role.entity';
import { Permission, PermissionSchema } from '../../domain/permissions/entities/permission.entity';
import { Session, SessionSchema } from '../../core/session/entities/session.entity';
import { AuditLog, AuditLogSchema } from '../../core/security/entities/audit-log.entity';

// 依赖模块
import { UsersModule } from '../../domain/users/users.module';
import { RolesModule } from '../../domain/roles/roles.module';
import { PermissionsModule } from '../../domain/permissions/permissions.module';
import { SecurityModule } from '../../core/security/security.module';
import { SessionModule } from '../../core/session/session.module';
import { CoreModule } from '../../core/shared/core.module';

@Module({
  imports: [
    // 数据库模型 (AdminService 需要直接访问多个模型)
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: Role.name, schema: RoleSchema },
      { name: Permission.name, schema: PermissionSchema },
      { name: Session.name, schema: SessionSchema },
      { name: AuditLog.name, schema: AuditLogSchema },
    ]),

    // 依赖模块
    CoreModule,
    UsersModule,
    RolesModule,
    PermissionsModule,
    SecurityModule,
    SessionModule,
  ],
  controllers: [
    AdminController,
    SystemController,
    UserManagementController,
    AuditController,
    SecurityController,
    StatisticsController,
  ],
  providers: [
    AdminService,
    SystemService,
    UserManagementService,
    AuditManagementService,
    SecurityManagementService,
    StatisticsService,
  ],
  exports: [
    AdminService,
    SystemService,
    UserManagementService,
    AuditManagementService,
    SecurityManagementService,
    StatisticsService,
  ],
})
export class AdminModule {}
