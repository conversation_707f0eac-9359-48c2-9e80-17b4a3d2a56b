import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { User, UserDocument, UserStatus } from '@auth/modules/user-management/entities/user.entity';
import { Role, RoleDocument } from '@auth/modules/rbac/entities/role.entity';
import { Session, SessionDocument } from '@auth/modules/session-management/entities/session.entity';
import { SecurityService, SecurityEventType } from '@auth/modules/security/services/security.service';

@Injectable()
export class UserManagementService {
  private readonly logger = new Logger(UserManagementService.name);

  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(Role.name) private roleModel: Model<RoleDocument>,
    @InjectModel(Session.name) private sessionModel: Model<SessionDocument>,
    private readonly securityService: SecurityService,
  ) {}

  /**
   * 获取用户列表
   */
  async getUsers(options: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
    status?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<any> {
    this.logger.log('获取用户列表');

    try {
      const {
        page = 1,
        limit = 20,
        search,
        role,
        status,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = options;

      // 构建查询条件
      const query: any = {};

      if (search) {
        query.$or = [
          { username: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { 'profile.firstName': { $regex: search, $options: 'i' } },
          { 'profile.lastName': { $regex: search, $options: 'i' } },
        ];
      }

      if (role) {
        query.roles = role;
      }

      if (status) {
        query.status = status;
      }

      // 构建排序
      const sort: any = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // 执行查询
      const [users, total] = await Promise.all([
        this.userModel
          .find(query)
          .populate('roles', 'name displayName')
          .sort(sort)
          .skip((page - 1) * limit)
          .limit(limit)
          .select('-password -security.mfaSecret')
          .exec(),
        this.userModel.countDocuments(query),
      ]);

      return {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error('获取用户列表失败', error);
      throw error;
    }
  }

  /**
   * 获取用户详情
   */
  async getUserById(userId: string): Promise<User> {
    this.logger.log(`获取用户详情: ${userId}`);

    try {
      const user = await this.userModel
        .findById(userId)
        .populate('roles', 'name displayName description')
        .select('-password -security.mfaSecret')
        .exec();

      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      return user;
    } catch (error) {
      this.logger.error('获取用户详情失败', error);
      throw error;
    }
  }

  /**
   * 更新用户状态
   */
  async updateUserStatus(
    adminId: string,
    userId: string,
    status: string,
    reason?: string,
  ): Promise<User> {
    this.logger.log(`更新用户状态: ${userId} -> ${status}`);

    try {
      const user = await this.userModel.findById(userId);
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      const oldStatus = user.status;
      user.status = status as any;
      user.updatedAt = new Date();

      await user.save();

      // 记录操作日志
      await this.securityService.logSecurityEvent({
        type: SecurityEventType.USER_STATUS_CHANGE,
        userId: adminId,
        targetUserId: userId,
        details: {
          oldStatus,
          newStatus: status,
          reason,
        },
        ipAddress: 'admin-system',
        userAgent: 'admin-service',
      });

      // 如果用户被禁用，终止所有会话
      if (status === 'suspended' || status === 'banned') {
        await this.terminateUserSessions(userId);
      }

      this.logger.log(`用户状态更新成功: ${userId}`);
      return user;
    } catch (error) {
      this.logger.error('更新用户状态失败', error);
      throw error;
    }
  }

  /**
   * 分配用户角色
   */
  async assignUserRoles(
    adminId: string,
    userId: string,
    roleIds: string[],
  ): Promise<User> {
    this.logger.log(`分配用户角色: ${userId}`);

    try {
      const user = await this.userModel.findById(userId);
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      // 验证角色是否存在
      const roles = await this.roleModel.find({ _id: { $in: roleIds } });
      if (roles.length !== roleIds.length) {
        throw new BadRequestException('部分角色不存在');
      }

      const oldRoles = user.roles;
      user.roles = roleIds;
      user.updatedAt = new Date();

      await user.save();

      // 记录操作日志
      await this.securityService.logSecurityEvent({
        type: SecurityEventType.USER_ROLES_CHANGE,
        userId: adminId,
        targetUserId: userId,
        details: {
          oldRoles,
          newRoles: roleIds,
          roleNames: roles.map(r => r.name),
        },
        ipAddress: 'admin-system',
        userAgent: 'admin-service',
      });

      this.logger.log(`用户角色分配成功: ${userId}`);
      return user;
    } catch (error) {
      this.logger.error('分配用户角色失败', error);
      throw error;
    }
  }

  /**
   * 重置用户密码
   */
  async resetUserPassword(
    adminId: string,
    userId: string,
    newPassword: string,
    forceChange = true,
  ): Promise<void> {
    this.logger.log(`重置用户密码: ${userId}`);

    try {
      const user = await this.userModel.findById(userId);
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      // 这里应该使用密码服务来哈希密码
      // const hashedPassword = await this.passwordService.hashPassword(newPassword);

      // TODO: 实现密码重置功能
      // user.passwordHash = hashedPassword;
      user.security.lastPasswordChange = new Date();
      // user.security.mustChangePassword = forceChange; // 这个属性不存在
      user.updatedAt = new Date();

      await user.save();

      // 记录操作日志
      await this.securityService.logSecurityEvent({
        type: SecurityEventType.PASSWORD_RESET_BY_ADMIN,
        userId: adminId,
        targetUserId: userId,
        details: {
          forceChange,
        },
        ipAddress: 'admin-system',
        userAgent: 'admin-service',
      });

      // 终止用户所有会话，强制重新登录
      await this.terminateUserSessions(userId);

      this.logger.log(`用户密码重置成功: ${userId}`);
    } catch (error) {
      this.logger.error('重置用户密码失败', error);
      throw error;
    }
  }

  /**
   * 锁定用户账户
   */
  async lockUserAccount(
    adminId: string,
    userId: string,
    reason: string,
    duration?: number,
  ): Promise<void> {
    this.logger.log(`锁定用户账户: ${userId}`);

    try {
      const user = await this.userModel.findById(userId);
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      // TODO: 实现账户锁定功能
      // 这些属性在 SecuritySettings 中不存在，需要添加或使用现有的 lockedUntil
      // user.security.accountLocked = true;
      // user.security.lockReason = reason;
      // user.security.lockedAt = new Date();

      if (duration) {
        user.security.lockedUntil = new Date(Date.now() + duration * 1000);
      } else {
        user.security.lockedUntil = new Date(Date.now() + 24 * 60 * 60 * 1000); // 默认锁定24小时
      }

      user.updatedAt = new Date();
      await user.save();

      // 记录操作日志
      await this.securityService.logSecurityEvent({
        type: SecurityEventType.ACCOUNT_LOCKED_BY_ADMIN,
        userId: adminId,
        targetUserId: userId,
        details: {
          reason,
          duration,
        },
        ipAddress: 'admin-system',
        userAgent: 'admin-service',
      });

      // 终止用户所有会话
      await this.terminateUserSessions(userId);

      this.logger.log(`用户账户锁定成功: ${userId}`);
    } catch (error) {
      this.logger.error('锁定用户账户失败', error);
      throw error;
    }
  }

  /**
   * 解锁用户账户
   */
  async unlockUserAccount(adminId: string, userId: string): Promise<void> {
    this.logger.log(`解锁用户账户: ${userId}`);

    try {
      const user = await this.userModel.findById(userId);
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      // TODO: 实现账户解锁功能
      // 这些属性在 SecuritySettings 中不存在
      // user.security.accountLocked = false;
      // user.security.lockReason = null;
      // user.security.lockedAt = null;
      // user.security.lockExpiresAt = null;
      user.security.loginAttempts = 0;
      // 清除锁定时间
      user.security.lockedUntil = undefined;
      user.updatedAt = new Date();

      await user.save();

      // 记录操作日志
      await this.securityService.logSecurityEvent({
        type: SecurityEventType.ACCOUNT_UNLOCKED_BY_ADMIN,
        userId: adminId,
        targetUserId: userId,
        details: {},
        ipAddress: 'admin-system',
        userAgent: 'admin-service',
      });

      this.logger.log(`用户账户解锁成功: ${userId}`);
    } catch (error) {
      this.logger.error('解锁用户账户失败', error);
      throw error;
    }
  }

  /**
   * 禁用用户MFA
   */
  async disableUserMfa(adminId: string, userId: string, reason: string): Promise<void> {
    this.logger.log(`禁用用户MFA: ${userId}`);

    try {
      const user = await this.userModel.findById(userId);
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      user.security.mfaEnabled = false;
      user.security.mfaSecret = null;
      user.security.backupCodes = [];
      user.updatedAt = new Date();

      await user.save();

      // 记录操作日志
      await this.securityService.logSecurityEvent({
        type: SecurityEventType.MFA_DISABLED_BY_ADMIN,
        userId: adminId,
        targetUserId: userId,
        details: {
          reason,
        },
        ipAddress: 'admin-system',
        userAgent: 'admin-service',
      });

      this.logger.log(`用户MFA禁用成功: ${userId}`);
    } catch (error) {
      this.logger.error('禁用用户MFA失败', error);
      throw error;
    }
  }

  /**
   * 获取用户会话
   */
  async getUserSessions(userId: string): Promise<any[]> {
    this.logger.log(`获取用户会话: ${userId}`);

    try {
      const sessions = await this.sessionModel
        .find({ userId })
        .sort({ createdAt: -1 })
        .exec();

      return sessions.map(session => ({
        id: session._id,
        deviceInfo: session.deviceInfo,
        ipAddress: session.ipAddress,
        location: session.location,
        active: session.active,
        createdAt: session.createdAt,
        lastActivity: session.lastActivity,
        expiresAt: session.expiresAt,
      }));
    } catch (error) {
      this.logger.error('获取用户会话失败', error);
      throw error;
    }
  }

  /**
   * 终止用户会话
   */
  async terminateUserSession(
    adminId: string,
    userId: string,
    sessionId: string,
  ): Promise<void> {
    this.logger.log(`终止用户会话: ${userId}/${sessionId}`);

    try {
      const session = await this.sessionModel.findOne({
        _id: sessionId,
        userId,
      });

      if (!session) {
        throw new NotFoundException('会话不存在');
      }

      session.active = false;
      // TODO: 这些属性在 Session 实体中不存在，需要添加或使用现有属性
      // session.endedAt = new Date();
      // session.endReason = 'terminated_by_admin';
      await session.save();

      // 记录操作日志
      await this.securityService.logSecurityEvent({
        type: SecurityEventType.SESSION_TERMINATED_BY_ADMIN,
        userId: adminId,
        targetUserId: userId,
        details: {
          sessionId,
          deviceInfo: session.deviceInfo,
        },
        ipAddress: 'admin-system',
        userAgent: 'admin-service',
      });

      this.logger.log(`用户会话终止成功: ${sessionId}`);
    } catch (error) {
      this.logger.error('终止用户会话失败', error);
      throw error;
    }
  }

  /**
   * 终止用户所有会话
   */
  async terminateUserSessions(userId: string): Promise<void> {
    this.logger.log(`终止用户所有会话: ${userId}`);

    try {
      await this.sessionModel.updateMany(
        { userId, active: true },
        {
          active: false,
          endedAt: new Date(),
          endReason: 'terminated_by_admin',
        },
      );

      this.logger.log(`用户所有会话终止成功: ${userId}`);
    } catch (error) {
      this.logger.error('终止用户所有会话失败', error);
      throw error;
    }
  }

  /**
   * 删除用户（软删除）
   */
  async deleteUser(adminId: string, userId: string, reason: string): Promise<void> {
    this.logger.log(`删除用户: ${userId}`);

    try {
      const user = await this.userModel.findById(userId);
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      // 软删除
      user.status = UserStatus.DELETED;
      // TODO: deletedAt 属性在 User 实体中不存在，需要添加或使用其他方式
      // user.deletedAt = new Date();
      user.updatedAt = new Date();
      await user.save();

      // 终止所有会话
      await this.terminateUserSessions(userId);

      // 记录操作日志
      await this.securityService.logSecurityEvent({
        type: SecurityEventType.USER_DELETED_BY_ADMIN,
        userId: adminId,
        targetUserId: userId,
        details: {
          reason,
        },
        ipAddress: 'admin-system',
        userAgent: 'admin-service',
      });

      this.logger.log(`用户删除成功: ${userId}`);
    } catch (error) {
      this.logger.error('删除用户失败', error);
      throw error;
    }
  }

  /**
   * 恢复已删除的用户
   */
  async restoreUser(adminId: string, userId: string): Promise<void> {
    this.logger.log(`恢复用户: ${userId}`);

    try {
      const user = await this.userModel.findById(userId);
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      if (user.status !== 'deleted') {
        throw new BadRequestException('用户未被删除');
      }

      user.status = UserStatus.ACTIVE;
      // TODO: deletedAt 属性在 User 实体中不存在
      // user.deletedAt = null;
      user.updatedAt = new Date();
      await user.save();

      // 记录操作日志
      await this.securityService.logSecurityEvent({
        type: SecurityEventType.USER_RESTORED_BY_ADMIN,
        userId: adminId,
        targetUserId: userId,
        details: {},
        ipAddress: 'admin-system',
        userAgent: 'admin-service',
      });

      this.logger.log(`用户恢复成功: ${userId}`);
    } catch (error) {
      this.logger.error('恢复用户失败', error);
      throw error;
    }
  }

  /**
   * 批量操作用户
   */
  async bulkUpdateUsers(
    adminId: string,
    userIds: string[],
    operation: {
      type: 'status' | 'roles' | 'lock' | 'unlock' | 'delete';
      value?: any;
      reason?: string;
    },
  ): Promise<{ success: number; failed: number; errors: any[] }> {
    this.logger.log(`批量操作用户: ${operation.type}, 数量: ${userIds.length}`);

    const results = {
      success: 0,
      failed: 0,
      errors: [],
    };

    for (const userId of userIds) {
      try {
        switch (operation.type) {
          case 'status':
            await this.updateUserStatus(adminId, userId, operation.value, operation.reason);
            break;
          case 'roles':
            await this.assignUserRoles(adminId, userId, operation.value);
            break;
          case 'lock':
            await this.lockUserAccount(adminId, userId, operation.reason);
            break;
          case 'unlock':
            await this.unlockUserAccount(adminId, userId);
            break;
          case 'delete':
            await this.deleteUser(adminId, userId, operation.reason);
            break;
        }
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          userId,
          error: error.message,
        });
      }
    }

    this.logger.log(`批量操作完成: 成功 ${results.success}, 失败 ${results.failed}`);
    return results;
  }
}
