import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';

import { JwtAuthGuard } from '../../../modules/security/guards/jwt-auth.guard';
import { RolesGuard } from '../../../modules/rbac/guards/roles.guard';
import { Roles } from '../../../shared/decorators/roles.decorator';
import { AdminPermissions } from '../../../shared/decorators/permissions.decorator';

@ApiTags('系统管理')
@Controller('admin/system')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class SystemController {
  @Get('info')
  @Roles('admin', 'super_admin')
  @AdminPermissions.systemAdmin()
  @ApiOperation({ summary: '获取系统信息' })
  getSystemInfo() {
    return {
      success: true,
      message: '系统控制器已创建，功能开发中...',
    };
  }
}
