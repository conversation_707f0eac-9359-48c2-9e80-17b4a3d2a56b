import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';

import { JwtAuthGuard } from '../../../infrastructure/guards/jwt-auth.guard';
import { RolesGuard } from '../../../infrastructure/guards/roles.guard';
import { Roles } from '../../../common/decorators/roles.decorator';
import { AdminPermissions } from '../../../common/decorators/permissions.decorator';

@ApiTags('用户管理')
@Controller('admin/users')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class UserManagementController {
  @Get()
  @Roles('admin', 'super_admin')
  @AdminPermissions.userAdmin()
  @ApiOperation({ summary: '获取用户列表' })
  getUsers() {
    return {
      success: true,
      message: '用户管理控制器已创建，功能开发中...',
    };
  }
}
