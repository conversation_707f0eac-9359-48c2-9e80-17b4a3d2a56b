import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';

import { JwtAuthGuard } from '../../../modules/security/guards/jwt-auth.guard';
import { RolesGuard } from '../../../modules/rbac/guards/roles.guard';
import { Roles } from '../../../shared/decorators/roles.decorator';
import { AdminPermissions } from '../../../shared/decorators/permissions.decorator';

@ApiTags('用户管理')
@Controller('admin/users')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class UserManagementController {
  @Get()
  @Roles('admin', 'super_admin')
  @AdminPermissions.userAdmin()
  @ApiOperation({ summary: '获取用户列表' })
  getUsers() {
    return {
      success: true,
      message: '用户管理控制器已创建，功能开发中...',
    };
  }
}
