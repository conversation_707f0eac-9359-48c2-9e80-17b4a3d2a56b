import {
  Injectable,
  Logger,
  ConflictException,
  NotFoundException,
  BadRequestException,
  Inject,
  forwardRef
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Role, RoleDocument } from '../entities/role.entity';
import { CreateRoleDto } from '../dto/create-role.dto';
import { UpdateRoleDto } from '../dto/update-role.dto';
import { PermissionsService } from './permissions.service';
import { Cacheable, CacheEvict, CachePut } from '@common/redis';

@Injectable()
export class RolesService {
  private readonly logger = new Logger(RolesService.name);

  constructor(
    @InjectModel(Role.name) private roleModel: Model<RoleDocument>,
    @Inject(forwardRef(() => PermissionsService))
    private permissionsService: PermissionsService,
  ) {}

  /**
   * 创建角色
   */
  async create(createRoleDto: CreateRoleDto): Promise<RoleDocument> {
    this.logger.log(`创建角色: ${createRoleDto.name}`);

    // 检查角色名称是否已存在
    const existingRole = await this.roleModel.findOne({ 
      name: createRoleDto.name.toLowerCase() 
    }).exec();
    
    if (existingRole) {
      throw new ConflictException('角色名称已存在');
    }

    // 验证权限是否存在
    if (createRoleDto.permissions && createRoleDto.permissions.length > 0) {
      await this.validatePermissions(createRoleDto.permissions);
    }

    // 验证继承角色是否存在
    if (createRoleDto.inherits && createRoleDto.inherits.length > 0) {
      await this.validateInheritedRoles(createRoleDto.inherits);
    }

    try {
      const role = new this.roleModel(createRoleDto);
      const savedRole = await role.save();
      
      this.logger.log(`角色创建成功: ${savedRole.name} (${savedRole.id})`);
      return savedRole;
    } catch (error) {
      this.logger.error('创建角色失败', error);
      throw new BadRequestException('创建角色失败');
    }
  }

  /**
   * 获取所有角色
   */
  async findAll(): Promise<RoleDocument[]> {
    return await this.roleModel.find({ enabled: true })
      .sort({ priority: -1, name: 1 })
      .exec();
  }

  /**
   * 根据ID获取角色
   */
  async findById(id: string): Promise<RoleDocument> {
    const role = await this.roleModel.findById(id).exec();
    if (!role) {
      throw new NotFoundException('角色不存在');
    }
    return role;
  }

  /**
   * 根据名称获取角色
   */
  async findByName(name: string): Promise<RoleDocument> {
    const role = await this.roleModel.findOne({ 
      name: name.toLowerCase(),
      enabled: true 
    }).exec();
    
    if (!role) {
      throw new NotFoundException('角色不存在');
    }
    return role;
  }

  /**
   * 获取多个角色
   */
  async findByNames(names: string[]): Promise<RoleDocument[]> {
    const lowerNames = names.map(name => name.toLowerCase());
    return await this.roleModel.find({ 
      name: { $in: lowerNames },
      enabled: true 
    }).exec();
  }

  /**
   * 更新角色
   */
  async update(id: string, updateRoleDto: UpdateRoleDto): Promise<RoleDocument> {
    this.logger.log(`更新角色: ${id}`);

    const role = await this.findById(id);

    // 检查是否尝试修改系统角色
    if (role.system && updateRoleDto.system === false) {
      throw new BadRequestException('不能修改系统角色的系统标识');
    }

    // 检查角色名称是否被其他角色使用
    if (updateRoleDto.name && updateRoleDto.name !== role.name) {
      const existingRole = await this.roleModel.findOne({ 
        name: updateRoleDto.name.toLowerCase(),
        _id: { $ne: id }
      }).exec();
      
      if (existingRole) {
        throw new ConflictException('角色名称已被其他角色使用');
      }
    }

    // 验证权限是否存在
    if (updateRoleDto.permissions) {
      await this.validatePermissions(updateRoleDto.permissions);
    }

    // 验证继承角色是否存在
    if (updateRoleDto.inherits) {
      await this.validateInheritedRoles(updateRoleDto.inherits);
      
      // 检查循环继承
      await this.checkCircularInheritance(role.name, updateRoleDto.inherits);
    }

    try {
      const updatedRole = await this.roleModel.findByIdAndUpdate(
        id,
        { $set: updateRoleDto },
        { new: true, runValidators: true }
      ).exec();

      if (!updatedRole) {
        throw new NotFoundException('角色不存在');
      }

      this.logger.log(`角色更新成功: ${updatedRole.name} (${updatedRole.id})`);
      return updatedRole;
    } catch (error) {
      this.logger.error('更新角色失败', error);
      throw new BadRequestException('更新角色失败');
    }
  }

  /**
   * 删除角色
   */
  async remove(id: string): Promise<void> {
    this.logger.log(`删除角色: ${id}`);

    const role = await this.findById(id);

    // 检查是否为系统角色
    if (role.system) {
      throw new BadRequestException('不能删除系统角色');
    }

    // 检查是否有用户使用此角色
    if (role.userCount > 0) {
      throw new BadRequestException('不能删除正在使用的角色');
    }

    await this.roleModel.findByIdAndDelete(id).exec();
    this.logger.log(`角色删除成功: ${role.name} (${role.id})`);
  }

  /**
   * 为角色添加权限
   */
  async addPermissions(roleId: string, permissions: string[]): Promise<RoleDocument> {
    this.logger.log(`为角色添加权限: ${roleId}`);

    const role = await this.findById(roleId);
    
    // 验证权限是否存在
    await this.validatePermissions(permissions);

    // 添加新权限（去重）
    const newPermissions = [...new Set([...role.permissions, ...permissions])];
    
    const updatedRole = await this.roleModel.findByIdAndUpdate(
      roleId,
      { $set: { permissions: newPermissions } },
      { new: true }
    ).exec();

    this.logger.log(`权限添加成功: ${roleId}, 权限数: ${permissions.length}`);
    return updatedRole!;
  }

  /**
   * 从角色移除权限
   */
  async removePermissions(roleId: string, permissions: string[]): Promise<RoleDocument> {
    this.logger.log(`从角色移除权限: ${roleId}`);

    const role = await this.findById(roleId);
    
    // 移除指定权限
    const updatedPermissions = role.permissions.filter(p => !permissions.includes(p));
    
    const updatedRole = await this.roleModel.findByIdAndUpdate(
      roleId,
      { $set: { permissions: updatedPermissions } },
      { new: true }
    ).exec();

    this.logger.log(`权限移除成功: ${roleId}, 权限数: ${permissions.length}`);
    return updatedRole!;
  }

  /**
   * 获取角色的所有权限（包括继承的权限）
   */
  async getRolePermissions(roleName: string): Promise<string[]> {
    const role = await this.findByName(roleName);
    const allPermissions = new Set<string>();

    // 添加直接权限
    role.permissions.forEach(permission => allPermissions.add(permission));

    // 递归添加继承的权限
    await this.addInheritedPermissions(role.inherits, allPermissions);

    return Array.from(allPermissions);
  }

  /**
   * 获取用户的所有权限
   */
  async getUserPermissions(roleNames: string[]): Promise<string[]> {
    const allPermissions = new Set<string>();

    for (const roleName of roleNames) {
      try {
        const rolePermissions = await this.getRolePermissions(roleName);
        rolePermissions.forEach(permission => allPermissions.add(permission));
      } catch (error) {
        this.logger.warn(`获取角色权限失败: ${roleName}`, error);
      }
    }

    return Array.from(allPermissions);
  }

  /**
   * 检查角色是否有指定权限
   */
  async checkRolePermission(roleNames: string[], resource: string, action: string): Promise<boolean> {
    const permission = `${resource}:${action}`;
    const userPermissions = await this.getUserPermissions(roleNames);
    
    return userPermissions.includes(permission) || userPermissions.includes('*');
  }

  /**
   * 获取系统角色
   */
  async getSystemRoles(): Promise<RoleDocument[]> {
    return await this.roleModel.find({ 
      system: true, 
      enabled: true 
    }).sort({ priority: -1 }).exec();
  }

  /**
   * 获取可分配角色
   */
  async getAssignableRoles(): Promise<RoleDocument[]> {
    return await this.roleModel.find({
      enabled: true,
      $or: [
        { 'constraints.maxUsers': { $exists: false } },
        { $expr: { $lt: ['$userCount', '$constraints.maxUsers'] } }
      ]
    }).sort({ priority: -1 }).exec();
  }

  /**
   * 更新角色用户数量
   */
  async updateUserCount(roleName: string, increment: number): Promise<void> {
    await this.roleModel.updateOne(
      { name: roleName.toLowerCase() },
      { $inc: { userCount: increment } }
    ).exec();
  }

  /**
   * 验证权限是否存在
   */
  private async validatePermissions(permissions: string[]): Promise<void> {
    // 通过 PermissionsService 验证权限
    for (const permissionName of permissions) {
      try {
        await this.permissionsService.findByName(permissionName);
      } catch (error) {
        throw new BadRequestException(`权限不存在: ${permissionName}`);
      }
    }
  }

  /**
   * 验证继承角色是否存在
   */
  private async validateInheritedRoles(roleNames: string[]): Promise<void> {
    const existingRoles = await this.roleModel.find({
      name: { $in: roleNames.map(name => name.toLowerCase()) },
      enabled: true
    }).exec();

    const existingRoleNames = existingRoles.map(r => r.name);
    const invalidRoles = roleNames.filter(name => !existingRoleNames.includes(name.toLowerCase()));

    if (invalidRoles.length > 0) {
      throw new BadRequestException(`以下角色不存在: ${invalidRoles.join(', ')}`);
    }
  }

  /**
   * 检查循环继承
   */
  private async checkCircularInheritance(roleName: string, inherits: string[]): Promise<void> {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCircle = async (currentRole: string): Promise<boolean> => {
      if (recursionStack.has(currentRole)) {
        return true; // 发现循环
      }

      if (visited.has(currentRole)) {
        return false; // 已经检查过，没有循环
      }

      visited.add(currentRole);
      recursionStack.add(currentRole);

      try {
        const role = await this.findByName(currentRole);
        for (const inheritedRole of role.inherits) {
          if (await hasCircle(inheritedRole)) {
            return true;
          }
        }
      } catch (error) {
        // 角色不存在，跳过
      }

      recursionStack.delete(currentRole);
      return false;
    };

    // 检查新的继承关系是否会造成循环
    for (const inheritedRole of inherits) {
      if (await hasCircle(inheritedRole)) {
        throw new BadRequestException('检测到循环继承关系');
      }
    }
  }

  /**
   * 递归添加继承的权限
   */
  private async addInheritedPermissions(roleNames: string[], permissions: Set<string>): Promise<void> {
    for (const roleName of roleNames) {
      try {
        const role = await this.findByName(roleName);
        
        // 添加角色的直接权限
        role.permissions.forEach(permission => permissions.add(permission));
        
        // 递归添加继承的权限
        if (role.inherits.length > 0) {
          await this.addInheritedPermissions(role.inherits, permissions);
        }
      } catch (error) {
        this.logger.warn(`获取继承角色权限失败: ${roleName}`, error);
      }
    }
  }
}
