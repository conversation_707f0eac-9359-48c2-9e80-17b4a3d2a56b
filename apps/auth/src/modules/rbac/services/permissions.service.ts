import {
  Injectable,
  Logger,
  ConflictException,
  NotFoundException,
  BadRequestException,
  Inject
} from '@nestjs/common';
import { PermissionDocument } from '../entities/permission.entity';
import { IPermissionRepository, CreatePermissionData, UpdatePermissionData } from '../repositories/permission.repository';
import { CreatePermissionDto } from '../dto/create-permission.dto';
import { UpdatePermissionDto } from '../dto/update-permission.dto';
import { Cacheable, CacheEvict, CachePut } from '@common/redis';

@Injectable()
export class PermissionsService {
  private readonly logger = new Logger(PermissionsService.name);

  constructor(
    @Inject('IPermissionRepository') private permissionRepository: IPermissionRepository,
  ) {}

  /**
   * 创建权限
   */
  async create(createPermissionDto: CreatePermissionDto): Promise<PermissionDocument> {
    this.logger.log(`创建权限: ${createPermissionDto.resource}:${createPermissionDto.action}`);

    // 检查权限是否已存在
    const existingPermission = await this.permissionRepository.findByName(createPermissionDto.name);


    if (existingPermission) {
      throw new ConflictException('权限已存在');
    }

    // 验证依赖权限是否存在
    if (createPermissionDto.dependencies && createPermissionDto.dependencies.length > 0) {
      await this.validatePermissions(createPermissionDto.dependencies);
    }

    // 验证冲突权限是否存在
    if (createPermissionDto.conflicts && createPermissionDto.conflicts.length > 0) {
      await this.validatePermissions(createPermissionDto.conflicts);
    }

    try {
      const permissionData: CreatePermissionData = {
        name: createPermissionDto.name,
        displayName: createPermissionDto.displayName,
        description: createPermissionDto.description,
        resource: createPermissionDto.resource,
        action: createPermissionDto.action,
        category: createPermissionDto.category,
        level: createPermissionDto.level,
        enabled: createPermissionDto.enabled,
        system: createPermissionDto.system,
      };

      const savedPermission = await this.permissionRepository.create(permissionData);
      
      this.logger.log(`权限创建成功: ${savedPermission.name} (${savedPermission.id})`);
      return savedPermission;
    } catch (error) {
      this.logger.error('创建权限失败', error);
      throw new BadRequestException('创建权限失败');
    }
  }

  /**
   * 获取所有权限
   */
  async findAll(): Promise<PermissionDocument[]> {
    return await this.permissionRepository.findAll({ enabled: true });
  }

  /**
   * 根据ID获取权限
   */
  async findById(id: string): Promise<PermissionDocument> {
    const permission = await this.permissionRepository.findById(id);
    if (!permission) {
      throw new NotFoundException('权限不存在');
    }
    return permission;
  }

  /**
   * 根据名称获取权限
   */
  async findByName(name: string): Promise<PermissionDocument> {
    const permission = await this.permissionRepository.findByName(name);
    
    if (!permission) {
      throw new NotFoundException('权限不存在');
    }
    return permission;
  }

  /**
   * 根据分类获取权限
   */
  async findByCategory(category: string): Promise<PermissionDocument[]> {
    return await this.permissionRepository.findByCategory(category);
  }

  /**
   * 搜索权限
   */
  async search(query: string): Promise<PermissionDocument[]> {
    // Repository不支持全文搜索，使用名称模糊匹配
    return await this.permissionRepository.findAll({
      name: query,
      enabled: true
    });
  }

  /**
   * 更新权限
   */
  async update(id: string, updatePermissionDto: UpdatePermissionDto): Promise<PermissionDocument> {
    this.logger.log(`更新权限: ${id}`);

    const permission = await this.findById(id);

    // 检查是否尝试修改系统权限
    if (permission.system && updatePermissionDto.system === false) {
      throw new BadRequestException('不能修改系统权限的系统标识');
    }

    // 检查资源和操作组合是否被其他权限使用
    if (updatePermissionDto.resource || updatePermissionDto.action) {
      const resource = updatePermissionDto.resource || permission.resource;
      const action = updatePermissionDto.action || permission.action;
      
      const existingPermission = await this.permissionRepository.findByResourceAndAction(
        resource.toLowerCase(),
        action.toLowerCase()
      );

      // 检查是否是同一个权限
      if (existingPermission && existingPermission.id !== id) {
        throw new ConflictException('资源和操作组合已存在');
      }
    }

    // 验证冲突权限
    if (updatePermissionDto.conflicts) {
      await this.validatePermissions(updatePermissionDto.conflicts);
    }

    try {
      const updateData: UpdatePermissionData = {
        displayName: updatePermissionDto.displayName,
        description: updatePermissionDto.description,
        resource: updatePermissionDto.resource,
        action: updatePermissionDto.action,
        category: updatePermissionDto.category,
        level: updatePermissionDto.level,
        enabled: updatePermissionDto.enabled,
      };

      const updatedPermission = await this.permissionRepository.update(id, updateData);

      if (!updatedPermission) {
        throw new NotFoundException('权限更新失败');
      }

      if (!updatedPermission) {
        throw new NotFoundException('权限不存在');
      }

      this.logger.log(`权限更新成功: ${updatedPermission.name} (${updatedPermission.id})`);
      return updatedPermission;
    } catch (error) {
      this.logger.error('更新权限失败', error);
      throw new BadRequestException('更新权限失败');
    }
  }

  /**
   * 删除权限
   */
  async remove(id: string): Promise<void> {
    this.logger.log(`删除权限: ${id}`);

    const permission = await this.findById(id);

    // 检查是否为系统权限
    if (permission.system) {
      throw new BadRequestException('不能删除系统权限');
    }

    // 检查是否有角色使用此权限
    // TODO: 实现角色权限检查

    await this.permissionRepository.delete(id);
    this.logger.log(`权限删除成功: ${permission.name} (${permission.id})`);
  }

  /**
   * 获取系统权限
   */
  async getSystemPermissions(): Promise<PermissionDocument[]> {
    return await this.permissionRepository.findSystemPermissions();
  }

  /**
   * 批量创建权限
   */
  async createBatch(permissions: CreatePermissionDto[]): Promise<PermissionDocument[]> {
    this.logger.log(`批量创建权限: ${permissions.length}个`);

    const createdPermissions: PermissionDocument[] = [];
    const errors: string[] = [];

    for (const permissionDto of permissions) {
      try {
        const permission = await this.create(permissionDto);
        createdPermissions.push(permission);
      } catch (error) {
        errors.push(`${permissionDto.resource}:${permissionDto.action} - ${error.message}`);
      }
    }

    if (errors.length > 0) {
      this.logger.warn(`批量创建权限部分失败: ${errors.join('; ')}`);
    }

    this.logger.log(`批量创建权限完成: 成功 ${createdPermissions.length}个, 失败 ${errors.length}个`);
    return createdPermissions;
  }

  /**
   * 验证权限是否存在
   */
  private async validatePermissions(permissions: string[]): Promise<void> {
    // Repository不支持批量查询，需要逐个验证
    const existingPermissionNames: string[] = [];
    for (const permissionName of permissions) {
      const permission = await this.permissionRepository.findByName(permissionName);
      if (permission) {
        existingPermissionNames.push(permission.name);
      }
    }
    const invalidPermissions = permissions.filter(p => !existingPermissionNames.includes(p));

    if (invalidPermissions.length > 0) {
      throw new BadRequestException(`以下权限不存在: ${invalidPermissions.join(', ')}`);
    }
  }
}
