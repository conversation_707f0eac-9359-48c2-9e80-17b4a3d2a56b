import { ApiProperty } from '@nestjs/swagger';
import { 
  IsString, 
  IsOptional, 
  IsArray, 
  IsBoolean,
  IsNumber,
  <PERSON><PERSON>ength, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>,
  <PERSON>,
  Max,
  ValidateNested
} from 'class-validator';
import { Type } from 'class-transformer';

// 权限条件DTO
export class PermissionConditionsDto {
  @ApiProperty({ 
    description: '属性条件',
    example: { department: 'IT', level: 'senior' },
    required: false
  })
  @IsOptional()
  attributes?: Record<string, any>;

  @ApiProperty({ 
    description: '时间范围',
    example: { start: '09:00', end: '18:00' },
    required: false
  })
  @IsOptional()
  @ValidateNested()
  timeRange?: {
    start: string;
    end: string;
  };

  @ApiProperty({ 
    description: 'IP范围',
    example: ['***********/24', '10.0.0.0/8'],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  ipRange?: string[];

  @ApiProperty({ 
    description: '设备类型',
    example: ['desktop', 'mobile'],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  deviceTypes?: string[];

  @ApiProperty({ 
    description: '地理位置限制',
    example: { countries: ['CN', 'US'], regions: ['Beijing', 'Shanghai'] },
    required: false
  })
  @IsOptional()
  @ValidateNested()
  geoRestrictions?: {
    countries: string[];
    regions: string[];
  };
}

// 创建权限DTO
export class CreatePermissionDto {
  @ApiProperty({ 
    description: '资源类型',
    example: 'user',
    minLength: 2,
    maxLength: 50
  })
  @IsString()
  @MinLength(2, { message: '资源类型至少需要2个字符' })
  @MaxLength(50, { message: '资源类型不能超过50个字符' })
  @Matches(/^[a-zA-Z0-9_-]+$/, { 
    message: '资源类型只能包含字母、数字、下划线和连字符' 
  })
  resource: string;

  @ApiProperty({ 
    description: '操作类型',
    example: 'read',
    minLength: 2,
    maxLength: 50
  })
  @IsString()
  @MinLength(2, { message: '操作类型至少需要2个字符' })
  @MaxLength(50, { message: '操作类型不能超过50个字符' })
  @Matches(/^[a-zA-Z0-9_-]+$/, { 
    message: '操作类型只能包含字母、数字、下划线和连字符' 
  })
  action: string;

  @ApiProperty({ 
    description: '权限名称（可选，默认为resource:action）',
    example: 'user:read',
    required: false
  })
  @IsOptional()
  @IsString()
  @MinLength(3, { message: '权限名称至少需要3个字符' })
  @MaxLength(100, { message: '权限名称不能超过100个字符' })
  @Matches(/^[a-zA-Z0-9_:-]+$/, { 
    message: '权限名称只能包含字母、数字、下划线、连字符和冒号' 
  })
  name?: string;

  @ApiProperty({ 
    description: '权限显示名称',
    example: '查看用户',
    minLength: 2,
    maxLength: 100
  })
  @IsString()
  @MinLength(2, { message: '显示名称至少需要2个字符' })
  @MaxLength(100, { message: '显示名称不能超过100个字符' })
  displayName: string;

  @ApiProperty({ 
    description: '权限描述',
    example: '允许查看用户基本信息',
    maxLength: 500,
    required: false
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: '权限描述不能超过500个字符' })
  description?: string;

  @ApiProperty({ 
    description: '权限条件',
    type: PermissionConditionsDto,
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PermissionConditionsDto)
  conditions?: PermissionConditionsDto;

  @ApiProperty({ 
    description: '权限分类',
    example: 'user_management',
    default: 'general'
  })
  @IsOptional()
  @IsString()
  category?: string = 'general';

  @ApiProperty({ 
    description: '权限级别',
    example: 3,
    minimum: 1,
    maximum: 10,
    default: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  level?: number = 1;

  @ApiProperty({ 
    description: '是否危险权限',
    example: false,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  dangerous?: boolean = false;

  @ApiProperty({ 
    description: '依赖权限',
    example: ['user:list'],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  dependencies?: string[];

  @ApiProperty({ 
    description: '互斥权限',
    example: ['user:delete'],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  conflicts?: string[];

  @ApiProperty({
    description: '权限标签',
    example: ['basic', 'user-management'],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: '是否系统权限',
    example: false,
    default: false,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  system?: boolean = false;

  @ApiProperty({
    description: '是否启用',
    example: true,
    default: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  enabled?: boolean = true;
}
