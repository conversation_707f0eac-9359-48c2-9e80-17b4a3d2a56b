import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RolesService } from '../services/roles.service';
import { PERMISSIONS_KEY } from '@auth/common/decorators/permissions.decorator';

export interface PermissionRequirement {
  resource: string;
  action: string;
  conditions?: Record<string, any>;
}

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private rolesService: RolesService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 获取权限要求
    const requiredPermissions = this.reflector.getAllAndOverride<PermissionRequirement[]>(
      PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true; // 没有权限要求，允许访问
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('用户未认证');
    }

    // 检查用户是否有所需权限
    for (const permission of requiredPermissions) {
      const hasPermission = await this.checkPermission(user, permission, request);
      
      if (!hasPermission) {
        throw new ForbiddenException(
          `缺少权限: ${permission.resource}:${permission.action}`
        );
      }
    }

    return true;
  }

  /**
   * 检查用户权限
   */
  private async checkPermission(
    user: any,
    permission: PermissionRequirement,
    request: any
  ): Promise<boolean> {
    // 超级管理员拥有所有权限
    if (user.roles?.includes('super_admin')) {
      return true;
    }

    // 检查直接权限
    const directPermission = `${permission.resource}:${permission.action}`;
    if (user.permissions?.includes(directPermission) || user.permissions?.includes('*')) {
      return this.checkConditions(permission.conditions, user, request);
    }

    // 检查角色权限
    if (user.roles && user.roles.length > 0) {
      const hasRolePermission = await this.rolesService.checkRolePermission(
        user.roles,
        permission.resource,
        permission.action
      );

      if (hasRolePermission) {
        return this.checkConditions(permission.conditions, user, request);
      }
    }

    return false;
  }

  /**
   * 检查权限条件
   */
  private checkConditions(
    conditions: Record<string, any> | undefined,
    user: any,
    request: any
  ): boolean {
    if (!conditions) {
      return true; // 没有条件限制
    }

    // 检查用户属性条件
    if (conditions.userAttributes) {
      for (const [key, value] of Object.entries(conditions.userAttributes)) {
        if (user[key] !== value) {
          return false;
        }
      }
    }

    // 检查资源所有者条件
    if (conditions.owner) {
      const resourceUserId = this.extractResourceUserId(request, conditions.owner);
      if (resourceUserId && resourceUserId !== user.id) {
        return false;
      }
    }

    // 检查时间条件
    if (conditions.timeRange) {
      const now = new Date();
      const currentTime = now.getHours() * 60 + now.getMinutes();
      
      const [startHour, startMin] = conditions.timeRange.start.split(':').map(Number);
      const [endHour, endMin] = conditions.timeRange.end.split(':').map(Number);
      
      const startTime = startHour * 60 + startMin;
      const endTime = endHour * 60 + endMin;
      
      if (currentTime < startTime || currentTime > endTime) {
        return false;
      }
    }

    // 检查IP范围条件
    if (conditions.ipRange) {
      const clientIp = this.getClientIp(request);
      if (!this.isIpInRange(clientIp, conditions.ipRange)) {
        return false;
      }
    }

    // 检查设备类型条件
    if (conditions.deviceTypes) {
      const deviceType = this.getDeviceType(request);
      if (!conditions.deviceTypes.includes(deviceType)) {
        return false;
      }
    }

    // 检查地理位置条件
    if (conditions.geoRestrictions) {
      // TODO: 实现地理位置检查
      // const location = await this.getLocationFromIp(this.getClientIp(request));
      // if (!this.isLocationAllowed(location, conditions.geoRestrictions)) {
      //   return false;
      // }
    }

    return true;
  }

  /**
   * 提取资源用户ID
   */
  private extractResourceUserId(request: any, ownerConfig: any): string | null {
    if (typeof ownerConfig === 'string') {
      // 简单的参数路径，如 'params.userId' 或 'body.userId'
      const parts = ownerConfig.split('.');
      let value = request;
      
      for (const part of parts) {
        value = value?.[part];
        if (value === undefined) {
          return null;
        }
      }
      
      return value;
    }

    if (typeof ownerConfig === 'object') {
      // 复杂的配置对象
      if (ownerConfig.param) {
        return request.params?.[ownerConfig.param];
      }
      
      if (ownerConfig.body) {
        return request.body?.[ownerConfig.body];
      }
      
      if (ownerConfig.query) {
        return request.query?.[ownerConfig.query];
      }
    }

    return null;
  }

  /**
   * 获取客户端IP
   */
  private getClientIp(request: any): string {
    return (
      request.headers['x-forwarded-for']?.split(',')[0] ||
      request.headers['x-real-ip'] ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      request.ip ||
      '127.0.0.1'
    );
  }

  /**
   * 检查IP是否在范围内
   */
  private isIpInRange(ip: string, ranges: string[]): boolean {
    // 简化的IP范围检查，实际应该使用专业的IP库
    for (const range of ranges) {
      if (range === '*' || range === ip) {
        return true;
      }
      
      // 检查CIDR格式
      if (range.includes('/')) {
        // TODO: 实现CIDR检查
        continue;
      }
      
      // 检查通配符格式
      if (range.includes('*')) {
        const pattern = range.replace(/\*/g, '.*');
        const regex = new RegExp(`^${pattern}$`);
        if (regex.test(ip)) {
          return true;
        }
      }
    }
    
    return false;
  }

  /**
   * 获取设备类型
   */
  private getDeviceType(request: any): string {
    const userAgent = request.headers['user-agent'] || '';
    
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      return 'mobile';
    }
    
    if (/Tablet/.test(userAgent)) {
      return 'tablet';
    }
    
    return 'desktop';
  }
}
