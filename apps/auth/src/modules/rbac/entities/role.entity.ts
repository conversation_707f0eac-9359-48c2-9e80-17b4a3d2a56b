import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

// 角色约束条件子文档
@Schema({ _id: false })
export class RoleConstraints {
  @ApiProperty({ description: '最大用户数', required: false })
  @Prop({ min: 0 })
  maxUsers?: number;

  @ApiProperty({ description: '有效开始时间', required: false })
  @Prop()
  validFrom?: Date;

  @ApiProperty({ description: '有效结束时间', required: false })
  @Prop()
  validUntil?: Date;

  @ApiProperty({ description: '其他条件', required: false })
  @Prop({ type: Object })
  conditions?: Record<string, any>;
}

// 角色主文档
@Schema({
  timestamps: true,
  collection: 'roles',
  toJSON: {
    transform: (doc, ret) => {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  },
})
export class Role {

  @ApiProperty({ description: '角色名称', uniqueItems: true })
  @Prop({ 
    required: true, 
    unique: true, 
    trim: true, 
    lowercase: true,
    minlength: 2,
    maxlength: 50,
    match: /^[a-zA-Z0-9_-]+$/
  })
  name: string;

  @ApiProperty({ description: '显示名称' })
  @Prop({ 
    required: true, 
    trim: true,
    minlength: 2,
    maxlength: 100
  })
  displayName: string;

  @ApiProperty({ description: '角色描述' })
  @Prop({ 
    trim: true,
    maxlength: 500
  })
  description: string;

  @ApiProperty({ description: '角色权限', type: [String] })
  @Prop({ type: [String], default: [] })
  permissions: string[];

  @ApiProperty({ description: '继承的角色', type: [String] })
  @Prop({ type: [String], default: [] })
  inherits: string[];

  @ApiProperty({ description: '角色约束', type: RoleConstraints })
  @Prop({ type: RoleConstraints, default: () => ({}) })
  constraints: RoleConstraints;

  @ApiProperty({ description: '是否系统角色', default: false })
  @Prop({ default: false })
  system: boolean;

  @ApiProperty({ description: '角色分类' })
  @Prop({ 
    trim: true,
    default: 'custom'
  })
  category: string;

  @ApiProperty({ description: '优先级', default: 0 })
  @Prop({ 
    default: 0,
    min: 0,
    max: 100
  })
  priority: number;

  @ApiProperty({ description: '是否启用', default: true })
  @Prop({ default: true })
  enabled: boolean;

  @ApiProperty({ description: '角色颜色' })
  @Prop({ 
    trim: true,
    match: /^#[0-9A-Fa-f]{6}$/
  })
  color?: string;

  @ApiProperty({ description: '角色图标' })
  @Prop({ trim: true })
  icon?: string;

  @ApiProperty({ description: '用户数量', default: 0 })
  @Prop({ default: 0, min: 0 })
  userCount: number;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 虚拟字段：是否有效
  get isValid(): boolean {
    const now = new Date();
    
    if (this.constraints.validFrom && this.constraints.validFrom > now) {
      return false;
    }
    
    if (this.constraints.validUntil && this.constraints.validUntil < now) {
      return false;
    }
    
    return this.enabled;
  }

  // 虚拟字段：是否达到用户数限制
  get isUserLimitReached(): boolean {
    if (!this.constraints.maxUsers) {
      return false;
    }
    
    return this.userCount >= this.constraints.maxUsers;
  }

  // 虚拟字段：权限数量
  get permissionCount(): number {
    return this.permissions.length;
  }
}

export const RoleSchema = SchemaFactory.createForClass(Role);

// 创建索引
RoleSchema.index({ name: 1 }, { unique: true });
RoleSchema.index({ category: 1 });
RoleSchema.index({ system: 1 });
RoleSchema.index({ enabled: 1 });
RoleSchema.index({ priority: -1 });
RoleSchema.index({ userCount: 1 });
RoleSchema.index({ createdAt: 1 });

// 复合索引
RoleSchema.index({ category: 1, enabled: 1 });
RoleSchema.index({ system: 1, enabled: 1 });
RoleSchema.index({ priority: -1, enabled: 1 });

// 中间件：保存前处理
RoleSchema.pre('save', function(next) {
  // 确保角色名称小写
  if (this.isModified('name')) {
    this.name = this.name.toLowerCase();
  }
  
  // 移除重复的权限
  if (this.isModified('permissions')) {
    this.permissions = [...new Set(this.permissions)];
  }
  
  // 移除重复的继承角色
  if (this.isModified('inherits')) {
    this.inherits = [...new Set(this.inherits)];
  }
  
  next();
});

// 实例方法：检查是否有权限
RoleSchema.methods.hasPermission = function(permission: string): boolean {
  return this.permissions.includes(permission) || this.permissions.includes('*');
};

// 实例方法：添加权限
RoleSchema.methods.addPermission = function(permission: string): void {
  if (!this.hasPermission(permission)) {
    this.permissions.push(permission);
  }
};

// 实例方法：移除权限
RoleSchema.methods.removePermission = function(permission: string): void {
  this.permissions = this.permissions.filter(p => p !== permission);
};

// 实例方法：添加继承角色
RoleSchema.methods.addInheritance = function(roleName: string): void {
  if (!this.inherits.includes(roleName)) {
    this.inherits.push(roleName);
  }
};

// 实例方法：移除继承角色
RoleSchema.methods.removeInheritance = function(roleName: string): void {
  this.inherits = this.inherits.filter(r => r !== roleName);
};

// 静态方法：获取系统角色
RoleSchema.statics.getSystemRoles = function() {
  return this.find({ system: true, enabled: true }).sort({ priority: -1 });
};

// 静态方法：获取可分配角色
RoleSchema.statics.getAssignableRoles = function() {
  return this.find({ 
    enabled: true,
    $or: [
      { 'constraints.maxUsers': { $exists: false } },
      { $expr: { $lt: ['$userCount', '$constraints.maxUsers'] } }
    ]
  }).sort({ priority: -1 });
};

// 角色文档接口，包含实例方法
export interface RoleDocument extends Role, Document {
  // 检查是否有权限
  hasPermission(permission: string): boolean;

  // 添加权限
  addPermission(permission: string): void;

  // 移除权限
  removePermission(permission: string): void;

  // 添加继承角色
  addInheritance(roleName: string): void;

  // 移除继承角色
  removeInheritance(roleName: string): void;
}
