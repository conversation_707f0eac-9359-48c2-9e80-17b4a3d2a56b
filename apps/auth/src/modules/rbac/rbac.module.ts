import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// 控制器
import { RolesController } from './controllers/roles.controller';
import { PermissionsController } from './controllers/permissions.controller';

// 服务
import { RolesService } from './services/roles.service';
import { PermissionsService } from './services/permissions.service';

// 仓储
import { RoleRepository } from './repositories/role.repository';
import { PermissionRepository } from './repositories/permission.repository';

// 实体
import { Role, RoleSchema } from './entities/role.entity';
import { Permission, PermissionSchema } from './entities/permission.entity';

// 守卫
import { RolesGuard } from './guards/roles.guard';
import { PermissionsGuard } from './guards/permissions.guard';

/**
 * RBAC模块 (基于角色的访问控制)
 * 
 * 整合了原roles模块和permissions模块的功能，包括：
 * - 角色管理（创建、更新、删除、查询）
 * - 权限管理（创建、更新、删除、查询）
 * - 角色权限关联管理
 * - 权限检查和验证
 * 
 * 职责范围：
 * - 角色和权限的CRUD操作
 * - 角色权限关系管理
 * - 权限验证和授权控制
 * - RBAC相关的守卫和装饰器
 * 
 * 设计原则：
 * - 单一职责：只处理角色权限相关功能
 * - 无循环依赖：不依赖其他业务模块
 * - 高内聚：角色和权限功能紧密相关
 */
@Module({
  imports: [
    // MongoDB Schema注册
    MongooseModule.forFeature([
      { name: Role.name, schema: RoleSchema },
      { name: Permission.name, schema: PermissionSchema },
    ]),
    
    // 注意：不导入其他业务模块，避免循环依赖
    // 如需其他服务，通过接口注入或事件通信
  ],
  controllers: [
    RolesController,
    PermissionsController,
  ],
  providers: [
    // 服务层
    RolesService,
    PermissionsService,
    
    // 仓储层
    RoleRepository,
    PermissionRepository,
    
    // 守卫
    RolesGuard,
    PermissionsGuard,
  ],
  exports: [
    // 服务导出
    RolesService,
    PermissionsService,
    
    // 仓储导出
    RoleRepository,
    PermissionRepository,
    
    // 守卫导出 - 供其他模块使用
    RolesGuard,
    PermissionsGuard,
  ],
})
export class RbacModule {
  constructor() {
    console.log('✅ RBAC模块已初始化 - 包含角色和权限管理功能');
  }
}
