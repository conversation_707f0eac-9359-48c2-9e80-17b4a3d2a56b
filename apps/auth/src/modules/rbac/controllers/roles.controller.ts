import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
  ValidationPipe,
  UseInterceptors,
  ClassSerializerInterceptor,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { Cacheable, CacheEvict, CachePut } from '@common/redis';
import { RolesService } from '../services/roles.service';
import { CreateRoleDto } from '../dto/create-role.dto';
import { UpdateRoleDto } from '../dto/update-role.dto';
import { Role } from '../entities/role.entity';
import { JwtAuthGuard } from '../../security/guards/jwt-auth.guard';
import { RolesGuard } from '../guards/roles.guard';
import { PermissionsGuard } from '../guards/permissions.guard';
import { Roles } from '../../../shared/decorators/roles.decorator';
import { Permissions } from '../../../shared/decorators/permissions.decorator';
import { ApiResponseDto } from '../../../shared/dto/response.dto';

@ApiTags('角色管理')
@Controller('roles')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
@UseInterceptors(ClassSerializerInterceptor)
@ApiBearerAuth('JWT-auth')
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @Roles('admin', 'role_manager')
  @Permissions.Role.create()
  @CacheEvict({
    key: ['roles:all', 'roles:system', 'roles:assignable'],
    condition: 'true',
    paramNames: []
  })
  @ApiOperation({ summary: '创建角色' })
  @ApiResponse({
    status: 201,
    description: '角色创建成功',
    type: Role,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 409,
    description: '角色名称已存在',
  })
  async create(
    @Body(ValidationPipe) createRoleDto: CreateRoleDto,
  ): Promise<ApiResponseDto<Role>> {
    const role = await this.rolesService.create(createRoleDto);
    
    return {
      success: true,
      data: role,
      message: '角色创建成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get()
  @Permissions.Role.list()
  @Cacheable({
    key: 'roles:all',
    ttl: 300,
    condition: 'true'
  })
  @ApiOperation({ summary: '获取角色列表' })
  @ApiResponse({
    status: 200,
    description: '获取角色列表成功',
    type: [Role],
  })
  async findAll(): Promise<ApiResponseDto<Role[]>> {
    const roles = await this.rolesService.findAll();
    
    return {
      success: true,
      data: roles,
      message: '获取角色列表成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('system')
  @Permissions.Role.read()
  @Cacheable({
    key: 'roles:system',
    ttl: 600,
    condition: 'true'
  })
  @ApiOperation({ summary: '获取系统角色' })
  @ApiResponse({
    status: 200,
    description: '获取系统角色成功',
    type: [Role],
  })
  async getSystemRoles(): Promise<ApiResponseDto<Role[]>> {
    const roles = await this.rolesService.getSystemRoles();
    
    return {
      success: true,
      data: roles,
      message: '获取系统角色成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('assignable')
  @Permissions.Role.read()
  @Cacheable({
    key: 'roles:assignable',
    ttl: 300,
    condition: 'true'
  })
  @ApiOperation({ summary: '获取可分配角色' })
  @ApiResponse({
    status: 200,
    description: '获取可分配角色成功',
    type: [Role],
  })
  async getAssignableRoles(): Promise<ApiResponseDto<Role[]>> {
    const roles = await this.rolesService.getAssignableRoles();
    
    return {
      success: true,
      data: roles,
      message: '获取可分配角色成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get(':id')
  @Permissions.Role.read()
  @Cacheable({
    key: 'roles:id:#{id}',
    ttl: 300,
    condition: '#{id != null}',
    paramNames: ['id']
  })
  @ApiOperation({ summary: '根据ID获取角色' })
  @ApiParam({
    name: 'id',
    description: '角色ID',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: '获取角色成功',
    type: Role,
  })
  @ApiResponse({
    status: 404,
    description: '角色不存在',
  })
  async findById(@Param('id') id: string): Promise<ApiResponseDto<Role>> {
    const role = await this.rolesService.findById(id);
    
    return {
      success: true,
      data: role,
      message: '获取角色成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Put(':id')
  @Roles('admin', 'role_manager')
  @Permissions.Role.update()
  @CacheEvict({
    key: ['roles:all', 'roles:id:#{id}', 'roles:system', 'roles:assignable'],
    condition: '#{id != null}',
    paramNames: ['id']
  })
  @ApiOperation({ summary: '更新角色' })
  @ApiParam({
    name: 'id',
    description: '角色ID',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: '角色更新成功',
    type: Role,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 404,
    description: '角色不存在',
  })
  async update(
    @Param('id') id: string,
    @Body(ValidationPipe) updateRoleDto: UpdateRoleDto,
  ): Promise<ApiResponseDto<Role>> {
    const role = await this.rolesService.update(id, updateRoleDto);
    
    return {
      success: true,
      data: role,
      message: '角色更新成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @Roles('admin', 'role_manager')
  @Permissions.Role.delete()
  @CacheEvict({
    key: ['roles:all', 'roles:id:#{id}', 'roles:system', 'roles:assignable'],
    condition: '#{id != null}',
    paramNames: ['id']
  })
  @ApiOperation({ summary: '删除角色' })
  @ApiParam({
    name: 'id',
    description: '角色ID',
    type: 'string',
  })
  @ApiResponse({
    status: 204,
    description: '角色删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '角色不存在',
  })
  async remove(@Param('id') id: string): Promise<ApiResponseDto<null>> {
    await this.rolesService.remove(id);
    
    return {
      success: true,
      data: null,
      message: '角色删除成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get(':id/permissions')
  @Permissions.Role.read()
  @ApiOperation({ summary: '获取角色权限' })
  @ApiParam({
    name: 'id',
    description: '角色ID',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: '获取角色权限成功',
  })
  async getRolePermissions(@Param('id') id: string): Promise<ApiResponseDto<string[]>> {
    const role = await this.rolesService.findById(id);
    const permissions = await this.rolesService.getRolePermissions(role.name);
    
    return {
      success: true,
      data: permissions,
      message: '获取角色权限成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Post(':id/permissions')
  @Roles('admin', 'role_manager')
  @Permissions.Role.update()
  @ApiOperation({ summary: '为角色添加权限' })
  @ApiParam({
    name: 'id',
    description: '角色ID',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: '权限添加成功',
    type: Role,
  })
  async addPermissions(
    @Param('id') id: string,
    @Body('permissions') permissions: string[],
  ): Promise<ApiResponseDto<Role>> {
    const role = await this.rolesService.addPermissions(id, permissions);
    
    return {
      success: true,
      data: role,
      message: '权限添加成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Delete(':id/permissions')
  @Roles('admin', 'role_manager')
  @Permissions.Role.update()
  @ApiOperation({ summary: '从角色移除权限' })
  @ApiParam({
    name: 'id',
    description: '角色ID',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: '权限移除成功',
    type: Role,
  })
  async removePermissions(
    @Param('id') id: string,
    @Body('permissions') permissions: string[],
  ): Promise<ApiResponseDto<Role>> {
    const role = await this.rolesService.removePermissions(id, permissions);
    
    return {
      success: true,
      data: role,
      message: '权限移除成功',
      timestamp: new Date().toISOString(),
    };
  }
}
