import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
  ValidationPipe,
  UseInterceptors,
  ClassSerializerInterceptor,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { Cacheable, CacheEvict, CachePut } from '@common/redis';
import { PermissionsService } from '../services/permissions.service';
import { CreatePermissionDto } from '../dto/create-permission.dto';
import { UpdatePermissionDto } from '../dto/update-permission.dto';
import { Permission } from '../entities/permission.entity';
import { JwtAuthGuard } from '../../security/guards/jwt-auth.guard';
import { RolesGuard } from '../guards/roles.guard';
import { PermissionsGuard } from '../guards/permissions.guard';
import { Roles } from '../../../shared/decorators/roles.decorator';
import { Permissions } from '../../../shared/decorators/permissions.decorator';
import { ApiResponseDto } from '../../../shared/dto/response.dto';

@ApiTags('权限管理')
@Controller('permissions')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
@UseInterceptors(ClassSerializerInterceptor)
@ApiBearerAuth('JWT-auth')
export class PermissionsController {
  constructor(private readonly permissionsService: PermissionsService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @Roles('admin', 'permission_manager')
  @Permissions.Permission.create()
  @CacheEvict({
    key: ['permissions:all', 'permissions:system', 'permissions:dangerous', 'permissions:statistics'],
    condition: 'true',
    paramNames: []
  })
  @ApiOperation({ summary: '创建权限' })
  @ApiResponse({
    status: 201,
    description: '权限创建成功',
    type: Permission,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 409,
    description: '权限已存在',
  })
  async create(
    @Body(ValidationPipe) createPermissionDto: CreatePermissionDto,
  ): Promise<ApiResponseDto<Permission>> {
    const permission = await this.permissionsService.create(createPermissionDto);
    
    return {
      success: true,
      data: permission,
      message: '权限创建成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('batch')
  @HttpCode(HttpStatus.CREATED)
  @Roles('admin')
  @Permissions.Permission.create()
  @CacheEvict({
    key: ['permissions:all', 'permissions:system', 'permissions:dangerous', 'permissions:statistics'],
    condition: 'true',
    paramNames: []
  })
  @ApiOperation({ summary: '批量创建权限' })
  @ApiResponse({
    status: 201,
    description: '权限批量创建成功',
    type: [Permission],
  })
  async createBatch(
    @Body(ValidationPipe) permissions: CreatePermissionDto[],
  ): Promise<ApiResponseDto<Permission[]>> {
    const createdPermissions = await this.permissionsService.createBatch(permissions);
    
    return {
      success: true,
      data: createdPermissions,
      message: `成功创建${createdPermissions.length}个权限`,
      timestamp: new Date().toISOString(),
    };
  }

  @Get()
  @Permissions.Permission.list()
  @Cacheable({
    key: 'permissions:all',
    ttl: 300,
    condition: 'true'
  })
  @ApiOperation({ summary: '获取权限列表' })
  @ApiResponse({
    status: 200,
    description: '获取权限列表成功',
    type: [Permission],
  })
  async findAll(): Promise<ApiResponseDto<Permission[]>> {
    const permissions = await this.permissionsService.findAll();
    
    return {
      success: true,
      data: permissions,
      message: '获取权限列表成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('search')
  @Permissions.Permission.read()
  @Cacheable({
    key: 'permissions:search:#{query}',
    ttl: 180,
    condition: '#{query != null && query.length() > 0}',
    paramNames: ['query']
  })
  @ApiOperation({ summary: '搜索权限' })
  @ApiQuery({
    name: 'q',
    description: '搜索关键词',
    required: true,
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: '搜索权限成功',
    type: [Permission],
  })
  async search(@Query('q') query: string): Promise<ApiResponseDto<Permission[]>> {
    const permissions = await this.permissionsService.search(query);
    
    return {
      success: true,
      data: permissions,
      message: '搜索权限成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('system')
  @Permissions.Permission.read()
  @Cacheable({
    key: 'permissions:system',
    ttl: 600,
    condition: 'true'
  })
  @ApiOperation({ summary: '获取系统权限' })
  @ApiResponse({
    status: 200,
    description: '获取系统权限成功',
    type: [Permission],
  })
  async getSystemPermissions(): Promise<ApiResponseDto<Permission[]>> {
    const permissions = await this.permissionsService.getSystemPermissions();
    
    return {
      success: true,
      data: permissions,
      message: '获取系统权限成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('category/:category')
  @Permissions.Permission.read()
  @Cacheable({
    key: 'permissions:category:#{category}',
    ttl: 300,
    condition: '#{category != null}',
    paramNames: ['category']
  })
  @ApiOperation({ summary: '根据分类获取权限' })
  @ApiParam({
    name: 'category',
    description: '权限分类',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: '获取分类权限成功',
    type: [Permission],
  })
  async findByCategory(@Param('category') category: string): Promise<ApiResponseDto<Permission[]>> {
    const permissions = await this.permissionsService.findByCategory(category);
    
    return {
      success: true,
      data: permissions,
      message: '获取分类权限成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get(':id')
  @Permissions.Permission.read()
  @Cacheable({
    key: 'permissions:id:#{id}',
    ttl: 300,
    condition: '#{id != null}',
    paramNames: ['id']
  })
  @ApiOperation({ summary: '根据ID获取权限' })
  @ApiParam({
    name: 'id',
    description: '权限ID',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: '获取权限成功',
    type: Permission,
  })
  @ApiResponse({
    status: 404,
    description: '权限不存在',
  })
  async findById(@Param('id') id: string): Promise<ApiResponseDto<Permission>> {
    const permission = await this.permissionsService.findById(id);
    
    return {
      success: true,
      data: permission,
      message: '获取权限成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Put(':id')
  @Roles('admin', 'permission_manager')
  @Permissions.Permission.update()
  @CacheEvict({
    key: ['permissions:all', 'permissions:id:#{id}', 'permissions:system', 'permissions:dangerous', 'permissions:statistics'],
    condition: '#{id != null}',
    paramNames: ['id']
  })
  @ApiOperation({ summary: '更新权限' })
  @ApiParam({
    name: 'id',
    description: '权限ID',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: '权限更新成功',
    type: Permission,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 404,
    description: '权限不存在',
  })
  async update(
    @Param('id') id: string,
    @Body(ValidationPipe) updatePermissionDto: UpdatePermissionDto,
  ): Promise<ApiResponseDto<Permission>> {
    const permission = await this.permissionsService.update(id, updatePermissionDto);
    
    return {
      success: true,
      data: permission,
      message: '权限更新成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @Roles('admin', 'permission_manager')
  @Permissions.Permission.delete()
  @CacheEvict({
    key: ['permissions:all', 'permissions:id:#{id}', 'permissions:system', 'permissions:dangerous', 'permissions:statistics'],
    condition: '#{id != null}',
    paramNames: ['id']
  })
  @ApiOperation({ summary: '删除权限' })
  @ApiParam({
    name: 'id',
    description: '权限ID',
    type: 'string',
  })
  @ApiResponse({
    status: 204,
    description: '权限删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '权限不存在',
  })
  async remove(@Param('id') id: string): Promise<ApiResponseDto<null>> {
    await this.permissionsService.remove(id);
    
    return {
      success: true,
      data: null,
      message: '权限删除成功',
      timestamp: new Date().toISOString(),
    };
  }
}
