import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// 服务
import { SessionService } from './services/session.service';

// 仓储
import { SessionRepository, ISessionRepository } from './repositories/session.repository';

// 实体
import { Session, SessionSchema } from './entities/session.entity';

/**
 * 会话管理模块
 * 
 * 负责用户会话的生命周期管理，包括：
 * - 会话创建和初始化
 * - 会话验证和更新
 * - 会话终止和清理
 * - 会话安全控制
 * 
 * 职责范围：
 * - 会话的CRUD操作
 * - 会话状态管理和验证
 * - 会话超时和清理机制
 * - 并发会话控制
 * - 会话安全监控
 * 
 * 设计原则：
 * - 单一职责：只处理会话管理
 * - 安全优先：严格的会话验证
 * - 性能优化：高效的会话查询
 * - 接口抽象：提供ISessionRepository接口
 */
@Module({
  imports: [
    // MongoDB Schema注册
    MongooseModule.forFeature([
      { name: Session.name, schema: SessionSchema },
    ]),
    
    // 注意：不导入其他业务模块，避免循环依赖
    // 通过接口注入的方式提供服务给其他模块
  ],
  providers: [
    // 服务层
    SessionService,
    
    // 仓储层
    SessionRepository,
    
    // 接口实现注册 - 供其他模块通过接口注入使用
    {
      provide: 'ISessionRepository',
      useClass: SessionRepository,
    },
    {
      provide: 'ISessionService',
      useClass: SessionService,
    },
  ],
  exports: [
    // 服务导出
    SessionService,
    
    // 仓储导出
    SessionRepository,
    
    // 接口导出 - 供依赖注入使用
    'ISessionRepository',
    'ISessionService',
  ],
})
export class SessionModule {
  constructor() {
    console.log('✅ 会话管理模块已初始化 - 提供会话生命周期管理功能');
  }
}
