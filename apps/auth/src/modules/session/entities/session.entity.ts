import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

// 设备信息子文档
@Schema({ _id: false })
export class DeviceInfo {
  @ApiProperty({ description: '设备类型' })
  @Prop({ required: true })
  type: string;

  @ApiProperty({ description: '设备名称' })
  @Prop({ required: true })
  name: string;

  @ApiProperty({ description: '设备指纹' })
  @Prop({ required: true })
  fingerprint: string;

  @ApiProperty({ description: '用户代理' })
  @Prop({ required: true })
  userAgent: string;

  @ApiProperty({ description: '操作系统' })
  @Prop()
  os: string;

  @ApiProperty({ description: '浏览器' })
  @Prop()
  browser: string;
}

// 地理位置子文档
@Schema({ _id: false })
export class Location {
  @ApiProperty({ description: '国家' })
  @Prop()
  country: string;

  @ApiProperty({ description: '城市' })
  @Prop()
  city: string;

  @ApiProperty({ description: '纬度' })
  @Prop()
  latitude: number;

  @ApiProperty({ description: '经度' })
  @Prop()
  longitude: number;
}

// 会话主文档
@Schema({
  timestamps: true,
  collection: 'sessions',
  toJSON: {
    transform: (doc, ret) => {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      delete ret.accessToken;
      delete ret.refreshToken;
      return ret;
    },
  },
})
export class Session {

  @ApiProperty({ description: '会话标识符' })
  @Prop({ required: true, unique: true, index: true })
  sessionId: string;

  @ApiProperty({ description: '用户ID' })
  @Prop({ required: true, type: Types.ObjectId, ref: 'User', index: true })
  userId: string;

  @ApiProperty({ description: '设备ID' })
  @Prop({ required: true, index: true })
  deviceId: string;

  @ApiProperty({ description: '设备信息', type: DeviceInfo })
  @Prop({ type: DeviceInfo })
  deviceInfo: DeviceInfo;

  @ApiProperty({ description: 'IP地址' })
  @Prop()
  ipAddress: string;

  @ApiProperty({ description: '地理位置', type: Location })
  @Prop({ type: Location })
  location: Location;

  @ApiProperty({ description: '是否受信任设备' })
  @Prop({ default: false })
  trusted: boolean;

  @ApiProperty({ description: '会话是否活跃' })
  @Prop({ default: true, index: true })
  active: boolean;

  @ApiProperty({ description: '是否记住我' })
  @Prop({ default: false })
  rememberMe: boolean;

  @ApiProperty({ description: '访问令牌' })
  @Prop({ select: false })
  accessToken: string;

  @ApiProperty({ description: '刷新令牌' })
  @Prop({ select: false })
  refreshToken: string;

  @ApiProperty({ description: '最后活动时间' })
  @Prop({ default: Date.now, index: true })
  lastActivity: Date;

  @ApiProperty({ description: '会话过期时间' })
  @Prop({ required: true, index: true })
  expiresAt: Date;

  @ApiProperty({ description: '绝对过期时间' })
  @Prop({ required: true, index: true })
  absoluteExpiresAt: Date;

  @ApiProperty({ description: '终止时间' })
  @Prop()
  terminatedAt: Date;

  @ApiProperty({ description: '终止原因' })
  @Prop()
  endReason?: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 虚拟字段：是否过期
  get isExpired(): boolean {
    return this.expiresAt < new Date() || this.absoluteExpiresAt < new Date();
  }

  // 虚拟字段：剩余时间（秒）
  get remainingTime(): number {
    const now = Date.now();
    const expiryTime = Math.min(this.expiresAt.getTime(), this.absoluteExpiresAt.getTime());
    return Math.max(0, Math.floor((expiryTime - now) / 1000));
  }

  // 虚拟字段：会话持续时间（秒）
  get duration(): number {
    const endTime = this.terminatedAt || new Date();
    return Math.floor((endTime.getTime() - this.createdAt.getTime()) / 1000);
  }

  // 虚拟字段：空闲时间（秒）
  get idleTime(): number {
    return Math.floor((Date.now() - this.lastActivity.getTime()) / 1000);
  }
}

export const SessionSchema = SchemaFactory.createForClass(Session);

// 创建索引
SessionSchema.index({ sessionId: 1 }, { unique: true });
SessionSchema.index({ userId: 1, active: 1 });
SessionSchema.index({ deviceId: 1 });
SessionSchema.index({ expiresAt: 1 });
SessionSchema.index({ absoluteExpiresAt: 1 });
SessionSchema.index({ lastActivity: 1 });
SessionSchema.index({ createdAt: 1 });

// 复合索引
SessionSchema.index({ userId: 1, active: 1, expiresAt: 1 });
SessionSchema.index({ deviceId: 1, trusted: 1 });

// TTL索引 - 自动删除过期文档
SessionSchema.index({ absoluteExpiresAt: 1 }, { expireAfterSeconds: 0 });

// 中间件：保存前处理
SessionSchema.pre('save', function(next) {
  // 确保绝对过期时间不小于会话过期时间
  if (this.absoluteExpiresAt < this.expiresAt) {
    this.absoluteExpiresAt = this.expiresAt;
  }
  
  next();
});

// 实例方法：延长会话
SessionSchema.methods.extend = function(additionalTime: number): void {
  const now = new Date();
  this.expiresAt = new Date(now.getTime() + additionalTime * 1000);
  this.lastActivity = now;
};

// 实例方法：检查是否需要续期
SessionSchema.methods.needsRenewal = function(threshold: number = 300): boolean {
  const now = Date.now();
  const timeUntilExpiry = this.expiresAt.getTime() - now;
  return timeUntilExpiry < threshold * 1000 && timeUntilExpiry > 0;
};

// 静态方法：清理过期会话
SessionSchema.statics.cleanupExpired = function() {
  return this.updateMany(
    {
      $or: [
        { expiresAt: { $lte: new Date() } },
        { absoluteExpiresAt: { $lte: new Date() } }
      ],
      active: true
    },
    {
      $set: {
        active: false,
        terminatedAt: new Date()
      }
    }
  );
};

// 会话文档接口，包含实例方法
export interface SessionDocument extends Session, Document {
  // 延长会话
  extend(additionalTime: number): void;

  // 检查是否需要续期
  needsRenewal(threshold?: number): boolean;
}
