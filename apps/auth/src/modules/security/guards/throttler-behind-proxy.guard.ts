/**
 * 代理后限流守卫
 * 处理在代理服务器后面的应用程序的速率限制
 */

import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Logger,
  Inject,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { Request, Response } from 'express';
import { ThrottlerException } from '@nestjs/throttler';
import { RATE_LIMIT } from '../../../shared/constants/auth.constants';
import { RedisService } from '@common/redis';

// 限流配置接口
export interface ThrottlerConfig {
  ttl: number;                    // 时间窗口（秒）
  limit: number;                  // 请求限制数量
  skipIf?: (context: ExecutionContext) => boolean; // 跳过条件
  skipSuccessfulRequests?: boolean; // 跳过成功请求
  skipFailedRequests?: boolean;   // 跳过失败请求
  generateKey?: (context: ExecutionContext) => string; // 自定义键生成
}

// 限流元数据键
export const THROTTLER_CONFIG_KEY = 'throttler_config';

/**
 * 限流装饰器
 */
export const Throttle = (config: Partial<ThrottlerConfig>) => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    if (descriptor) {
      // 方法装饰器
      Reflect.defineMetadata(THROTTLER_CONFIG_KEY, config, descriptor.value);
    } else {
      // 类装饰器
      Reflect.defineMetadata(THROTTLER_CONFIG_KEY, config, target);
    }
  };
};

@Injectable()
export class ThrottlerBehindProxyGuard implements CanActivate {
  private readonly logger = new Logger(ThrottlerBehindProxyGuard.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly reflector: Reflector,
    @Inject(RedisService) private readonly redisService: RedisService,
  ) {
    console.log('🔧 ThrottlerBehindProxyGuard 构造函数被调用');
    console.log('🔧 ConfigService 注入成功:', !!this.configService);
    console.log('🔧 RedisService 注入成功:', !!this.redisService);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    console.log('🚀 ThrottlerBehindProxyGuard.canActivate 被调用');

    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();

    console.log(`🔍 请求路径: ${request.path}`);
    console.log(`🔍 请求方法: ${request.method}`);

    // 获取限流配置
    const config = this.getThrottlerConfig(context);

    // 检查是否跳过限流
    if (config.skipIf && config.skipIf(context)) {
      return true;
    }

    // 生成限流键
    const key = this.generateThrottlerKey(context, config);
    
    // 检查限流
    const { allowed, totalHits, timeToReset } = await this.checkLimit(key, config);

    // 设置响应头
    this.setRateLimitHeaders(response, config, totalHits, timeToReset);

    if (!allowed) {
      this.logRateLimitExceeded(request, key, config, totalHits);
      throw new ThrottlerException('请求过于频繁，请稍后重试');
    }

    // 记录请求
    this.logRequest(request, key, config, totalHits);

    return true;
  }

  /**
   * 获取限流配置
   */
  private getThrottlerConfig(context: ExecutionContext): ThrottlerConfig {
    // 🔧 开发环境直接跳过限流
    const nodeEnv = this.configService.get('NODE_ENV', 'development');
    const rateLimitEnabled = this.configService.get('RATE_LIMIT_ENABLED', 'true') === 'true';

    // 详细调试信息
    console.log(`🔍 ThrottlerBehindProxyGuard 详细调试:`);
    console.log(`   process.env.NODE_ENV: "${process.env.NODE_ENV}"`);
    console.log(`   configService.get('NODE_ENV'): "${nodeEnv}"`);
    console.log(`   process.env.RATE_LIMIT_ENABLED: "${process.env.RATE_LIMIT_ENABLED}"`);
    console.log(`   configService.get('RATE_LIMIT_ENABLED'): "${this.configService.get('RATE_LIMIT_ENABLED')}"`);
    console.log(`   rateLimitEnabled (computed): ${rateLimitEnabled}`);

    if (nodeEnv === 'development' || nodeEnv === 'test' || !rateLimitEnabled) {
      return {
        ttl: 1,
        limit: 999999,
        skipIf: () => true, // 直接跳过
      };
    }

    const handler = context.getHandler();
    const controller = context.getClass();

    // 检查方法级别配置
    const methodConfig = this.reflector.get<Partial<ThrottlerConfig>>(
      THROTTLER_CONFIG_KEY,
      handler,
    );

    // 检查控制器级别配置
    const controllerConfig = this.reflector.get<Partial<ThrottlerConfig>>(
      THROTTLER_CONFIG_KEY,
      controller,
    );

    // 获取路径特定配置
    const pathConfig = this.getPathSpecificConfig(context);

    // 合并配置
    const defaultConfig: ThrottlerConfig = {
      ttl: this.configService.get('THROTTLE_TTL', 60),
      limit: this.configService.get('THROTTLE_LIMIT', 10),
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
    };

    return {
      ...defaultConfig,
      ...pathConfig,
      ...controllerConfig,
      ...methodConfig,
    };
  }

  /**
   * 获取路径特定配置
   */
  private getPathSpecificConfig(context: ExecutionContext): Partial<ThrottlerConfig> {
    const request = context.switchToHttp().getRequest<Request>();
    const path = request.path;

    // 登录端点配置
    if (path.includes('/auth/login')) {
      return {
        ttl: RATE_LIMIT.LOGIN.WINDOW / 1000,
        limit: RATE_LIMIT.LOGIN.MAX_ATTEMPTS,
      };
    }

    // 注册端点配置
    if (path.includes('/auth/register')) {
      return {
        ttl: RATE_LIMIT.REGISTER.WINDOW / 1000,
        limit: RATE_LIMIT.REGISTER.MAX_ATTEMPTS,
      };
    }

    // 密码重置端点配置
    if (path.includes('/auth/reset-password')) {
      return {
        ttl: RATE_LIMIT.PASSWORD_RESET.WINDOW / 1000,
        limit: RATE_LIMIT.PASSWORD_RESET.MAX_ATTEMPTS,
      };
    }

    // MFA验证端点配置
    if (path.includes('/mfa/verify')) {
      return {
        ttl: RATE_LIMIT.MFA_VERIFY.WINDOW / 1000,
        limit: RATE_LIMIT.MFA_VERIFY.MAX_ATTEMPTS,
      };
    }

    return {};
  }

  /**
   * 生成限流键
   */
  private generateThrottlerKey(
    context: ExecutionContext,
    config: ThrottlerConfig,
  ): string {
    const request = context.switchToHttp().getRequest<Request>();

    // 使用自定义键生成器
    if (config.generateKey) {
      return config.generateKey(context);
    }

    // 获取真实IP地址
    const ip = this.getRealIpAddress(request);
    const path = request.path;
    const method = request.method;

    // 对于认证相关端点，使用更细粒度的键
    if (this.isAuthEndpoint(path)) {
      const identifier = this.getAuthIdentifier(request);
      return `throttle:${method}:${path}:${ip}:${identifier}`;
    }

    // 默认键格式
    return `throttle:${method}:${path}:${ip}`;
  }

  /**
   * 获取真实IP地址
   */
  private getRealIpAddress(request: Request): string {
    // 检查各种代理头
    const forwardedFor = request.headers['x-forwarded-for'] as string;
    const realIp = request.headers['x-real-ip'] as string;
    const cfConnectingIp = request.headers['cf-connecting-ip'] as string;
    const xClientIp = request.headers['x-client-ip'] as string;

    // 优先级顺序
    if (cfConnectingIp) {
      return cfConnectingIp;
    }

    if (realIp) {
      return realIp;
    }

    if (forwardedFor) {
      // X-Forwarded-For 可能包含多个IP，取第一个
      return forwardedFor.split(',')[0].trim();
    }

    if (xClientIp) {
      return xClientIp;
    }

    // 回退到连接IP
    return request.connection.remoteAddress || 
           request.socket.remoteAddress || 
           'unknown';
  }

  /**
   * 检查是否为认证端点
   */
  private isAuthEndpoint(path: string): boolean {
    const authPaths = [
      '/auth/login',
      '/auth/register',
      '/auth/reset-password',
      '/auth/confirm-reset',
      '/mfa/verify',
    ];

    return authPaths.some(authPath => path.includes(authPath));
  }

  /**
   * 获取认证标识符
   */
  private getAuthIdentifier(request: Request): string {
    const body = request.body || {};
    
    // 尝试从请求体中获取标识符
    return body.email || 
           body.username || 
           body.identifier || 
           body.phone || 
           'anonymous';
  }

  /**
   * 检查限流 - 使用 Redis 存储
   */
  private async checkLimit(
    key: string,
    config: ThrottlerConfig,
  ): Promise<{ allowed: boolean; totalHits: number; timeToReset: number }> {
    const now = Date.now();
    const ttlSeconds = config.ttl;
    const redisKey = `throttle:${key}`;

    try {
      // 使用 Redis 原子操作
      const client = this.redisService.getClient();
      const pipeline = client.pipeline();
      pipeline.incr(redisKey);
      pipeline.expire(redisKey, ttlSeconds);
      pipeline.ttl(redisKey);

      const results = await pipeline.exec();
      if (!results || results.length < 3) {
        throw new Error('Pipeline execution failed');
      }

      const totalHits = results[0][1] as number;
      const ttl = results[2][1] as number;

      const allowed = totalHits <= config.limit;
      const timeToReset = ttl > 0 ? ttl * 1000 : 0;

      return {
        allowed,
        totalHits,
        timeToReset,
      };
    } catch (error) {
      this.logger.error('Redis 限流检查失败，使用内存回退', error);

      // Redis 失败时回退到内存存储
      return this.checkLimitInMemory(key, config);
    }
  }

  /**
   * 内存存储回退方案
   */
  private checkLimitInMemory(
    key: string,
    config: ThrottlerConfig,
  ): { allowed: boolean; totalHits: number; timeToReset: number } {
    const now = Date.now();

    // 简化的内存存储逻辑
    const record = {
      count: 1,
      resetTime: now + (config.ttl * 1000),
    };

    const allowed = record.count <= config.limit;
    const timeToReset = Math.max(0, record.resetTime - now);

    return {
      allowed,
      totalHits: record.count,
      timeToReset,
    };
  }

  /**
   * 设置速率限制响应头
   */
  private setRateLimitHeaders(
    response: Response,
    config: ThrottlerConfig,
    totalHits: number,
    timeToReset: number,
  ): void {
    response.setHeader('X-RateLimit-Limit', config.limit);
    response.setHeader('X-RateLimit-Remaining', Math.max(0, config.limit - totalHits));
    response.setHeader('X-RateLimit-Reset', new Date(Date.now() + timeToReset).toISOString());
    response.setHeader('X-RateLimit-RetryAfter', Math.ceil(timeToReset / 1000));
  }

  // 移除内存清理方法，因为现在使用 Redis 自动过期

  /**
   * 记录限流超出日志
   */
  private logRateLimitExceeded(
    request: Request,
    key: string,
    config: ThrottlerConfig,
    totalHits: number,
  ): void {
    const logData = {
      type: 'RATE_LIMIT_EXCEEDED',
      key,
      limit: config.limit,
      totalHits,
      ttl: config.ttl,
      request: {
        method: request.method,
        path: request.path,
        ip: this.getRealIpAddress(request),
        userAgent: request.get('User-Agent'),
      },
      timestamp: new Date().toISOString(),
    };

    this.logger.warn('速率限制超出', logData);
  }

  /**
   * 记录请求日志
   */
  private logRequest(
    request: Request,
    key: string,
    config: ThrottlerConfig,
    totalHits: number,
  ): void {
    // 只在调试模式下记录
    if (this.configService.get('NODE_ENV') === 'development') {
      const logData = {
        type: 'RATE_LIMIT_CHECK',
        key,
        limit: config.limit,
        totalHits,
        remaining: config.limit - totalHits,
        request: {
          method: request.method,
          path: request.path,
          ip: this.getRealIpAddress(request),
        },
        timestamp: new Date().toISOString(),
      };

      this.logger.debug('速率限制检查', logData);
    }
  }
}
