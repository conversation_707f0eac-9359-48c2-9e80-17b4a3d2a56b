/**
 * 响应拦截器
 * 统一处理API响应格式
 */

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Request, Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { ApiResponseDto } from '../../../shared/dto/response.dto';

@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, ApiResponseDto<T>> {
  private readonly logger = new Logger(ResponseInterceptor.name);

  constructor(private readonly configService: ConfigService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<ApiResponseDto<T>> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    
    const startTime = Date.now();
    const requestId = request.headers['x-request-id'] as string || this.generateRequestId();
    const apiVersion = this.configService.get('API_VERSION', 'v1');

    // 设置响应头
    response.setHeader('X-Request-ID', requestId);
    response.setHeader('X-API-Version', apiVersion);
    response.setHeader('X-Timestamp', new Date().toISOString());

    return next.handle().pipe(
      map((data: T) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        // 记录响应日志
        this.logResponse(request, response, duration, requestId);

        // 构建成功响应 - 使用现有的 ApiResponseDto 格式
        const successResponse: ApiResponseDto<T> = {
          success: true,
          data,
          message: this.getSuccessMessage(request.method, request.path),
          timestamp: new Date().toISOString(),
        };

        // 添加性能指标
        if (this.shouldIncludeMetrics(request)) {
          (successResponse as any).meta = {
            duration,
            timestamp: new Date().toISOString(),
            path: request.path,
            method: request.method,
          };
        }

        return successResponse;
      }),
    );
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 记录响应日志
   */
  private logResponse(
    request: Request,
    response: Response,
    duration: number,
    requestId: string,
  ): void {
    const { method, url, ip } = request;
    const { statusCode } = response;
    const userAgent = request.get('User-Agent') || '';

    const logData = {
      requestId,
      method,
      url,
      statusCode,
      duration,
      ip,
      userAgent,
      timestamp: new Date().toISOString(),
    };

    if (statusCode >= 400) {
      this.logger.warn('HTTP Response Error', logData);
    } else if (duration > 1000) {
      this.logger.warn('Slow HTTP Response', logData);
    } else {
      this.logger.log('HTTP Response', logData);
    }
  }

  /**
   * 获取成功消息
   */
  private getSuccessMessage(method: string, path: string): string {
    const messages: Record<string, string> = {
      GET: '查询成功',
      POST: '创建成功',
      PUT: '更新成功',
      PATCH: '更新成功',
      DELETE: '删除成功',
    };

    // 特殊路径的自定义消息
    const pathMessages: Record<string, Record<string, string>> = {
      '/auth/login': { POST: '登录成功' },
      '/auth/logout': { POST: '登出成功' },
      '/auth/register': { POST: '注册成功' },
      '/auth/refresh': { POST: '令牌刷新成功' },
      '/auth/reset-password': { POST: '密码重置邮件已发送' },
      '/auth/confirm-reset': { POST: '密码重置成功' },
      '/users/me/password': { PUT: '密码修改成功' },
      '/users/me/mfa/enable': { POST: 'MFA启用成功' },
      '/users/me/mfa/disable': { POST: 'MFA禁用成功' },
    };

    // 检查特殊路径
    if (pathMessages[path] && pathMessages[path][method]) {
      return pathMessages[path][method];
    }

    // 使用默认消息
    return messages[method] || '操作成功';
  }

  /**
   * 是否包含性能指标
   */
  private shouldIncludeMetrics(request: Request): boolean {
    // 开发环境或调试模式下包含指标
    const environment = this.configService.get('NODE_ENV', 'development');
    const includeMetrics = this.configService.get('RESPONSE_INCLUDE_METRICS', false);
    const debugMode = request.headers['x-debug'] === 'true';

    return environment === 'development' || includeMetrics || debugMode;
  }
}

/**
 * 分页响应拦截器
 * 专门处理分页数据的响应格式
 */
@Injectable()
export class PaginationResponseInterceptor<T> implements NestInterceptor<T, ApiResponseDto<T>> {
  private readonly logger = new Logger(PaginationResponseInterceptor.name);

  constructor(private readonly configService: ConfigService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<ApiResponseDto<T>> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    
    const requestId = request.headers['x-request-id'] as string || this.generateRequestId();
    const apiVersion = this.configService.get('API_VERSION', 'v1');

    // 设置响应头
    response.setHeader('X-Request-ID', requestId);
    response.setHeader('X-API-Version', apiVersion);

    return next.handle().pipe(
      map((data: any) => {
        // 检查是否为分页数据
        if (this.isPaginationData(data)) {
          // 设置分页相关的响应头
          response.setHeader('X-Total-Count', data.pagination.total);
          response.setHeader('X-Page', data.pagination.page);
          response.setHeader('X-Per-Page', data.pagination.limit);
          response.setHeader('X-Total-Pages', data.pagination.totalPages);
          
          if (data.pagination.hasNext) {
            response.setHeader('X-Next-Page', data.pagination.nextPage);
          }
          
          if (data.pagination.hasPrev) {
            response.setHeader('X-Prev-Page', data.pagination.prevPage);
          }
        }

        const successResponse: ApiResponseDto<T> = {
          success: true,
          data,
          message: '查询成功',
          timestamp: new Date().toISOString(),
        };

        return successResponse;
      }),
    );
  }

  /**
   * 检查是否为分页数据
   */
  private isPaginationData(data: any): boolean {
    return data && 
           typeof data === 'object' && 
           data.pagination && 
           typeof data.pagination.total === 'number' &&
           typeof data.pagination.page === 'number' &&
           typeof data.pagination.limit === 'number';
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 文件响应拦截器
 * 处理文件上传和下载的响应
 */
@Injectable()
export class FileResponseInterceptor implements NestInterceptor {
  private readonly logger = new Logger(FileResponseInterceptor.name);

  constructor(private readonly configService: ConfigService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    return next.handle().pipe(
      map((data: any) => {
        // 检查是否为文件数据
        if (this.isFileData(data)) {
          // 设置文件相关的响应头
          if (data.mimetype) {
            response.setHeader('Content-Type', data.mimetype);
          }
          
          if (data.size) {
            response.setHeader('Content-Length', data.size);
          }
          
          if (data.filename) {
            response.setHeader('Content-Disposition', `attachment; filename="${data.filename}"`);
          }

          // 设置缓存头
          const maxAge = this.configService.get('FILE_CACHE_MAX_AGE', 3600);
          response.setHeader('Cache-Control', `public, max-age=${maxAge}`);
          response.setHeader('ETag', data.id || data.hash);
        }

        return data;
      }),
    );
  }

  /**
   * 检查是否为文件数据
   */
  private isFileData(data: any): boolean {
    return data && 
           typeof data === 'object' && 
           (data.filename || data.mimetype || data.size);
  }
}

/**
 * 缓存响应拦截器
 * 处理缓存相关的响应头
 */
@Injectable()
export class CacheResponseInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CacheResponseInterceptor.name);

  constructor(private readonly configService: ConfigService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    return next.handle().pipe(
      map((data: any) => {
        // 检查是否为缓存数据
        if (this.isCacheData(data)) {
          // 设置缓存相关的响应头
          response.setHeader('X-Cache-Hit', data.hit ? 'true' : 'false');
          
          if (data.ttl) {
            response.setHeader('X-Cache-TTL', data.ttl);
          }
          
          if (data.createdAt) {
            response.setHeader('X-Cache-Created', data.createdAt);
          }
          
          if (data.updatedAt) {
            response.setHeader('X-Cache-Updated', data.updatedAt);
          }

          // 设置标准缓存头
          if (data.hit && data.ttl > 0) {
            response.setHeader('Cache-Control', `public, max-age=${Math.floor(data.ttl / 1000)}`);
          }
        }

        return data;
      }),
    );
  }

  /**
   * 检查是否为缓存数据
   */
  private isCacheData(data: any): boolean {
    return data && 
           typeof data === 'object' && 
           typeof data.hit === 'boolean';
  }
}
