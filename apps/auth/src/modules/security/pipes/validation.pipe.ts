/**
 * 验证管道
 * 增强的数据验证管道，提供详细的错误信息和自定义验证逻辑
 */

import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { validate, ValidationError } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { ConfigService } from '@nestjs/config';

// 验证选项接口
export interface ValidationPipeOptions {
  skipMissingProperties?: boolean;
  whitelist?: boolean;
  forbidNonWhitelisted?: boolean;
  transform?: boolean;
  disableErrorMessages?: boolean;
  validationError?: {
    target?: boolean;
    value?: boolean;
  };
  exceptionFactory?: (errors: ValidationError[]) => any;
  groups?: string[];
  always?: boolean;
  strictGroups?: boolean;
  dismissDefaultMessages?: boolean;
  validationContext?: any;
  stopAtFirstError?: boolean;
}

// 验证错误详情接口
export interface ValidationErrorDetail {
  field: string;
  value: any;
  constraints: Record<string, string>;
  children?: ValidationErrorDetail[];
}

// 验证异常接口
export interface ValidationException {
  message: string;
  errors: ValidationErrorDetail[];
  statusCode: number;
  timestamp: string;
  path?: string;
}

@Injectable()
export class ValidationPipe implements PipeTransform<any> {
  private readonly logger = new Logger(ValidationPipe.name);
  private readonly options: ValidationPipeOptions;

  constructor(
    private readonly configService: ConfigService,
    options?: ValidationPipeOptions,
  ) {
    this.options = {
      skipMissingProperties: false,
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      disableErrorMessages: this.configService.get('NODE_ENV') === 'production',
      validationError: {
        target: false,
        value: false,
      },
      stopAtFirstError: false,
      ...options,
    };
  }

  async transform(value: any, metadata: ArgumentMetadata): Promise<any> {
    // 如果没有类型信息，直接返回
    if (!metadata.metatype || !this.toValidate(metadata.metatype)) {
      return value;
    }

    // 转换为类实例
    const object = plainToClass(metadata.metatype, value, {
      enableImplicitConversion: this.options.transform,
    });

    // 执行验证
    const errors = await validate(object, {
      skipMissingProperties: this.options.skipMissingProperties,
      whitelist: this.options.whitelist,
      forbidNonWhitelisted: this.options.forbidNonWhitelisted,
      groups: this.options.groups,
      always: this.options.always,
      strictGroups: this.options.strictGroups,
      dismissDefaultMessages: this.options.dismissDefaultMessages,
      validationError: this.options.validationError,
      stopAtFirstError: this.options.stopAtFirstError,
    });

    if (errors.length > 0) {
      this.logValidationErrors(errors, metadata);
      
      if (this.options.exceptionFactory) {
        throw this.options.exceptionFactory(errors);
      }
      
      throw this.createValidationException(errors, metadata);
    }

    return this.options.transform ? object : value;
  }

  /**
   * 检查是否需要验证
   */
  private toValidate(metatype: Function): boolean {
    const types: Function[] = [String, Boolean, Number, Array, Object];
    return !types.includes(metatype);
  }

  /**
   * 创建验证异常
   */
  private createValidationException(
    errors: ValidationError[],
    metadata: ArgumentMetadata,
  ): BadRequestException {
    const validationErrors = this.transformValidationErrors(errors);
    
    const exception: ValidationException = {
      message: '数据验证失败',
      errors: validationErrors,
      statusCode: 400,
      timestamp: new Date().toISOString(),
    };

    return new BadRequestException(exception);
  }

  /**
   * 转换验证错误
   */
  private transformValidationErrors(errors: ValidationError[]): ValidationErrorDetail[] {
    return errors.map(error => this.mapChildrenToValidationErrors(error));
  }

  /**
   * 映射子错误到验证错误
   */
  private mapChildrenToValidationErrors(error: ValidationError): ValidationErrorDetail {
    const validationError: ValidationErrorDetail = {
      field: error.property,
      value: error.value,
      constraints: error.constraints || {},
    };

    if (error.children && error.children.length > 0) {
      validationError.children = error.children.map(child =>
        this.mapChildrenToValidationErrors(child),
      );
    }

    return validationError;
  }

  /**
   * 记录验证错误
   */
  private logValidationErrors(
    errors: ValidationError[],
    metadata: ArgumentMetadata,
  ): void {
    const errorDetails = {
      type: metadata.type,
      metatype: metadata.metatype?.name,
      data: metadata.data,
      errors: this.transformValidationErrors(errors),
      timestamp: new Date().toISOString(),
    };

    this.logger.warn('数据验证失败', errorDetails);
  }
}

/**
 * 自定义验证管道
 * 支持更多自定义验证逻辑
 */
@Injectable()
export class CustomValidationPipe extends ValidationPipe {
  private readonly customLogger = new Logger(CustomValidationPipe.name);

  constructor(
    configService: ConfigService,
    options?: ValidationPipeOptions,
  ) {
    super(configService, options);
  }

  async transform(value: any, metadata: ArgumentMetadata): Promise<any> {
    // 执行基础验证
    const result = await super.transform(value, metadata);

    // 执行自定义验证
    await this.performCustomValidation(result, metadata);

    return result;
  }

  /**
   * 执行自定义验证
   */
  private async performCustomValidation(
    value: any,
    metadata: ArgumentMetadata,
  ): Promise<void> {
    if (!value || typeof value !== 'object') {
      return;
    }

    // 业务规则验证
    await this.validateBusinessRules(value, metadata);

    // 安全验证
    await this.validateSecurity(value, metadata);

    // 性能验证
    await this.validatePerformance(value, metadata);
  }

  /**
   * 验证业务规则
   */
  private async validateBusinessRules(
    value: any,
    metadata: ArgumentMetadata,
  ): Promise<void> {
    // 用户注册验证
    if (metadata.metatype?.name === 'RegisterDto') {
      await this.validateUserRegistration(value);
    }

    // 密码修改验证
    if (metadata.metatype?.name === 'ChangePasswordDto') {
      await this.validatePasswordChange(value);
    }

    // 角色分配验证
    if (metadata.metatype?.name === 'AssignRoleDto') {
      await this.validateRoleAssignment(value);
    }
  }

  /**
   * 验证用户注册
   */
  private async validateUserRegistration(value: any): Promise<void> {
    const errors: string[] = [];

    // 检查密码确认
    if (value.password !== value.confirmPassword) {
      errors.push('密码确认不匹配');
    }

    // 检查用户名格式
    if (value.username && !/^[a-zA-Z0-9_-]+$/.test(value.username)) {
      errors.push('用户名只能包含字母、数字、下划线和连字符');
    }

    // 检查邮箱域名
    if (value.email && this.isDisposableEmail(value.email)) {
      errors.push('不允许使用临时邮箱');
    }

    if (errors.length > 0) {
      throw new BadRequestException({
        message: '用户注册验证失败',
        errors: errors.map(error => ({
          field: 'general',
          value: null,
          constraints: { custom: error },
        })),
      });
    }
  }

  /**
   * 验证密码修改
   */
  private async validatePasswordChange(value: any): Promise<void> {
    const errors: string[] = [];

    // 检查新密码确认
    if (value.newPassword !== value.confirmPassword) {
      errors.push('新密码确认不匹配');
    }

    // 检查新密码不能与当前密码相同
    if (value.currentPassword === value.newPassword) {
      errors.push('新密码不能与当前密码相同');
    }

    if (errors.length > 0) {
      throw new BadRequestException({
        message: '密码修改验证失败',
        errors: errors.map(error => ({
          field: 'general',
          value: null,
          constraints: { custom: error },
        })),
      });
    }
  }

  /**
   * 验证角色分配
   */
  private async validateRoleAssignment(value: any): Promise<void> {
    const errors: string[] = [];

    // 检查角色数量限制
    if (value.roles && value.roles.length > 10) {
      errors.push('用户角色数量不能超过10个');
    }

    // 检查系统角色保护
    const systemRoles = ['super_admin', 'system_admin'];
    if (value.roles && value.roles.some((role: string) => systemRoles.includes(role))) {
      errors.push('不能分配系统保护角色');
    }

    if (errors.length > 0) {
      throw new BadRequestException({
        message: '角色分配验证失败',
        errors: errors.map(error => ({
          field: 'roles',
          value: value.roles,
          constraints: { custom: error },
        })),
      });
    }
  }

  /**
   * 验证安全性
   */
  private async validateSecurity(
    value: any,
    metadata: ArgumentMetadata,
  ): Promise<void> {
    // SQL注入检测
    this.detectSqlInjection(value);

    // XSS检测
    this.detectXss(value);

    // 路径遍历检测
    this.detectPathTraversal(value);
  }

  /**
   * 检测SQL注入
   */
  private detectSqlInjection(value: any): void {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(--|\/\*|\*\/|;)/,
      /(\b(OR|AND)\b.*=.*)/i,
    ];

    this.checkPatterns(value, sqlPatterns, 'SQL注入检测');
  }

  /**
   * 检测XSS
   */
  private detectXss(value: any): void {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/i,
      /on\w+\s*=/i,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    ];

    this.checkPatterns(value, xssPatterns, 'XSS检测');
  }

  /**
   * 检测路径遍历
   */
  private detectPathTraversal(value: any): void {
    const pathPatterns = [
      /\.\.\//,
      /\.\.\\/, 
      /%2e%2e%2f/i,
      /%2e%2e%5c/i,
    ];

    this.checkPatterns(value, pathPatterns, '路径遍历检测');
  }

  /**
   * 检查模式匹配
   */
  private checkPatterns(value: any, patterns: RegExp[], type: string): void {
    if (typeof value === 'string') {
      for (const pattern of patterns) {
        if (pattern.test(value)) {
          this.customLogger.warn(`${type}警告`, { value, pattern: pattern.source });
          throw new BadRequestException(`检测到潜在的安全威胁: ${type}`);
        }
      }
    } else if (typeof value === 'object' && value !== null) {
      for (const key in value) {
        this.checkPatterns(value[key], patterns, type);
      }
    }
  }

  /**
   * 验证性能
   */
  private async validatePerformance(
    value: any,
    metadata: ArgumentMetadata,
  ): Promise<void> {
    // 检查数据大小
    const jsonString = JSON.stringify(value);
    const maxSize = 1024 * 1024; // 1MB

    if (jsonString.length > maxSize) {
      throw new BadRequestException('请求数据过大');
    }

    // 检查数组长度
    this.checkArrayLimits(value);

    // 检查字符串长度
    this.checkStringLimits(value);
  }

  /**
   * 检查数组限制
   */
  private checkArrayLimits(value: any): void {
    if (Array.isArray(value)) {
      if (value.length > 1000) {
        throw new BadRequestException('数组长度超出限制');
      }
      value.forEach(item => this.checkArrayLimits(item));
    } else if (typeof value === 'object' && value !== null) {
      Object.values(value).forEach(item => this.checkArrayLimits(item));
    }
  }

  /**
   * 检查字符串长度限制
   */
  private checkStringLimits(value: any): void {
    if (typeof value === 'string') {
      if (value.length > 10000) {
        throw new BadRequestException('字符串长度超出限制');
      }
    } else if (typeof value === 'object' && value !== null) {
      Object.values(value).forEach(item => this.checkStringLimits(item));
    }
  }

  /**
   * 检查是否为临时邮箱
   */
  private isDisposableEmail(email: string): boolean {
    const disposableDomains = [
      '10minutemail.com',
      'guerrillamail.com',
      'mailinator.com',
      'tempmail.org',
      'yopmail.com',
      'throwaway.email',
    ];

    const domain = email.split('@')[1]?.toLowerCase();
    return disposableDomains.includes(domain);
  }
}
