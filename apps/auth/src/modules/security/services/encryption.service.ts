import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

export interface EncryptionResult {
  encrypted: string;
  iv: string;
  tag?: string;
  salt?: string;
}

export interface DecryptionInput {
  encrypted: string;
  iv: string;
  tag?: string;
  salt?: string;
}

@Injectable()
export class EncryptionService {
  private readonly logger = new Logger(EncryptionService.name);
  private readonly algorithm: string;
  private readonly keyDerivation: string;
  private readonly iterations: number;
  private readonly saltLength: number;
  private readonly ivLength: number;
  private readonly tagLength: number;
  private readonly masterKey: string;

  constructor(private configService: ConfigService) {
    this.algorithm = this.configService.get<string>('security.dataProtection.encryption.algorithm', 'aes-256-gcm');
    this.keyDerivation = this.configService.get<string>('security.dataProtection.encryption.keyDerivation', 'pbkdf2');
    this.iterations = this.configService.get<number>('security.dataProtection.encryption.iterations', 100000);
    this.saltLength = this.configService.get<number>('security.dataProtection.encryption.saltLength', 32);
    this.ivLength = this.configService.get<number>('security.dataProtection.encryption.ivLength', 16);
    this.tagLength = this.configService.get<number>('security.dataProtection.encryption.tagLength', 16);
    this.masterKey = this.configService.get<string>('security.dataProtection.encryption.masterKey');

    if (!this.masterKey) {
      this.logger.warn('主加密密钥未配置，加密功能将不可用');
    }
  }

  /**
   * 加密数据
   */
  encrypt(data: string, password?: string): EncryptionResult {
    if (!this.masterKey && !password) {
      throw new Error('加密密钥未配置');
    }

    try {
      const salt = crypto.randomBytes(this.saltLength);
      const iv = crypto.randomBytes(this.ivLength);
      
      // 派生密钥
      const key = this.deriveKey(password || this.masterKey, salt);
      
      // 创建加密器
      const cipher = crypto.createCipher(this.algorithm, key);
      
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const result: EncryptionResult = {
        encrypted,
        iv: iv.toString('hex'),
        salt: salt.toString('hex'),
      };

      // 如果使用GCM模式，添加认证标签
      if (this.algorithm.includes('gcm')) {
        result.tag = (cipher as any).getAuthTag().toString('hex');
      }

      return result;
    } catch (error) {
      this.logger.error('数据加密失败', error);
      throw new Error('数据加密失败');
    }
  }

  /**
   * 解密数据
   */
  decrypt(input: DecryptionInput, password?: string): string {
    if (!this.masterKey && !password) {
      throw new Error('解密密钥未配置');
    }

    try {
      const salt = Buffer.from(input.salt!, 'hex');
      const iv = Buffer.from(input.iv, 'hex');
      
      // 派生密钥
      const key = this.deriveKey(password || this.masterKey, salt);
      
      // 创建解密器
      const decipher = crypto.createDecipher(this.algorithm, key);
      
      // 如果使用GCM模式，设置认证标签
      if (this.algorithm.includes('gcm') && input.tag) {
        (decipher as any).setAuthTag(Buffer.from(input.tag, 'hex'));
      }
      
      let decrypted = decipher.update(input.encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      this.logger.error('数据解密失败', error);
      throw new Error('数据解密失败');
    }
  }

  /**
   * 生成随机密钥
   */
  generateKey(length = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * 生成随机盐值
   */
  generateSalt(length = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * 计算哈希值
   */
  hash(data: string, algorithm = 'sha256'): string {
    return crypto.createHash(algorithm).update(data).digest('hex');
  }

  /**
   * 计算HMAC
   */
  hmac(data: string, key: string, algorithm = 'sha256'): string {
    return crypto.createHmac(algorithm, key).update(data).digest('hex');
  }

  /**
   * 验证HMAC
   */
  verifyHmac(data: string, key: string, signature: string, algorithm = 'sha256'): boolean {
    const expectedSignature = this.hmac(data, key, algorithm);
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  }

  /**
   * 生成数字签名
   */
  sign(data: string, privateKey: string, algorithm = 'RSA-SHA256'): string {
    const sign = crypto.createSign(algorithm);
    sign.update(data);
    return sign.sign(privateKey, 'hex');
  }

  /**
   * 验证数字签名
   */
  verify(data: string, signature: string, publicKey: string, algorithm = 'RSA-SHA256'): boolean {
    const verify = crypto.createVerify(algorithm);
    verify.update(data);
    return verify.verify(publicKey, signature, 'hex');
  }

  /**
   * 生成RSA密钥对
   */
  generateKeyPair(keySize = 2048): { publicKey: string; privateKey: string } {
    const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
      modulusLength: keySize,
      publicKeyEncoding: {
        type: 'spki',
        format: 'pem',
      },
      privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem',
      },
    });

    return { publicKey, privateKey };
  }

  /**
   * 加密敏感字段
   */
  encryptField(value: string, fieldName: string): string {
    if (!value) return value;
    
    try {
      const result = this.encrypt(value);
      return JSON.stringify(result);
    } catch (error) {
      this.logger.error(`字段加密失败: ${fieldName}`, error);
      return value; // 返回原值，避免数据丢失
    }
  }

  /**
   * 解密敏感字段
   */
  decryptField(encryptedValue: string, fieldName: string): string {
    if (!encryptedValue) return encryptedValue;
    
    try {
      // 检查是否为加密数据
      if (!encryptedValue.startsWith('{')) {
        return encryptedValue; // 可能是未加密的旧数据
      }
      
      const input = JSON.parse(encryptedValue) as DecryptionInput;
      return this.decrypt(input);
    } catch (error) {
      this.logger.error(`字段解密失败: ${fieldName}`, error);
      return encryptedValue; // 返回原值，避免数据丢失
    }
  }

  /**
   * 批量加密对象字段
   */
  encryptObject(obj: Record<string, any>, fields: string[]): Record<string, any> {
    const result = { ...obj };
    
    for (const field of fields) {
      if (result[field]) {
        result[field] = this.encryptField(result[field], field);
      }
    }
    
    return result;
  }

  /**
   * 批量解密对象字段
   */
  decryptObject(obj: Record<string, any>, fields: string[]): Record<string, any> {
    const result = { ...obj };
    
    for (const field of fields) {
      if (result[field]) {
        result[field] = this.decryptField(result[field], field);
      }
    }
    
    return result;
  }

  /**
   * 派生密钥
   */
  private deriveKey(password: string, salt: Buffer): Buffer {
    switch (this.keyDerivation) {
      case 'pbkdf2':
        return crypto.pbkdf2Sync(password, salt, this.iterations, 32, 'sha256');
      case 'scrypt':
        return crypto.scryptSync(password, salt, 32);
      default:
        throw new Error(`不支持的密钥派生算法: ${this.keyDerivation}`);
    }
  }

  /**
   * 安全比较字符串
   */
  safeCompare(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }
    
    return crypto.timingSafeEqual(Buffer.from(a), Buffer.from(b));
  }

  /**
   * 生成安全随机字符串
   */
  generateSecureRandom(length = 32, encoding: BufferEncoding = 'hex'): string {
    return crypto.randomBytes(length).toString(encoding);
  }

  /**
   * 计算文件哈希
   */
  async hashFile(filePath: string, algorithm = 'sha256'): Promise<string> {
    return new Promise((resolve, reject) => {
      const hash = crypto.createHash(algorithm);
      const fs = require('fs');
      const stream = fs.createReadStream(filePath);
      
      stream.on('data', (data: Buffer) => hash.update(data));
      stream.on('end', () => resolve(hash.digest('hex')));
      stream.on('error', reject);
    });
  }
}
