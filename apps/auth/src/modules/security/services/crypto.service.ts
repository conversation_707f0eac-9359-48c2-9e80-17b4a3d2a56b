/**
 * 加密服务
 * 提供各种加密、解密、哈希和签名功能
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import * as bcrypt from 'bcrypt';
import { ENCRYPTION } from '../../../common/constants/auth.constants';
import {MICROSERVICE_NAMES} from "@libs/shared";

// 加密结果接口
export interface EncryptionResult {
  encrypted: string;              // 加密后的数据
  iv: string;                     // 初始化向量
  salt: string;                   // 盐值
  tag?: string;                   // 认证标签（GCM模式）
}

// 解密参数接口
export interface DecryptionParams {
  encrypted: string;              // 加密的数据
  iv: string;                     // 初始化向量
  salt: string;                   // 盐值
  tag?: string;                   // 认证标签（GCM模式）
}

// 密钥对接口
export interface KeyPair {
  publicKey: string;              // 公钥
  privateKey: string;             // 私钥
}

@Injectable()
export class CryptoService {
  private readonly logger = new Logger(CryptoService.name);
  private readonly algorithm: string;
  private readonly keyLength: number;
  private readonly ivLength: number;
  private readonly saltLength: number;
  private readonly iterations: number;
  private readonly masterKey: string;

  constructor(private readonly configService: ConfigService) {
    this.algorithm = this.configService.get('ENCRYPTION_ALGORITHM', ENCRYPTION.ALGORITHM);
    this.keyLength = this.configService.get('ENCRYPTION_KEY_LENGTH', ENCRYPTION.KEY_LENGTH);
    this.ivLength = this.configService.get('ENCRYPTION_IV_LENGTH', ENCRYPTION.IV_LENGTH);
    this.saltLength = this.configService.get('ENCRYPTION_SALT_LENGTH', ENCRYPTION.SALT_LENGTH);
    this.iterations = this.configService.get('ENCRYPTION_ITERATIONS', ENCRYPTION.ITERATIONS);
    this.masterKey = this.configService.get('ENCRYPTION_MASTER_KEY') || this.generateKey();
  }

  /**
   * 加密数据
   */
  encrypt(data: string, password?: string): EncryptionResult {
    try {
      const salt = this.generateSalt();
      const iv = this.generateIV();
      const key = this.deriveKey(password || this.masterKey, salt);
      
      const cipher = crypto.createCipher(this.algorithm, key);
      // 设置附加认证数据 (AAD) - 仅在 GCM 模式下可用
      (cipher as any).setAAD(Buffer.from(MICROSERVICE_NAMES.AUTH_SERVICE));
      
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const result: EncryptionResult = {
        encrypted,
        iv: iv.toString('hex'),
        salt: salt.toString('hex'),
      };

      // 如果使用GCM模式，添加认证标签
      if (this.algorithm.includes('gcm')) {
        result.tag = (cipher as any).getAuthTag().toString('hex');
      }

      return result;
    } catch (error) {
      this.logger.error('数据加密失败', error);
      throw new Error('数据加密失败');
    }
  }

  /**
   * 解密数据
   */
  decrypt(params: DecryptionParams, password?: string): string {
    try {
      const { encrypted, iv, salt, tag } = params;
      const key = this.deriveKey(password || this.masterKey, Buffer.from(salt, 'hex'));
      
      const decipher = crypto.createDecipher(this.algorithm, key);
      // 设置附加认证数据 (AAD) - 仅在 GCM 模式下可用
      (decipher as any).setAAD(Buffer.from(MICROSERVICE_NAMES.AUTH_SERVICE));
      
      // 如果使用GCM模式，设置认证标签
      if (this.algorithm.includes('gcm') && tag) {
        (decipher as any).setAuthTag(Buffer.from(tag, 'hex'));
      }
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      this.logger.error('数据解密失败', error);
      throw new Error('数据解密失败');
    }
  }

  /**
   * 哈希密码
   */
  async hashPassword(password: string, saltRounds: number = 12): Promise<string> {
    try {
      return await bcrypt.hash(password, saltRounds);
    } catch (error) {
      this.logger.error('密码哈希失败', error);
      throw new Error('密码哈希失败');
    }
  }

  /**
   * 验证密码
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      this.logger.error('密码验证失败', error);
      return false;
    }
  }

  /**
   * 生成随机密钥
   */
  generateKey(length: number = this.keyLength): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * 生成随机盐值
   */
  generateSalt(length: number = this.saltLength): Buffer {
    return crypto.randomBytes(length);
  }

  /**
   * 生成初始化向量
   */
  generateIV(length: number = this.ivLength): Buffer {
    return crypto.randomBytes(length);
  }

  /**
   * 生成随机令牌
   */
  generateToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * 生成UUID
   */
  generateUUID(): string {
    return crypto.randomUUID();
  }

  /**
   * 计算哈希值
   */
  hash(data: string, algorithm: string = 'sha256'): string {
    return crypto.createHash(algorithm).update(data).digest('hex');
  }

  /**
   * 计算HMAC
   */
  hmac(data: string, key: string, algorithm: string = 'sha256'): string {
    return crypto.createHmac(algorithm, key).update(data).digest('hex');
  }

  /**
   * 验证HMAC
   */
  verifyHmac(data: string, key: string, signature: string, algorithm: string = 'sha256'): boolean {
    try {
      const expectedSignature = this.hmac(data, key, algorithm);
      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
    } catch (error) {
      this.logger.error('HMAC验证失败', error);
      return false;
    }
  }

  /**
   * 生成数字签名
   */
  sign(data: string, privateKey: string, algorithm: string = 'RSA-SHA256'): string {
    try {
      const sign = crypto.createSign(algorithm);
      sign.update(data);
      return sign.sign(privateKey, 'hex');
    } catch (error) {
      this.logger.error('数字签名失败', error);
      throw new Error('数字签名失败');
    }
  }

  /**
   * 验证数字签名
   */
  verify(data: string, signature: string, publicKey: string, algorithm: string = 'RSA-SHA256'): boolean {
    try {
      const verify = crypto.createVerify(algorithm);
      verify.update(data);
      return verify.verify(publicKey, signature, 'hex');
    } catch (error) {
      this.logger.error('数字签名验证失败', error);
      return false;
    }
  }

  /**
   * 生成RSA密钥对
   */
  generateKeyPair(keySize: number = 2048): KeyPair {
    try {
      const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
        modulusLength: keySize,
        publicKeyEncoding: {
          type: 'spki',
          format: 'pem',
        },
        privateKeyEncoding: {
          type: 'pkcs8',
          format: 'pem',
        },
      });

      return { publicKey, privateKey };
    } catch (error) {
      this.logger.error('密钥对生成失败', error);
      throw new Error('密钥对生成失败');
    }
  }

  /**
   * 生成椭圆曲线密钥对
   */
  generateECKeyPair(namedCurve: string = 'secp256k1'): KeyPair {
    try {
      const { publicKey, privateKey } = crypto.generateKeyPairSync('ec', {
        namedCurve,
        publicKeyEncoding: {
          type: 'spki',
          format: 'pem',
        },
        privateKeyEncoding: {
          type: 'pkcs8',
          format: 'pem',
        },
      });

      return { publicKey, privateKey };
    } catch (error) {
      this.logger.error('椭圆曲线密钥对生成失败', error);
      throw new Error('椭圆曲线密钥对生成失败');
    }
  }

  /**
   * 派生密钥
   */
  private deriveKey(password: string, salt: Buffer): Buffer {
    return crypto.pbkdf2Sync(password, salt, this.iterations, this.keyLength, 'sha256');
  }

  /**
   * 生成安全的随机数
   */
  generateSecureRandom(min: number, max: number): number {
    const range = max - min + 1;
    const bytesNeeded = Math.ceil(Math.log2(range) / 8);
    const maxValue = Math.pow(256, bytesNeeded);
    const threshold = maxValue - (maxValue % range);

    let randomValue;
    do {
      const randomBytes = crypto.randomBytes(bytesNeeded);
      randomValue = 0;
      for (let i = 0; i < bytesNeeded; i++) {
        randomValue = randomValue * 256 + randomBytes[i];
      }
    } while (randomValue >= threshold);

    return min + (randomValue % range);
  }

  /**
   * 生成密码
   */
  generatePassword(
    length: number = 12,
    options: {
      includeUppercase?: boolean;
      includeLowercase?: boolean;
      includeNumbers?: boolean;
      includeSymbols?: boolean;
      excludeSimilar?: boolean;
    } = {}
  ): string {
    const {
      includeUppercase = true,
      includeLowercase = true,
      includeNumbers = true,
      includeSymbols = true,
      excludeSimilar = false,
    } = options;

    let charset = '';
    if (includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz';
    if (includeNumbers) charset += '0123456789';
    if (includeSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';

    if (excludeSimilar) {
      charset = charset.replace(/[0O1lI]/g, '');
    }

    if (!charset) {
      throw new Error('至少需要选择一种字符类型');
    }

    let password = '';
    for (let i = 0; i < length; i++) {
      const randomIndex = this.generateSecureRandom(0, charset.length - 1);
      password += charset[randomIndex];
    }

    return password;
  }

  /**
   * 计算密码强度
   */
  calculatePasswordStrength(password: string): {
    score: number;
    level: 'weak' | 'fair' | 'good' | 'strong';
    feedback: string[];
  } {
    let score = 0;
    const feedback: string[] = [];

    // 长度检查
    if (password.length >= 8) score += 1;
    else feedback.push('密码长度至少8位');

    if (password.length >= 12) score += 1;
    else if (password.length >= 8) feedback.push('建议密码长度12位以上');

    // 字符类型检查
    if (/[a-z]/.test(password)) score += 1;
    else feedback.push('包含小写字母');

    if (/[A-Z]/.test(password)) score += 1;
    else feedback.push('包含大写字母');

    if (/[0-9]/.test(password)) score += 1;
    else feedback.push('包含数字');

    if (/[^a-zA-Z0-9]/.test(password)) score += 1;
    else feedback.push('包含特殊字符');

    // 复杂度检查
    if (!/(.)\1{2,}/.test(password)) score += 1;
    else feedback.push('避免连续重复字符');

    if (!/123|abc|qwe/i.test(password)) score += 1;
    else feedback.push('避免常见字符序列');

    let level: 'weak' | 'fair' | 'good' | 'strong';
    if (score <= 2) level = 'weak';
    else if (score <= 4) level = 'fair';
    else if (score <= 6) level = 'good';
    else level = 'strong';

    return { score, level, feedback };
  }
}
