/**
 * 全局异常过滤器
 * 捕获和处理所有未处理的异常
 */

import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { ApiResponseDto } from '../../../shared/dto/response.dto';
import { ErrorResponse } from '../../../shared/interfaces/response.interface';
import { ERROR_CODES, ERROR_MESSAGES, ERROR_HTTP_STATUS } from '../../../shared/constants/error.constants';

// 异常详情接口
export interface ExceptionDetails {
  name: string;
  message: string;
  stack?: string;
  cause?: any;
  timestamp: string;
  requestId: string;
  path: string;
  method: string;
  ip: string;
  userAgent?: string;
  userId?: string;
}

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  constructor(private readonly configService: ConfigService) {}

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const exceptionDetails = this.buildExceptionDetails(exception, request);
    const errorResponse = this.buildErrorResponse(exception, exceptionDetails);

    // 记录异常日志
    this.logException(exception, exceptionDetails);

    // 设置响应头
    this.setResponseHeaders(response, exceptionDetails);

    // 发送错误响应
    const statusCode = this.getHttpStatusCodeFromResponse(errorResponse);
    response.status(statusCode).json(errorResponse);
  }

  /**
   * 构建异常详情
   */
  private buildExceptionDetails(exception: unknown, request: Request): ExceptionDetails {
    const requestId = this.getRequestId(request);
    const timestamp = new Date().toISOString();

    let name = 'UnknownError';
    let message = '未知错误';
    let stack: string | undefined;
    let cause: any;

    if (exception instanceof Error) {
      name = exception.name;
      message = exception.message;
      stack = exception.stack;
      cause = (exception as any).cause;
    } else if (typeof exception === 'string') {
      message = exception;
    } else if (exception && typeof exception === 'object') {
      name = (exception as any).name || name;
      message = (exception as any).message || message;
      stack = (exception as any).stack;
      cause = (exception as any).cause;
    }

    return {
      name,
      message,
      stack,
      cause,
      timestamp,
      requestId,
      path: request.path,
      method: request.method,
      ip: this.getClientIp(request),
      userAgent: request.get('User-Agent'),
      userId: (request as any).user?.id,
    };
  }

  /**
   * 构建错误响应
   */
  private buildErrorResponse(exception: unknown, details: ExceptionDetails): ErrorResponse {
    let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    let errorCode: string = ERROR_CODES.SYSTEM_INTERNAL_ERROR;
    let message: string = ERROR_MESSAGES[ERROR_CODES.SYSTEM_INTERNAL_ERROR];
    let errorDetails: any = undefined;

    // HTTP异常处理
    if (exception instanceof HttpException) {
      statusCode = exception.getStatus();
      const response = exception.getResponse();
      
      if (typeof response === 'string') {
        message = response;
      } else if (typeof response === 'object' && response !== null) {
        message = (response as any).message || message;
        errorCode = (response as any).code || this.getErrorCodeByStatus(statusCode);
        errorDetails = (response as any).details;
      }
    }
    // 数据库错误处理
    else if (this.isDatabaseError(exception)) {
      statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      errorCode = ERROR_CODES.SYSTEM_DATABASE_ERROR;
      message = ERROR_MESSAGES[ERROR_CODES.SYSTEM_DATABASE_ERROR];
    }
    // 验证错误处理
    else if (this.isValidationError(exception)) {
      statusCode = HttpStatus.BAD_REQUEST;
      errorCode = ERROR_CODES.VALIDATION_FAILED;
      message = ERROR_MESSAGES[ERROR_CODES.VALIDATION_FAILED];
      errorDetails = this.extractValidationDetails(exception);
    }
    // 超时错误处理
    else if (this.isTimeoutError(exception)) {
      statusCode = HttpStatus.REQUEST_TIMEOUT;
      errorCode = ERROR_CODES.SYSTEM_TIMEOUT;
      message = ERROR_MESSAGES[ERROR_CODES.SYSTEM_TIMEOUT];
    }
    // 网络错误处理
    else if (this.isNetworkError(exception)) {
      statusCode = HttpStatus.BAD_GATEWAY;
      errorCode = ERROR_CODES.SYSTEM_NETWORK_ERROR;
      message = ERROR_MESSAGES[ERROR_CODES.SYSTEM_NETWORK_ERROR];
    }

    const errorResponse: ErrorResponse = {
      success: false,
      error: {
        code: errorCode as any,
        message: this.sanitizeErrorMessage(message),
        details: errorDetails,
        timestamp: details.timestamp,
        path: details.path,
      },
      timestamp: details.timestamp,
      requestId: details.requestId || 'unknown',
      version: 'v1',
      data: null,
    };

    // 开发环境包含堆栈信息
    if (this.configService.get('NODE_ENV') === 'development' && details.stack) {
      errorResponse.error.stack = details.stack;
    }

    return errorResponse;
  }

  /**
   * 记录异常日志
   */
  private logException(exception: unknown, details: ExceptionDetails): void {
    const logData = {
      exception: {
        name: details.name,
        message: details.message,
        stack: details.stack,
        cause: details.cause,
      },
      request: {
        method: details.method,
        path: details.path,
        ip: details.ip,
        userAgent: details.userAgent,
        userId: details.userId,
      },
      requestId: details.requestId,
      timestamp: details.timestamp,
    };

    // 根据异常类型选择日志级别
    if (exception instanceof HttpException) {
      const status = exception.getStatus();
      if (status >= 500) {
        this.logger.error('HTTP服务器错误', logData);
      } else if (status >= 400) {
        this.logger.warn('HTTP客户端错误', logData);
      } else {
        this.logger.log('HTTP异常', logData);
      }
    } else if (this.isDatabaseError(exception)) {
      this.logger.error('数据库错误', logData);
    } else if (this.isValidationError(exception)) {
      this.logger.warn('验证错误', logData);
    } else {
      this.logger.error('未处理异常', logData);
    }
  }

  /**
   * 设置响应头
   */
  private setResponseHeaders(response: Response, details: ExceptionDetails): void {
    response.setHeader('X-Request-ID', details.requestId);
    response.setHeader('X-Timestamp', details.timestamp);
    response.setHeader('X-Error-Type', details.name);
    
    // 安全头
    response.setHeader('X-Content-Type-Options', 'nosniff');
    response.setHeader('X-Frame-Options', 'DENY');
    response.setHeader('X-XSS-Protection', '1; mode=block');
  }

  /**
   * 获取请求ID
   */
  private getRequestId(request: Request): string {
    return (request.headers['x-request-id'] as string) || 
           `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取客户端IP
   */
  private getClientIp(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      (request.headers['x-real-ip'] as string) ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }

  /**
   * 根据状态码获取错误代码
   */
  private getErrorCodeByStatus(status: number): string {
    const statusToCode: Record<number, string> = {
      400: ERROR_CODES.VALIDATION_FAILED,
      401: ERROR_CODES.AUTH_TOKEN_INVALID,
      403: ERROR_CODES.AUTH_INSUFFICIENT_PERMISSIONS,
      404: ERROR_CODES.USER_NOT_FOUND,
      409: ERROR_CODES.USER_ALREADY_EXISTS,
      422: ERROR_CODES.VALIDATION_FAILED,
      429: ERROR_CODES.AUTH_RATE_LIMITED,
      500: ERROR_CODES.SYSTEM_INTERNAL_ERROR,
      502: ERROR_CODES.SYSTEM_NETWORK_ERROR,
      503: ERROR_CODES.SYSTEM_MAINTENANCE,
      504: ERROR_CODES.SYSTEM_TIMEOUT,
    };

    return statusToCode[status] || ERROR_CODES.SYSTEM_INTERNAL_ERROR;
  }

  /**
   * 从错误响应中获取HTTP状态码
   */
  private getHttpStatusCodeFromResponse(errorResponse: ErrorResponse): number {
    const errorCode = errorResponse.error.code;

    // 根据错误代码映射到HTTP状态码
    const codeToStatus: Record<string, number> = {
      [ERROR_CODES.VALIDATION_FAILED]: HttpStatus.BAD_REQUEST,
      [ERROR_CODES.AUTH_TOKEN_INVALID]: HttpStatus.UNAUTHORIZED,
      [ERROR_CODES.AUTH_INSUFFICIENT_PERMISSIONS]: HttpStatus.FORBIDDEN,
      [ERROR_CODES.USER_NOT_FOUND]: HttpStatus.NOT_FOUND,
      [ERROR_CODES.USER_ALREADY_EXISTS]: HttpStatus.CONFLICT,
      [ERROR_CODES.AUTH_RATE_LIMITED]: HttpStatus.TOO_MANY_REQUESTS,
      [ERROR_CODES.SYSTEM_INTERNAL_ERROR]: HttpStatus.INTERNAL_SERVER_ERROR,
      [ERROR_CODES.SYSTEM_DATABASE_ERROR]: HttpStatus.INTERNAL_SERVER_ERROR,
      [ERROR_CODES.SYSTEM_NETWORK_ERROR]: HttpStatus.BAD_GATEWAY,
      [ERROR_CODES.SYSTEM_MAINTENANCE]: HttpStatus.SERVICE_UNAVAILABLE,
      [ERROR_CODES.SYSTEM_TIMEOUT]: HttpStatus.REQUEST_TIMEOUT,
    };

    return codeToStatus[errorCode as string] || HttpStatus.INTERNAL_SERVER_ERROR;
  }

  /**
   * 检查是否为数据库错误
   */
  private isDatabaseError(exception: unknown): boolean {
    if (!exception || typeof exception !== 'object') {
      return false;
    }

    const error = exception as any;
    
    // MongoDB错误
    if (error.name === 'MongoError' || error.name === 'MongoServerError') {
      return true;
    }

    // Mongoose错误
    if (error.name === 'ValidationError' || error.name === 'CastError') {
      return true;
    }

    // 连接错误
    if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
      return true;
    }

    return false;
  }

  /**
   * 检查是否为验证错误
   */
  private isValidationError(exception: unknown): boolean {
    if (!exception || typeof exception !== 'object') {
      return false;
    }

    const error = exception as any;
    
    return error.name === 'ValidationError' || 
           error.name === 'ValidatorError' ||
           (Array.isArray(error.errors) && error.errors.length > 0);
  }

  /**
   * 检查是否为超时错误
   */
  private isTimeoutError(exception: unknown): boolean {
    if (!exception || typeof exception !== 'object') {
      return false;
    }

    const error = exception as any;
    
    return error.name === 'TimeoutError' ||
           error.code === 'ETIMEDOUT' ||
           error.message?.includes('timeout');
  }

  /**
   * 检查是否为网络错误
   */
  private isNetworkError(exception: unknown): boolean {
    if (!exception || typeof exception !== 'object') {
      return false;
    }

    const error = exception as any;
    
    return error.code === 'ECONNREFUSED' ||
           error.code === 'ENOTFOUND' ||
           error.code === 'ECONNRESET' ||
           error.name === 'NetworkError';
  }

  /**
   * 提取验证错误详情
   */
  private extractValidationDetails(exception: unknown): any {
    if (!exception || typeof exception !== 'object') {
      return undefined;
    }

    const error = exception as any;
    
    if (Array.isArray(error.errors)) {
      return error.errors.map((err: any) => ({
        field: err.property || err.path,
        value: err.value,
        constraints: err.constraints || { [err.kind]: err.message },
      }));
    }

    if (error.errors && typeof error.errors === 'object') {
      return Object.values(error.errors).map((err: any) => ({
        field: err.path,
        value: err.value,
        constraints: { [err.kind]: err.message },
      }));
    }

    return undefined;
  }

  /**
   * 清理错误消息
   */
  private sanitizeErrorMessage(message: string): string {
    // 移除敏感信息
    const sensitivePatterns = [
      /password/gi,
      /token/gi,
      /secret/gi,
      /key/gi,
      /authorization/gi,
    ];

    let sanitized = message;
    
    for (const pattern of sensitivePatterns) {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    }

    // 限制消息长度
    if (sanitized.length > 500) {
      sanitized = sanitized.substring(0, 500) + '...';
    }

    return sanitized;
  }
}
