import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

@Schema({
  timestamps: true,
  collection: 'audit_logs',
  toJSON: {
    transform: (doc, ret) => {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  },
})
export class AuditLog {

  @ApiProperty({ description: '日志类型' })
  @Prop({ required: true, index: true })
  type: string;

  @ApiProperty({ description: '事件名称' })
  @Prop({ required: true, index: true })
  event: string;

  @ApiProperty({ description: '用户ID' })
  @Prop({ type: Types.ObjectId, ref: 'User', index: true })
  userId?: string;

  @ApiProperty({ description: '会话ID' })
  @Prop({ index: true })
  sessionId?: string;

  @ApiProperty({ description: 'IP地址' })
  @Prop({ index: true })
  ipAddress?: string;

  @ApiProperty({ description: '用户代理' })
  @Prop()
  userAgent?: string;

  @ApiProperty({ description: '资源类型' })
  @Prop({ index: true })
  resource?: string;

  @ApiProperty({ description: '操作类型' })
  @Prop({ index: true })
  action?: string;

  @ApiProperty({ description: '详细信息' })
  @Prop({ type: Object })
  details?: Record<string, any>;

  @ApiProperty({ description: '事件时间' })
  @Prop({ required: true, index: true })
  timestamp: Date;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export const AuditLogSchema = SchemaFactory.createForClass(AuditLog);

// 创建索引
AuditLogSchema.index({ type: 1, event: 1 });
AuditLogSchema.index({ userId: 1, timestamp: -1 });
AuditLogSchema.index({ ipAddress: 1, timestamp: -1 });
AuditLogSchema.index({ resource: 1, action: 1 });
AuditLogSchema.index({ timestamp: -1 });

// 复合索引
AuditLogSchema.index({ type: 1, userId: 1, timestamp: -1 });
AuditLogSchema.index({ type: 1, ipAddress: 1, timestamp: -1 });
AuditLogSchema.index({ event: 1, timestamp: -1 });

// TTL索引 - 根据配置自动删除过期日志
AuditLogSchema.index({ timestamp: 1 }, { 
  expireAfterSeconds: 365 * 24 * 60 * 60 // 默认365天，可通过配置覆盖
});

// 审计日志文档接口
export interface AuditLogDocument extends AuditLog, Document {
  // 审计日志通常只需要基本的 Document 方法
  // 如果需要自定义方法，可以在这里添加
}
