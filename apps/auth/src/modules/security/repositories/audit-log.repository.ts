import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AuditLog, AuditLogDocument } from '../entities/audit-log.entity';

export interface CreateAuditLogData {
  type: string;
  userId?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  resource?: string;
  action?: string;
  details?: any;
  result?: 'success' | 'failure' | 'error';
  riskScore?: number;
  timestamp?: Date;
}

export interface AuditLogQuery {
  userId?: string;
  type?: string;
  result?: string;
  startDate?: Date;
  endDate?: Date;
  riskScore?: { min?: number; max?: number };
  limit?: number;
  offset?: number;
}

export interface AuditLogStats {
  totalEvents: number;
  successEvents: number;
  failureEvents: number;
  errorEvents: number;
  highRiskEvents: number;
  uniqueUsers: number;
  eventsByType: { [key: string]: number };
  eventsByHour: { [key: string]: number };
}

/**
 * 审计日志仓储接口
 */
export interface IAuditLogRepository {
  create(auditData: CreateAuditLogData): Promise<AuditLogDocument>;
  findById(id: string): Promise<AuditLogDocument | null>;
  findByQuery(query: AuditLogQuery): Promise<AuditLogDocument[]>;
  findByUserId(userId: string, limit?: number): Promise<AuditLogDocument[]>;
  findByType(type: string, limit?: number): Promise<AuditLogDocument[]>;
  findHighRiskEvents(threshold?: number, limit?: number): Promise<AuditLogDocument[]>;
  getStats(startDate?: Date, endDate?: Date): Promise<AuditLogStats>;
  deleteOldLogs(retentionDays: number): Promise<number>;
  countByQuery(query: AuditLogQuery): Promise<number>;
}

/**
 * 审计日志仓储实现
 * 
 * 负责审计日志数据的持久化操作，包括：
 * - 审计日志的创建和查询
 * - 安全事件的统计分析
 * - 高风险事件的检测
 * - 日志的清理和维护
 */
@Injectable()
export class AuditLogRepository implements IAuditLogRepository {
  private readonly logger = new Logger(AuditLogRepository.name);

  constructor(
    @InjectModel(AuditLog.name) private auditLogModel: Model<AuditLogDocument>,
  ) {}

  /**
   * 创建审计日志
   */
  async create(auditData: CreateAuditLogData): Promise<AuditLogDocument> {
    const auditLog = new this.auditLogModel({
      ...auditData,
      timestamp: auditData.timestamp || new Date(),
    });
    return await auditLog.save();
  }

  /**
   * 根据ID查找审计日志
   */
  async findById(id: string): Promise<AuditLogDocument | null> {
    return await this.auditLogModel.findById(id).exec();
  }

  /**
   * 根据查询条件查找审计日志
   */
  async findByQuery(query: AuditLogQuery): Promise<AuditLogDocument[]> {
    const filter: any = {};

    if (query.userId) filter.userId = query.userId;
    if (query.type) filter.type = query.type;
    if (query.result) filter.result = query.result;
    
    if (query.startDate || query.endDate) {
      filter.timestamp = {};
      if (query.startDate) filter.timestamp.$gte = query.startDate;
      if (query.endDate) filter.timestamp.$lte = query.endDate;
    }

    if (query.riskScore) {
      filter.riskScore = {};
      if (query.riskScore.min !== undefined) filter.riskScore.$gte = query.riskScore.min;
      if (query.riskScore.max !== undefined) filter.riskScore.$lte = query.riskScore.max;
    }

    return await this.auditLogModel
      .find(filter)
      .sort({ timestamp: -1 })
      .limit(query.limit || 100)
      .skip(query.offset || 0)
      .exec();
  }

  /**
   * 根据用户ID查找审计日志
   */
  async findByUserId(userId: string, limit: number = 100): Promise<AuditLogDocument[]> {
    return await this.auditLogModel
      .find({ userId })
      .sort({ timestamp: -1 })
      .limit(limit)
      .exec();
  }

  /**
   * 根据事件类型查找审计日志
   */
  async findByType(type: string, limit: number = 100): Promise<AuditLogDocument[]> {
    return await this.auditLogModel
      .find({ type })
      .sort({ timestamp: -1 })
      .limit(limit)
      .exec();
  }

  /**
   * 查找高风险事件
   */
  async findHighRiskEvents(threshold: number = 80, limit: number = 100): Promise<AuditLogDocument[]> {
    return await this.auditLogModel
      .find({ riskScore: { $gte: threshold } })
      .sort({ timestamp: -1, riskScore: -1 })
      .limit(limit)
      .exec();
  }

  /**
   * 获取审计日志统计信息
   */
  async getStats(startDate?: Date, endDate?: Date): Promise<AuditLogStats> {
    const filter: any = {};
    if (startDate || endDate) {
      filter.timestamp = {};
      if (startDate) filter.timestamp.$gte = startDate;
      if (endDate) filter.timestamp.$lte = endDate;
    }

    const [
      totalEvents,
      successEvents,
      failureEvents,
      errorEvents,
      highRiskEvents,
      uniqueUsers,
      eventsByType,
      eventsByHour
    ] = await Promise.all([
      // 总事件数
      this.auditLogModel.countDocuments(filter),
      
      // 成功事件数
      this.auditLogModel.countDocuments({ ...filter, result: 'success' }),
      
      // 失败事件数
      this.auditLogModel.countDocuments({ ...filter, result: 'failure' }),
      
      // 错误事件数
      this.auditLogModel.countDocuments({ ...filter, result: 'error' }),
      
      // 高风险事件数
      this.auditLogModel.countDocuments({ ...filter, riskScore: { $gte: 80 } }),
      
      // 唯一用户数
      this.auditLogModel.distinct('userId', filter).then(users => users.length),
      
      // 按类型统计
      this.auditLogModel.aggregate([
        { $match: filter },
        { $group: { _id: '$type', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]),
      
      // 按小时统计
      this.auditLogModel.aggregate([
        { $match: filter },
        {
          $group: {
            _id: { $dateToString: { format: '%Y-%m-%d %H:00', date: '$timestamp' } },
            count: { $sum: 1 }
          }
        },
        { $sort: { _id: 1 } }
      ])
    ]);

    // 转换聚合结果
    const eventsByTypeMap: { [key: string]: number } = {};
    eventsByType.forEach((item: any) => {
      eventsByTypeMap[item._id] = item.count;
    });

    const eventsByHourMap: { [key: string]: number } = {};
    eventsByHour.forEach((item: any) => {
      eventsByHourMap[item._id] = item.count;
    });

    return {
      totalEvents,
      successEvents,
      failureEvents,
      errorEvents,
      highRiskEvents,
      uniqueUsers,
      eventsByType: eventsByTypeMap,
      eventsByHour: eventsByHourMap,
    };
  }

  /**
   * 删除过期日志
   */
  async deleteOldLogs(retentionDays: number): Promise<number> {
    const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);
    const result = await this.auditLogModel.deleteMany({
      timestamp: { $lt: cutoffDate }
    }).exec();
    
    this.logger.log(`删除了 ${result.deletedCount} 条过期审计日志`);
    return result.deletedCount;
  }

  /**
   * 统计查询结果数量
   */
  async countByQuery(query: AuditLogQuery): Promise<number> {
    const filter: any = {};

    if (query.userId) filter.userId = query.userId;
    if (query.type) filter.type = query.type;
    if (query.result) filter.result = query.result;
    
    if (query.startDate || query.endDate) {
      filter.timestamp = {};
      if (query.startDate) filter.timestamp.$gte = query.startDate;
      if (query.endDate) filter.timestamp.$lte = query.endDate;
    }

    if (query.riskScore) {
      filter.riskScore = {};
      if (query.riskScore.min !== undefined) filter.riskScore.$gte = query.riskScore.min;
      if (query.riskScore.max !== undefined) filter.riskScore.$lte = query.riskScore.max;
    }

    return await this.auditLogModel.countDocuments(filter);
  }
}
