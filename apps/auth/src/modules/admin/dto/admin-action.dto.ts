import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject, IsEnum, IsArray } from 'class-validator';
import {MICROSERVICE_NAMES} from "@libs/shared";

export enum AdminActionType {
  CLEAR_CACHE = 'clear_cache',
  RESTART_SERVICE = 'restart_service',
  UPDATE_CONFIG = 'update_config',
  CLEANUP_LOGS = 'cleanup_logs',
  OPTIMIZE_DATABASE = 'optimize_database',
  REFRESH_CERTIFICATES = 'refresh_certificates',
  SYNC_DATA = 'sync_data',
  BACKUP_DATABASE = 'backup_database',
  RESTORE_DATABASE = 'restore_database',
  MAINTENANCE_MODE = 'maintenance_mode',
  SECURITY_SCAN = 'security_scan',
  PERFORMANCE_TEST = 'performance_test',
}

export class AdminActionDto {
  @ApiProperty({ 
    description: '管理员操作类型',
    enum: AdminActionType,
    example: AdminActionType.CLEAR_CACHE
  })
  @IsEnum(AdminActionType)
  action: AdminActionType;

  @ApiProperty({ 
    description: '操作描述',
    example: '清理系统缓存以提高性能'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    description: '操作参数',
    example: { cacheType: 'all', force: true }
  })
  @IsOptional()
  @IsObject()
  parameters?: Record<string, any>;

  @ApiProperty({ 
    description: '目标服务或组件',
    example: [MICROSERVICE_NAMES.AUTH_SERVICE, MICROSERVICE_NAMES.CHARACTER_SERVICE]
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  targets?: string[];

  @ApiProperty({ 
    description: '是否强制执行',
    example: false
  })
  @IsOptional()
  force?: boolean;

  @ApiProperty({ 
    description: '计划执行时间（可选，立即执行则不填）',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  scheduledTime?: Date;
}
