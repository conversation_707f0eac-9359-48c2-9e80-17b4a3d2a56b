import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';

import { JwtAuthGuard } from '../../security/guards/jwt-auth.guard';
import { RolesGuard } from '../../rbac/guards/roles.guard';
import { Roles } from '../../../shared/decorators/roles.decorator';
import { AdminPermissions } from '../../../shared/decorators/permissions.decorator';

@ApiTags('安全管理')
@Controller('admin/security')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class SecurityController {
  @Get('alerts')
  @Roles('admin', 'super_admin')
  @AdminPermissions.securityAdmin()
  @ApiOperation({ summary: '获取安全警告' })
  getSecurityAlerts() {
    return {
      success: true,
      message: '安全控制器已创建，功能开发中...',
    };
  }
}
