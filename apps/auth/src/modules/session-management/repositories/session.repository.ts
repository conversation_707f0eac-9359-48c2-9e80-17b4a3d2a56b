import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Session, SessionDocument } from '../entities/session.entity';

export interface CreateSessionData {
  sessionId: string;
  userId: string;
  deviceId: string;
  deviceInfo?: any;
  ipAddress?: string;
  location?: any;
  trusted: boolean;
  active: boolean;
  rememberMe: boolean;
  lastActivity: Date;
  expiresAt: Date;
  absoluteExpiresAt: Date;
}

export interface UpdateSessionData {
  lastActivity?: Date;
  expiresAt?: Date;
  trusted?: boolean;
  active?: boolean;
  terminatedAt?: Date;
}

/**
 * 会话仓储接口
 */
export interface ISessionRepository {
  create(sessionData: CreateSessionData): Promise<SessionDocument>;
  findById(sessionId: string): Promise<SessionDocument | null>;
  findByUserId(userId: string): Promise<SessionDocument[]>;
  findActiveByUserId(userId: string): Promise<SessionDocument[]>;
  update(sessionId: string, updateData: UpdateSessionData): Promise<SessionDocument | null>;
  delete(sessionId: string): Promise<void>;
  deleteByUserId(userId: string): Promise<number>;
  countActiveByUserId(userId: string): Promise<number>;
  countTotalByUserId(userId: string): Promise<number>;
  countRecentByUserId(userId: string, days: number): Promise<number>;
  findExpiredSessions(): Promise<SessionDocument[]>;
  deleteExpiredSessions(): Promise<number>;
  updateLastActivity(sessionId: string): Promise<void>;
  terminateSession(sessionId: string): Promise<void>;
  terminateAllUserSessions(userId: string): Promise<number>;
  trustDevice(sessionId: string): Promise<void>;
}

/**
 * 会话仓储实现
 * 
 * 负责会话数据的持久化操作，包括：
 * - 会话的CRUD操作
 * - 会话查询和统计
 * - 会话状态管理
 * - 过期会话清理
 */
@Injectable()
export class SessionRepository implements ISessionRepository {
  private readonly logger = new Logger(SessionRepository.name);

  constructor(
    @InjectModel(Session.name) private sessionModel: Model<SessionDocument>,
  ) {}

  /**
   * 创建会话
   */
  async create(sessionData: CreateSessionData): Promise<SessionDocument> {
    const session = new this.sessionModel(sessionData);
    return await session.save();
  }

  /**
   * 根据ID查找会话
   */
  async findById(sessionId: string): Promise<SessionDocument | null> {
    return await this.sessionModel.findOne({
      sessionId,
      active: true,
      expiresAt: { $gt: new Date() }
    }).exec();
  }

  /**
   * 根据用户ID查找所有会话
   */
  async findByUserId(userId: string): Promise<SessionDocument[]> {
    return await this.sessionModel.find({ userId })
      .sort({ lastActivity: -1 })
      .exec();
  }

  /**
   * 根据用户ID查找活跃会话
   */
  async findActiveByUserId(userId: string): Promise<SessionDocument[]> {
    return await this.sessionModel.find({
      userId,
      active: true,
      expiresAt: { $gt: new Date() }
    }).sort({ lastActivity: -1 }).exec();
  }

  /**
   * 更新会话
   */
  async update(sessionId: string, updateData: UpdateSessionData): Promise<SessionDocument | null> {
    return await this.sessionModel.findOneAndUpdate(
      { sessionId },
      { $set: updateData },
      { new: true }
    ).exec();
  }

  /**
   * 删除会话
   */
  async delete(sessionId: string): Promise<void> {
    await this.sessionModel.deleteOne({ sessionId }).exec();
  }

  /**
   * 删除用户所有会话
   */
  async deleteByUserId(userId: string): Promise<number> {
    const result = await this.sessionModel.deleteMany({ userId }).exec();
    return result.deletedCount;
  }

  /**
   * 统计用户活跃会话数
   */
  async countActiveByUserId(userId: string): Promise<number> {
    return await this.sessionModel.countDocuments({
      userId,
      active: true,
      expiresAt: { $gt: new Date() }
    });
  }

  /**
   * 统计用户总会话数
   */
  async countTotalByUserId(userId: string): Promise<number> {
    return await this.sessionModel.countDocuments({ userId });
  }

  /**
   * 统计用户最近会话数
   */
  async countRecentByUserId(userId: string, days: number): Promise<number> {
    const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    return await this.sessionModel.countDocuments({
      userId,
      createdAt: { $gte: cutoffDate }
    });
  }

  /**
   * 查找过期会话
   */
  async findExpiredSessions(): Promise<SessionDocument[]> {
    return await this.sessionModel.find({
      $or: [
        { expiresAt: { $lt: new Date() } },
        { absoluteExpiresAt: { $lt: new Date() } }
      ]
    }).exec();
  }

  /**
   * 删除过期会话
   */
  async deleteExpiredSessions(): Promise<number> {
    const result = await this.sessionModel.deleteMany({
      $or: [
        { expiresAt: { $lt: new Date() } },
        { absoluteExpiresAt: { $lt: new Date() } }
      ]
    }).exec();
    return result.deletedCount;
  }

  /**
   * 更新最后活动时间
   */
  async updateLastActivity(sessionId: string): Promise<void> {
    await this.sessionModel.updateOne(
      { sessionId },
      { $set: { lastActivity: new Date() } }
    ).exec();
  }

  /**
   * 终止会话
   */
  async terminateSession(sessionId: string): Promise<void> {
    await this.sessionModel.updateOne(
      { sessionId },
      {
        $set: {
          active: false,
          terminatedAt: new Date(),
        }
      }
    ).exec();
  }

  /**
   * 终止用户所有会话
   */
  async terminateAllUserSessions(userId: string): Promise<number> {
    const result = await this.sessionModel.updateMany(
      { userId, active: true },
      {
        $set: {
          active: false,
          terminatedAt: new Date(),
        }
      }
    ).exec();
    return result.modifiedCount;
  }

  /**
   * 设置设备为受信任
   */
  async trustDevice(sessionId: string): Promise<void> {
    await this.sessionModel.updateOne(
      { sessionId },
      { $set: { trusted: true } }
    ).exec();
  }
}
