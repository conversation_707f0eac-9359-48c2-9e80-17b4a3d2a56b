import { Injectable, Logger, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SessionDocument } from '../entities/session.entity';
import { ISessionRepository, CreateSessionData } from '../repositories/session.repository';
import * as crypto from 'crypto';

export interface CreateSessionDto {
  userId: string;
  deviceInfo?: {
    type: string;
    name: string;
    fingerprint: string;
    userAgent: string;
    ipAddress: string;
  };
  rememberMe?: boolean;
}

export interface UpdateSessionTokensDto {
  accessToken: string;
  refreshToken: string;
}

@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);
  private readonly maxConcurrentSessions: number;
  private readonly sessionTimeout: number;
  private readonly absoluteTimeout: number;

  constructor(
    @Inject('ISessionRepository') private sessionRepository: ISessionRepository,
    private configService: ConfigService,
  ) {
    this.maxConcurrentSessions = this.configService.get<number>('auth.session.maxConcurrentSessions', 5);
    this.sessionTimeout = this.configService.get<number>('auth.session.sessionTimeout', 3600);
    this.absoluteTimeout = this.configService.get<number>('auth.session.absoluteTimeout', 86400);
  }

  /**
   * 创建会话
   */
  async createSession(createSessionDto: CreateSessionDto): Promise<SessionDocument> {
    this.logger.log(`创建会话: 用户 ${createSessionDto.userId}`);

    // 检查并清理过期会话
    await this.cleanupExpiredSessions(createSessionDto.userId);

    // 检查并发会话数限制
    await this.enforceSessionLimit(createSessionDto.userId);

    // 生成会话ID和设备ID
    const sessionId = this.generateSessionId();
    const deviceId = createSessionDto.deviceInfo?.fingerprint || this.generateDeviceId();

    // 计算过期时间
    const now = new Date();
    const sessionTimeout = createSessionDto.rememberMe 
      ? this.absoluteTimeout * 7 // 记住我：7倍的绝对超时时间
      : this.sessionTimeout;
    
    const expiresAt = new Date(now.getTime() + sessionTimeout * 1000);
    const absoluteExpiresAt = new Date(now.getTime() + this.absoluteTimeout * 1000);

    // 解析地理位置信息（简化版）
    const location = this.parseLocation(createSessionDto.deviceInfo?.ipAddress);

    // 创建会话文档
    const sessionData = {
      sessionId,
      userId: createSessionDto.userId,
      deviceId,
      deviceInfo: createSessionDto.deviceInfo ? {
        type: createSessionDto.deviceInfo.type,
        name: createSessionDto.deviceInfo.name,
        fingerprint: createSessionDto.deviceInfo.fingerprint,
        userAgent: createSessionDto.deviceInfo.userAgent,
        os: this.parseOS(createSessionDto.deviceInfo.userAgent),
        browser: this.parseBrowser(createSessionDto.deviceInfo.userAgent),
      } : undefined,
      ipAddress: createSessionDto.deviceInfo?.ipAddress,
      location,
      trusted: false, // 新设备默认不受信任
      active: true,
      rememberMe: createSessionDto.rememberMe || false,
      lastActivity: now,
      expiresAt,
      absoluteExpiresAt,
    };

    const session = await this.sessionRepository.create(sessionData);

    this.logger.log(`会话创建成功: ${sessionId}`);
    return session;
  }

  /**
   * 根据ID查找会话
   */
  async findById(sessionId: string): Promise<SessionDocument | null> {
    return await this.sessionRepository.findById(sessionId);
  }

  /**
   * 根据用户ID查找所有活跃会话
   */
  async findByUserId(userId: string): Promise<SessionDocument[]> {
    return await this.sessionRepository.findActiveByUserId(userId);
  }

  /**
   * 更新会话令牌信息
   */
  async updateSessionTokens(sessionId: string, tokens: UpdateSessionTokensDto): Promise<void> {
    await this.sessionRepository.update(sessionId, {
      lastActivity: new Date(),
      // Note: Repository doesn't handle token fields, this might need to be added to UpdateSessionData interface
    });
  }

  /**
   * 更新最后活动时间
   */
  async updateLastActivity(sessionId: string): Promise<void> {
    // 先获取会话信息以检查是否需要延长过期时间
    const session = await this.sessionRepository.findById(sessionId);
    if (!session) {
      return;
    }

    const now = new Date();
    const updates: any = {
      lastActivity: now,
    };

    // 如果会话即将过期，延长过期时间
    const timeUntilExpiry = session.expiresAt.getTime() - now.getTime();
    const renewalThreshold = this.configService.get<number>('auth.session.renewalThreshold', 300) * 1000; // 5分钟

    if (timeUntilExpiry < renewalThreshold && timeUntilExpiry > 0) {
      const sessionTimeout = session.rememberMe
        ? this.absoluteTimeout * 7
        : this.sessionTimeout;

      updates.expiresAt = new Date(now.getTime() + sessionTimeout * 1000);
    }

    await this.sessionRepository.update(sessionId, updates);
  }

  /**
   * 终止会话
   */
  async terminateSession(sessionId: string): Promise<void> {
    await this.sessionRepository.terminateSession(sessionId);
    this.logger.log(`会话已终止: ${sessionId}`);
  }

  /**
   * 终止用户所有会话
   */
  async terminateAllUserSessions(userId: string): Promise<number> {
    const terminatedCount = await this.sessionRepository.terminateAllUserSessions(userId);
    this.logger.log(`用户所有会话已终止: ${userId}, 数量: ${terminatedCount}`);
    return terminatedCount;
  }

  /**
   * 设置设备为受信任
   */
  async trustDevice(sessionId: string): Promise<void> {
    await this.sessionRepository.trustDevice(sessionId);
    this.logger.log(`设备已设为受信任: ${sessionId}`);
  }

  /**
   * 获取用户会话统计
   */
  async getUserSessionStats(userId: string): Promise<any> {
    const [activeSessions, totalSessions, recentSessions] = await Promise.all([
      this.sessionRepository.countActiveByUserId(userId),
      this.sessionRepository.countTotalByUserId(userId),
      this.sessionRepository.countRecentByUserId(userId, 7)
    ]);

    return {
      activeSessions,
      totalSessions,
      recentSessions,
      maxConcurrentSessions: this.maxConcurrentSessions,
    };
  }

  /**
   * 清理过期会话
   */
  async cleanupExpiredSessions(userId?: string): Promise<number> {
    if (userId) {
      // Repository不支持按用户清理，需要先获取用户的过期会话然后逐个处理
      const expiredSessions = await this.sessionRepository.findExpiredSessions();
      const userExpiredSessions = expiredSessions.filter(session => session.userId === userId);

      let cleanedCount = 0;
      for (const session of userExpiredSessions) {
        await this.sessionRepository.terminateSession(session.sessionId);
        cleanedCount++;
      }

      if (cleanedCount > 0) {
        this.logger.log(`清理用户过期会话: ${cleanedCount}个`);
      }
      return cleanedCount;
    } else {
      const cleanedCount = await this.sessionRepository.deleteExpiredSessions();
      if (cleanedCount > 0) {
        this.logger.log(`清理过期会话: ${cleanedCount}个`);
      }
      return cleanedCount;
    }
  }

  /**
   * 强制执行会话数限制
   */
  private async enforceSessionLimit(userId: string): Promise<void> {
    const activeSessions = await this.sessionRepository.findActiveByUserId(userId);

    if (activeSessions.length >= this.maxConcurrentSessions) {
      // 按最后活动时间排序，终止最旧的会话
      activeSessions.sort((a, b) => a.lastActivity.getTime() - b.lastActivity.getTime());
      const sessionsToTerminate = activeSessions.slice(0, activeSessions.length - this.maxConcurrentSessions + 1);

      for (const session of sessionsToTerminate) {
        await this.terminateSession(session.sessionId);
      }

      this.logger.log(`强制终止旧会话: 用户 ${userId}, 数量: ${sessionsToTerminate.length}`);
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 生成设备ID
   */
  private generateDeviceId(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * 解析操作系统
   */
  private parseOS(userAgent: string): string {
    if (!userAgent) return 'Unknown';
    
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac OS')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    
    return 'Unknown';
  }

  /**
   * 解析浏览器
   */
  private parseBrowser(userAgent: string): string {
    if (!userAgent) return 'Unknown';
    
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    if (userAgent.includes('Opera')) return 'Opera';
    
    return 'Unknown';
  }

  /**
   * 解析地理位置（简化版）
   */
  private parseLocation(ipAddress?: string): any {
    if (!ipAddress) return null;
    
    // 这里应该集成真实的IP地理位置服务
    // 目前返回模拟数据
    return {
      country: 'Unknown',
      city: 'Unknown',
      latitude: 0,
      longitude: 0,
    };
  }
}
