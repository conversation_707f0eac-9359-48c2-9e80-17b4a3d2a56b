import { Injectable, Logger, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SessionDocument } from '../entities/session.entity';
import { ISessionRepository, CreateSessionData } from '../repositories/session.repository';
import * as crypto from 'crypto';

export interface CreateSessionDto {
  userId: string;
  deviceInfo?: {
    type: string;
    name: string;
    fingerprint: string;
    userAgent: string;
    ipAddress: string;
  };
  rememberMe?: boolean;
}

export interface UpdateSessionTokensDto {
  accessToken: string;
  refreshToken: string;
}

@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);
  private readonly maxConcurrentSessions: number;
  private readonly sessionTimeout: number;
  private readonly absoluteTimeout: number;

  constructor(
    @Inject('ISessionRepository') private sessionRepository: ISessionRepository,
    private configService: ConfigService,
  ) {
    this.maxConcurrentSessions = this.configService.get<number>('auth.session.maxConcurrentSessions', 5);
    this.sessionTimeout = this.configService.get<number>('auth.session.sessionTimeout', 3600);
    this.absoluteTimeout = this.configService.get<number>('auth.session.absoluteTimeout', 86400);
  }

  /**
   * 创建会话
   */
  async createSession(createSessionDto: CreateSessionDto): Promise<SessionDocument> {
    this.logger.log(`创建会话: 用户 ${createSessionDto.userId}`);

    // 检查并清理过期会话
    await this.cleanupExpiredSessions(createSessionDto.userId);

    // 检查并发会话数限制
    await this.enforceSessionLimit(createSessionDto.userId);

    // 生成会话ID和设备ID
    const sessionId = this.generateSessionId();
    const deviceId = createSessionDto.deviceInfo?.fingerprint || this.generateDeviceId();

    // 计算过期时间
    const now = new Date();
    const sessionTimeout = createSessionDto.rememberMe 
      ? this.absoluteTimeout * 7 // 记住我：7倍的绝对超时时间
      : this.sessionTimeout;
    
    const expiresAt = new Date(now.getTime() + sessionTimeout * 1000);
    const absoluteExpiresAt = new Date(now.getTime() + this.absoluteTimeout * 1000);

    // 解析地理位置信息（简化版）
    const location = this.parseLocation(createSessionDto.deviceInfo?.ipAddress);

    // 创建会话文档
    const sessionData = {
      sessionId,
      userId: createSessionDto.userId,
      deviceId,
      deviceInfo: createSessionDto.deviceInfo ? {
        type: createSessionDto.deviceInfo.type,
        name: createSessionDto.deviceInfo.name,
        fingerprint: createSessionDto.deviceInfo.fingerprint,
        userAgent: createSessionDto.deviceInfo.userAgent,
        os: this.parseOS(createSessionDto.deviceInfo.userAgent),
        browser: this.parseBrowser(createSessionDto.deviceInfo.userAgent),
      } : undefined,
      ipAddress: createSessionDto.deviceInfo?.ipAddress,
      location,
      trusted: false, // 新设备默认不受信任
      active: true,
      rememberMe: createSessionDto.rememberMe || false,
      lastActivity: now,
      expiresAt,
      absoluteExpiresAt,
    };

    const session = await this.sessionRepository.create(sessionData);

    this.logger.log(`会话创建成功: ${sessionId}`);
    return session;
  }

  /**
   * 根据ID查找会话
   */
  async findById(sessionId: string): Promise<SessionDocument | null> {
    return await this.sessionModel.findOne({
      sessionId,
      active: true,
      expiresAt: { $gt: new Date() }
    }).exec();
  }

  /**
   * 根据用户ID查找所有活跃会话
   */
  async findByUserId(userId: string): Promise<SessionDocument[]> {
    return await this.sessionModel.find({
      userId,
      active: true,
      expiresAt: { $gt: new Date() }
    }).sort({ lastActivity: -1 }).exec();
  }

  /**
   * 更新会话令牌信息
   */
  async updateSessionTokens(sessionId: string, tokens: UpdateSessionTokensDto): Promise<void> {
    await this.sessionModel.updateOne(
      { sessionId },
      {
        $set: {
          accessToken: tokens.accessToken,
          refreshToken: tokens.refreshToken,
          lastActivity: new Date(),
        }
      }
    ).exec();
  }

  /**
   * 更新最后活动时间
   */
  async updateLastActivity(sessionId: string): Promise<void> {
    const session = await this.sessionModel.findOne({ sessionId }).exec();
    if (!session) {
      return;
    }

    const now = new Date();
    const updates: any = {
      lastActivity: now,
    };

    // 如果会话即将过期，延长过期时间
    const timeUntilExpiry = session.expiresAt.getTime() - now.getTime();
    const renewalThreshold = this.configService.get<number>('auth.session.renewalThreshold', 300) * 1000; // 5分钟

    if (timeUntilExpiry < renewalThreshold && timeUntilExpiry > 0) {
      const sessionTimeout = session.rememberMe 
        ? this.absoluteTimeout * 7
        : this.sessionTimeout;
      
      updates.expiresAt = new Date(now.getTime() + sessionTimeout * 1000);
    }

    await this.sessionModel.updateOne(
      { sessionId },
      { $set: updates }
    ).exec();
  }

  /**
   * 终止会话
   */
  async terminateSession(sessionId: string): Promise<void> {
    await this.sessionModel.updateOne(
      { sessionId },
      {
        $set: {
          active: false,
          terminatedAt: new Date(),
        }
      }
    ).exec();

    this.logger.log(`会话已终止: ${sessionId}`);
  }

  /**
   * 终止用户所有会话
   */
  async terminateAllUserSessions(userId: string): Promise<number> {
    const result = await this.sessionModel.updateMany(
      { userId, active: true },
      {
        $set: {
          active: false,
          terminatedAt: new Date(),
        }
      }
    ).exec();

    this.logger.log(`用户所有会话已终止: ${userId}, 数量: ${result.modifiedCount}`);
    return result.modifiedCount;
  }

  /**
   * 设置设备为受信任
   */
  async trustDevice(sessionId: string): Promise<void> {
    await this.sessionModel.updateOne(
      { sessionId },
      { $set: { trusted: true } }
    ).exec();

    this.logger.log(`设备已设为受信任: ${sessionId}`);
  }

  /**
   * 获取用户会话统计
   */
  async getUserSessionStats(userId: string): Promise<any> {
    const [activeSessions, totalSessions, recentSessions] = await Promise.all([
      // 活跃会话数
      this.sessionModel.countDocuments({
        userId,
        active: true,
        expiresAt: { $gt: new Date() }
      }),
      
      // 总会话数
      this.sessionModel.countDocuments({ userId }),
      
      // 最近7天的会话数
      this.sessionModel.countDocuments({
        userId,
        createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
      })
    ]);

    return {
      activeSessions,
      totalSessions,
      recentSessions,
      maxConcurrentSessions: this.maxConcurrentSessions,
    };
  }

  /**
   * 清理过期会话
   */
  async cleanupExpiredSessions(userId?: string): Promise<number> {
    const filter: any = {
      $or: [
        { expiresAt: { $lte: new Date() } },
        { absoluteExpiresAt: { $lte: new Date() } }
      ]
    };

    if (userId) {
      filter.userId = userId;
    }

    const result = await this.sessionModel.updateMany(
      filter,
      {
        $set: {
          active: false,
          terminatedAt: new Date(),
        }
      }
    ).exec();

    if (result.modifiedCount > 0) {
      this.logger.log(`清理过期会话: ${result.modifiedCount}个`);
    }

    return result.modifiedCount;
  }

  /**
   * 强制执行会话数限制
   */
  private async enforceSessionLimit(userId: string): Promise<void> {
    const activeSessions = await this.sessionModel.find({
      userId,
      active: true,
      expiresAt: { $gt: new Date() }
    }).sort({ lastActivity: 1 }).exec(); // 按最后活动时间升序排列

    if (activeSessions.length >= this.maxConcurrentSessions) {
      // 终止最旧的会话
      const sessionsToTerminate = activeSessions.slice(0, activeSessions.length - this.maxConcurrentSessions + 1);
      
      for (const session of sessionsToTerminate) {
        await this.terminateSession(session.sessionId);
      }

      this.logger.log(`强制终止旧会话: 用户 ${userId}, 数量: ${sessionsToTerminate.length}`);
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 生成设备ID
   */
  private generateDeviceId(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * 解析操作系统
   */
  private parseOS(userAgent: string): string {
    if (!userAgent) return 'Unknown';
    
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac OS')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    
    return 'Unknown';
  }

  /**
   * 解析浏览器
   */
  private parseBrowser(userAgent: string): string {
    if (!userAgent) return 'Unknown';
    
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    if (userAgent.includes('Opera')) return 'Opera';
    
    return 'Unknown';
  }

  /**
   * 解析地理位置（简化版）
   */
  private parseLocation(ipAddress?: string): any {
    if (!ipAddress) return null;
    
    // 这里应该集成真实的IP地理位置服务
    // 目前返回模拟数据
    return {
      country: 'Unknown',
      city: 'Unknown',
      latitude: 0,
      longitude: 0,
    };
  }
}
