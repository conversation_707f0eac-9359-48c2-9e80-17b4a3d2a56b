import { ApiProperty } from '@nestjs/swagger';
import { Exclude, Expose, Transform, Type } from 'class-transformer';
import { UserStatus, Gender, UserProfile, GameProfile, SecuritySettings } from '../entities/user.entity';

/**
 * 用户响应DTO - 专门用于API响应的纯净数据传输对象
 * 
 * 设计原则：
 * 1. 只包含需要返回给客户端的数据
 * 2. 不包含任何方法或函数
 * 3. 不包含敏感信息
 * 4. 不包含数据库特定字段
 * 5. 使用class-transformer进行精确控制
 */
export class UserResponseDto {
  @ApiProperty({ description: '用户ID' })
  @Expose()
  id: string;

  @ApiProperty({ description: '用户名' })
  @Expose()
  username: string;

  @ApiProperty({ description: '邮箱地址' })
  @Expose()
  email: string;

  @ApiProperty({ description: '手机号码', required: false })
  @Expose()
  phone?: string;

  @ApiProperty({ description: '用户个人信息', type: UserProfile })
  @Expose()
  @Type(() => UserProfile)
  profile: UserProfile;

  @ApiProperty({ description: '游戏个人信息', type: GameProfile })
  @Expose()
  @Type(() => GameProfile)
  gameProfile: GameProfile;

  @ApiProperty({ description: '用户状态', enum: UserStatus })
  @Expose()
  status: UserStatus;

  @ApiProperty({ description: '邮箱是否已验证' })
  @Expose()
  emailVerified: boolean;

  @ApiProperty({ description: '手机是否已验证' })
  @Expose()
  phoneVerified: boolean;

  @ApiProperty({ description: '用户角色', type: [String] })
  @Expose()
  roles: string[];

  @ApiProperty({ description: '直接权限', type: [String] })
  @Expose()
  permissions: string[];

  @ApiProperty({ description: '最后登录时间', required: false })
  @Expose()
  @Transform(({ value }) => value instanceof Date ? value.toISOString() : value)
  lastLoginAt?: string;

  @ApiProperty({ description: '最后活跃时间', required: false })
  @Expose()
  @Transform(({ value }) => value instanceof Date ? value.toISOString() : value)
  lastActiveAt?: string;

  @ApiProperty({ description: '注册IP地址', required: false })
  @Expose()
  registrationIp?: string;

  @ApiProperty({ description: '最后登录IP地址', required: false })
  @Expose()
  lastLoginIp?: string;

  @ApiProperty({ description: '创建时间' })
  @Expose()
  @Transform(({ value }) => value instanceof Date ? value.toISOString() : value)
  createdAt: string;

  @ApiProperty({ description: '更新时间' })
  @Expose()
  @Transform(({ value }) => value instanceof Date ? value.toISOString() : value)
  updatedAt: string;

  // 虚拟字段：全名
  @ApiProperty({ description: '全名' })
  @Expose()
  get fullName(): string {
    return `${this.profile?.firstName || ''} ${this.profile?.lastName || ''}`.trim();
  }

  // 虚拟字段：是否为高级用户
  @ApiProperty({ description: '是否为高级用户' })
  @Expose()
  get isPremium(): boolean {
    return !!(this.gameProfile?.premiumUntil && new Date(this.gameProfile.premiumUntil) > new Date());
  }

  // 虚拟字段：账户年龄（天）
  @ApiProperty({ description: '账户年龄（天）' })
  @Expose()
  get accountAge(): number {
    if (!this.createdAt) return 0;
    return Math.floor((Date.now() - new Date(this.createdAt).getTime()) / (1000 * 60 * 60 * 24));
  }

  // 安全相关的公开信息（不包含敏感数据）
  @ApiProperty({ description: '安全设置概览' })
  @Expose()
  get securityOverview() {
    return {
      mfaEnabled: false, // 从实际数据中获取，这里先设为默认值
      trustedDevicesCount: 0,
      lastPasswordChange: null,
      accountLocked: false,
    };
  }

  // 排除所有敏感和内部字段
  @Exclude()
  passwordHash: any;

  @Exclude()
  salt: any;

  @Exclude()
  security: any;

  @Exclude()
  _id: any;

  @Exclude()
  __v: any;

  @Exclude()
  deletedAt: any;

  // 排除所有方法和函数
  @Exclude()
  isPasswordExpired: any;

  @Exclude()
  incrementLoginAttempts: any;

  @Exclude()
  resetLoginAttempts: any;

  @Exclude()
  save: any;

  @Exclude()
  remove: any;

  @Exclude()
  updateOne: any;

  @Exclude()
  toJSON: any;

  @Exclude()
  toObject: any;

  // 排除JWT临时属性
  @Exclude()
  sessionId: any;

  @Exclude()
  deviceId: any;

  @Exclude()
  jti: any;
}

/**
 * 用户简要信息DTO - 用于列表显示等场景
 */
export class UserSummaryDto {
  @ApiProperty({ description: '用户ID' })
  @Expose()
  id: string;

  @ApiProperty({ description: '用户名' })
  @Expose()
  username: string;

  @ApiProperty({ description: '邮箱地址' })
  @Expose()
  email: string;

  @ApiProperty({ description: '全名' })
  @Expose()
  get fullName(): string {
    return `${this.profile?.firstName || ''} ${this.profile?.lastName || ''}`.trim();
  }

  @ApiProperty({ description: '用户状态', enum: UserStatus })
  @Expose()
  status: UserStatus;

  @ApiProperty({ description: '用户角色', type: [String] })
  @Expose()
  roles: string[];

  @ApiProperty({ description: '创建时间' })
  @Expose()
  @Transform(({ value }) => value?.toISOString())
  createdAt: string;

  @ApiProperty({ description: '最后登录时间', required: false })
  @Expose()
  @Transform(({ value }) => value?.toISOString())
  lastLoginAt?: string;

  @Expose()
  @Type(() => UserProfile)
  profile: UserProfile;

  // 排除所有其他字段
  @Exclude()
  passwordHash: any;

  @Exclude()
  salt: any;

  @Exclude()
  security: any;

  @Exclude()
  gameProfile: any;

  @Exclude()
  permissions: any;

  @Exclude()
  phone: any;

  @Exclude()
  emailVerified: any;

  @Exclude()
  phoneVerified: any;

  @Exclude()
  lastActiveAt: any;

  @Exclude()
  registrationIp: any;

  @Exclude()
  lastLoginIp: any;

  @Exclude()
  updatedAt: any;

  @Exclude()
  deletedAt: any;

  @Exclude()
  _id: any;

  @Exclude()
  __v: any;

  // 排除所有方法
  @Exclude()
  isPasswordExpired: any;

  @Exclude()
  incrementLoginAttempts: any;

  @Exclude()
  resetLoginAttempts: any;

  @Exclude()
  sessionId: any;

  @Exclude()
  deviceId: any;

  @Exclude()
  jti: any;
}

/**
 * 用户安全信息DTO - 用于安全相关的API响应
 */
export class UserSecurityDto {
  @ApiProperty({ description: '用户ID' })
  @Expose()
  id: string;

  @ApiProperty({ description: '用户名' })
  @Expose()
  username: string;

  @ApiProperty({ description: '是否启用MFA' })
  @Expose()
  mfaEnabled: boolean;

  @ApiProperty({ description: '可信设备数量' })
  @Expose()
  trustedDevicesCount: number;

  @ApiProperty({ description: '最后密码修改时间' })
  @Expose()
  @Transform(({ value }) => value?.toISOString())
  lastPasswordChange: string;

  @ApiProperty({ description: '账户是否被锁定' })
  @Expose()
  accountLocked: boolean;

  @ApiProperty({ description: '登录尝试次数' })
  @Expose()
  loginAttempts: number;

  @ApiProperty({ description: '锁定到期时间', required: false })
  @Expose()
  @Transform(({ value }) => value?.toISOString())
  lockedUntil?: string;

  // 排除所有敏感信息
  @Exclude()
  mfaSecret: any;

  @Exclude()
  backupCodes: any;

  @Exclude()
  passwordHistory: any;

  @Exclude()
  trustedDevices: any;

  // 排除所有其他字段
  @Exclude()
  passwordHash: any;

  @Exclude()
  salt: any;

  @Exclude()
  email: any;

  @Exclude()
  phone: any;

  @Exclude()
  profile: any;

  @Exclude()
  gameProfile: any;

  @Exclude()
  status: any;

  @Exclude()
  roles: any;

  @Exclude()
  permissions: any;

  @Exclude()
  _id: any;

  @Exclude()
  __v: any;

  @Exclude()
  createdAt: any;

  @Exclude()
  updatedAt: any;

  @Exclude()
  deletedAt: any;

  // 排除所有方法
  @Exclude()
  isPasswordExpired: any;

  @Exclude()
  incrementLoginAttempts: any;

  @Exclude()
  resetLoginAttempts: any;

  @Exclude()
  sessionId: any;

  @Exclude()
  deviceId: any;

  @Exclude()
  jti: any;
}
