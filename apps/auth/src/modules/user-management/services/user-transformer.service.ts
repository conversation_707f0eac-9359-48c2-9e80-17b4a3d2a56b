import { Injectable } from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import { UserDocument } from '../entities/user.entity';
import { UserResponseDto, UserSummaryDto, UserSecurityDto } from '../dto/user-response.dto';

/**
 * 用户数据转换服务
 * 
 * 职责：
 * 1. 将UserDocument安全地转换为响应DTO
 * 2. 确保敏感信息不会泄露
 * 3. 提供不同场景的转换方法
 * 4. 避免循环引用问题
 * 
 * 设计原则：
 * - 单一职责：只负责数据转换
 * - 类型安全：使用强类型转换
 * - 安全优先：默认排除敏感信息
 * - 性能优化：避免不必要的数据复制
 */
@Injectable()
export class UserTransformerService {
  
  /**
   * 转换为完整的用户响应DTO
   * 用于获取用户详细信息的API
   */
  toUserResponse(userDocument: UserDocument): UserResponseDto {
    if (!userDocument) {
      return null;
    }

    // 创建纯净的数据对象，避免循环引用
    const plainData = this.createPlainUserData(userDocument);
    
    // 使用class-transformer进行安全转换
    return plainToClass(UserResponseDto, plainData, {
      excludeExtraneousValues: true, // 只包含@Expose()标记的字段
      enableImplicitConversion: true,
    });
  }

  /**
   * 转换为用户摘要DTO
   * 用于用户列表、搜索结果等场景
   */
  toUserSummary(userDocument: UserDocument): UserSummaryDto {
    if (!userDocument) {
      return null;
    }

    const plainData = this.createPlainUserData(userDocument);
    
    return plainToClass(UserSummaryDto, plainData, {
      excludeExtraneousValues: true,
      enableImplicitConversion: true,
    });
  }

  /**
   * 转换为用户安全信息DTO
   * 用于安全设置相关的API
   */
  toUserSecurity(userDocument: UserDocument): UserSecurityDto {
    if (!userDocument) {
      return null;
    }

    const plainData = this.createPlainUserData(userDocument);
    
    // 添加安全相关的计算字段
    const securityData = {
      ...plainData,
      mfaEnabled: userDocument.security?.mfaEnabled || false,
      trustedDevicesCount: userDocument.security?.trustedDevices?.length || 0,
      lastPasswordChange: userDocument.security?.lastPasswordChange,
      accountLocked: userDocument.security?.accountLocked || false,
      loginAttempts: userDocument.security?.loginAttempts || 0,
      lockedUntil: userDocument.security?.lockedUntil,
    };
    
    return plainToClass(UserSecurityDto, securityData, {
      excludeExtraneousValues: true,
      enableImplicitConversion: true,
    });
  }

  /**
   * 批量转换用户列表
   */
  toUserResponseList(userDocuments: UserDocument[]): UserResponseDto[] {
    if (!userDocuments || !Array.isArray(userDocuments)) {
      return [];
    }

    return userDocuments.map(doc => this.toUserResponse(doc)).filter(Boolean);
  }

  /**
   * 批量转换用户摘要列表
   */
  toUserSummaryList(userDocuments: UserDocument[]): UserSummaryDto[] {
    if (!userDocuments || !Array.isArray(userDocuments)) {
      return [];
    }

    return userDocuments.map(doc => this.toUserSummary(doc)).filter(Boolean);
  }

  /**
   * 创建纯净的用户数据对象
   * 
   * 这个方法的关键作用：
   * 1. 提取UserDocument中的纯数据
   * 2. 排除所有方法和函数
   * 3. 排除Mongoose特定属性
   * 4. 排除敏感信息
   * 5. 转换ObjectId为字符串
   */
  private createPlainUserData(userDocument: UserDocument): any {
    // 使用toObject()获取纯数据，但不使用toJSON()避免Schema转换
    const plainObject = userDocument.toObject({
      versionKey: false,        // 排除__v
      transform: (doc, ret) => {
        // 转换_id为id
        if (ret._id) {
          ret.id = ret._id.toString();
          delete ret._id;
        }
        
        // 删除敏感字段
        delete ret.passwordHash;
        delete ret.salt;
        
        // 删除可能的JWT临时属性
        delete ret.sessionId;
        delete ret.deviceId;
        delete ret.jti;
        
        return ret;
      }
    });

    // 确保所有日期字段都是Date对象
    if (plainObject.createdAt && typeof plainObject.createdAt === 'string') {
      plainObject.createdAt = new Date(plainObject.createdAt);
    }
    if (plainObject.updatedAt && typeof plainObject.updatedAt === 'string') {
      plainObject.updatedAt = new Date(plainObject.updatedAt);
    }
    if (plainObject.lastLoginAt && typeof plainObject.lastLoginAt === 'string') {
      plainObject.lastLoginAt = new Date(plainObject.lastLoginAt);
    }
    if (plainObject.lastActiveAt && typeof plainObject.lastActiveAt === 'string') {
      plainObject.lastActiveAt = new Date(plainObject.lastActiveAt);
    }

    return plainObject;
  }

  /**
   * 验证转换结果的完整性
   * 用于开发和测试阶段
   */
  validateTransformation(original: UserDocument, transformed: UserResponseDto): boolean {
    if (!original || !transformed) {
      return false;
    }

    // 检查关键字段是否正确转换
    const checks = [
      original._id?.toString() === transformed.id,
      original.username === transformed.username,
      original.email === transformed.email,
      original.status === transformed.status,
      Array.isArray(transformed.roles),
      Array.isArray(transformed.permissions),
    ];

    // 检查敏感字段是否被正确排除
    const sensitiveChecks = [
      !('passwordHash' in transformed),
      !('salt' in transformed),
      !('incrementLoginAttempts' in transformed),
      !('resetLoginAttempts' in transformed),
      !('sessionId' in transformed),
      !('deviceId' in transformed),
      !('jti' in transformed),
    ];

    return checks.every(Boolean) && sensitiveChecks.every(Boolean);
  }

  /**
   * 获取转换统计信息
   * 用于监控和调试
   */
  getTransformationStats(userDocument: UserDocument): {
    originalFields: number;
    transformedFields: number;
    excludedFields: string[];
  } {
    const original = userDocument.toObject();
    const transformed = this.toUserResponse(userDocument);
    
    const originalFields = Object.keys(original).length;
    const transformedFields = Object.keys(transformed).length;
    
    const excludedFields = Object.keys(original).filter(
      key => !(key in transformed)
    );

    return {
      originalFields,
      transformedFields,
      excludedFields,
    };
  }
}
