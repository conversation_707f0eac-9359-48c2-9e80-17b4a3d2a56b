import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// 控制器
import { AdminController } from './controllers/admin.controller';
import { SystemController } from './controllers/system.controller';
import { UserManagementController } from './controllers/user-management.controller';
import { AuditController } from './controllers/audit.controller';
import { SecurityController } from './controllers/security.controller';
import { StatisticsController } from './controllers/statistics.controller';

// 服务
import { AdminService } from './services/admin.service';
import { SystemService } from './services/system.service';
import { UserManagementService } from './services/user-management.service';
import { AuditManagementService } from './services/audit-management.service';
import { SecurityManagementService } from './services/security-management.service';
import { StatisticsService } from './services/statistics.service';

// 实体 - 管理模块需要访问多个实体进行统计和管理
import { User, UserSchema } from '../user-management/entities/user.entity';
import { Role, RoleSchema } from '../rbac/entities/role.entity';
import { Permission, PermissionSchema } from '../rbac/entities/permission.entity';
import { Session, SessionSchema } from '../session-management/entities/session.entity';
import { AuditLog, AuditLogSchema } from '../security/entities/audit-log.entity';

// 依赖模块
import { UserManagementModule } from '../user-management/user-management.module';
import { RbacModule } from '../rbac/rbac.module';
import { SessionManagementModule } from '../session-management/session-management.module';
import { SecurityModule } from '../security/security.module';

/**
 * 管理模块
 * 
 * 提供系统管理和监控功能，包括：
 * - 管理员仪表板和系统概览
 * - 用户管理和统计分析
 * - 系统监控和健康检查
 * - 审计日志管理和查询
 * - 安全事件监控和处理
 * - 系统配置和维护操作
 * 
 * 职责范围：
 * - 系统级别的管理和监控
 * - 跨模块的数据统计和分析
 * - 管理员专用的操作接口
 * - 系统性能和安全监控
 * - 数据导出和报告生成
 * 
 * 设计原则：
 * - 高权限：仅限管理员访问
 * - 跨模块：整合多个模块的数据
 * - 只读优先：大部分操作为查询和监控
 * - 安全审计：所有操作都有审计记录
 */
@Module({
  imports: [
    // 数据库模型 - 管理模块需要直接访问多个模型进行统计
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: Role.name, schema: RoleSchema },
      { name: Permission.name, schema: PermissionSchema },
      { name: Session.name, schema: SessionSchema },
      { name: AuditLog.name, schema: AuditLogSchema },
    ]),

    // 依赖模块 - 获取各模块的服务
    UserManagementModule,
    RbacModule,
    SessionManagementModule,
    SecurityModule,
  ],
  controllers: [
    AdminController,
    SystemController,
    UserManagementController,
    AuditController,
    SecurityController,
    StatisticsController,
  ],
  providers: [
    AdminService,
    SystemService,
    UserManagementService,
    AuditManagementService,
    SecurityManagementService,
    StatisticsService,
  ],
  exports: [
    // 导出管理服务供其他模块使用（如果需要）
    AdminService,
    SystemService,
    StatisticsService,
  ],
})
export class AdministrationModule {
  constructor() {
    console.log('✅ 管理模块已初始化 - 提供系统管理和监控功能');
  }
}
