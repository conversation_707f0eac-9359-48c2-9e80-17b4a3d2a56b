import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';

import { JwtAuthGuard } from '../../../infrastructure/guards/jwt-auth.guard';
import { RolesGuard } from '../../../infrastructure/guards/roles.guard';
import { Roles } from '../../../common/decorators/roles.decorator';
import { AdminPermissions } from '../../../common/decorators/permissions.decorator';

@ApiTags('审计管理')
@Controller('admin/audit')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class AuditController {
  @Get('logs')
  @Roles('admin', 'super_admin')
  @AdminPermissions.auditAdmin()
  @ApiOperation({ summary: '获取审计日志' })
  getAuditLogs() {
    return {
      success: true,
      message: '审计控制器已创建，功能开发中...',
    };
  }
}
