import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { User, UserDocument } from '../../user-management/entities/user.entity';
import { Role, RoleDocument } from '../../rbac/entities/role.entity';
import { Permission, PermissionDocument } from '../../rbac/entities/permission.entity';
import { Session, SessionDocument } from '../../session-management/entities/session.entity';
import { AuditLog, AuditLogDocument } from '../../security/entities/audit-log.entity';

import { SystemService } from './system.service';
import { StatisticsService } from './statistics.service';
import { SecurityService } from '../../security/services/security.service';

import { AdminDashboardDto } from '../dto/admin-dashboard.dto';
import { SystemStatusDto } from '../dto/system-status.dto';
import { AdminActionDto } from '../dto/admin-action.dto';
import { SecurityEventType } from '../../security/services/security.service';

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);

  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(Role.name) private roleModel: Model<RoleDocument>,
    @InjectModel(Permission.name) private permissionModel: Model<PermissionDocument>,
    @InjectModel(Session.name) private sessionModel: Model<SessionDocument>,
    @InjectModel(AuditLog.name) private auditLogModel: Model<AuditLogDocument>,
    private readonly systemService: SystemService,
    private readonly statisticsService: StatisticsService,
    private readonly securityService: SecurityService,
  ) {}

  /**
   * 获取管理员仪表板数据
   */
  async getDashboard(adminId: string): Promise<AdminDashboardDto> {
    this.logger.log(`获取管理员仪表板数据: ${adminId}`);

    try {
      // 并行获取各种统计数据
      const [
        userStats,
        sessionStats,
        securityStats,
        systemStats,
        recentActivities,
      ] = await Promise.all([
        this.getUserStatistics(),
        this.getSessionStatistics(),
        this.getSecurityStatistics(),
        this.getSystemStatistics(),
        this.getRecentActivities(),
      ]);

      const dashboard: AdminDashboardDto = {
        overview: {
          totalUsers: userStats.total,
          activeUsers: userStats.active,
          newUsersToday: userStats.newToday,
          activeSessions: sessionStats.active,
          securityAlerts: securityStats.alerts,
          systemHealth: systemStats.health,
        },
        userMetrics: {
          totalUsers: userStats.total,
          activeUsers: userStats.active,
          inactiveUsers: userStats.inactive,
          newUsersToday: userStats.newToday,
          newUsersThisWeek: userStats.newThisWeek,
          newUsersThisMonth: userStats.newThisMonth,
          userGrowthRate: userStats.growthRate,
        },
        sessionMetrics: {
          activeSessions: sessionStats.active,
          totalSessions: sessionStats.total,
          averageSessionDuration: sessionStats.averageDuration,
          sessionsToday: sessionStats.today,
          deviceBreakdown: sessionStats.deviceBreakdown,
        },
        securityMetrics: {
          totalAlerts: securityStats.alerts,
          criticalAlerts: securityStats.critical,
          resolvedAlerts: securityStats.resolved,
          failedLoginAttempts: securityStats.failedLogins,
          blockedIPs: securityStats.blockedIPs,
          mfaAdoptionRate: securityStats.mfaAdoption,
        },
        systemMetrics: {
          cpuUsage: systemStats.cpu,
          memoryUsage: systemStats.memory,
          diskUsage: systemStats.disk,
          networkTraffic: systemStats.network,
          responseTime: systemStats.responseTime,
          uptime: systemStats.uptime,
        },
        recentActivities: recentActivities,
        alerts: await this.getSystemAlerts(),
      };

      // 记录访问日志
      await this.securityService.logSecurityEvent({
        type: SecurityEventType.ADMIN_DASHBOARD_ACCESS,
        userId: adminId,
        details: { action: 'view_dashboard' },
        ipAddress: 'system',
        userAgent: 'admin-service',
      });

      return dashboard;
    } catch (error) {
      this.logger.error('获取仪表板数据失败', error);
      throw error;
    }
  }

  /**
   * 获取系统状态
   */
  async getSystemStatus(): Promise<SystemStatusDto> {
    this.logger.log('获取系统状态');

    try {
      const status = await this.systemService.getSystemStatus();
      return status;
    } catch (error) {
      this.logger.error('获取系统状态失败', error);
      throw error;
    }
  }

  /**
   * 获取系统健康状态
   */
  async getSystemHealth(): Promise<any> {
    this.logger.log('获取系统健康状态');

    try {
      return await this.systemService.getHealthCheck();
    } catch (error) {
      this.logger.error('获取系统健康状态失败', error);
      throw error;
    }
  }

  /**
   * 获取系统指标
   */
  async getSystemMetrics(timeRange?: string): Promise<any> {
    this.logger.log(`获取系统指标: ${timeRange}`);

    try {
      return await this.systemService.getMetrics(timeRange);
    } catch (error) {
      this.logger.error('获取系统指标失败', error);
      throw error;
    }
  }

  /**
   * 执行系统维护
   */
  async performMaintenance(adminId: string, actionDto: AdminActionDto): Promise<any> {
    this.logger.log(`执行系统维护: ${adminId}, 操作: ${actionDto.action}`);

    try {
      // 记录维护操作
      await this.securityService.logSecurityEvent({
        type: SecurityEventType.SYSTEM_MAINTENANCE,
        userId: adminId,
        details: { action: actionDto.action, parameters: actionDto.parameters },
        ipAddress: 'system',
        userAgent: 'admin-service',
      });

      const result = await this.systemService.performMaintenance(actionDto);
      
      this.logger.log(`系统维护完成: ${actionDto.action}`);
      return result;
    } catch (error) {
      this.logger.error('系统维护失败', error);
      throw error;
    }
  }

  /**
   * 创建系统备份
   */
  async createBackup(adminId: string): Promise<any> {
    this.logger.log(`创建系统备份: ${adminId}`);

    try {
      // 记录备份操作
      await this.securityService.logSecurityEvent({
        type: SecurityEventType.SYSTEM_BACKUP,
        userId: adminId,
        details: { action: 'create_backup' },
        ipAddress: 'system',
        userAgent: 'admin-service',
      });

      const result = await this.systemService.createBackup();
      
      this.logger.log('系统备份创建完成');
      return result;
    } catch (error) {
      this.logger.error('系统备份创建失败', error);
      throw error;
    }
  }

  /**
   * 获取系统日志
   */
  async getSystemLogs(options: any): Promise<any> {
    this.logger.log('获取系统日志');

    try {
      return await this.systemService.getSystemLogs(options);
    } catch (error) {
      this.logger.error('获取系统日志失败', error);
      throw error;
    }
  }

  /**
   * 获取系统设置
   */
  async getSystemSettings(): Promise<any> {
    this.logger.log('获取系统设置');

    try {
      return await this.systemService.getSettings();
    } catch (error) {
      this.logger.error('获取系统设置失败', error);
      throw error;
    }
  }

  /**
   * 更新系统设置
   */
  async updateSystemSettings(adminId: string, settings: any): Promise<any> {
    this.logger.log(`更新系统设置: ${adminId}`);

    try {
      // 记录设置更新
      await this.securityService.logSecurityEvent({
        type: SecurityEventType.SYSTEM_SETTINGS_UPDATE,
        userId: adminId,
        details: { settings },
        ipAddress: 'system',
        userAgent: 'admin-service',
      });

      const result = await this.systemService.updateSettings(settings);
      
      this.logger.log('系统设置更新完成');
      return result;
    } catch (error) {
      this.logger.error('系统设置更新失败', error);
      throw error;
    }
  }

  /**
   * 获取管理员通知
   */
  async getAdminNotifications(adminId: string): Promise<any> {
    this.logger.log(`获取管理员通知: ${adminId}`);

    try {
      // 这里可以从数据库或缓存中获取通知
      // 暂时返回模拟数据
      return {
        notifications: [
          {
            id: '1',
            type: 'security',
            title: '安全警告',
            message: '检测到异常登录活动',
            severity: 'high',
            read: false,
            createdAt: new Date(),
          },
          {
            id: '2',
            type: 'system',
            title: '系统更新',
            message: '系统将在今晚进行维护',
            severity: 'medium',
            read: false,
            createdAt: new Date(),
          },
        ],
        unreadCount: 2,
      };
    } catch (error) {
      this.logger.error('获取管理员通知失败', error);
      throw error;
    }
  }

  /**
   * 标记通知为已读
   */
  async markNotificationAsRead(adminId: string, notificationId: string): Promise<any> {
    this.logger.log(`标记通知为已读: ${adminId}, 通知: ${notificationId}`);

    try {
      // 这里应该更新数据库中的通知状态
      // 暂时返回成功响应
      return { success: true };
    } catch (error) {
      this.logger.error('标记通知失败', error);
      throw error;
    }
  }

  // 私有方法 - 获取各种统计数据

  private async getUserStatistics(): Promise<any> {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const [total, active, newToday, newThisWeek, newThisMonth] = await Promise.all([
      this.userModel.countDocuments(),
      this.userModel.countDocuments({ status: 'active' }),
      this.userModel.countDocuments({ createdAt: { $gte: today } }),
      this.userModel.countDocuments({ createdAt: { $gte: thisWeek } }),
      this.userModel.countDocuments({ createdAt: { $gte: thisMonth } }),
    ]);

    return {
      total,
      active,
      inactive: total - active,
      newToday,
      newThisWeek,
      newThisMonth,
      growthRate: total > 0 ? (newThisMonth / total) * 100 : 0,
    };
  }

  private async getSessionStatistics(): Promise<any> {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    const [active, total, todaySessions] = await Promise.all([
      this.sessionModel.countDocuments({ active: true }),
      this.sessionModel.countDocuments(),
      this.sessionModel.countDocuments({ createdAt: { $gte: today } }),
    ]);

    return {
      active,
      total,
      today: todaySessions,
      averageDuration: 1800, // 30分钟，实际应该计算
      deviceBreakdown: {
        web: 60,
        mobile: 30,
        desktop: 10,
      },
    };
  }

  private async getSecurityStatistics(): Promise<any> {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    const [totalAlerts, failedLogins, mfaUsers, totalUsers] = await Promise.all([
      this.auditLogModel.countDocuments({ 
        type: { $in: ['security_alert', 'failed_login', 'suspicious_activity'] },
        createdAt: { $gte: today }
      }),
      this.auditLogModel.countDocuments({ 
        type: 'failed_login',
        createdAt: { $gte: today }
      }),
      this.userModel.countDocuments({ 'security.mfaEnabled': true }),
      this.userModel.countDocuments(),
    ]);

    return {
      alerts: totalAlerts,
      critical: Math.floor(totalAlerts * 0.1),
      resolved: Math.floor(totalAlerts * 0.8),
      failedLogins,
      blockedIPs: 5, // 实际应该从安全服务获取
      mfaAdoption: totalUsers > 0 ? (mfaUsers / totalUsers) * 100 : 0,
    };
  }

  private async getSystemStatistics(): Promise<any> {
    // 这里应该从系统监控服务获取实际数据
    return {
      health: 'healthy',
      cpu: 45.2,
      memory: 67.8,
      disk: 23.4,
      network: 12.5,
      responseTime: 120,
      uptime: 99.9,
    };
  }

  private async getRecentActivities(): Promise<any[]> {
    const recentLogs = await this.auditLogModel
      .find()
      .sort({ createdAt: -1 })
      .limit(10)
      .populate('userId', 'username email')
      .exec();

    return recentLogs.map(log => ({
      id: log._id,
      type: log.type,
      user: log.userId,
      description: log.details?.description || log.type,
      timestamp: log.createdAt,
      ipAddress: log.ipAddress,
    }));
  }

  private async getSystemAlerts(): Promise<any[]> {
    // 这里应该从监控系统获取实际警告
    return [
      {
        id: '1',
        type: 'performance',
        severity: 'warning',
        message: 'CPU使用率较高',
        timestamp: new Date(),
      },
      {
        id: '2',
        type: 'security',
        severity: 'info',
        message: '检测到新的登录设备',
        timestamp: new Date(),
      },
    ];
  }
}
