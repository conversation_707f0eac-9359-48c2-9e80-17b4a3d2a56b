import { Injectable, Logger, BadRequestException, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../../user-management/services/users.service';
import { SecurityService, SecurityEventType } from '../../security/services/security.service';
import * as speakeasy from 'speakeasy';
import * as qrcode from 'qrcode';
import * as crypto from 'crypto';

export interface MfaSetupResult {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
  manualEntryKey: string;
}

export interface MfaResult {
  enabled: boolean;
  backupCodes: string[];
}

export interface SmsCodeResult {
  sent: boolean;
  expiresAt: Date;
}

@Injectable()
export class MfaService {
  private readonly logger = new Logger(MfaService.name);
  private readonly appName: string;
  private readonly smsEnabled: boolean;
  private readonly totpWindow: number;
  private readonly backupCodeCount: number;

  constructor(
    private configService: ConfigService,
    private usersService: UsersService,
    private securityService: SecurityService,
  ) {
    this.appName = this.configService.get<string>('app.name', 'Football Manager');
    this.smsEnabled = this.configService.get<boolean>('sms.enabled', false);
    this.totpWindow = this.configService.get<number>('auth.mfa.totpWindow', 2);
    this.backupCodeCount = this.configService.get<number>('auth.mfa.backupCodeCount', 10);
  }

  /**
   * 生成TOTP密钥
   */
  async generateTotpSecret(userId: string): Promise<MfaSetupResult> {
    this.logger.log(`生成TOTP密钥: ${userId}`);

    const user = await this.usersService.findById(userId);
    
    // 生成密钥
    const secret = speakeasy.generateSecret({
      name: `${this.appName} (${user.email})`,
      issuer: this.appName,
      length: 32,
    });

    // 生成QR码URL
    const qrCodeUrl = await qrcode.toDataURL(secret.otpauth_url!);

    // 生成备用码
    const backupCodes = this.generateBackupCodes();

    // 记录安全事件
    await this.securityService.logSecurityEvent({
      type: SecurityEventType.MFA_ENABLED,
      userId,
      details: {
        action: 'totp_secret_generated',
      }
    });

    return {
      secret: secret.base32,
      qrCodeUrl,
      backupCodes,
      manualEntryKey: secret.base32,
    };
  }

  /**
   * 启用MFA
   */
  async enableMfa(userId: string, secret: string, code: string): Promise<MfaResult> {
    this.logger.log(`启用MFA: ${userId}`);

    // 验证TOTP代码
    const isValidCode = speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token: code,
      window: this.totpWindow,
    });

    if (!isValidCode) {
      throw new BadRequestException('无效的验证码');
    }

    // 生成备用码
    const backupCodes = this.generateBackupCodes();
    const hashedBackupCodes = backupCodes.map(code => this.hashBackupCode(code));

    // 更新用户MFA设置
    await this.usersService.update(userId, {
      security: {
        mfaEnabled: true,
        mfaSecret: secret,
        backupCodes: hashedBackupCodes,
      }
    } as any);

    // 记录安全事件
    await this.securityService.logSecurityEvent({
      type: SecurityEventType.MFA_ENABLED,
      userId,
      details: {
        action: 'mfa_enabled',
        method: 'totp',
      }
    });

    this.logger.log(`MFA启用成功: ${userId}`);

    return {
      enabled: true,
      backupCodes,
    };
  }

  /**
   * 禁用MFA
   */
  async disableMfa(userId: string, password: string, code: string): Promise<void> {
    this.logger.log(`禁用MFA: ${userId}`);

    const user = await this.usersService.findById(userId);

    // 验证密码
    const isValidPassword = await this.usersService.validatePassword(user, password);
    if (!isValidPassword) {
      throw new UnauthorizedException('密码错误');
    }

    // 验证MFA代码或备用码
    const isValidMfa = await this.verifyTotpCode(userId, code) || 
                       await this.verifyBackupCode(userId, code);

    if (!isValidMfa) {
      throw new BadRequestException('无效的验证码');
    }

    // 更新用户MFA设置
    await this.usersService.update(userId, {
      security: {
        mfaEnabled: false,
        mfaSecret: null,
        backupCodes: [],
      }
    } as any);

    // 记录安全事件
    await this.securityService.logSecurityEvent({
      type: SecurityEventType.MFA_DISABLED,
      userId,
      details: {
        action: 'mfa_disabled',
      }
    });

    this.logger.log(`MFA禁用成功: ${userId}`);
  }

  /**
   * 验证TOTP代码
   */
  async verifyTotpCode(userId: string, code: string): Promise<boolean> {
    try {
      const user = await this.usersService.findById(userId);
      
      if (!user.security.mfaEnabled || !user.security.mfaSecret) {
        return false;
      }

      const isValid = speakeasy.totp.verify({
        secret: user.security.mfaSecret,
        encoding: 'base32',
        token: code,
        window: this.totpWindow,
      });

      if (isValid) {
        // 记录成功验证
        await this.securityService.logSecurityEvent({
          type: SecurityEventType.LOGIN_SUCCESS,
          userId,
          details: {
            action: 'mfa_verified',
            method: 'totp',
          }
        });
      }

      return isValid;
    } catch (error) {
      this.logger.error('TOTP验证失败', error);
      return false;
    }
  }

  /**
   * 验证备用码
   */
  async verifyBackupCode(userId: string, code: string): Promise<boolean> {
    try {
      const user = await this.usersService.findById(userId);
      
      if (!user.security.mfaEnabled || !user.security.backupCodes) {
        return false;
      }

      const hashedCode = this.hashBackupCode(code);
      const codeIndex = user.security.backupCodes.indexOf(hashedCode);

      if (codeIndex === -1) {
        return false;
      }

      // 移除已使用的备用码
      const updatedBackupCodes = [...user.security.backupCodes];
      updatedBackupCodes.splice(codeIndex, 1);

      await this.usersService.update(userId, {
        security: {
          ...user.security,
          backupCodes: updatedBackupCodes,
        }
      } as any);

      // 记录备用码使用
      await this.securityService.logSecurityEvent({
        type: SecurityEventType.LOGIN_SUCCESS,
        userId,
        details: {
          action: 'backup_code_used',
          remainingCodes: updatedBackupCodes.length,
        }
      });

      this.logger.log(`备用码使用成功: ${userId}, 剩余: ${updatedBackupCodes.length}`);
      return true;
    } catch (error) {
      this.logger.error('备用码验证失败', error);
      return false;
    }
  }

  /**
   * 发送短信验证码
   */
  async sendSmsCode(userId: string, phone: string): Promise<SmsCodeResult> {
    if (!this.smsEnabled) {
      throw new BadRequestException('短信功能未启用');
    }

    this.logger.log(`发送短信验证码: ${userId}`);

    // 生成6位数字验证码
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5分钟过期

    // TODO: 集成短信服务提供商
    // await this.smsProvider.sendCode(phone, code);

    // 存储验证码（实际应该存储在Redis中）
    // await this.cacheService.set(`sms_code:${userId}`, code, 300);

    // 记录安全事件
    await this.securityService.logSecurityEvent({
      type: SecurityEventType.LOGIN_SUCCESS,
      userId,
      details: {
        action: 'sms_code_sent',
        phone: this.maskPhoneNumber(phone),
      }
    });

    this.logger.log(`短信验证码发送成功: ${userId}`);

    return {
      sent: true,
      expiresAt,
    };
  }

  /**
   * 验证短信验证码
   */
  async verifySmsCode(userId: string, code: string): Promise<boolean> {
    if (!this.smsEnabled) {
      return false;
    }

    try {
      // TODO: 从Redis获取验证码
      // const storedCode = await this.cacheService.get(`sms_code:${userId}`);
      // const isValid = storedCode === code;

      // 模拟验证（实际应该从缓存中验证）
      const isValid = code.length === 6 && /^\d+$/.test(code);

      if (isValid) {
        // 删除已使用的验证码
        // await this.cacheService.del(`sms_code:${userId}`);

        // 记录成功验证
        await this.securityService.logSecurityEvent({
          type: SecurityEventType.LOGIN_SUCCESS,
          userId,
          details: {
            action: 'sms_code_verified',
          }
        });
      }

      return isValid;
    } catch (error) {
      this.logger.error('短信验证码验证失败', error);
      return false;
    }
  }

  /**
   * 生成新的备用码
   */
  async generateNewBackupCodes(userId: string): Promise<string[]> {
    this.logger.log(`生成新备用码: ${userId}`);

    const backupCodes = this.generateBackupCodes();
    const hashedBackupCodes = backupCodes.map(code => this.hashBackupCode(code));

    // 更新用户备用码
    await this.usersService.update(userId, {
      security: {
        backupCodes: hashedBackupCodes,
      }
    } as any);

    // 记录安全事件
    await this.securityService.logSecurityEvent({
      type: SecurityEventType.MFA_ENABLED,
      userId,
      details: {
        action: 'backup_codes_regenerated',
        count: backupCodes.length,
      }
    });

    return backupCodes;
  }

  /**
   * 获取MFA状态
   */
  async getMfaStatus(userId: string): Promise<any> {
    const user = await this.usersService.findById(userId);
    
    return {
      enabled: user.security.mfaEnabled,
      backupCodesCount: user.security.backupCodes?.length || 0,
      trustedDevicesCount: user.security.trustedDevices?.length || 0,
    };
  }

  /**
   * 生成备用码
   */
  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    
    for (let i = 0; i < this.backupCodeCount; i++) {
      // 生成8位字母数字组合
      const code = crypto.randomBytes(4).toString('hex').toUpperCase();
      codes.push(code);
    }
    
    return codes;
  }

  /**
   * 哈希备用码
   */
  private hashBackupCode(code: string): string {
    return crypto.createHash('sha256').update(code).digest('hex');
  }

  /**
   * 脱敏手机号
   */
  private maskPhoneNumber(phone: string): string {
    if (phone.length <= 4) return phone;
    return phone.slice(0, 3) + '****' + phone.slice(-4);
  }
}
