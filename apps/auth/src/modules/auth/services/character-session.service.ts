import { Injectable, Logger, NotFoundException, BadRequestException, Inject } from '@nestjs/common';

// 实体
import { CharacterSessionDocument } from '../entities/character-session.entity';

// 仓储
import { ICharacterSessionRepository, CreateCharacterSessionData } from '../repositories/character-session.repository';

// 核心服务
import { RedisService } from '@common/redis';

/**
 * 角色会话服务
 * 负责角色会话的创建、管理和清理
 */
@Injectable()
export class CharacterSessionService {
  private readonly logger = new Logger(CharacterSessionService.name);

  constructor(
    @Inject('ICharacterSessionRepository')
    private readonly characterSessionRepository: ICharacterSessionRepository,
    private readonly redisService: RedisService,
  ) {}

  /**
   * 创建角色会话
   */
  async createSession(sessionData: CreateCharacterSessionData): Promise<CharacterSessionDocument> {
    this.logger.log(`创建角色会话: ${sessionData.userId} -> ${sessionData.serverId}`);

    try {
      // 1. 验证输入数据
      this.validateSessionData(sessionData);

      // 2. 检查是否已有活跃会话
      const existingSession = await this.characterSessionRepository.findActiveSession(sessionData.userId, sessionData.serverId);
      if (existingSession) {
        this.logger.warn(`用户在该区服已有活跃会话: ${sessionData.userId}@${sessionData.serverId}`);
        // 终止现有会话
        await this.characterSessionRepository.terminate(existingSession.id);
      }

      // 3. 创建新会话
      const session = await this.characterSessionRepository.create(sessionData);

      const savedSession = await session.save();

      // 4. 缓存到Redis（双重保险）
      await this.cacheSession(savedSession);

      // 5. 更新用户会话映射
      await this.updateUserSessionMapping(sessionData.userId, savedSession.id);

      this.logger.log(`角色会话创建成功: ${savedSession.id}`);
      return savedSession;

    } catch (error) {
      this.logger.error(`创建角色会话失败: ${sessionData.userId}`, error);
      throw error;
    }
  }

  /**
   * 获取会话信息
   */
  async getSession(sessionId: string): Promise<CharacterSessionDocument | null> {
    try {
      // 1. 先从Redis缓存获取
      const cached = await this.getSessionFromCache(sessionId);
      if (cached) {
        return cached;
      }

      // 2. 从数据库获取
      const session = await this.characterSessionRepository.findById(sessionId);
      if (!session) {
        return null;
      }

      // 3. 检查会话是否有效
      if (!(session as any).isValid()) {
        await this.characterSessionRepository.terminate(sessionId);
        return null;
      }

      // 4. 缓存到Redis
      await this.cacheSession(session);

      return session;

    } catch (error) {
      this.logger.error(`获取会话失败: ${sessionId}`, error);
      return null;
    }
  }

  /**
   * 验证会话有效性
   */
  async validateSession(sessionId: string): Promise<boolean> {
    try {
      const session = await this.getSession(sessionId);
      return session ? (session as any).isValid() : false;
    } catch (error) {
      this.logger.error(`验证会话失败: ${sessionId}`, error);
      return false;
    }
  }

  /**
   * 更新会话活动时间
   */
  async updateSessionActivity(sessionId: string, socketId?: string): Promise<void> {
    try {
      const updateData: any = {
        lastActivity: new Date(),
      };

      if (socketId) {
        updateData.socketId = socketId;
      }

      // 1. 更新数据库
      await this.characterSessionModel.findByIdAndUpdate(
        sessionId,
        updateData,
        { new: true }
      ).exec();

      // 2. 更新缓存
      const session = await this.getSession(sessionId);
      if (session) {
        await this.cacheSession(session);
      }

    } catch (error) {
      this.logger.error(`更新会话活动时间失败: ${sessionId}`, error);
      // 不抛出错误，避免影响主要业务流程
    }
  }

  /**
   * 延长会话时间
   */
  async extendSession(sessionId: string, additionalSeconds: number = 14400): Promise<void> {
    try {
      const session = await this.getSession(sessionId);
      if (!session) {
        throw new NotFoundException('会话不存在');
      }

      if (!(session as any).isValid()) {
        throw new BadRequestException('会话已过期，无法延长');
      }

      // 延长会话
      (session as any).extend(additionalSeconds);
      await session.save();

      // 更新缓存
      await this.cacheSession(session);

      this.logger.log(`会话延长成功: ${sessionId}, 延长${additionalSeconds}秒`);

    } catch (error) {
      this.logger.error(`延长会话失败: ${sessionId}`, error);
      throw error;
    }
  }

  /**
   * 终止会话
   */
  async terminateSession(sessionId: string): Promise<void> {
    try {
      // 1. 更新数据库
      const session = await this.characterSessionModel.findByIdAndUpdate(
        sessionId,
        {
          active: false,
          lastActivity: new Date(),
        },
        { new: true }
      ).exec();

      if (session) {
        // 2. 清除缓存
        await this.clearSessionCache(sessionId);

        // 3. 清除用户会话映射
        await this.clearUserSessionMapping(session.userId);

        this.logger.log(`会话终止成功: ${sessionId}`);
      }

    } catch (error) {
      this.logger.error(`终止会话失败: ${sessionId}`, error);
      throw error;
    }
  }

  /**
   * 终止用户的所有会话
   */
  async terminateUserSessions(userId: string): Promise<void> {
    try {
      // 1. 查找用户的所有活跃会话
      const sessions = await this.characterSessionModel.find({
        userId,
        active: true,
      }).exec();

      // 2. 批量终止
      const terminatePromises = sessions.map(session => 
        this.terminateSession(session.id)
      );

      await Promise.allSettled(terminatePromises);

      this.logger.log(`用户所有会话已终止: ${userId}, 共${sessions.length}个会话`);

    } catch (error) {
      this.logger.error(`终止用户会话失败: ${userId}`, error);
      throw error;
    }
  }

  /**
   * 清理过期会话（定时任务）
   */
  async cleanupExpiredSessions(): Promise<void> {
    try {
      const result = await this.characterSessionModel.updateMany(
        {
          active: true,
          expiresAt: { $lt: new Date() },
        },
        {
          active: false,
          lastActivity: new Date(),
        }
      ).exec();

      if (result.modifiedCount > 0) {
        this.logger.log(`清理过期会话: ${result.modifiedCount}个`);
      }

    } catch (error) {
      this.logger.error('清理过期会话失败', error);
    }
  }

  /**
   * 私有方法：验证会话数据
   */
  private validateSessionData(data: CreateSessionData): void {
    if (!data.userId) {
      throw new BadRequestException('用户ID不能为空');
    }

    if (!data.characterId) {
      throw new BadRequestException('角色ID不能为空');
    }

    if (!data.serverId) {
      throw new BadRequestException('服务器ID不能为空');
    }

    if (!data.serverName) {
      throw new BadRequestException('服务器名称不能为空');
    }

    if (!data.expiresAt || data.expiresAt <= new Date()) {
      throw new BadRequestException('过期时间必须在未来');
    }
  }

  /**
   * 私有方法：查找活跃会话
   */
  private async findActiveSession(userId: string, serverId: string): Promise<CharacterSessionDocument | null> {
    return this.characterSessionModel.findOne({
      userId,
      serverId,
      active: true,
      expiresAt: { $gt: new Date() },
    }).exec();
  }

  /**
   * 私有方法：缓存会话到Redis
   */
  private async cacheSession(session: CharacterSessionDocument): Promise<void> {
    const cacheKey = `character_session:${session.id}`;
    const ttl = Math.floor((session.expiresAt.getTime() - Date.now()) / 1000);
    
    if (ttl > 0) {
      await this.redisService.set(cacheKey, JSON.stringify(session.toJSON()), ttl, 'global');
    }
  }

  /**
   * 私有方法：从Redis获取会话
   */
  private async getSessionFromCache(sessionId: string): Promise<CharacterSessionDocument | null> {
    try {
      const cacheKey = `character_session:${sessionId}`;
      const cached = await this.redisService.get(cacheKey, 'global');
      
      if (cached) {
        const sessionData = JSON.parse(cached as string);
        // 创建一个临时的文档对象用于方法调用
        const tempSession = new this.characterSessionModel(sessionData);
        return (tempSession as any).isValid() ? tempSession : null;
      }

      return null;
    } catch (error) {
      this.logger.warn(`从缓存获取会话失败: ${sessionId}`, error);
      return null;
    }
  }

  /**
   * 私有方法：清除会话缓存
   */
  private async clearSessionCache(sessionId: string): Promise<void> {
    const cacheKey = `character_session:${sessionId}`;
    await this.redisService.del(cacheKey, 'global');
  }

  /**
   * 私有方法：更新用户会话映射
   */
  private async updateUserSessionMapping(userId: string, sessionId: string): Promise<void> {
    const mappingKey = `user:${userId}:character_session`;
    await this.redisService.set(mappingKey, sessionId, 4 * 3600, 'global'); // 4小时
  }

  /**
   * 私有方法：清除用户会话映射
   */
  private async clearUserSessionMapping(userId: string): Promise<void> {
    const mappingKey = `user:${userId}:character_session`;
    await this.redisService.del(mappingKey, 'global');
  }
}

// 类型定义
interface CreateSessionData {
  userId: string;
  characterId: string;
  serverId: string;
  serverName: string;
  deviceInfo?: string;
  ipAddress?: string;
  userAgent?: string;
  expiresAt: Date;
}
