import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { AuthService } from '../services/auth.service';
import { JwtPayload } from '../services/jwt.service';
import { JwtUserDocument } from '@auth/modules/user/entities/user.entity';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    private authService: AuthService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('auth.jwt.secret'),
      issuer: configService.get<string>('auth.jwt.issuer'),
      audience: configService.get<string>('auth.jwt.audience'),
    });
  }

  async validate(payload: JwtPayload): Promise<JwtUserDocument> {
    try {
      // 验证令牌并获取用户信息
      const user = await this.authService.validateToken(payload.sub);

      // 直接在原始UserDocument对象上添加JWT属性
      // 这样保留了所有原始方法和原型链
      (user as JwtUserDocument).sessionId = payload.sessionId;
      (user as JwtUserDocument).deviceId = payload.deviceId;
      (user as JwtUserDocument).jti = payload.jti;

      return user as JwtUserDocument;
    } catch (error) {
      throw new UnauthorizedException('令牌验证失败');
    }
  }
}
