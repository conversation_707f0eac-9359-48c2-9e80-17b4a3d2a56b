import { ApiProperty } from '@nestjs/swagger';
import { 
  IsString, 
  IsEmail, 
  IsOptional, 
  IsEnum, 
  IsDateString, 
  IsBoolean,
  MinLength, 
  MaxLength, 
  Matches,
  IsPhoneNumber,
  ValidateNested,
  IsObject
} from 'class-validator';
import { Type } from 'class-transformer';
import { Gender } from '../entities/user.entity';

// 用户个人信息DTO
export class CreateUserProfileDto {
  @ApiProperty({ 
    description: '名字',
    example: 'John',
    minLength: 1,
    maxLength: 50
  })
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  firstName: string;

  @ApiProperty({ 
    description: '姓氏',
    example: 'Doe',
    minLength: 1,
    maxLength: 50
  })
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  lastName: string;

  @ApiProperty({ 
    description: '头像URL',
    example: 'https://example.com/avatar.jpg',
    required: false
  })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiProperty({ 
    description: '出生日期',
    example: '1990-01-01',
    required: false
  })
  @IsOptional()
  @IsDateString()
  dateOfBirth?: string;

  @ApiProperty({ 
    description: '性别',
    enum: Gender,
    example: Gender.MALE,
    required: false
  })
  @IsOptional()
  @IsEnum(Gender)
  gender?: Gender;

  @ApiProperty({ 
    description: '国家代码',
    example: 'US',
    minLength: 2,
    maxLength: 2,
    required: false
  })
  @IsOptional()
  @IsString()
  @MinLength(2)
  @MaxLength(2)
  @Matches(/^[A-Z]{2}$/, { message: '国家代码必须是2位大写字母' })
  country?: string;

  @ApiProperty({ 
    description: '时区',
    example: 'America/New_York',
    required: false
  })
  @IsOptional()
  @IsString()
  timezone?: string;

  @ApiProperty({ 
    description: '首选语言',
    example: 'zh',
    default: 'zh',
    maxLength: 5
  })
  @IsOptional()
  @IsString()
  @MaxLength(5)
  language?: string = 'zh';
}

// 游戏偏好设置DTO
export class GamePreferencesDto {
  @ApiProperty({ 
    description: '是否启用通知',
    example: true,
    default: true
  })
  @IsOptional()
  @IsBoolean()
  notifications?: boolean = true;

  @ApiProperty({ 
    description: '是否自动保存',
    example: true,
    default: true
  })
  @IsOptional()
  @IsBoolean()
  autoSave?: boolean = true;

  @ApiProperty({ 
    description: '主题设置',
    example: 'light',
    default: 'light'
  })
  @IsOptional()
  @IsString()
  theme?: string = 'light';

  @ApiProperty({ 
    description: '语言设置',
    example: 'zh',
    default: 'zh'
  })
  @IsOptional()
  @IsString()
  gameLanguage?: string = 'zh';

  @ApiProperty({ 
    description: '音效设置',
    example: true,
    default: true
  })
  @IsOptional()
  @IsBoolean()
  soundEnabled?: boolean = true;

  @ApiProperty({ 
    description: '音乐设置',
    example: true,
    default: true
  })
  @IsOptional()
  @IsBoolean()
  musicEnabled?: boolean = true;
}

// 创建用户DTO
export class CreateUserDto {
  @ApiProperty({ 
    description: '用户名',
    example: 'john_doe',
    minLength: 5,
    maxLength: 35
  })
  @IsString()
  @MinLength(5, { message: '用户名至少需要5个字符' })
  @MaxLength(35, { message: '用户名不能超过35个字符' })
  @Matches(/^[a-zA-Z0-9_-]+$/, { 
    message: '用户名只能包含字母、数字、下划线和连字符' 
  })
  username: string;

  @ApiProperty({ 
    description: '邮箱地址',
    example: '<EMAIL>'
  })
  @IsEmail({}, { message: '请输入有效的邮箱地址' })
  email: string;

  @ApiProperty({ 
    description: '手机号码',
    example: '+1234567890',
    required: false
  })
  @IsOptional()
  @IsPhoneNumber(null, { message: '请输入有效的手机号码' })
  phone?: string;

  @ApiProperty({ 
    description: '密码',
    example: 'SecurePass123!',
    minLength: 8,
    maxLength: 128
  })
  @IsString()
  @MinLength(8, { message: '密码至少需要8个字符' })
  @MaxLength(128, { message: '密码不能超过128个字符' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
    message: '密码必须包含至少一个大写字母、一个小写字母、一个数字和一个特殊字符'
  })
  password: string;

  @ApiProperty({ 
    description: '确认密码',
    example: 'SecurePass123!'
  })
  @IsString()
  confirmPassword: string;

  @ApiProperty({ 
    description: '用户个人信息',
    type: CreateUserProfileDto
  })
  @ValidateNested()
  @Type(() => CreateUserProfileDto)
  profile: CreateUserProfileDto;

  @ApiProperty({ 
    description: '游戏偏好设置',
    type: GamePreferencesDto,
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => GamePreferencesDto)
  gamePreferences?: GamePreferencesDto;

  @ApiProperty({ 
    description: '是否接受服务条款',
    example: true
  })
  @IsBoolean()
  acceptTerms: boolean;

  @ApiProperty({ 
    description: '是否同意接收营销邮件',
    example: false,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  marketingConsent?: boolean = false;

  @ApiProperty({ 
    description: '注册来源',
    example: 'web',
    required: false
  })
  @IsOptional()
  @IsString()
  registrationSource?: string;

  @ApiProperty({ 
    description: '推荐人用户名',
    example: 'referrer_user',
    required: false
  })
  @IsOptional()
  @IsString()
  referredBy?: string;
}
