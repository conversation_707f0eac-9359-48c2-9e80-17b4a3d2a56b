import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery, QueryOptions } from 'mongoose';
import { User, UserDocument, UserStatus } from '../entities/user.entity';
import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto, SearchUsersDto } from '../dto/update-user.dto';

export interface PaginationResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

@Injectable()
export class UserRepository {
  private readonly logger = new Logger(UserRepository.name);

  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
  ) {}

  /**
   * 创建用户
   */
  async create(createUserDto: CreateUserDto): Promise<UserDocument> {
    try {
      const user = new this.userModel(createUserDto);
      return await user.save();
    } catch (error) {
      this.logger.error('创建用户失败', error);
      throw error;
    }
  }

  /**
   * 根据ID查找用户
   */
  async findById(id: string, includePassword = false): Promise<UserDocument | null> {
    try {
      const query = this.userModel.findById(id);
      
      if (includePassword) {
        query.select('+passwordHash +salt');
      }
      
      return await query.exec();
    } catch (error) {
      this.logger.error(`根据ID查找用户失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 根据用户名查找用户
   */
  async findByUsername(username: string, includePassword = false): Promise<UserDocument | null> {
    try {
      const query = this.userModel.findOne({ 
        username: username.toLowerCase(),
        status: { $ne: UserStatus.DELETED }
      });
      
      if (includePassword) {
        query.select('+passwordHash +salt');
      }
      
      return await query.exec();
    } catch (error) {
      this.logger.error(`根据用户名查找用户失败: ${username}`, error);
      throw error;
    }
  }

  /**
   * 根据邮箱查找用户
   */
  async findByEmail(email: string, includePassword = false): Promise<UserDocument | null> {
    try {
      const query = this.userModel.findOne({ 
        email: email.toLowerCase(),
        status: { $ne: UserStatus.DELETED }
      });
      
      if (includePassword) {
        query.select('+passwordHash +salt');
      }
      
      return await query.exec();
    } catch (error) {
      this.logger.error(`根据邮箱查找用户失败: ${email}`, error);
      throw error;
    }
  }

  /**
   * 根据手机号查找用户
   */
  async findByPhone(phone: string): Promise<UserDocument | null> {
    try {
      return await this.userModel.findOne({ 
        phone,
        status: { $ne: UserStatus.DELETED }
      }).exec();
    } catch (error) {
      this.logger.error(`根据手机号查找用户失败: ${phone}`, error);
      throw error;
    }
  }

  /**
   * 根据用户名或邮箱查找用户
   */
  async findByUsernameOrEmail(identifier: string, includePassword = false): Promise<UserDocument | null> {
    try {
      const query = this.userModel.findOne({
        $or: [
          { username: identifier.toLowerCase() },
          { email: identifier.toLowerCase() }
        ],
        status: { $ne: UserStatus.DELETED }
      });
      
      if (includePassword) {
        query.select('+passwordHash +salt +security.mfaSecret +security.passwordHistory +security.backupCodes');
      }
      
      return await query.exec();
    } catch (error) {
      this.logger.error(`根据用户名或邮箱查找用户失败: ${identifier}`, error);
      throw error;
    }
  }

  /**
   * 更新用户
   */
  async update(id: string, updateUserDto: UpdateUserDto): Promise<UserDocument | null> {
    try {
      return await this.userModel.findByIdAndUpdate(
        id,
        { $set: updateUserDto },
        { new: true, runValidators: true }
      ).exec();
    } catch (error) {
      this.logger.error(`更新用户失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 部分更新用户
   */
  async updatePartial(
    filter: FilterQuery<UserDocument>,
    update: UpdateQuery<UserDocument>,
    options?: QueryOptions
  ): Promise<UserDocument | null> {
    try {
      return await this.userModel.findOneAndUpdate(
        filter,
        update,
        { new: true, runValidators: true, ...options }
      ).exec();
    } catch (error) {
      this.logger.error('部分更新用户失败', error);
      throw error;
    }
  }

  /**
   * 删除用户（软删除）
   */
  async softDelete(id: string): Promise<UserDocument | null> {
    try {
      return await this.userModel.findByIdAndUpdate(
        id,
        { 
          $set: { 
            status: UserStatus.DELETED,
            deletedAt: new Date()
          }
        },
        { new: true }
      ).exec();
    } catch (error) {
      this.logger.error(`软删除用户失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 永久删除用户
   */
  async hardDelete(id: string): Promise<boolean> {
    try {
      const result = await this.userModel.findByIdAndDelete(id).exec();
      return !!result;
    } catch (error) {
      this.logger.error(`永久删除用户失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 搜索用户（分页）
   */
  async search(searchDto: SearchUsersDto): Promise<PaginationResult<UserDocument>> {
    try {
      const {
        search,
        status,
        role,
        emailVerified,
        phoneVerified,
        registeredAfter,
        registeredBefore,
        lastLoginAfter,
        lastLoginBefore,
        page = 1,
        limit = 20,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = searchDto;

      // 构建查询条件
      const filter: FilterQuery<UserDocument> = {
        status: { $ne: UserStatus.DELETED }
      };

      // 搜索关键词
      if (search) {
        filter.$or = [
          { username: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { 'profile.firstName': { $regex: search, $options: 'i' } },
          { 'profile.lastName': { $regex: search, $options: 'i' } }
        ];
      }

      // 状态过滤
      if (status) {
        filter.status = status;
      }

      // 角色过滤
      if (role) {
        filter.roles = { $in: [role] };
      }

      // 邮箱验证状态过滤
      if (typeof emailVerified === 'boolean') {
        filter.emailVerified = emailVerified;
      }

      // 手机验证状态过滤
      if (typeof phoneVerified === 'boolean') {
        filter.phoneVerified = phoneVerified;
      }

      // 注册日期过滤
      if (registeredAfter || registeredBefore) {
        filter.createdAt = {};
        if (registeredAfter) {
          filter.createdAt.$gte = new Date(registeredAfter);
        }
        if (registeredBefore) {
          filter.createdAt.$lte = new Date(registeredBefore);
        }
      }

      // 最后登录日期过滤
      if (lastLoginAfter || lastLoginBefore) {
        filter.lastLoginAt = {};
        if (lastLoginAfter) {
          filter.lastLoginAt.$gte = new Date(lastLoginAfter);
        }
        if (lastLoginBefore) {
          filter.lastLoginAt.$lte = new Date(lastLoginBefore);
        }
      }

      // 计算跳过的文档数
      const skip = (page - 1) * limit;

      // 构建排序
      const sort: any = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // 执行查询
      const [data, total] = await Promise.all([
        this.userModel
          .find(filter)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .exec(),
        this.userModel.countDocuments(filter).exec()
      ]);

      // 计算分页信息
      const pages = Math.ceil(total / limit);
      const hasNext = page < pages;
      const hasPrev = page > 1;

      return {
        data,
        total,
        page,
        limit,
        pages,
        hasNext,
        hasPrev
      };
    } catch (error) {
      this.logger.error('搜索用户失败', error);
      throw error;
    }
  }

  /**
   * 批量更新用户
   */
  async bulkUpdate(userIds: string[], updates: UpdateQuery<UserDocument>): Promise<number> {
    try {
      const result = await this.userModel.updateMany(
        { _id: { $in: userIds } },
        { $set: updates }
      ).exec();
      
      return result.modifiedCount;
    } catch (error) {
      this.logger.error('批量更新用户失败', error);
      throw error;
    }
  }

  /**
   * 获取用户统计信息
   */
  async getStatistics(): Promise<any> {
    try {
      const [
        totalUsers,
        activeUsers,
        verifiedUsers,
        premiumUsers,
        recentUsers
      ] = await Promise.all([
        // 总用户数
        this.userModel.countDocuments({ status: { $ne: UserStatus.DELETED } }),
        
        // 活跃用户数（30天内登录）
        this.userModel.countDocuments({
          status: UserStatus.ACTIVE,
          lastLoginAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }),
        
        // 已验证用户数
        this.userModel.countDocuments({
          status: { $ne: UserStatus.DELETED },
          emailVerified: true
        }),
        
        // 高级用户数
        this.userModel.countDocuments({
          status: { $ne: UserStatus.DELETED },
          'gameProfile.premiumUntil': { $gt: new Date() }
        }),
        
        // 最近7天注册用户数
        this.userModel.countDocuments({
          createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
        })
      ]);

      return {
        totalUsers,
        activeUsers,
        verifiedUsers,
        premiumUsers,
        recentUsers,
        timestamp: new Date()
      };
    } catch (error) {
      this.logger.error('获取用户统计信息失败', error);
      throw error;
    }
  }

  /**
   * 检查用户名是否存在
   */
  async existsByUsername(username: string): Promise<boolean> {
    try {
      const count = await this.userModel.countDocuments({
        username: username.toLowerCase(),
        status: { $ne: UserStatus.DELETED }
      }).exec();
      
      return count > 0;
    } catch (error) {
      this.logger.error(`检查用户名是否存在失败: ${username}`, error);
      throw error;
    }
  }

  /**
   * 检查邮箱是否存在
   */
  async existsByEmail(email: string): Promise<boolean> {
    try {
      const count = await this.userModel.countDocuments({
        email: email.toLowerCase(),
        status: { $ne: UserStatus.DELETED }
      }).exec();
      
      return count > 0;
    } catch (error) {
      this.logger.error(`检查邮箱是否存在失败: ${email}`, error);
      throw error;
    }
  }

  /**
   * 检查手机号是否存在
   */
  async existsByPhone(phone: string): Promise<boolean> {
    try {
      const count = await this.userModel.countDocuments({
        phone,
        status: { $ne: UserStatus.DELETED }
      }).exec();
      
      return count > 0;
    } catch (error) {
      this.logger.error(`检查手机号是否存在失败: ${phone}`, error);
      throw error;
    }
  }
}
