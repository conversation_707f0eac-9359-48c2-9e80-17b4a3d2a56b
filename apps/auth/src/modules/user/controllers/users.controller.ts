import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UseInterceptors,
  ClassSerializerInterceptor,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { Cacheable, CacheEvict, CachePut } from '@common/redis';
import { UsersService } from '../services/users.service';
import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto, UpdatePasswordDto, SearchUsersDto } from '../dto/update-user.dto';
import { User, UserDocument } from '../entities/user.entity';
import { UserResponseDto, UserSummaryDto } from '../dto/user-response.dto';
import { UserTransformerService } from '../services/user-transformer.service';
import { JwtAuthGuard } from '../../security/guards/jwt-auth.guard';
import { RolesGuard } from '../../rbac/guards/roles.guard';
import { Roles } from '@auth/common/decorators/roles.decorator';
import { CurrentUser } from '@auth/common/decorators/current-user.decorator';
import { ApiResponseDto } from '@auth/common/dto/response.dto';

@ApiTags('用户管理')
@Controller('users')
@UseInterceptors(ClassSerializerInterceptor)
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly userTransformer: UserTransformerService,
  ) {}

  @Post()
  @UseGuards(ThrottlerGuard)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '创建用户' })
  @ApiResponse({
    status: 201,
    description: '用户创建成功',
    type: User,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 409,
    description: '用户名或邮箱已存在',
  })
  @ApiResponse({
    status: 429,
    description: '请求过于频繁',
  })
  async create(
    @Body(ValidationPipe) createUserDto: CreateUserDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<UserResponseDto>> {
    const registrationIp = req.ip || req.connection.remoteAddress;
    const user = await this.usersService.create(createUserDto, registrationIp);

    // 使用专门的转换服务，确保类型安全和数据纯净
    const userResponse = this.userTransformer.toUserResponse(user);

    return {
      success: true,
      data: userResponse,
      message: '用户创建成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: '获取当前用户信息' })
  @ApiResponse({
    status: 200,
    description: '获取用户信息成功',
    type: User,
  })
  @ApiResponse({
    status: 401,
    description: '未授权访问',
  })
  async getProfile(@CurrentUser() user: UserDocument): Promise<ApiResponseDto<UserResponseDto>> {
    const userProfile = await this.usersService.findById(user.id);

    // 使用专门的转换服务，确保类型安全和数据纯净
    const userResponse = this.userTransformer.toUserResponse(userProfile);

    return {
      success: true,
      data: userResponse,
      message: '获取用户信息成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Put('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: '更新当前用户信息' })
  @ApiResponse({
    status: 200,
    description: '用户信息更新成功',
    type: User,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: '未授权访问',
  })
  @ApiResponse({
    status: 409,
    description: '邮箱或手机号已被使用',
  })
  async updateProfile(
    @CurrentUser() user: UserDocument,
    @Body(ValidationPipe) updateUserDto: UpdateUserDto,
  ): Promise<ApiResponseDto<User>> {
    const updatedUser = await this.usersService.update(user.id, updateUserDto);

    // 使用toJSON()方法确保返回纯净的数据，避免循环引用
    const cleanUserData = updatedUser.toJSON() as User;

    return {
      success: true,
      data: cleanUserData,
      message: '用户信息更新成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Put('me/password')
  @UseGuards(JwtAuthGuard, ThrottlerGuard)
  @ApiBearerAuth('JWT-auth')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '修改当前用户密码' })
  @ApiResponse({
    status: 204,
    description: '密码修改成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误或密码不符合要求',
  })
  @ApiResponse({
    status: 401,
    description: '当前密码错误或未授权访问',
  })
  @ApiResponse({
    status: 429,
    description: '请求过于频繁',
  })
  async updatePassword(
    @CurrentUser() user: UserDocument,
    @Body(ValidationPipe) updatePasswordDto: UpdatePasswordDto,
  ): Promise<ApiResponseDto<null>> {
    await this.usersService.updatePassword(user.id, updatePasswordDto);
    
    return {
      success: true,
      data: null,
      message: '密码修改成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Delete('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除当前用户账户' })
  @ApiResponse({
    status: 204,
    description: '账户删除成功',
  })
  @ApiResponse({
    status: 401,
    description: '未授权访问',
  })
  async deleteAccount(@CurrentUser() user: UserDocument): Promise<ApiResponseDto<null>> {
    await this.usersService.remove(user.id);
    
    return {
      success: true,
      data: null,
      message: '账户删除成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: '根据ID获取用户信息' })
  @ApiParam({
    name: 'id',
    description: '用户ID',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: '获取用户信息成功',
    type: User,
  })
  @ApiResponse({
    status: 401,
    description: '未授权访问',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足',
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在',
  })
  async findById(@Param('id') id: string): Promise<ApiResponseDto<User>> {
    const user = await this.usersService.findById(id);

    // 使用toJSON()方法确保返回纯净的数据，避免循环引用
    const cleanUserData = user.toJSON() as User;

    return {
      success: true,
      data: cleanUserData,
      message: '获取用户信息成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: '搜索用户列表' })
  @ApiQuery({
    name: 'search',
    description: '搜索关键词',
    required: false,
    type: 'string',
  })
  @ApiQuery({
    name: 'status',
    description: '用户状态过滤',
    required: false,
    enum: ['active', 'inactive', 'suspended'],
  })
  @ApiQuery({
    name: 'page',
    description: '页码',
    required: false,
    type: 'number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: '每页数量',
    required: false,
    type: 'number',
    example: 20,
  })
  @ApiResponse({
    status: 200,
    description: '获取用户列表成功',
  })
  @ApiResponse({
    status: 401,
    description: '未授权访问',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足',
  })
  async search(@Query(ValidationPipe) searchDto: SearchUsersDto): Promise<ApiResponseDto<any>> {
    const result = await this.usersService.search(searchDto);
    
    return {
      success: true,
      data: result,
      message: '获取用户列表成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('statistics/overview')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth('JWT-auth')
  @Cacheable({
    key: 'user:statistics',
    ttl: 600,
    condition: 'true'
  })
  @ApiOperation({ summary: '获取用户统计信息' })
  @ApiResponse({
    status: 200,
    description: '获取统计信息成功',
  })
  @ApiResponse({
    status: 401,
    description: '未授权访问',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足',
  })
  async getStatistics(): Promise<ApiResponseDto<any>> {
    const statistics = await this.usersService.getStatistics();
    
    return {
      success: true,
      data: statistics,
      message: '获取统计信息成功',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 根据用户名获取用户信息
   */
  @Get('username/:username')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: '根据用户名获取用户信息' })
  @ApiParam({
    name: 'username',
    description: '用户名',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: '获取用户信息成功',
    type: User,
  })
  @ApiResponse({
    status: 401,
    description: '未授权访问',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足',
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在',
  })
  async findByUsername(@Param('username') username: string): Promise<ApiResponseDto<User>> {
    const user = await this.usersService.findByUsername(username);

    // 使用toJSON()方法确保返回纯净的数据，避免循环引用
    const cleanUserData = user.toJSON() as User;

    return {
      success: true,
      data: cleanUserData,
      message: '获取用户信息成功',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 根据邮箱获取用户信息
   */
  @Get('email/:email')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: '根据邮箱获取用户信息' })
  @ApiParam({
    name: 'email',
    description: '邮箱地址',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: '获取用户信息成功',
    type: User,
  })
  @ApiResponse({
    status: 401,
    description: '未授权访问',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足',
  })
  @ApiResponse({
    status: 404,
    description: '用户不存在',
  })
  async findByEmail(@Param('email') email: string): Promise<ApiResponseDto<User>> {
    const user = await this.usersService.findByEmail(email);

    // 使用toJSON()方法确保返回纯净的数据，避免循环引用
    const cleanUserData = user.toJSON() as User;

    return {
      success: true,
      data: cleanUserData,
      message: '获取用户信息成功',
      timestamp: new Date().toISOString(),
    };
  }
}
