import { Module } from '@nestjs/common';

// 导入依赖模块
import { RolesModule } from '../../domain/roles/roles.module';

// 导入现有的守卫
import { JwtAuthGuard } from '../../modules/security/guards/jwt-auth.guard';
import { RolesGuard } from '../../modules/rbac/guards/roles.guard';
import { PermissionsGuard } from '../../modules/rbac/guards/permissions.guard';
import { ThrottlerBehindProxyGuard } from '../../modules/security/guards/throttler-behind-proxy.guard';

/**
 * 守卫模块
 * 
 * 专门管理所有守卫组件，包括：
 * - JWT 认证守卫
 * - 角色守卫
 * - 权限守卫
 * - 限流代理守卫
 */
@Module({
  imports: [
    RolesModule, // PermissionsGuard 需要 RolesService
  ],
  providers: [
    JwtAuthGuard,
    RolesGuard,
    PermissionsGuard,
    ThrottlerBehindProxyGuard,
  ],
  exports: [
    JwtAuthGuard,
    RolesGuard,
    PermissionsGuard,
    ThrottlerBehindProxyGuard,
  ],
})
export class GuardsModule {}
