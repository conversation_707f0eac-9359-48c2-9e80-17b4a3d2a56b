/**
 * 验证服务
 * 提供各种数据验证功能
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { validate, ValidationError } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { PASSWORD_POLICY } from '../../common/constants/auth.constants';

// 验证结果接口
export interface ValidationResult {
  valid: boolean;
  errors: ValidationErrorDetail[];
  warnings: ValidationWarningDetail[];
}

// 验证错误详情接口
export interface ValidationErrorDetail {
  field: string;
  code: string;
  message: string;
  value?: any;
  constraints?: Record<string, string>;
}

// 验证警告详情接口
export interface ValidationWarningDetail {
  field: string;
  code: string;
  message: string;
  value?: any;
}

// 密码验证结果接口
export interface PasswordValidationResult {
  valid: boolean;
  score: number;
  level: 'weak' | 'fair' | 'good' | 'strong';
  errors: string[];
  suggestions: string[];
}

@Injectable()
export class ValidationService {
  private readonly logger = new Logger(ValidationService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * 验证DTO对象
   */
  async validateDto<T extends object>(
    dtoClass: new () => T,
    data: any,
    options?: {
      skipMissingProperties?: boolean;
      whitelist?: boolean;
      forbidNonWhitelisted?: boolean;
    }
  ): Promise<ValidationResult> {
    try {
      const dto = plainToClass(dtoClass, data);
      const errors = await validate(dto, {
        skipMissingProperties: options?.skipMissingProperties ?? false,
        whitelist: options?.whitelist ?? true,
        forbidNonWhitelisted: options?.forbidNonWhitelisted ?? true,
      });

      const validationErrors = this.transformValidationErrors(errors);
      
      return {
        valid: validationErrors.length === 0,
        errors: validationErrors,
        warnings: [],
      };
    } catch (error) {
      this.logger.error('DTO验证失败', error);
      return {
        valid: false,
        errors: [{
          field: 'general',
          code: 'VALIDATION_ERROR',
          message: '验证过程中发生错误',
        }],
        warnings: [],
      };
    }
  }

  /**
   * 验证邮箱地址
   */
  validateEmail(email: string): ValidationResult {
    const errors: ValidationErrorDetail[] = [];
    const warnings: ValidationWarningDetail[] = [];

    if (!email) {
      errors.push({
        field: 'email',
        code: 'EMAIL_REQUIRED',
        message: '邮箱地址不能为空',
      });
      return { valid: false, errors, warnings };
    }

    // 基础格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      errors.push({
        field: 'email',
        code: 'EMAIL_INVALID_FORMAT',
        message: '邮箱地址格式无效',
        value: email,
      });
    }

    // 长度验证
    if (email.length > 254) {
      errors.push({
        field: 'email',
        code: 'EMAIL_TOO_LONG',
        message: '邮箱地址过长',
        value: email,
      });
    }

    // 本地部分验证
    const [localPart, domain] = email.split('@');
    if (localPart.length > 64) {
      errors.push({
        field: 'email',
        code: 'EMAIL_LOCAL_TOO_LONG',
        message: '邮箱本地部分过长',
        value: email,
      });
    }

    // 域名验证
    if (domain && domain.length > 253) {
      errors.push({
        field: 'email',
        code: 'EMAIL_DOMAIN_TOO_LONG',
        message: '邮箱域名过长',
        value: email,
      });
    }

    // 临时邮箱检查
    const tempEmailDomains = [
      '10minutemail.com', 'guerrillamail.com', 'mailinator.com',
      'tempmail.org', 'yopmail.com', 'throwaway.email'
    ];
    if (domain && tempEmailDomains.some(tempDomain => domain.includes(tempDomain))) {
      warnings.push({
        field: 'email',
        code: 'EMAIL_TEMPORARY',
        message: '检测到临时邮箱地址',
        value: email,
      });
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  /**
   * 验证手机号码
   */
  validatePhone(phone: string, countryCode?: string): ValidationResult {
    const errors: ValidationErrorDetail[] = [];
    const warnings: ValidationWarningDetail[] = [];

    if (!phone) {
      errors.push({
        field: 'phone',
        code: 'PHONE_REQUIRED',
        message: '手机号码不能为空',
      });
      return { valid: false, errors, warnings };
    }

    // 移除所有非数字字符
    const cleanPhone = phone.replace(/\D/g, '');

    // 基础长度验证
    if (cleanPhone.length < 10 || cleanPhone.length > 15) {
      errors.push({
        field: 'phone',
        code: 'PHONE_INVALID_LENGTH',
        message: '手机号码长度无效',
        value: phone,
      });
    }

    // 中国手机号验证
    if (!countryCode || countryCode === 'CN') {
      const chinaPhoneRegex = /^1[3-9]\d{9}$/;
      if (!chinaPhoneRegex.test(cleanPhone)) {
        errors.push({
          field: 'phone',
          code: 'PHONE_INVALID_FORMAT',
          message: '手机号码格式无效',
          value: phone,
        });
      }
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  /**
   * 验证用户名
   */
  validateUsername(username: string): ValidationResult {
    const errors: ValidationErrorDetail[] = [];
    const warnings: ValidationWarningDetail[] = [];

    if (!username) {
      errors.push({
        field: 'username',
        code: 'USERNAME_REQUIRED',
        message: '用户名不能为空',
      });
      return { valid: false, errors, warnings };
    }

    // 长度验证
    if (username.length < 5) {
      errors.push({
        field: 'username',
        code: 'USERNAME_TOO_SHORT',
        message: '用户名至少5个字符',
        value: username,
      });
    }

    if (username.length > 35) {
      errors.push({
        field: 'username',
        code: 'USERNAME_TOO_LONG',
        message: '用户名不能超过35个字符',
        value: username,
      });
    }

    // 格式验证
    const usernameRegex = /^[a-zA-Z0-9_-]+$/;
    if (!usernameRegex.test(username)) {
      errors.push({
        field: 'username',
        code: 'USERNAME_INVALID_FORMAT',
        message: '用户名只能包含字母、数字、下划线和连字符',
        value: username,
      });
    }

    // 开头结尾验证
    if (username.startsWith('_') || username.startsWith('-') || 
        username.endsWith('_') || username.endsWith('-')) {
      errors.push({
        field: 'username',
        code: 'USERNAME_INVALID_START_END',
        message: '用户名不能以下划线或连字符开头或结尾',
        value: username,
      });
    }

    // 保留词检查
    const reservedWords = [
      'admin', 'administrator', 'root', 'system', 'api', 'www',
      'mail', 'email', 'support', 'help', 'info', 'contact',
      'user', 'users', 'guest', 'anonymous', 'null', 'undefined'
    ];
    if (reservedWords.includes(username.toLowerCase())) {
      errors.push({
        field: 'username',
        code: 'USERNAME_RESERVED',
        message: '用户名为保留词，不能使用',
        value: username,
      });
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  /**
   * 验证密码
   */
  validatePassword(password: string, username?: string): PasswordValidationResult {
    const errors: string[] = [];
    const suggestions: string[] = [];
    let score = 0;

    if (!password) {
      return {
        valid: false,
        score: 0,
        level: 'weak',
        errors: ['密码不能为空'],
        suggestions: ['请输入密码'],
      };
    }

    // 长度检查
    const minLength = this.configService.get('PASSWORD_MIN_LENGTH', PASSWORD_POLICY.MIN_LENGTH);
    const maxLength = this.configService.get('PASSWORD_MAX_LENGTH', PASSWORD_POLICY.MAX_LENGTH);
    
    if (password.length < minLength) {
      errors.push(`密码长度至少${minLength}位`);
    } else {
      score += 1;
    }

    if (password.length > maxLength) {
      errors.push(`密码长度不能超过${maxLength}位`);
    }

    // 字符类型检查
    const requireUppercase = this.configService.get('PASSWORD_REQUIRE_UPPERCASE', PASSWORD_POLICY.REQUIRE_UPPERCASE);
    const requireLowercase = this.configService.get('PASSWORD_REQUIRE_LOWERCASE', PASSWORD_POLICY.REQUIRE_LOWERCASE);
    const requireNumbers = this.configService.get('PASSWORD_REQUIRE_NUMBERS', PASSWORD_POLICY.REQUIRE_NUMBERS);
    const requireSpecialChars = this.configService.get('PASSWORD_REQUIRE_SPECIAL_CHARS', PASSWORD_POLICY.REQUIRE_SPECIAL_CHARS);

    if (requireLowercase && !/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母');
    } else if (/[a-z]/.test(password)) {
      score += 1;
    }

    if (requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('密码必须包含大写字母');
    } else if (/[A-Z]/.test(password)) {
      score += 1;
    }

    if (requireNumbers && !/[0-9]/.test(password)) {
      errors.push('密码必须包含数字');
    } else if (/[0-9]/.test(password)) {
      score += 1;
    }

    if (requireSpecialChars && !/[^a-zA-Z0-9]/.test(password)) {
      errors.push('密码必须包含特殊字符');
    } else if (/[^a-zA-Z0-9]/.test(password)) {
      score += 1;
    }

    // 复杂度检查
    if (/(.)\1{2,}/.test(password)) {
      errors.push('密码不能包含连续重复字符');
    } else {
      score += 1;
    }

    if (/123|abc|qwe|password|admin/i.test(password)) {
      errors.push('密码不能包含常见字符序列');
    } else {
      score += 1;
    }

    // 用户名检查
    if (username && password.toLowerCase().includes(username.toLowerCase())) {
      errors.push('密码不能包含用户名');
    } else if (username) {
      score += 1;
    }

    // 计算强度等级
    let level: 'weak' | 'fair' | 'good' | 'strong';
    if (score <= 2) {
      level = 'weak';
      suggestions.push('密码强度较弱，建议增加复杂度');
    } else if (score <= 4) {
      level = 'fair';
      suggestions.push('密码强度一般，建议进一步增强');
    } else if (score <= 6) {
      level = 'good';
      suggestions.push('密码强度良好');
    } else {
      level = 'strong';
      suggestions.push('密码强度很强');
    }

    return {
      valid: errors.length === 0,
      score,
      level,
      errors,
      suggestions,
    };
  }

  /**
   * 验证URL
   */
  validateUrl(url: string, options?: { allowedProtocols?: string[] }): ValidationResult {
    const errors: ValidationErrorDetail[] = [];
    const warnings: ValidationWarningDetail[] = [];

    if (!url) {
      errors.push({
        field: 'url',
        code: 'URL_REQUIRED',
        message: 'URL不能为空',
      });
      return { valid: false, errors, warnings };
    }

    try {
      const urlObj = new URL(url);
      
      // 协议检查
      const allowedProtocols = options?.allowedProtocols || ['http:', 'https:'];
      if (!allowedProtocols.includes(urlObj.protocol)) {
        errors.push({
          field: 'url',
          code: 'URL_INVALID_PROTOCOL',
          message: `URL协议无效，允许的协议: ${allowedProtocols.join(', ')}`,
          value: url,
        });
      }

      // 长度检查
      if (url.length > 2048) {
        errors.push({
          field: 'url',
          code: 'URL_TOO_LONG',
          message: 'URL过长',
          value: url,
        });
      }

    } catch (error) {
      errors.push({
        field: 'url',
        code: 'URL_INVALID_FORMAT',
        message: 'URL格式无效',
        value: url,
      });
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  /**
   * 验证IP地址
   */
  validateIpAddress(ip: string): ValidationResult {
    const errors: ValidationErrorDetail[] = [];
    const warnings: ValidationWarningDetail[] = [];

    if (!ip) {
      errors.push({
        field: 'ip',
        code: 'IP_REQUIRED',
        message: 'IP地址不能为空',
      });
      return { valid: false, errors, warnings };
    }

    // IPv4验证
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    
    // IPv6验证
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

    if (!ipv4Regex.test(ip) && !ipv6Regex.test(ip)) {
      errors.push({
        field: 'ip',
        code: 'IP_INVALID_FORMAT',
        message: 'IP地址格式无效',
        value: ip,
      });
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  /**
   * 转换验证错误
   */
  private transformValidationErrors(errors: ValidationError[]): ValidationErrorDetail[] {
    const result: ValidationErrorDetail[] = [];

    for (const error of errors) {
      if (error.constraints) {
        for (const [code, message] of Object.entries(error.constraints)) {
          result.push({
            field: error.property,
            code: code.toUpperCase(),
            message,
            value: error.value,
            constraints: error.constraints,
          });
        }
      }

      // 递归处理嵌套错误
      if (error.children && error.children.length > 0) {
        const childErrors = this.transformValidationErrors(error.children);
        result.push(...childErrors.map(childError => ({
          ...childError,
          field: `${error.property}.${childError.field}`,
        })));
      }
    }

    return result;
  }
}
