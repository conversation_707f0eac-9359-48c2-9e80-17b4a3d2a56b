/**
 * 工具服务
 * 提供各种通用工具函数
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
// import * as geoip from 'geoip-lite'; // 临时禁用以解决内存问题

// 地理位置信息接口
export interface LocationInfo {
  country: string;
  region: string;
  city: string;
  latitude: number;
  longitude: number;
  timezone: string;
}

// 设备信息接口
export interface ParsedDeviceInfo {
  type: 'desktop' | 'mobile' | 'tablet' | 'bot' | 'unknown';
  os: string;
  browser: string;
  version: string;
  platform: string;
}

// 用户代理解析结果接口
export interface UserAgentInfo {
  browser: {
    name: string;
    version: string;
  };
  os: {
    name: string;
    version: string;
  };
  device: {
    type: string;
    vendor: string;
    model: string;
  };
  cpu: {
    architecture: string;
  };
}

@Injectable()
export class UtilsService {
  private readonly logger = new Logger(UtilsService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * 生成设备指纹
   */
  generateDeviceFingerprint(userAgent: string, ip: string, acceptLanguage?: string): string {
    const data = [
      userAgent,
      ip,
      acceptLanguage || '',
    ].join('|');
    
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  /**
   * 解析IP地址获取地理位置
   * 临时返回模拟数据以解决内存问题
   */
  getLocationFromIp(ip: string): LocationInfo | null {
    try {
      // 跳过本地IP
      if (this.isLocalIp(ip)) {
        return null;
      }

      // 临时返回模拟数据，避免使用 geoip-lite
      this.logger.debug(`IP地理位置查询: ${ip} (返回模拟数据)`);

      return {
        country: 'CN',
        region: 'Beijing',
        city: 'Beijing',
        latitude: 39.9042,
        longitude: 116.4074,
        timezone: 'Asia/Shanghai',
      };
    } catch (error) {
      this.logger.error('IP地理位置解析失败', error);
      return null;
    }
  }

  /**
   * 检查是否为本地IP
   */
  isLocalIp(ip: string): boolean {
    const localIpPatterns = [
      /^127\./,                    // 127.x.x.x
      /^10\./,                     // 10.x.x.x
      /^172\.(1[6-9]|2[0-9]|3[01])\./, // 172.16.x.x - 172.31.x.x
      /^192\.168\./,               // 192.168.x.x
      /^::1$/,                     // IPv6 localhost
      /^fc00:/,                    // IPv6 unique local
      /^fe80:/,                    // IPv6 link local
    ];

    return localIpPatterns.some(pattern => pattern.test(ip));
  }

  /**
   * 解析User-Agent
   */
  parseUserAgent(userAgent: string): ParsedDeviceInfo {
    const ua = userAgent.toLowerCase();
    
    // 检测设备类型
    let type: 'desktop' | 'mobile' | 'tablet' | 'bot' | 'unknown' = 'unknown';
    
    if (ua.includes('bot') || ua.includes('crawler') || ua.includes('spider')) {
      type = 'bot';
    } else if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      type = 'mobile';
    } else if (ua.includes('tablet') || ua.includes('ipad')) {
      type = 'tablet';
    } else if (ua.includes('windows') || ua.includes('mac') || ua.includes('linux')) {
      type = 'desktop';
    }

    // 检测操作系统
    let os = 'Unknown';
    if (ua.includes('windows nt 10')) os = 'Windows 10';
    else if (ua.includes('windows nt 6.3')) os = 'Windows 8.1';
    else if (ua.includes('windows nt 6.2')) os = 'Windows 8';
    else if (ua.includes('windows nt 6.1')) os = 'Windows 7';
    else if (ua.includes('windows')) os = 'Windows';
    else if (ua.includes('mac os x')) os = 'macOS';
    else if (ua.includes('linux')) os = 'Linux';
    else if (ua.includes('android')) os = 'Android';
    else if (ua.includes('ios') || ua.includes('iphone') || ua.includes('ipad')) os = 'iOS';

    // 检测浏览器
    let browser = 'Unknown';
    let version = '';
    
    if (ua.includes('chrome') && !ua.includes('edge')) {
      browser = 'Chrome';
      const match = ua.match(/chrome\/(\d+)/);
      version = match ? match[1] : '';
    } else if (ua.includes('firefox')) {
      browser = 'Firefox';
      const match = ua.match(/firefox\/(\d+)/);
      version = match ? match[1] : '';
    } else if (ua.includes('safari') && !ua.includes('chrome')) {
      browser = 'Safari';
      const match = ua.match(/version\/(\d+)/);
      version = match ? match[1] : '';
    } else if (ua.includes('edge')) {
      browser = 'Edge';
      const match = ua.match(/edge\/(\d+)/);
      version = match ? match[1] : '';
    } else if (ua.includes('opera')) {
      browser = 'Opera';
      const match = ua.match(/opera\/(\d+)/);
      version = match ? match[1] : '';
    }

    return {
      type,
      os,
      browser,
      version,
      platform: os,
    };
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 格式化持续时间
   */
  formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}天 ${hours % 24}小时`;
    } else if (hours > 0) {
      return `${hours}小时 ${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟 ${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 格式化相对时间
   */
  formatRelativeTime(date: Date): string {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const months = Math.floor(days / 30);
    const years = Math.floor(days / 365);

    if (years > 0) {
      return `${years}年前`;
    } else if (months > 0) {
      return `${months}个月前`;
    } else if (days > 0) {
      return `${days}天前`;
    } else if (hours > 0) {
      return `${hours}小时前`;
    } else if (minutes > 0) {
      return `${minutes}分钟前`;
    } else {
      return '刚刚';
    }
  }

  /**
   * 深度克隆对象
   */
  deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime()) as unknown as T;
    }

    if (obj instanceof Array) {
      return obj.map(item => this.deepClone(item)) as unknown as T;
    }

    if (typeof obj === 'object') {
      const cloned = {} as T;
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          cloned[key] = this.deepClone(obj[key]);
        }
      }
      return cloned;
    }

    return obj;
  }

  /**
   * 深度合并对象
   */
  deepMerge<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T {
    if (!sources.length) return target;
    const source = sources.shift();

    if (this.isObject(target) && this.isObject(source)) {
      for (const key in source) {
        if (this.isObject(source[key])) {
          if (!target[key]) Object.assign(target, { [key]: {} });
          this.deepMerge(target[key], source[key]);
        } else {
          Object.assign(target, { [key]: source[key] });
        }
      }
    }

    return this.deepMerge(target, ...sources);
  }

  /**
   * 检查是否为对象
   */
  private isObject(item: any): boolean {
    return item && typeof item === 'object' && !Array.isArray(item);
  }

  /**
   * 生成随机字符串
   */
  generateRandomString(length: number, charset?: string): string {
    const defaultCharset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const chars = charset || defaultCharset;
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }

  /**
   * 生成随机数字
   */
  generateRandomNumber(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * 延迟执行
   */
  delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 重试执行
   */
  async retry<T>(
    fn: () => Promise<T>,
    options: {
      maxAttempts?: number;
      delay?: number;
      backoff?: boolean;
    } = {}
  ): Promise<T> {
    const { maxAttempts = 3, delay = 1000, backoff = true } = options;
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxAttempts) {
          throw lastError;
        }

        const waitTime = backoff ? delay * Math.pow(2, attempt - 1) : delay;
        await this.delay(waitTime);
      }
    }

    throw lastError!;
  }

  /**
   * 节流函数
   */
  throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return function(this: any, ...args: Parameters<T>) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  /**
   * 防抖函数
   */
  debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout;
    
    return function(this: any, ...args: Parameters<T>) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  }

  /**
   * 数组分块
   */
  chunk<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * 数组去重
   */
  unique<T>(array: T[], key?: keyof T): T[] {
    if (!key) {
      return [...new Set(array)];
    }
    
    const seen = new Set();
    return array.filter(item => {
      const value = item[key];
      if (seen.has(value)) {
        return false;
      }
      seen.add(value);
      return true;
    });
  }

  /**
   * 对象转查询字符串
   */
  objectToQueryString(obj: Record<string, any>): string {
    const params = new URLSearchParams();
    
    for (const [key, value] of Object.entries(obj)) {
      if (value !== null && value !== undefined) {
        if (Array.isArray(value)) {
          value.forEach(item => params.append(key, String(item)));
        } else {
          params.append(key, String(value));
        }
      }
    }
    
    return params.toString();
  }

  /**
   * 查询字符串转对象
   */
  queryStringToObject(queryString: string): Record<string, any> {
    const params = new URLSearchParams(queryString);
    const obj: Record<string, any> = {};
    
    for (const [key, value] of params.entries()) {
      if (obj[key]) {
        if (Array.isArray(obj[key])) {
          obj[key].push(value);
        } else {
          obj[key] = [obj[key], value];
        }
      } else {
        obj[key] = value;
      }
    }
    
    return obj;
  }

  /**
   * 掩码敏感信息
   */
  maskSensitiveData(data: string, type: 'email' | 'phone' | 'id' | 'custom', customPattern?: RegExp): string {
    switch (type) {
      case 'email':
        const emailParts = data.split('@');
        if (emailParts.length === 2) {
          const [local, domain] = emailParts;
          const maskedLocal = local.length > 2 
            ? local[0] + '*'.repeat(local.length - 2) + local[local.length - 1]
            : local;
          return `${maskedLocal}@${domain}`;
        }
        return data;
        
      case 'phone':
        if (data.length >= 7) {
          return data.slice(0, 3) + '*'.repeat(data.length - 6) + data.slice(-3);
        }
        return data;
        
      case 'id':
        if (data.length >= 8) {
          return data.slice(0, 4) + '*'.repeat(data.length - 8) + data.slice(-4);
        }
        return data;
        
      case 'custom':
        if (customPattern) {
          return data.replace(customPattern, '*');
        }
        return data;
        
      default:
        return data;
    }
  }
}
