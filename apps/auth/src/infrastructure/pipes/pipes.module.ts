import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

// 导入现有的管道
import { ValidationPipe, CustomValidationPipe } from './validation.pipe';
import {
  ParseObjectIdPipe,
  ParseOptionalObjectIdPipe,
  ParseObjectIdArrayPipe,
  ParseQueryObjectIdPipe,
  ParseParamObjectIdPipe,
  ParseBodyObjectIdPipe
} from './parse-object-id.pipe';

/**
 * 管道模块
 * 
 * 专门管理所有管道组件，包括：
 * - 验证管道系列
 * - ObjectId 解析管道系列
 */
@Module({
  providers: [
    {
      provide: ValidationPipe,
      useFactory: (configService: ConfigService) => new ValidationPipe(configService),
      inject: [ConfigService],
    },
    {
      provide: CustomValidationPipe,
      useFactory: (configService: ConfigService) => new CustomValidationPipe(configService),
      inject: [ConfigService],
    },
    {
      provide: ParseObjectIdPipe,
      useFactory: () => new ParseObjectIdPipe(),
    },
    {
      provide: ParseOptionalObjectIdPipe,
      useFactory: () => new ParseOptionalObjectIdPipe(),
    },
    {
      provide: ParseObjectIdArrayPipe,
      useFactory: () => new ParseObjectIdArrayPipe(),
    },
    {
      provide: ParseQueryObjectIdPipe,
      useFactory: () => new ParseQueryObjectIdPipe(),
    },
    {
      provide: ParseParamObjectIdPipe,
      useFactory: () => new ParseParamObjectIdPipe(),
    },
    {
      provide: ParseBodyObjectIdPipe,
      useFactory: () => new ParseBodyObjectIdPipe(),
    },
  ],
  exports: [
    ValidationPipe,
    CustomValidationPipe,
    ParseObjectIdPipe,
    ParseOptionalObjectIdPipe,
    ParseObjectIdArrayPipe,
    ParseQueryObjectIdPipe,
    ParseParamObjectIdPipe,
    ParseBodyObjectIdPipe,
  ],
})
export class PipesModule {}
