import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RedisModule } from '@common/redis';

// 导入 CoreModule
import { CoreModule } from '../core/shared/core.module';

// 导入专用模块
import { GuardsModule } from './guards/guards.module';
import { InterceptorsModule } from './interceptors/interceptors.module';
import { PipesModule } from './pipes/pipes.module';
import { FiltersModule } from './filters/filters.module';

// 装饰器导出 - 避免命名冲突
export * from '../common/decorators/roles.decorator';
export * from '../common/decorators/permissions.decorator';
export * from '../common/decorators/current-user.decorator';
export * from '../common/decorators/api-response.decorator';

// 从 auth.decorator 中选择性导出，避免与现有装饰器冲突
export {
  Public,
  Auth,
  RequireRoles,
  RequireAdmin,
  RequireSuperAdmin,
  RequireMfa,
  RequireEmailVerified,
  RequirePhoneVerified,
  AllowInactive,
  RateLimit,
  SecureEndpoint,
  ApiKeyAuth,
  RequireGameRoles,
  RequireTeamOwner,
  RequireCoach,
  RequireManagement,
  DevOnly,
  TestOnly,
  MaintenanceAllowed,
  InternalApi,
  Experimental,
  Deprecated,
  RequireStringPermissions, // 新的字符串权限装饰器
} from '../common/decorators/auth.decorator';

// 常量导出
export * from '../common/constants/auth.constants';
export * from '../common/constants/error.constants';
export * from '../common/constants/role.constants';

// 接口导出
export * from '../common/interfaces/auth.interface';
export * from '../common/interfaces/user.interface';
export * from '../common/interfaces/response.interface';

// DTO导出
export * from '../common/dto/pagination.dto';
export * from '../common/dto/response.dto';

@Global()
@Module({
  imports: [
    ConfigModule,
    // Redis 模块 - 使用异步工厂确保连接就绪
    RedisModule.forRootAsync(),
    CoreModule,
    GuardsModule,
    InterceptorsModule,
    PipesModule,
    FiltersModule,
  ],
  providers: [
    // 注意：所有组件现在由专用模块提供，无需重复注册
  ],
  exports: [
    // 重新导出所有专用模块
    CoreModule,
    GuardsModule,
    InterceptorsModule,
    PipesModule,
    FiltersModule,
  ],
})
export class SharedModule {}

// 导出所有服务和组件供外部使用
export { CryptoService } from './services/crypto.service';
export { ValidationService } from './services/validation.service';
export { UtilsService } from './services/utils.service';

export { JwtAuthGuard } from './guards/jwt-auth.guard';
export { RolesGuard } from './guards/roles.guard';
export { PermissionsGuard } from './guards/permissions.guard';
export { ThrottlerBehindProxyGuard } from './guards/throttler-behind-proxy.guard';

export {
  ResponseInterceptor,
  PaginationResponseInterceptor,
  FileResponseInterceptor,
  CacheResponseInterceptor
} from './interceptors/response.interceptor';
export { LoggingInterceptor, SecurityLoggingInterceptor } from './interceptors/logging.interceptor';
export { TimeoutInterceptor, DynamicTimeoutInterceptor } from './interceptors/timeout.interceptor';

export { ValidationPipe, CustomValidationPipe } from './pipes/validation.pipe';
export {
  ParseObjectIdPipe,
  ParseOptionalObjectIdPipe,
  ParseObjectIdArrayPipe,
  ParseQueryObjectIdPipe,
  ParseParamObjectIdPipe,
  ParseBodyObjectIdPipe
} from './pipes/parse-object-id.pipe';

export { AllExceptionsFilter } from './filters/all-exceptions.filter';
export { HttpExceptionFilter } from './filters/http-exception.filter';
export { ValidationExceptionFilter, MongoValidationExceptionFilter } from './filters/validation-exception.filter';
