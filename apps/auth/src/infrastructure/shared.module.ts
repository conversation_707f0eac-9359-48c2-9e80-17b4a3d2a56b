import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RedisModule } from '@common/redis';

// 导入 CoreModule
import { CoreModule } from '../core/shared/core.module';

// 导入专用模块
import { GuardsModule } from './guards/guards.module';
import { InterceptorsModule } from './interceptors/interceptors.module';
import { PipesModule } from './pipes/pipes.module';
import { FiltersModule } from './filters/filters.module';

// 装饰器导出 - 避免命名冲突
export * from '../shared/decorators/roles.decorator';
export * from '../shared/decorators/permissions.decorator';
export * from '../shared/decorators/current-user.decorator';
export * from '../shared/decorators/api-response.decorator';

// 从 auth.decorator 中选择性导出，避免与现有装饰器冲突
export {
  Public,
  Auth,
  RequireRoles,
  RequireAdmin,
  RequireSuperAdmin,
  RequireMfa,
  RequireEmailVerified,
  RequirePhoneVerified,
  AllowInactive,
  RateLimit,
  SecureEndpoint,
  ApiKeyAuth,
  RequireGameRoles,
  RequireTeamOwner,
  RequireCoach,
  RequireManagement,
  DevOnly,
  TestOnly,
  MaintenanceAllowed,
  InternalApi,
  Experimental,
  Deprecated,
  RequireStringPermissions, // 新的字符串权限装饰器
} from '../shared/decorators/auth.decorator';

// 常量导出
export * from '../shared/constants/auth.constants';
export * from '../shared/constants/error.constants';
export * from '../shared/constants/role.constants';

// 接口导出
export * from '../shared/interfaces/auth.interface';
export * from '../shared/interfaces/user.interface';
export * from '../shared/interfaces/response.interface';

// DTO导出
export * from '../shared/dto/pagination.dto';
export * from '../shared/dto/response.dto';

@Global()
@Module({
  imports: [
    ConfigModule,
    // Redis 模块 - 使用异步工厂确保连接就绪
    RedisModule.forRootAsync(),
    CoreModule,
    GuardsModule,
    InterceptorsModule,
    PipesModule,
    FiltersModule,
  ],
  providers: [
    // 注意：所有组件现在由专用模块提供，无需重复注册
  ],
  exports: [
    // 重新导出所有专用模块
    CoreModule,
    GuardsModule,
    InterceptorsModule,
    PipesModule,
    FiltersModule,
  ],
})
export class SharedModule {}

// 导出所有服务和组件供外部使用
export { CryptoService } from '../modules/security/services/crypto.service';
export { ValidationService } from '../modules/security/services/validation.service';
export { UtilsService } from '../modules/security/services/utils.service';

export { JwtAuthGuard } from '../modules/security/guards/jwt-auth.guard';
export { RolesGuard } from '../modules/rbac/guards/roles.guard';
export { PermissionsGuard } from '../modules/rbac/guards/permissions.guard';
export { ThrottlerBehindProxyGuard } from '../modules/security/guards/throttler-behind-proxy.guard';

export {
  ResponseInterceptor,
  PaginationResponseInterceptor,
  FileResponseInterceptor,
  CacheResponseInterceptor
} from '../modules/security/interceptors/response.interceptor';
export { LoggingInterceptor, SecurityLoggingInterceptor } from '../modules/security/interceptors/logging.interceptor';
export { TimeoutInterceptor, DynamicTimeoutInterceptor } from '../modules/security/interceptors/timeout.interceptor';

export { ValidationPipe, CustomValidationPipe } from '../modules/security/pipes/validation.pipe';
export {
  ParseObjectIdPipe,
  ParseOptionalObjectIdPipe,
  ParseObjectIdArrayPipe,
  ParseQueryObjectIdPipe,
  ParseParamObjectIdPipe,
  ParseBodyObjectIdPipe
} from '../modules/security/pipes/parse-object-id.pipe';

export { AllExceptionsFilter } from '../modules/security/filters/all-exceptions.filter';
export { HttpExceptionFilter } from '../modules/security/filters/http-exception.filter';
export { ValidationExceptionFilter, MongoValidationExceptionFilter } from '../modules/security/filters/validation-exception.filter';
