import { Module } from '@nestjs/common';

// 导入现有的过滤器
import { AllExceptionsFilter } from './all-exceptions.filter';
import { HttpExceptionFilter } from './http-exception.filter';
import { ValidationExceptionFilter, MongoValidationExceptionFilter } from './validation-exception.filter';

/**
 * 过滤器模块
 * 
 * 专门管理所有异常过滤器组件，包括：
 * - 全局异常过滤器
 * - HTTP 异常过滤器
 * - 验证异常过滤器系列
 */
@Module({
  providers: [
    AllExceptionsFilter,
    HttpExceptionFilter,
    ValidationExceptionFilter,
    MongoValidationExceptionFilter,
  ],
  exports: [
    AllExceptionsFilter,
    HttpExceptionFilter,
    ValidationExceptionFilter,
    MongoValidationExceptionFilter,
  ],
})
export class FiltersModule {}
