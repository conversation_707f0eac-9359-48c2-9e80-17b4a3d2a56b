/**
 * 验证异常过滤器
 * 专门处理数据验证相关的异常
 */

import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { ValidationError } from 'class-validator';
import { ApiResponseDto } from '../../common/dto/response.dto';
import { ErrorResponse } from '../../common/interfaces/response.interface';
import { ERROR_CODES, ERROR_MESSAGES } from '../../common/constants/error.constants';

// 验证错误详情接口
export interface ValidationErrorDetail {
  field: string;
  value: any;
  constraints: Record<string, string>;
  children?: ValidationErrorDetail[];
}

// 验证异常详情接口
export interface ValidationExceptionDetails {
  errors: ValidationErrorDetail[];
  timestamp: string;
  path: string;
  method: string;
  requestId: string;
  ip: string;
  userAgent?: string;
  userId?: string;
}

@Catch(BadRequestException)
export class ValidationExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(ValidationExceptionFilter.name);

  constructor(private readonly configService: ConfigService) {}

  catch(exception: BadRequestException, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // 检查是否为验证异常
    if (!this.isValidationException(exception)) {
      // 如果不是验证异常，让其他过滤器处理
      throw exception;
    }

    const details = this.buildExceptionDetails(exception, request);
    const errorResponse = this.buildErrorResponse(exception, details);

    // 记录验证异常日志
    this.logValidationException(exception, details);

    // 设置响应头
    this.setResponseHeaders(response, details);

    // 发送错误响应
    response.status(400).json(errorResponse);
  }

  /**
   * 检查是否为验证异常
   */
  private isValidationException(exception: BadRequestException): boolean {
    const response = exception.getResponse();
    
    if (typeof response === 'object' && response !== null) {
      const responseObj = response as any;
      
      // 检查是否包含验证错误标识
      return (
        responseObj.message === '数据验证失败' ||
        Array.isArray(responseObj.message) ||
        Array.isArray(responseObj.errors) ||
        responseObj.code === ERROR_CODES.VALIDATION_FAILED
      );
    }

    return false;
  }

  /**
   * 构建异常详情
   */
  private buildExceptionDetails(
    exception: BadRequestException,
    request: Request,
  ): ValidationExceptionDetails {
    const requestId = this.getRequestId(request);
    const timestamp = new Date().toISOString();
    const errors = this.extractValidationErrors(exception);

    return {
      errors,
      timestamp,
      path: request.path,
      method: request.method,
      requestId,
      ip: this.getClientIp(request),
      userAgent: request.get('User-Agent'),
      userId: (request as any).user?.id,
    };
  }

  /**
   * 提取验证错误
   */
  private extractValidationErrors(exception: BadRequestException): ValidationErrorDetail[] {
    const response = exception.getResponse();
    
    if (typeof response === 'object' && response !== null) {
      const responseObj = response as any;
      
      // 处理class-validator错误
      if (Array.isArray(responseObj.message)) {
        return this.parseClassValidatorErrors(responseObj.message);
      }
      
      // 处理自定义验证错误
      if (Array.isArray(responseObj.errors)) {
        return responseObj.errors;
      }
      
      // 处理单个错误
      if (responseObj.message && typeof responseObj.message === 'string') {
        return [{
          field: 'general',
          value: null,
          constraints: { validation: responseObj.message },
        }];
      }
    }

    // 默认错误
    return [{
      field: 'general',
      value: null,
      constraints: { validation: exception.message },
    }];
  }

  /**
   * 解析class-validator错误
   */
  private parseClassValidatorErrors(messages: string[]): ValidationErrorDetail[] {
    return messages.map(message => {
      // 尝试解析字段和约束
      const fieldMatch = message.match(/^(\w+) (.+)$/);
      
      if (fieldMatch) {
        const field = fieldMatch[1];
        const constraint = fieldMatch[2];
        
        return {
          field,
          value: null,
          constraints: { validation: constraint },
        };
      }
      
      // 如果无法解析，返回通用错误
      return {
        field: 'unknown',
        value: null,
        constraints: { validation: message },
      };
    });
  }

  /**
   * 构建错误响应
   */
  private buildErrorResponse(
    exception: BadRequestException,
    details: ValidationExceptionDetails,
  ): ErrorResponse {
    const errorResponse: ErrorResponse = {
      success: false,
      error: {
        code: ERROR_CODES.VALIDATION_FAILED,
        message: ERROR_MESSAGES[ERROR_CODES.VALIDATION_FAILED],
        details: {
          errors: details.errors,
          totalErrors: details.errors.length,
          fields: this.getFailedFields(details.errors),
        },
        timestamp: details.timestamp,
        path: details.path,
      },
      timestamp: details.timestamp,
      requestId: details.requestId,
      version: this.configService.get('API_VERSION', 'v1'),
    };

    return errorResponse;
  }

  /**
   * 获取失败的字段列表
   */
  private getFailedFields(errors: ValidationErrorDetail[]): string[] {
    const fields = new Set<string>();
    
    const extractFields = (errorList: ValidationErrorDetail[]) => {
      errorList.forEach(error => {
        fields.add(error.field);
        if (error.children && error.children.length > 0) {
          extractFields(error.children);
        }
      });
    };
    
    extractFields(errors);
    return Array.from(fields);
  }

  /**
   * 记录验证异常日志
   */
  private logValidationException(
    exception: BadRequestException,
    details: ValidationExceptionDetails,
  ): void {
    const logData = {
      type: 'VALIDATION_ERROR',
      errors: details.errors,
      totalErrors: details.errors.length,
      failedFields: this.getFailedFields(details.errors),
      request: {
        method: details.method,
        path: details.path,
        ip: details.ip,
        userAgent: details.userAgent,
        userId: details.userId,
      },
      requestId: details.requestId,
      timestamp: details.timestamp,
    };

    this.logger.warn('数据验证失败', logData);
  }

  /**
   * 设置响应头
   */
  private setResponseHeaders(response: Response, details: ValidationExceptionDetails): void {
    response.setHeader('X-Request-ID', details.requestId);
    response.setHeader('X-Timestamp', details.timestamp);
    response.setHeader('X-Error-Type', 'ValidationError');
    response.setHeader('X-Validation-Errors', details.errors.length);
    
    // 安全头
    response.setHeader('X-Content-Type-Options', 'nosniff');
    response.setHeader('X-Frame-Options', 'DENY');
    response.setHeader('X-XSS-Protection', '1; mode=block');
  }

  /**
   * 获取请求ID
   */
  private getRequestId(request: Request): string {
    return (request.headers['x-request-id'] as string) || 
           `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取客户端IP
   */
  private getClientIp(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      (request.headers['x-real-ip'] as string) ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }
}

/**
 * MongoDB验证异常过滤器
 * 专门处理MongoDB/Mongoose验证错误
 */
@Catch()
export class MongoValidationExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(MongoValidationExceptionFilter.name);

  constructor(private readonly configService: ConfigService) {}

  catch(exception: any, host: ArgumentsHost): void {
    // 只处理MongoDB验证错误
    if (!this.isMongoValidationError(exception)) {
      throw exception;
    }

    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const details = this.buildMongoExceptionDetails(exception, request);
    const errorResponse = this.buildMongoErrorResponse(exception, details);

    // 记录MongoDB验证异常日志
    this.logMongoValidationException(exception, details);

    // 设置响应头
    this.setResponseHeaders(response, details);

    // 发送错误响应
    response.status(400).json(errorResponse);
  }

  /**
   * 检查是否为MongoDB验证错误
   */
  private isMongoValidationError(exception: any): boolean {
    return (
      exception &&
      typeof exception === 'object' &&
      (exception.name === 'ValidationError' ||
       exception.name === 'ValidatorError' ||
       exception.name === 'CastError' ||
       (exception.errors && typeof exception.errors === 'object'))
    );
  }

  /**
   * 构建MongoDB异常详情
   */
  private buildMongoExceptionDetails(exception: any, request: Request): ValidationExceptionDetails {
    const requestId = this.getRequestId(request);
    const timestamp = new Date().toISOString();
    const errors = this.extractMongoValidationErrors(exception);

    return {
      errors,
      timestamp,
      path: request.path,
      method: request.method,
      requestId,
      ip: this.getClientIp(request),
      userAgent: request.get('User-Agent'),
      userId: (request as any).user?.id,
    };
  }

  /**
   * 提取MongoDB验证错误
   */
  private extractMongoValidationErrors(exception: any): ValidationErrorDetail[] {
    const errors: ValidationErrorDetail[] = [];

    if (exception.name === 'CastError') {
      errors.push({
        field: exception.path,
        value: exception.value,
        constraints: {
          type: `期望类型 ${exception.kind}，但收到 ${typeof exception.value}`,
        },
      });
    } else if (exception.errors) {
      Object.values(exception.errors).forEach((error: any) => {
        errors.push({
          field: error.path,
          value: error.value,
          constraints: {
            [error.kind]: error.message,
          },
        });
      });
    } else {
      errors.push({
        field: 'general',
        value: null,
        constraints: {
          mongo: exception.message || '数据库验证失败',
        },
      });
    }

    return errors;
  }

  /**
   * 构建MongoDB错误响应
   */
  private buildMongoErrorResponse(exception: any, details: ValidationExceptionDetails): ErrorResponse {
    return {
      success: false,
      error: {
        code: ERROR_CODES.VALIDATION_FAILED,
        message: '数据验证失败',
        details: {
          type: 'MongoValidationError',
          errors: details.errors,
          totalErrors: details.errors.length,
          fields: details.errors.map(e => e.field),
        },
        timestamp: details.timestamp,
        path: details.path,
      },
      timestamp: details.timestamp,
      requestId: details.requestId,
      version: this.configService.get('API_VERSION', 'v1'),
    };
  }

  /**
   * 记录MongoDB验证异常日志
   */
  private logMongoValidationException(exception: any, details: ValidationExceptionDetails): void {
    const logData = {
      type: 'MONGO_VALIDATION_ERROR',
      mongoError: {
        name: exception.name,
        message: exception.message,
        code: exception.code,
      },
      errors: details.errors,
      request: {
        method: details.method,
        path: details.path,
        ip: details.ip,
        userId: details.userId,
      },
      requestId: details.requestId,
      timestamp: details.timestamp,
    };

    this.logger.warn('MongoDB数据验证失败', logData);
  }

  /**
   * 设置响应头
   */
  private setResponseHeaders(response: Response, details: ValidationExceptionDetails): void {
    response.setHeader('X-Request-ID', details.requestId);
    response.setHeader('X-Timestamp', details.timestamp);
    response.setHeader('X-Error-Type', 'MongoValidationError');
    response.setHeader('X-Validation-Errors', details.errors.length);
  }

  /**
   * 获取请求ID
   */
  private getRequestId(request: Request): string {
    return (request.headers['x-request-id'] as string) || 
           `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取客户端IP
   */
  private getClientIp(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      (request.headers['x-real-ip'] as string) ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }
}
