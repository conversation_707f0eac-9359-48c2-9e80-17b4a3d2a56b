/**
 * HTTP异常过滤器
 * 专门处理HTTP异常的格式化和响应
 */

import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { ApiResponseDto } from '../../common/dto/response.dto';
import { ERROR_CODES, ERROR_MESSAGES } from '../../common/constants/error.constants';

// HTTP异常详情接口
export interface HttpExceptionDetails {
  statusCode: number;
  error: string;
  message: string | string[];
  timestamp: string;
  path: string;
  method: string;
  requestId: string;
  ip: string;
  userAgent?: string;
  userId?: string;
}

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  constructor(private readonly configService: ConfigService) {}

  catch(exception: HttpException, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse();
    
    const details = this.buildExceptionDetails(exception, request, status);
    const errorResponse = this.buildErrorResponse(exceptionResponse, details);

    // 记录异常日志
    this.logHttpException(exception, details);

    // 设置响应头
    this.setResponseHeaders(response, details);

    // 发送错误响应
    response.status(status).json(errorResponse);
  }

  /**
   * 构建异常详情
   */
  private buildExceptionDetails(
    exception: HttpException,
    request: Request,
    status: number,
  ): HttpExceptionDetails {
    const requestId = this.getRequestId(request);
    const timestamp = new Date().toISOString();

    return {
      statusCode: status,
      error: this.getErrorName(status),
      message: exception.message,
      timestamp,
      path: request.path,
      method: request.method,
      requestId,
      ip: this.getClientIp(request),
      userAgent: request.get('User-Agent'),
      userId: (request as any).user?.id,
    };
  }

  /**
   * 构建错误响应
   */
  private buildErrorResponse(
    exceptionResponse: string | object,
    details: HttpExceptionDetails,
  ): ApiResponseDto<null> {
    let errorCode = this.getErrorCodeByStatus(details.statusCode);
    let message = details.message;
    let errorDetails: any = undefined;

    // 处理不同类型的异常响应
    if (typeof exceptionResponse === 'string') {
      message = exceptionResponse;
    } else if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
      const response = exceptionResponse as any;
      
      // 提取错误信息
      message = response.message || message;
      errorCode = response.code || errorCode;
      errorDetails = response.details || response.errors;

      // 处理验证错误
      if (response.statusCode === HttpStatus.BAD_REQUEST && Array.isArray(response.message)) {
        errorDetails = this.formatValidationErrors(response.message);
        message = '数据验证失败';
        errorCode = ERROR_CODES.VALIDATION_FAILED;
      }

      // 处理认证错误
      if (response.statusCode === HttpStatus.UNAUTHORIZED) {
        errorCode = this.getAuthErrorCode(response);
        message = this.getAuthErrorMessage(errorCode);
      }

      // 处理权限错误
      if (response.statusCode === HttpStatus.FORBIDDEN) {
        errorCode = ERROR_CODES.AUTH_INSUFFICIENT_PERMISSIONS;
        message = ERROR_MESSAGES[ERROR_CODES.AUTH_INSUFFICIENT_PERMISSIONS];
      }
    }

    const errorResponse: ApiResponseDto<null> = {
      success: false,
      data: null,
      message: this.sanitizeMessage(message),
      timestamp: details.timestamp,
      errorCode,
      errors: errorDetails,
    };

    return errorResponse;
  }

  /**
   * 记录HTTP异常日志
   */
  private logHttpException(exception: HttpException, details: HttpExceptionDetails): void {
    const logData = {
      statusCode: details.statusCode,
      error: details.error,
      message: details.message,
      path: details.path,
      method: details.method,
      ip: details.ip,
      userAgent: details.userAgent,
      userId: details.userId,
      requestId: details.requestId,
      timestamp: details.timestamp,
      stack: exception.stack,
    };

    // 根据状态码选择日志级别
    if (details.statusCode >= 500) {
      this.logger.error('HTTP服务器错误', logData);
    } else if (details.statusCode >= 400) {
      this.logger.warn('HTTP客户端错误', logData);
    } else {
      this.logger.log('HTTP异常', logData);
    }
  }

  /**
   * 设置响应头
   */
  private setResponseHeaders(response: Response, details: HttpExceptionDetails): void {
    response.setHeader('X-Request-ID', details.requestId);
    response.setHeader('X-Timestamp', details.timestamp);
    response.setHeader('X-Error-Code', details.statusCode);
    response.setHeader('X-Error-Type', details.error);
    
    // 安全头
    response.setHeader('X-Content-Type-Options', 'nosniff');
    response.setHeader('X-Frame-Options', 'DENY');
    response.setHeader('X-XSS-Protection', '1; mode=block');

    // CORS头（如果需要）
    if (this.configService.get('CORS_ENABLED', false)) {
      response.setHeader('Access-Control-Allow-Origin', '*');
      response.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE');
      response.setHeader('Access-Control-Allow-Headers', 'Content-Type, Accept, Authorization');
    }
  }

  /**
   * 获取请求ID
   */
  private getRequestId(request: Request): string {
    return (request.headers['x-request-id'] as string) || 
           `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取客户端IP
   */
  private getClientIp(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      (request.headers['x-real-ip'] as string) ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }

  /**
   * 根据状态码获取错误名称
   */
  private getErrorName(status: number): string {
    const statusNames: Record<number, string> = {
      400: 'Bad Request',
      401: 'Unauthorized',
      403: 'Forbidden',
      404: 'Not Found',
      405: 'Method Not Allowed',
      406: 'Not Acceptable',
      408: 'Request Timeout',
      409: 'Conflict',
      410: 'Gone',
      411: 'Length Required',
      412: 'Precondition Failed',
      413: 'Payload Too Large',
      414: 'URI Too Long',
      415: 'Unsupported Media Type',
      416: 'Range Not Satisfiable',
      417: 'Expectation Failed',
      418: "I'm a teapot",
      421: 'Misdirected Request',
      422: 'Unprocessable Entity',
      423: 'Locked',
      424: 'Failed Dependency',
      425: 'Too Early',
      426: 'Upgrade Required',
      428: 'Precondition Required',
      429: 'Too Many Requests',
      431: 'Request Header Fields Too Large',
      451: 'Unavailable For Legal Reasons',
      500: 'Internal Server Error',
      501: 'Not Implemented',
      502: 'Bad Gateway',
      503: 'Service Unavailable',
      504: 'Gateway Timeout',
      505: 'HTTP Version Not Supported',
      506: 'Variant Also Negotiates',
      507: 'Insufficient Storage',
      508: 'Loop Detected',
      510: 'Not Extended',
      511: 'Network Authentication Required',
    };

    return statusNames[status] || 'Unknown Error';
  }

  /**
   * 根据状态码获取错误代码
   */
  private getErrorCodeByStatus(status: number): string {
    const statusToCode: Record<number, string> = {
      400: ERROR_CODES.VALIDATION_FAILED,
      401: ERROR_CODES.AUTH_TOKEN_INVALID,
      403: ERROR_CODES.AUTH_INSUFFICIENT_PERMISSIONS,
      404: ERROR_CODES.USER_NOT_FOUND,
      405: ERROR_CODES.VALIDATION_FAILED,
      408: ERROR_CODES.SYSTEM_TIMEOUT,
      409: ERROR_CODES.USER_ALREADY_EXISTS,
      422: ERROR_CODES.VALIDATION_FAILED,
      429: ERROR_CODES.AUTH_RATE_LIMITED,
      500: ERROR_CODES.SYSTEM_INTERNAL_ERROR,
      502: ERROR_CODES.SYSTEM_NETWORK_ERROR,
      503: ERROR_CODES.SYSTEM_MAINTENANCE,
      504: ERROR_CODES.SYSTEM_TIMEOUT,
    };

    return statusToCode[status] || ERROR_CODES.SYSTEM_INTERNAL_ERROR;
  }

  /**
   * 获取认证错误代码
   */
  private getAuthErrorCode(response: any): string {
    const message = response.message?.toLowerCase() || '';
    
    if (message.includes('token') && message.includes('expired')) {
      return ERROR_CODES.AUTH_TOKEN_EXPIRED;
    }
    
    if (message.includes('token') && message.includes('invalid')) {
      return ERROR_CODES.AUTH_TOKEN_INVALID;
    }
    
    if (message.includes('token') && message.includes('missing')) {
      return ERROR_CODES.AUTH_TOKEN_MISSING;
    }
    
    if (message.includes('credentials')) {
      return ERROR_CODES.AUTH_INVALID_CREDENTIALS;
    }
    
    if (message.includes('mfa')) {
      return ERROR_CODES.AUTH_MFA_REQUIRED;
    }
    
    if (message.includes('locked')) {
      return ERROR_CODES.AUTH_ACCOUNT_LOCKED;
    }
    
    if (message.includes('suspended')) {
      return ERROR_CODES.AUTH_ACCOUNT_SUSPENDED;
    }

    return ERROR_CODES.AUTH_TOKEN_INVALID;
  }

  /**
   * 获取认证错误消息
   */
  private getAuthErrorMessage(errorCode: string): string {
    return ERROR_MESSAGES[errorCode] || ERROR_MESSAGES[ERROR_CODES.AUTH_TOKEN_INVALID];
  }

  /**
   * 格式化验证错误
   */
  private formatValidationErrors(messages: string[]): any[] {
    return messages.map(message => {
      // 尝试解析class-validator的错误格式
      const match = message.match(/^(\w+) (.+)$/);
      if (match) {
        return {
          field: match[1],
          message: match[2],
          code: 'VALIDATION_ERROR',
        };
      }
      
      return {
        field: 'unknown',
        message,
        code: 'VALIDATION_ERROR',
      };
    });
  }

  /**
   * 清理消息内容
   */
  private sanitizeMessage(message: string | string[]): string {
    let sanitized: string;
    
    if (Array.isArray(message)) {
      sanitized = message.join('; ');
    } else {
      sanitized = message || '未知错误';
    }

    // 移除敏感信息
    const sensitivePatterns = [
      /password/gi,
      /token/gi,
      /secret/gi,
      /key/gi,
      /authorization/gi,
    ];

    for (const pattern of sensitivePatterns) {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    }

    // 限制消息长度
    if (sanitized.length > 300) {
      sanitized = sanitized.substring(0, 300) + '...';
    }

    return sanitized;
  }
}
