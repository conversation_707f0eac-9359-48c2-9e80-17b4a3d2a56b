import { Module } from '@nestjs/common';

// 导入现有的拦截器
import {
  ResponseInterceptor,
  PaginationResponseInterceptor,
  FileResponseInterceptor,
  CacheResponseInterceptor
} from '../../modules/security/interceptors/response.interceptor';
import { LoggingInterceptor, SecurityLoggingInterceptor } from '../../modules/security/interceptors/logging.interceptor';
import { TimeoutInterceptor, DynamicTimeoutInterceptor } from '../../modules/security/interceptors/timeout.interceptor';

/**
 * 拦截器模块
 * 
 * 专门管理所有拦截器组件，包括：
 * - 响应拦截器系列
 * - 日志拦截器系列
 * - 超时拦截器系列
 */
@Module({
  providers: [
    ResponseInterceptor,
    PaginationResponseInterceptor,
    FileResponseInterceptor,
    CacheResponseInterceptor,
    LoggingInterceptor,
    SecurityLoggingInterceptor,
    TimeoutInterceptor,
    DynamicTimeoutInterceptor,
  ],
  exports: [
    ResponseInterceptor,
    PaginationResponseInterceptor,
    FileResponseInterceptor,
    CacheResponseInterceptor,
    LoggingInterceptor,
    SecurityLoggingInterceptor,
    TimeoutInterceptor,
    DynamicTimeoutInterceptor,
  ],
})
export class InterceptorsModule {}
