import { Module } from '@nestjs/common';

// 导入现有的拦截器
import {
  ResponseInterceptor,
  PaginationResponseInterceptor,
  FileResponseInterceptor,
  CacheResponseInterceptor
} from './response.interceptor';
import { LoggingInterceptor, SecurityLoggingInterceptor } from './logging.interceptor';
import { TimeoutInterceptor, DynamicTimeoutInterceptor } from './timeout.interceptor';

/**
 * 拦截器模块
 * 
 * 专门管理所有拦截器组件，包括：
 * - 响应拦截器系列
 * - 日志拦截器系列
 * - 超时拦截器系列
 */
@Module({
  providers: [
    ResponseInterceptor,
    PaginationResponseInterceptor,
    FileResponseInterceptor,
    CacheResponseInterceptor,
    LoggingInterceptor,
    SecurityLoggingInterceptor,
    TimeoutInterceptor,
    DynamicTimeoutInterceptor,
  ],
  exports: [
    ResponseInterceptor,
    PaginationResponseInterceptor,
    FileResponseInterceptor,
    CacheResponseInterceptor,
    LoggingInterceptor,
    SecurityLoggingInterceptor,
    TimeoutInterceptor,
    DynamicTimeoutInterceptor,
  ],
})
export class InterceptorsModule {}
