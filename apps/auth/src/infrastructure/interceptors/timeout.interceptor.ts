/**
 * 超时拦截器
 * 为请求设置超时时间，防止长时间阻塞
 */

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  RequestTimeoutException,
  Logger,
} from '@nestjs/common';
import { Observable, throwError, TimeoutError } from 'rxjs';
import { timeout, catchError } from 'rxjs/operators';
import { Request } from 'express';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';

// 超时配置接口
export interface TimeoutConfig {
  default: number;              // 默认超时时间（毫秒）
  upload: number;               // 文件上传超时时间
  download: number;             // 文件下载超时时间
  auth: number;                 // 认证相关超时时间
  database: number;             // 数据库操作超时时间
  external: number;             // 外部API调用超时时间
}

// 超时装饰器元数据键
export const TIMEOUT_KEY = 'timeout';

/**
 * 超时装饰器
 * 用于为特定的控制器方法设置自定义超时时间
 */
export const Timeout = (milliseconds: number) => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    if (descriptor) {
      // 方法装饰器
      Reflect.defineMetadata(TIMEOUT_KEY, milliseconds, descriptor.value);
    } else {
      // 类装饰器
      Reflect.defineMetadata(TIMEOUT_KEY, milliseconds, target);
    }
  };
};

@Injectable()
export class TimeoutInterceptor implements NestInterceptor {
  private readonly logger = new Logger(TimeoutInterceptor.name);
  private readonly config: TimeoutConfig;

  constructor(
    private readonly configService: ConfigService,
    private readonly reflector: Reflector,
  ) {
    this.config = {
      default: this.configService.get('TIMEOUT_DEFAULT', 30000),
      upload: this.configService.get('TIMEOUT_UPLOAD', 300000),
      download: this.configService.get('TIMEOUT_DOWNLOAD', 300000),
      auth: this.configService.get('TIMEOUT_AUTH', 10000),
      database: this.configService.get('TIMEOUT_DATABASE', 15000),
      external: this.configService.get('TIMEOUT_EXTERNAL', 20000),
    };
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const handler = context.getHandler();
    const controller = context.getClass();

    // 获取超时时间
    const timeoutMs = this.getTimeoutForRequest(request, handler, controller);

    // 如果超时时间为0或负数，则不设置超时
    if (timeoutMs <= 0) {
      return next.handle();
    }

    const startTime = Date.now();
    const requestId = this.getRequestId(request);

    this.logger.debug(`设置请求超时: ${timeoutMs}ms`, {
      requestId,
      method: request.method,
      path: request.path,
      timeout: timeoutMs,
    });

    return next.handle().pipe(
      timeout(timeoutMs),
      catchError((error) => {
        const duration = Date.now() - startTime;
        
        if (error instanceof TimeoutError) {
          this.logTimeout(request, requestId, timeoutMs, duration);
          return throwError(() => new RequestTimeoutException({
            message: `请求超时 (${timeoutMs}ms)`,
            timeout: timeoutMs,
            duration,
            path: request.path,
            method: request.method,
            requestId,
          }));
        }

        return throwError(() => error);
      }),
    );
  }

  /**
   * 获取请求的超时时间
   */
  private getTimeoutForRequest(
    request: Request,
    handler: Function,
    controller: Function,
  ): number {
    // 1. 检查方法级别的超时设置
    const methodTimeout = this.reflector.get<number>(TIMEOUT_KEY, handler);
    if (methodTimeout !== undefined) {
      return methodTimeout;
    }

    // 2. 检查控制器级别的超时设置
    const controllerTimeout = this.reflector.get<number>(TIMEOUT_KEY, controller);
    if (controllerTimeout !== undefined) {
      return controllerTimeout;
    }

    // 3. 根据请求类型确定超时时间
    return this.getTimeoutByRequestType(request);
  }

  /**
   * 根据请求类型获取超时时间
   */
  private getTimeoutByRequestType(request: Request): number {
    const { method, path } = request;
    const contentType = request.get('Content-Type') || '';

    // 文件上传请求
    if (this.isFileUploadRequest(request)) {
      return this.config.upload;
    }

    // 文件下载请求
    if (this.isFileDownloadRequest(request)) {
      return this.config.download;
    }

    // 认证相关请求
    if (this.isAuthRequest(path)) {
      return this.config.auth;
    }

    // 数据库密集型请求
    if (this.isDatabaseIntensiveRequest(request)) {
      return this.config.database;
    }

    // 外部API调用请求
    if (this.isExternalApiRequest(path)) {
      return this.config.external;
    }

    // 默认超时时间
    return this.config.default;
  }

  /**
   * 检查是否为文件上传请求
   */
  private isFileUploadRequest(request: Request): boolean {
    const contentType = request.get('Content-Type') || '';
    return (
      request.method === 'POST' &&
      (contentType.includes('multipart/form-data') ||
       contentType.includes('application/octet-stream') ||
       request.path.includes('/upload'))
    );
  }

  /**
   * 检查是否为文件下载请求
   */
  private isFileDownloadRequest(request: Request): boolean {
    return (
      request.method === 'GET' &&
      (request.path.includes('/download') ||
       request.path.includes('/export') ||
       request.path.includes('/file/'))
    );
  }

  /**
   * 检查是否为认证请求
   */
  private isAuthRequest(path: string): boolean {
    const authPaths = [
      '/auth/',
      '/login',
      '/logout',
      '/register',
      '/reset-password',
      '/verify',
    ];

    return authPaths.some(authPath => path.includes(authPath));
  }

  /**
   * 检查是否为数据库密集型请求
   */
  private isDatabaseIntensiveRequest(request: Request): boolean {
    const { method, path, query } = request;

    // 批量操作
    if (path.includes('/bulk') || path.includes('/batch')) {
      return true;
    }

    // 复杂查询
    if (method === 'GET' && this.hasComplexQuery(query)) {
      return true;
    }

    // 报表和统计
    if (path.includes('/report') || path.includes('/statistics') || path.includes('/analytics')) {
      return true;
    }

    // 导出操作
    if (path.includes('/export')) {
      return true;
    }

    return false;
  }

  /**
   * 检查是否为外部API请求
   */
  private isExternalApiRequest(path: string): boolean {
    const externalPaths = [
      '/external/',
      '/webhook/',
      '/callback/',
      '/oauth/',
      '/sms/',
      '/email/',
    ];

    return externalPaths.some(externalPath => path.includes(externalPath));
  }

  /**
   * 检查是否有复杂查询
   */
  private hasComplexQuery(query: any): boolean {
    if (!query || typeof query !== 'object') {
      return false;
    }

    const complexParams = ['search', 'filter', 'sort', 'include', 'populate'];
    const queryKeys = Object.keys(query);

    // 查询参数较多
    if (queryKeys.length > 10) {
      return true;
    }

    // 包含复杂查询参数
    return complexParams.some(param => queryKeys.includes(param));
  }

  /**
   * 记录超时日志
   */
  private logTimeout(
    request: Request,
    requestId: string,
    timeoutMs: number,
    actualDuration: number,
  ): void {
    const timeoutLog = {
      type: 'REQUEST_TIMEOUT',
      requestId,
      method: request.method,
      path: request.path,
      timeout: timeoutMs,
      actualDuration,
      userAgent: request.get('User-Agent'),
      ip: this.getClientIp(request),
      timestamp: new Date().toISOString(),
    };

    this.logger.warn('⏰ 请求超时', timeoutLog);
  }

  /**
   * 获取请求ID
   */
  private getRequestId(request: Request): string {
    return (request.headers['x-request-id'] as string) || 
           `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取客户端IP
   */
  private getClientIp(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      (request.headers['x-real-ip'] as string) ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }
}

/**
 * 动态超时拦截器
 * 根据系统负载动态调整超时时间
 */
@Injectable()
export class DynamicTimeoutInterceptor implements NestInterceptor {
  private readonly logger = new Logger(DynamicTimeoutInterceptor.name);
  private readonly baseConfig: TimeoutConfig;

  constructor(
    private readonly configService: ConfigService,
    private readonly reflector: Reflector,
  ) {
    this.baseConfig = {
      default: this.configService.get('TIMEOUT_DEFAULT', 30000),
      upload: this.configService.get('TIMEOUT_UPLOAD', 300000),
      download: this.configService.get('TIMEOUT_DOWNLOAD', 300000),
      auth: this.configService.get('TIMEOUT_AUTH', 10000),
      database: this.configService.get('TIMEOUT_DATABASE', 15000),
      external: this.configService.get('TIMEOUT_EXTERNAL', 20000),
    };
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const handler = context.getHandler();
    const controller = context.getClass();

    // 获取基础超时时间
    const baseTimeout = this.getBaseTimeout(request, handler, controller);
    
    // 根据系统负载调整超时时间
    const adjustedTimeout = this.adjustTimeoutByLoad(baseTimeout);

    if (adjustedTimeout <= 0) {
      return next.handle();
    }

    const startTime = Date.now();
    const requestId = this.getRequestId(request);

    return next.handle().pipe(
      timeout(adjustedTimeout),
      catchError((error) => {
        if (error instanceof TimeoutError) {
          const duration = Date.now() - startTime;
          this.logDynamicTimeout(request, requestId, baseTimeout, adjustedTimeout, duration);
          
          return throwError(() => new RequestTimeoutException({
            message: `请求超时 (调整后: ${adjustedTimeout}ms, 原始: ${baseTimeout}ms)`,
            timeout: adjustedTimeout,
            originalTimeout: baseTimeout,
            duration,
            path: request.path,
            method: request.method,
            requestId,
          }));
        }

        return throwError(() => error);
      }),
    );
  }

  /**
   * 获取基础超时时间
   */
  private getBaseTimeout(
    request: Request,
    handler: Function,
    controller: Function,
  ): number {
    // 检查装饰器设置
    const methodTimeout = this.reflector.get<number>(TIMEOUT_KEY, handler);
    if (methodTimeout !== undefined) {
      return methodTimeout;
    }

    const controllerTimeout = this.reflector.get<number>(TIMEOUT_KEY, controller);
    if (controllerTimeout !== undefined) {
      return controllerTimeout;
    }

    return this.baseConfig.default;
  }

  /**
   * 根据系统负载调整超时时间
   */
  private adjustTimeoutByLoad(baseTimeout: number): number {
    // 获取系统负载指标
    const loadFactor = this.getSystemLoadFactor();
    
    // 根据负载调整超时时间
    let adjustedTimeout = baseTimeout;
    
    if (loadFactor > 0.8) {
      // 高负载：增加50%超时时间
      adjustedTimeout = Math.floor(baseTimeout * 1.5);
    } else if (loadFactor > 0.6) {
      // 中等负载：增加25%超时时间
      adjustedTimeout = Math.floor(baseTimeout * 1.25);
    } else if (loadFactor < 0.3) {
      // 低负载：减少20%超时时间
      adjustedTimeout = Math.floor(baseTimeout * 0.8);
    }

    // 确保最小超时时间
    const minTimeout = this.configService.get('TIMEOUT_MIN', 1000);
    const maxTimeout = this.configService.get('TIMEOUT_MAX', 600000);
    
    return Math.max(minTimeout, Math.min(maxTimeout, adjustedTimeout));
  }

  /**
   * 获取系统负载因子
   */
  private getSystemLoadFactor(): number {
    try {
      // 获取内存使用率
      const memoryUsage = process.memoryUsage();
      const totalMemory = memoryUsage.heapTotal;
      const usedMemory = memoryUsage.heapUsed;
      const memoryFactor = usedMemory / totalMemory;

      // 获取CPU使用率（简化版本）
      const cpuFactor = this.getCpuUsage();

      // 综合负载因子
      return Math.max(memoryFactor, cpuFactor);
    } catch (error) {
      this.logger.warn('获取系统负载失败，使用默认值', error);
      return 0.5; // 默认中等负载
    }
  }

  /**
   * 获取CPU使用率（简化版本）
   */
  private getCpuUsage(): number {
    // 这里可以实现更复杂的CPU使用率计算
    // 目前返回一个基于事件循环延迟的简化指标
    const start = process.hrtime.bigint();
    setImmediate(() => {
      const delay = Number(process.hrtime.bigint() - start) / 1000000; // 转换为毫秒
      // 如果事件循环延迟超过10ms，认为负载较高
      return Math.min(delay / 10, 1);
    });
    
    return 0.5; // 默认值
  }

  /**
   * 记录动态超时日志
   */
  private logDynamicTimeout(
    request: Request,
    requestId: string,
    baseTimeout: number,
    adjustedTimeout: number,
    actualDuration: number,
  ): void {
    const timeoutLog = {
      type: 'DYNAMIC_TIMEOUT',
      requestId,
      method: request.method,
      path: request.path,
      baseTimeout,
      adjustedTimeout,
      actualDuration,
      loadFactor: this.getSystemLoadFactor(),
      timestamp: new Date().toISOString(),
    };

    this.logger.warn('⏰ 动态超时', timeoutLog);
  }

  /**
   * 获取请求ID
   */
  private getRequestId(request: Request): string {
    return (request.headers['x-request-id'] as string) || 
           `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
