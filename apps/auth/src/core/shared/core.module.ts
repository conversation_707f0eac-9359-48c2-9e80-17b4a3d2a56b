import { Module } from '@nestjs/common';

// 从现有位置导入服务
import { PasswordService } from '../../modules/auth/services/password.service';
import { EncryptionService } from '../../modules/security/services/encryption.service';
import { CryptoService } from '../../modules/security/services/crypto.service';
import { ValidationService } from '../../modules/security/services/validation.service';
import { UtilsService } from '../../modules/security/services/utils.service';

/**
 * 核心模块
 *
 * 提供系统核心服务，包括：
 * - 密码服务
 * - 加密服务
 * - 加密工具服务
 * - 验证服务
 * - 工具服务
 */
@Module({
  providers: [
    PasswordService,
    EncryptionService,
    CryptoService,
    ValidationService,
    UtilsService,
  ],
  exports: [
    PasswordService,
    EncryptionService,
    CryptoService,
    ValidationService,
    UtilsService,
  ],
})
export class CoreModule {}
