import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SecurityService } from './security.service';
import { AuditService } from './audit.service';
import { RiskService } from './risk.service';
import { EncryptionService } from './encryption.service';
import { AuditLog, AuditLogSchema } from './entities/audit-log.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: AuditLog.name, schema: AuditLogSchema }
    ]),
  ],
  providers: [
    SecurityService,
    AuditService,
    RiskService,
    EncryptionService,
  ],
  exports: [
    SecurityService,
    AuditService,
    RiskService,
    EncryptionService,
  ],
})
export class SecurityModule {}
