import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SecurityService } from '../../modules/security/services/security.service';
import { AuditService } from '../../modules/security/services/audit.service';
import { RiskService } from '../../modules/security/services/risk.service';
import { EncryptionService } from '../../modules/security/services/encryption.service';
import { AuditLog, AuditLogSchema } from '../../modules/security/entities/audit-log.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: AuditLog.name, schema: AuditLogSchema }
    ]),
  ],
  providers: [
    SecurityService,
    AuditService,
    RiskService,
    EncryptionService,
  ],
  exports: [
    SecurityService,
    AuditService,
    RiskService,
    EncryptionService,
  ],
})
export class SecurityModule {}
