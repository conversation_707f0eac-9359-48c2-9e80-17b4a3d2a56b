import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface RiskFactors {
  eventType: string;
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
  location?: {
    country: string;
    city: string;
    latitude: number;
    longitude: number;
  };
  deviceFingerprint?: string;
  timestamp: Date;
  previousActivity?: any[];
}

export interface RiskScore {
  total: number;
  factors: {
    location: number;
    device: number;
    behavior: number;
    time: number;
    network: number;
  };
  level: 'low' | 'medium' | 'high' | 'critical';
  recommendations: string[];
}

@Injectable()
export class RiskService {
  private readonly logger = new Logger(RiskService.name);
  private readonly enabled: boolean;
  private readonly factorWeights: any;
  private readonly thresholds: any;

  constructor(private configService: ConfigService) {
    this.enabled = this.configService.get<boolean>('security.riskScoring.enabled', false);
    this.factorWeights = this.configService.get('security.riskScoring.factors', {
      location: 0.3,
      device: 0.2,
      behavior: 0.3,
      time: 0.1,
      network: 0.1,
    });
    this.thresholds = this.configService.get('security.riskScoring.thresholds', {
      low: 30,
      medium: 60,
      high: 80,
    });
  }

  /**
   * 计算风险评分
   */
  async calculateRiskScore(factors: RiskFactors): Promise<number> {
    if (!this.enabled) {
      return 0;
    }

    try {
      const riskScore = await this.calculateDetailedRiskScore(factors);
      return riskScore.total;
    } catch (error) {
      this.logger.error('风险评分计算失败', error);
      return 0;
    }
  }

  /**
   * 计算详细风险评分
   */
  async calculateDetailedRiskScore(factors: RiskFactors): Promise<RiskScore> {
    const scores = {
      location: await this.calculateLocationRisk(factors),
      device: await this.calculateDeviceRisk(factors),
      behavior: await this.calculateBehaviorRisk(factors),
      time: await this.calculateTimeRisk(factors),
      network: await this.calculateNetworkRisk(factors),
    };

    // 计算加权总分
    const total = Math.round(
      scores.location * this.factorWeights.location +
      scores.device * this.factorWeights.device +
      scores.behavior * this.factorWeights.behavior +
      scores.time * this.factorWeights.time +
      scores.network * this.factorWeights.network
    );

    // 确定风险等级
    let level: 'low' | 'medium' | 'high' | 'critical';
    if (total >= this.thresholds.high) {
      level = 'critical';
    } else if (total >= this.thresholds.medium) {
      level = 'high';
    } else if (total >= this.thresholds.low) {
      level = 'medium';
    } else {
      level = 'low';
    }

    // 生成建议
    const recommendations = this.generateRecommendations(scores, level);

    return {
      total,
      factors: scores,
      level,
      recommendations,
    };
  }

  /**
   * 计算地理位置风险
   */
  private async calculateLocationRisk(factors: RiskFactors): Promise<number> {
    let risk = 0;

    if (!factors.location || !factors.userId) {
      return 0;
    }

    // 检查是否来自高风险国家
    const highRiskCountries = ['CN', 'RU', 'KP', 'IR']; // 示例
    if (highRiskCountries.includes(factors.location.country)) {
      risk += 30;
    }

    // 检查地理位置变化
    // TODO: 获取用户历史位置数据
    // const userHistory = await this.getUserLocationHistory(factors.userId);
    // if (userHistory.length > 0) {
    //   const lastLocation = userHistory[0];
    //   const distance = this.calculateDistance(factors.location, lastLocation);
    //   const timeDiff = factors.timestamp.getTime() - lastLocation.timestamp.getTime();
    //   
    //   // 短时间内大距离移动
    //   if (distance > 1000 && timeDiff < 3600000) { // 1000km, 1小时
    //     risk += 40;
    //   }
    // }

    // VPN/代理检测
    if (await this.isVpnOrProxy(factors.ipAddress)) {
      risk += 25;
    }

    return Math.min(risk, 100);
  }

  /**
   * 计算设备风险
   */
  private async calculateDeviceRisk(factors: RiskFactors): Promise<number> {
    let risk = 0;

    if (!factors.deviceFingerprint || !factors.userId) {
      return 20; // 无设备指纹信息本身就是风险
    }

    // 检查是否为新设备
    // TODO: 检查用户已知设备
    // const knownDevices = await this.getUserKnownDevices(factors.userId);
    // const isKnownDevice = knownDevices.some(d => d.fingerprint === factors.deviceFingerprint);
    // 
    // if (!isKnownDevice) {
    //   risk += 30;
    // }

    // 检查设备特征
    if (factors.userAgent) {
      // 检查是否为自动化工具
      const botPatterns = ['bot', 'crawler', 'spider', 'scraper'];
      const isBot = botPatterns.some(pattern => 
        factors.userAgent!.toLowerCase().includes(pattern)
      );
      
      if (isBot) {
        risk += 50;
      }

      // 检查是否为过时的浏览器
      if (this.isOutdatedBrowser(factors.userAgent)) {
        risk += 15;
      }
    }

    return Math.min(risk, 100);
  }

  /**
   * 计算行为风险
   */
  private async calculateBehaviorRisk(factors: RiskFactors): Promise<number> {
    let risk = 0;

    // 检查事件类型风险
    const eventRiskMap: Record<string, number> = {
      'login_failure': 20,
      'brute_force_attempt': 80,
      'suspicious_activity': 60,
      'anomaly_detected': 70,
      'permission_denied': 30,
      'admin_action': 40,
      'data_access': 25,
    };

    risk += eventRiskMap[factors.eventType] || 0;

    // 检查频率异常
    if (factors.previousActivity) {
      const recentActivity = factors.previousActivity.filter(
        activity => factors.timestamp.getTime() - activity.timestamp.getTime() < 300000 // 5分钟内
      );

      if (recentActivity.length > 10) {
        risk += 30; // 高频活动
      }
    }

    return Math.min(risk, 100);
  }

  /**
   * 计算时间风险
   */
  private async calculateTimeRisk(factors: RiskFactors): Promise<number> {
    let risk = 0;

    const hour = factors.timestamp.getHours();
    
    // 深夜时间段风险较高
    if (hour >= 0 && hour <= 5) {
      risk += 20;
    }

    // 工作时间外风险中等
    if (hour < 9 || hour > 18) {
      risk += 10;
    }

    // 周末风险稍高
    const dayOfWeek = factors.timestamp.getDay();
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      risk += 5;
    }

    return Math.min(risk, 100);
  }

  /**
   * 计算网络风险
   */
  private async calculateNetworkRisk(factors: RiskFactors): Promise<number> {
    let risk = 0;

    if (!factors.ipAddress) {
      return 0;
    }

    // 检查IP信誉
    if (await this.isBlacklistedIP(factors.ipAddress)) {
      risk += 60;
    }

    // 检查是否为Tor网络
    if (await this.isTorNetwork(factors.ipAddress)) {
      risk += 40;
    }

    // 检查是否为数据中心IP
    if (await this.isDataCenterIP(factors.ipAddress)) {
      risk += 25;
    }

    return Math.min(risk, 100);
  }

  /**
   * 生成安全建议
   */
  private generateRecommendations(scores: any, level: string): string[] {
    const recommendations: string[] = [];

    if (level === 'critical' || level === 'high') {
      recommendations.push('立即要求多因子认证');
      recommendations.push('限制敏感操作权限');
      recommendations.push('增加安全监控频率');
    }

    if (scores.location > 50) {
      recommendations.push('验证地理位置变化');
      recommendations.push('考虑启用地理位置限制');
    }

    if (scores.device > 50) {
      recommendations.push('要求设备验证');
      recommendations.push('添加设备到信任列表');
    }

    if (scores.behavior > 50) {
      recommendations.push('审查用户行为模式');
      recommendations.push('临时限制账户权限');
    }

    if (scores.network > 50) {
      recommendations.push('检查网络来源');
      recommendations.push('考虑IP地址限制');
    }

    if (recommendations.length === 0) {
      recommendations.push('继续正常监控');
    }

    return recommendations;
  }

  /**
   * 检查是否为VPN或代理
   */
  private async isVpnOrProxy(ipAddress?: string): Promise<boolean> {
    if (!ipAddress) return false;
    
    // TODO: 集成第三方IP检测服务
    // 这里返回模拟结果
    return false;
  }

  /**
   * 检查是否为黑名单IP
   */
  private async isBlacklistedIP(ipAddress: string): Promise<boolean> {
    // TODO: 检查IP黑名单数据库
    return false;
  }

  /**
   * 检查是否为Tor网络
   */
  private async isTorNetwork(ipAddress: string): Promise<boolean> {
    // TODO: 检查Tor出口节点列表
    return false;
  }

  /**
   * 检查是否为数据中心IP
   */
  private async isDataCenterIP(ipAddress: string): Promise<boolean> {
    // TODO: 检查数据中心IP范围
    return false;
  }

  /**
   * 检查是否为过时浏览器
   */
  private isOutdatedBrowser(userAgent: string): boolean {
    // 简化的过时浏览器检测
    const outdatedPatterns = [
      /MSIE [1-9]\./,
      /Chrome\/[1-5]\d\./,
      /Firefox\/[1-4]\d\./,
    ];

    return outdatedPatterns.some(pattern => pattern.test(userAgent));
  }
}
