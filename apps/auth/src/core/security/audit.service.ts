import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AuditLog, AuditLogDocument } from './entities/audit-log.entity';

export interface AuditLogEntry {
  type: string;
  event: string;
  userId?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  resource?: string;
  action?: string;
  details?: Record<string, any>;
  timestamp: Date;
}

export interface AuditQuery {
  type?: string;
  event?: string;
  userId?: string;
  sessionId?: string;
  ipAddress?: string;
  resource?: string;
  action?: string;
  timeRange?: {
    start: Date;
    end: Date;
  };
  details?: Record<string, any>;
}

@Injectable()
export class AuditService {
  private readonly logger = new Logger(AuditService.name);
  private readonly enabled: boolean;

  constructor(
    @InjectModel(AuditLog.name) private auditLogModel: Model<AuditLogDocument>,
    private configService: ConfigService,
  ) {
    this.enabled = this.configService.get<boolean>('security.audit.enabled', true);
  }

  /**
   * 记录审计日志
   */
  async log(entry: AuditLogEntry): Promise<void> {
    if (!this.enabled) {
      return;
    }

    try {
      const auditLog = new this.auditLogModel(entry);
      await auditLog.save();
      
      this.logger.debug(`审计日志记录: ${entry.type}:${entry.event}`, {
        userId: entry.userId,
        resource: entry.resource,
        action: entry.action,
      });
    } catch (error) {
      this.logger.error('审计日志记录失败', error);
    }
  }

  /**
   * 查询审计日志
   */
  async query(query: AuditQuery, page = 1, limit = 100): Promise<{
    data: AuditLogDocument[];
    total: number;
    page: number;
    limit: number;
  }> {
    const filter: any = {};

    if (query.type) filter.type = query.type;
    if (query.event) filter.event = query.event;
    if (query.userId) filter.userId = query.userId;
    if (query.sessionId) filter.sessionId = query.sessionId;
    if (query.ipAddress) filter.ipAddress = query.ipAddress;
    if (query.resource) filter.resource = query.resource;
    if (query.action) filter.action = query.action;

    if (query.timeRange) {
      filter.timestamp = {
        $gte: query.timeRange.start,
        $lte: query.timeRange.end,
      };
    }

    if (query.details) {
      for (const [key, value] of Object.entries(query.details)) {
        filter[`details.${key}`] = value;
      }
    }

    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.auditLogModel
        .find(filter)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.auditLogModel.countDocuments(filter).exec(),
    ]);

    return { data, total, page, limit };
  }

  /**
   * 统计事件数量
   */
  async countEvents(query: AuditQuery): Promise<number> {
    const filter: any = {};

    if (query.type) filter.type = query.type;
    if (query.event) filter.event = query.event;
    if (query.userId) filter.userId = query.userId;
    if (query.sessionId) filter.sessionId = query.sessionId;
    if (query.ipAddress) filter.ipAddress = query.ipAddress;
    if (query.resource) filter.resource = query.resource;
    if (query.action) filter.action = query.action;

    if (query.timeRange) {
      filter.timestamp = {
        $gte: query.timeRange.start,
        $lte: query.timeRange.end,
      };
    }

    if (query.details) {
      for (const [key, value] of Object.entries(query.details)) {
        filter[`details.${key}`] = value;
      }
    }

    return await this.auditLogModel.countDocuments(filter).exec();
  }

  /**
   * 按类型统计事件
   */
  async getEventsByType(type: string, timeRange: { start: Date; end: Date }): Promise<any[]> {
    return await this.auditLogModel.aggregate([
      {
        $match: {
          type,
          timestamp: {
            $gte: timeRange.start,
            $lte: timeRange.end,
          },
        },
      },
      {
        $group: {
          _id: '$event',
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 },
      },
    ]).exec();
  }

  /**
   * 获取高风险用户
   */
  async getTopRiskyUsers(timeRange: { start: Date; end: Date }, limit = 10): Promise<any[]> {
    return await this.auditLogModel.aggregate([
      {
        $match: {
          type: 'security',
          userId: { $exists: true },
          timestamp: {
            $gte: timeRange.start,
            $lte: timeRange.end,
          },
        },
      },
      {
        $group: {
          _id: '$userId',
          totalEvents: { $sum: 1 },
          avgRiskScore: { $avg: '$details.riskScore' },
          maxRiskScore: { $max: '$details.riskScore' },
          events: { $push: '$event' },
        },
      },
      {
        $sort: { avgRiskScore: -1, totalEvents: -1 },
      },
      {
        $limit: limit,
      },
    ]).exec();
  }

  /**
   * 获取高风险IP地址
   */
  async getTopRiskyIPs(timeRange: { start: Date; end: Date }, limit = 10): Promise<any[]> {
    return await this.auditLogModel.aggregate([
      {
        $match: {
          type: 'security',
          ipAddress: { $exists: true },
          timestamp: {
            $gte: timeRange.start,
            $lte: timeRange.end,
          },
        },
      },
      {
        $group: {
          _id: '$ipAddress',
          totalEvents: { $sum: 1 },
          avgRiskScore: { $avg: '$details.riskScore' },
          maxRiskScore: { $max: '$details.riskScore' },
          uniqueUsers: { $addToSet: '$userId' },
          events: { $push: '$event' },
        },
      },
      {
        $addFields: {
          uniqueUserCount: { $size: '$uniqueUsers' },
        },
      },
      {
        $sort: { avgRiskScore: -1, totalEvents: -1 },
      },
      {
        $limit: limit,
      },
    ]).exec();
  }

  /**
   * 获取用户最近位置
   */
  async getRecentUserLocations(userId: string, days = 30): Promise<any[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return await this.auditLogModel.aggregate([
      {
        $match: {
          userId,
          'details.location': { $exists: true },
          timestamp: { $gte: startDate },
        },
      },
      {
        $group: {
          _id: {
            latitude: '$details.location.latitude',
            longitude: '$details.location.longitude',
          },
          lastSeen: { $max: '$timestamp' },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { lastSeen: -1 },
      },
      {
        $project: {
          latitude: '$_id.latitude',
          longitude: '$_id.longitude',
          timestamp: '$lastSeen',
          count: 1,
        },
      },
    ]).exec();
  }

  /**
   * 获取用户已知设备
   */
  async getUserKnownDevices(userId: string): Promise<any[]> {
    return await this.auditLogModel.aggregate([
      {
        $match: {
          userId,
          'details.deviceFingerprint': { $exists: true },
        },
      },
      {
        $group: {
          _id: '$details.deviceFingerprint',
          firstSeen: { $min: '$timestamp' },
          lastSeen: { $max: '$timestamp' },
          count: { $sum: 1 },
          userAgent: { $last: '$userAgent' },
        },
      },
      {
        $project: {
          fingerprint: '$_id',
          firstSeen: 1,
          lastSeen: 1,
          count: 1,
          userAgent: 1,
        },
      },
      {
        $sort: { lastSeen: -1 },
      },
    ]).exec();
  }

  /**
   * 获取用户行为基线
   */
  async getUserBehaviorBaseline(userId: string): Promise<any> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [loginHours, commonIPs, commonDevices] = await Promise.all([
      // 常用登录时间
      this.auditLogModel.aggregate([
        {
          $match: {
            userId,
            event: 'login_success',
            timestamp: { $gte: thirtyDaysAgo },
          },
        },
        {
          $group: {
            _id: { $hour: '$timestamp' },
            count: { $sum: 1 },
          },
        },
        {
          $match: { count: { $gte: 3 } }, // 至少出现3次的时间段
        },
        {
          $project: {
            hour: '$_id',
            count: 1,
          },
        },
      ]),

      // 常用IP地址
      this.auditLogModel.aggregate([
        {
          $match: {
            userId,
            ipAddress: { $exists: true },
            timestamp: { $gte: thirtyDaysAgo },
          },
        },
        {
          $group: {
            _id: '$ipAddress',
            count: { $sum: 1 },
          },
        },
        {
          $sort: { count: -1 },
        },
        {
          $limit: 5,
        },
      ]),

      // 常用设备
      this.auditLogModel.aggregate([
        {
          $match: {
            userId,
            'details.deviceFingerprint': { $exists: true },
            timestamp: { $gte: thirtyDaysAgo },
          },
        },
        {
          $group: {
            _id: '$details.deviceFingerprint',
            count: { $sum: 1 },
          },
        },
        {
          $sort: { count: -1 },
        },
        {
          $limit: 3,
        },
      ]),
    ]);

    return {
      normalLoginHours: loginHours.map(h => h.hour),
      commonIPs: commonIPs.map(ip => ip._id),
      commonDevices: commonDevices.map(d => d._id),
    };
  }

  /**
   * 清理过期日志
   */
  async cleanupExpiredLogs(): Promise<number> {
    const retentionDays = this.configService.get<number>('security.audit.retention', 365);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const result = await this.auditLogModel.deleteMany({
      timestamp: { $lt: cutoffDate },
    }).exec();

    if (result.deletedCount > 0) {
      this.logger.log(`清理过期审计日志: ${result.deletedCount}条`);
    }

    return result.deletedCount;
  }

  /**
   * 获取审计统计信息
   */
  async getStatistics(timeRange?: { start: Date; end: Date }): Promise<any> {
    const filter: any = {};
    
    if (timeRange) {
      filter.timestamp = {
        $gte: timeRange.start,
        $lte: timeRange.end,
      };
    }

    const [
      totalLogs,
      logsByType,
      logsByEvent,
      topUsers,
      topIPs,
    ] = await Promise.all([
      this.auditLogModel.countDocuments(filter),
      
      this.auditLogModel.aggregate([
        { $match: filter },
        { $group: { _id: '$type', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
      ]),
      
      this.auditLogModel.aggregate([
        { $match: filter },
        { $group: { _id: '$event', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 },
      ]),
      
      this.auditLogModel.aggregate([
        { $match: { ...filter, userId: { $exists: true } } },
        { $group: { _id: '$userId', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 },
      ]),
      
      this.auditLogModel.aggregate([
        { $match: { ...filter, ipAddress: { $exists: true } } },
        { $group: { _id: '$ipAddress', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 },
      ]),
    ]);

    return {
      totalLogs,
      logsByType,
      logsByEvent,
      topUsers,
      topIPs,
      timeRange,
      generatedAt: new Date(),
    };
  }
}
