import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SessionService } from '../../modules/session-management/services/session.service';
import { Session, SessionSchema } from '../../modules/session-management/entities/session.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Session.name, schema: SessionSchema }
    ]),
  ],
  providers: [SessionService],
  exports: [SessionService],
})
export class SessionModule {}
