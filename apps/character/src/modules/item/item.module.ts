import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// Item相关组件
import { ItemController } from './item.controller';
import { ItemService } from './item.service';
// Item模块不需要直接导入配置表Schema，通过GameConfigFacade访问
import { Item, ItemSchema } from '@character/common/schemas/item.schema';
import { ItemRepository } from '@character/common/repositories/item.repository';

@Module({
  imports: [
    // 注册Item相关Schema
    MongooseModule.forFeature([
      // 配置表通过GameConfigFacade访问，不需要在这里注册
      { name: Item.name, schema: ItemSchema },
    ]),
  ],
  
  controllers: [ItemController],
  
  providers: [
    ItemService,
    ItemRepository,
  ],
  
  exports: [
    ItemService,
    ItemRepository,
  ],
})
export class ItemModule {}
