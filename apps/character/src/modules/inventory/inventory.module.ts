import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// Inventory相关组件
import { InventoryController } from './inventory.controller';
import { InventoryService } from './inventory.service';
import { Inventory, InventorySchema } from '@character/common/schemas/inventory.schema';
import { InventoryRepository } from '@character/common/repositories/inventory.repository';

@Module({
  imports: [
    // 注册Inventory Schema
    MongooseModule.forFeature([
      { name: Inventory.name, schema: InventorySchema },
    ]),
  ],
  
  controllers: [InventoryController],
  
  providers: [
    InventoryService,
    InventoryRepository,
  ],
  
  exports: [
    InventoryService,
    InventoryRepository,
  ],
})
export class InventoryModule {}
