# Gateway认证功能重构清单

## 概述
移除Gateway中的越权认证功能，改为通过微服务客户端调用Auth服务，保留性能优化手段。

## 1. apps/gateway/src/domain/user/user.service.ts

### 1.1 移除导入
```typescript
// ❌ 删除这行
import { JwtService } from '@nestjs/jwt';
```

### 1.2 移除构造函数中的JwtService依赖
```typescript
// ❌ 删除
constructor(
  private readonly jwtService: JwtService,  // 删除这行
  private readonly configService: ConfigService,
  // ... 其他依赖保留
)

// ✅ 修改为
constructor(
  private readonly configService: ConfigService,
  private readonly redisService: RedisService,
  private readonly microserviceClient: MicroserviceClientService,
)
```

### 1.3 移除类中的常量定义
```typescript
// ❌ 删除这些常量
private readonly TOKEN_BLACKLIST_PREFIX = 'auth:blacklist:';
private readonly REFRESH_TOKEN_PREFIX = 'auth:refresh:';
```

### 1.4 重构login方法
```typescript
// ❌ 删除原有的login方法实现
async login(loginDto: LoginDto): Promise<LoginResponse> {
  // 原有实现...
}

// ✅ 替换为
async login(loginDto: LoginDto): Promise<LoginResponse> {
  try {
    // 调用Auth服务进行完整的登录处理
    const authResult = await this.microserviceClient.call(
      MICROSERVICE_NAMES.AUTH_SERVICE,
      'auth.login',
      {
        ...loginDto,
        deviceInfo: {
          userAgent: loginDto.userAgent,
          ipAddress: loginDto.ipAddress,
          fingerprint: loginDto.deviceFingerprint,
        }
      }
    );

    if (!authResult.success) {
      throw new UnauthorizedException(authResult.message || 'Invalid credentials');
    }

    // Gateway只做数据转换和缓存
    const response = {
      accessToken: authResult.accessToken,
      refreshToken: authResult.refreshToken,
      expiresIn: authResult.expiresIn,
      expiresAt: authResult.expiresAt,
      tokenType: authResult.tokenType || 'Bearer',
      user: {
        id: authResult.user.id,
        username: authResult.user.username,
        email: authResult.user.email,
        roles: authResult.user.roles || [],
      },
    };

    // 缓存用户信息以提高性能
    await this.cacheUserInfo(authResult.user.id, authResult.user);

    this.logger.log(`用户登录成功: ${authResult.user.username}`);
    return response;

  } catch (error) {
    this.logger.error(`登录失败: ${error.message}`);
    if (error instanceof UnauthorizedException) {
      throw error;
    }
    throw new UnauthorizedException('认证失败');
  }
}
```

### 1.5 重构refreshToken方法
```typescript
// ❌ 删除原有的refreshToken方法实现

// ✅ 替换为
async refreshToken(refreshToken: string): Promise<Omit<LoginResponse, 'user'>> {
  try {
    const refreshResult = await this.microserviceClient.call(
      MICROSERVICE_NAMES.AUTH_SERVICE,
      'auth.refreshToken',
      { refreshToken }
    );

    if (!refreshResult.success) {
      throw new UnauthorizedException(refreshResult.message || 'Invalid refresh token');
    }

    this.logger.log(`Token刷新成功: 用户 ${refreshResult.userId}`);

    return {
      accessToken: refreshResult.accessToken,
      refreshToken: refreshResult.refreshToken,
      expiresIn: refreshResult.expiresIn,
      expiresAt: refreshResult.expiresAt,
      tokenType: refreshResult.tokenType || 'Bearer',
    };

  } catch (error) {
    this.logger.error(`Token刷新失败: ${error.message}`);
    if (error instanceof UnauthorizedException) {
      throw error;
    }
    throw new UnauthorizedException('Token刷新失败');
  }
}
```

### 1.6 重构verifyToken方法
```typescript
// ❌ 删除原有的verifyToken方法实现

// ✅ 替换为
async verifyToken(token: string): Promise<JwtPayload> {
  try {
    // 检查缓存的验证结果
    const cacheKey = `token_validation:${this.hashToken(token)}`;
    const cached = await this.redisService.get(cacheKey);
    if (cached) {
      const cachedResult = JSON.parse(cached);
      this.logger.debug('使用缓存的Token验证结果');
      return cachedResult;
    }

    // 调用Auth服务验证Token
    const validationResult = await this.microserviceClient.call(
      MICROSERVICE_NAMES.AUTH_SERVICE,
      'auth.validateToken',
      { token }
    );

    if (!validationResult.valid) {
      throw new UnauthorizedException(validationResult.message || 'Invalid token');
    }

    // 缓存验证结果（5分钟）
    await this.redisService.set(cacheKey, JSON.stringify(validationResult.payload), 300);

    return validationResult.payload;

  } catch (error) {
    this.logger.error(`Token验证失败: ${error.message}`);
    if (error instanceof UnauthorizedException) {
      throw error;
    }
    throw new UnauthorizedException('Token验证失败');
  }
}
```

### 1.7 添加logout方法
```typescript
// ✅ 添加新的logout方法
async logout(token: string): Promise<void> {
  try {
    await this.microserviceClient.call(
      MICROSERVICE_NAMES.AUTH_SERVICE,
      'auth.logout',
      { token }
    );

    this.logger.log('用户登出成功');

  } catch (error) {
    this.logger.error(`登出失败: ${error.message}`);
    // 登出失败不抛出异常，确保客户端能正常处理
  }
}
```

### 1.8 删除所有越权的私有方法
```typescript
// ❌ 删除以下所有方法
private async generateGatewayTokens(user: any): Promise<LoginResponse>
private async generateNewTokens(user: any, oldRefreshToken: string): Promise<Omit<LoginResponse, 'user'>>
private async generateRefreshToken(userId: string): Promise<string>
private async storeRefreshToken(userId: string, refreshToken: string): Promise<void>
private async validateRefreshToken(refreshToken: string): Promise<string | null>
private async removeRefreshToken(userId: string, refreshToken: string): Promise<void>
private async removeAllRefreshTokens(userId: string): Promise<void>
private async addTokenToBlacklist(token: string): Promise<void>
private async isTokenBlacklisted(token: string): Promise<boolean>
private getTokenExpirationTime(): number
```

### 1.9 添加新的辅助方法
```typescript
// ✅ 添加缓存用户信息方法
private async cacheUserInfo(userId: string, user: any): Promise<void> {
  try {
    const cacheKey = `user_info:${userId}`;
    const userInfo = {
      id: user.id,
      username: user.username,
      email: user.email,
      roles: user.roles || [],
      permissions: user.permissions || [],
      lastCached: new Date(),
    };
    
    // 缓存用户信息10分钟
    await this.redisService.set(cacheKey, JSON.stringify(userInfo), 600);
    
  } catch (error) {
    this.logger.warn(`缓存用户信息失败: ${error.message}`);
    // 缓存失败不影响主流程
  }
}

// ✅ 添加Token哈希方法
private hashToken(token: string): string {
  const crypto = require('crypto');
  return crypto.createHash('sha256').update(token).digest('hex').substring(0, 16);
}
```

## 2. apps/gateway/src/core/auth/auth.service.ts

### 2.1 重构generateJwtToken方法
```typescript
// ❌ 删除原有的generateJwtToken方法实现

// ✅ 替换为（如果需要保留此方法）
async generateJwtToken(user: User, deviceInfo?: DeviceInfo): Promise<AuthToken> {
  // 调用Auth服务生成Token
  const tokenResult = await this.microserviceClient.call(
    'auth',
    'auth.generateTokenPair',
    {
      user,
      deviceInfo
    }
  );

  return tokenResult;
}
```

### 2.2 重构Token验证方法
```typescript
// ❌ 删除原有的Token验证实现

// ✅ 替换为
async validateToken(token: string): Promise<any> {
  try {
    // 检查缓存的验证结果
    const cacheKey = `token_validation:${this.hashToken(token)}`;
    const cached = await this.redisService.get(cacheKey);
    if (cached) {
      const cachedResult = JSON.parse(cached);
      this.logger.debug('使用缓存的Token验证结果');
      return cachedResult;
    }

    // 调用Auth服务验证Token
    const validationResult = await this.microserviceClient.call(
      'auth',
      'auth.validateToken',
      { token }
    );

    if (!validationResult.valid) {
      throw new UnauthorizedException(validationResult.message || 'Invalid token');
    }

    // 缓存验证结果（5分钟）
    await this.redisService.set(cacheKey, JSON.stringify(validationResult), 300);

    return validationResult;

  } catch (error) {
    this.logger.error(`Token验证失败: ${error.message}`);
    if (error instanceof UnauthorizedException) {
      throw error;
    }
    throw new UnauthorizedException('Token验证失败');
  }
}
```

### 2.3 删除会话管理方法
```typescript
// ❌ 删除以下方法
async createSession(user: User, ipAddress: string, userAgent: string, deviceInfo?: DeviceInfo): Promise<AuthSession>
async revokeToken(tokenId: string): Promise<void>
private async storeSession(session: AuthSession): Promise<void>
private async updateSessionLastAccess(sessionId: string): Promise<void>
```

### 2.4 删除黑名单管理方法
```typescript
// ❌ 删除以下方法
private async isTokenBlacklisted(token: string): Promise<boolean>
private async addTokenToBlacklist(tokenId: string): Promise<void>
private async storeTokenInfo(tokenId: string, info: any): Promise<void>
private async deleteTokenInfo(tokenId: string): Promise<void>
```

### 2.5 添加Token哈希方法
```typescript
// ✅ 添加
private hashToken(token: string): string {
  const crypto = require('crypto');
  return crypto.createHash('sha256').update(token).digest('hex').substring(0, 16);
}
```

## 3. apps/gateway/src/domain/user/user.controller.ts

### 3.1 更新logout端点
```typescript
// ❌ 如果原有logout方法有userId参数，删除

// ✅ 修改为
@Post('logout')
@UseGuards(JwtAuthGuard)
async logout(@Request() req) {
  const token = req.headers.authorization?.replace('Bearer ', '');
  await this.userService.logout(token);
  return { message: '登出成功' };
}
```

## 4. 需要确保的Auth服务接口

确保Auth服务提供以下接口：

### 4.1 登录接口
```typescript
// MessagePattern: 'auth.login'
interface LoginRequest {
  username: string;
  password: string;
  deviceInfo?: {
    userAgent: string;
    ipAddress: string;
    fingerprint: string;
  };
}

interface LoginResponse {
  success: boolean;
  message?: string;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  expiresAt: Date;
  tokenType: string;
  user: {
    id: string;
    username: string;
    email: string;
    roles: string[];
  };
}
```

### 4.2 Token验证接口
```typescript
// MessagePattern: 'auth.validateToken'
interface ValidateTokenRequest {
  token: string;
}

interface ValidateTokenResponse {
  valid: boolean;
  message?: string;
  payload?: JwtPayload;
}
```

### 4.3 Token刷新接口
```typescript
// MessagePattern: 'auth.refreshToken'
interface RefreshTokenRequest {
  refreshToken: string;
}

interface RefreshTokenResponse {
  success: boolean;
  message?: string;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  expiresAt: Date;
  tokenType: string;
  userId: string;
}
```

### 4.4 登出接口
```typescript
// MessagePattern: 'auth.logout'
interface LogoutRequest {
  token: string;
}

interface LogoutResponse {
  success: boolean;
  message?: string;
}
```

## 5. 性能优化保留

### 5.1 缓存策略
- ✅ 保留Token验证结果缓存（5分钟）
- ✅ 保留用户信息缓存（10分钟）
- ✅ 使用Token哈希作为缓存key，避免直接存储Token

### 5.2 缓存配置
```typescript
const CACHE_CONFIG = {
  TOKEN_VALIDATION: {
    ttl: 300, // 5分钟
    key: (token: string) => `token_validation:${hashToken(token)}`
  },
  USER_INFO: {
    ttl: 600, // 10分钟
    key: (userId: string) => `user_info:${userId}`
  }
};
```

## 6. 测试验证

### 6.1 功能测试
- [ ] 用户登录功能正常
- [ ] Token刷新功能正常
- [ ] Token验证功能正常
- [ ] 用户登出功能正常
- [ ] 缓存功能正常工作

### 6.2 性能测试
- [ ] Token验证响应时间（应该在10-50ms）
- [ ] 缓存命中率（应该>80%）
- [ ] Auth服务调用频率（应该显著降低）

## 7. 注意事项

1. **渐进式修改**：建议按文件逐个修改，每修改一个文件就测试一次
2. **保留错误处理**：确保所有微服务调用都有适当的错误处理
3. **日志记录**：保留重要操作的日志记录
4. **向后兼容**：确保API接口保持向后兼容
5. **缓存失效**：微服务调用失败时，不影响主流程

## 8. 完成检查清单

- [ ] 移除Gateway中的Token生成逻辑
- [ ] 移除Gateway中的Token黑名单管理
- [ ] 移除Gateway中的会话管理
- [ ] 实现通过微服务调用Auth服务
- [ ] 保留并优化缓存机制
- [ ] 更新相关的Controller方法
- [ ] 测试所有认证相关功能
- [ ] 验证性能优化效果
- [ ] 更新相关文档
