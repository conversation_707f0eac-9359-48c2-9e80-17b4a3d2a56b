import { Injectable } from '@nestjs/common';
import { HealthIndicator, HealthIndicatorResult, HealthCheckError } from '@nestjs/terminus';
import { RedisService } from '@common/redis';

/**
 * Redis 健康检查指示器
 */
@Injectable()
export class RedisHealthIndicator extends HealthIndicator {
  constructor(private readonly redisService: RedisService) {
    super();
  }

  /**
   * 检查 Redis 连接健康状态 - 严格遵循NestJS标准格式
   */
  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      const startTime = Date.now();
      const isHealthy = await this.redisService.ping();
      const responseTime = Date.now() - startTime;

      if (!isHealthy) {
        throw new Error('Redis ping failed');
      }

      // 严格遵循NestJS健康检查标准格式，不添加额外字段
      const result = this.getStatus(key, true, {
        responseTime: `${responseTime}ms`,
        status: 'up',
      });

      return result;
    } catch (error) {
      const result = this.getStatus(key, false, {
        status: 'down',
        error: error.message,
      });

      throw new HealthCheckError('Redis check failed', result);
    }
  }
}
