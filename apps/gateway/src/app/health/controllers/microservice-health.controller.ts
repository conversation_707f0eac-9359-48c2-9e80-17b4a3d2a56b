import { Controller, Get, Param, HttpException, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { MicroserviceHealthIndicator } from '../indicators/microservice-health.indicator';
import { HealthService } from '../health.service';

/**
 * 微服务健康检查控制器
 * 专门负责检查微服务的健康状态
 */
@ApiTags('微服务健康检查')
@Controller('health/microservices')
export class MicroserviceHealthController {
  constructor(
    private readonly microserviceHealthIndicator: MicroserviceHealthIndicator,
    private readonly healthService: HealthService,
  ) {}

  /**
   * 微服务状态总览
   * 用途：检查所有微服务的健康状态
   * 使用场景：运维监控、服务发现
   */
  @Get()
  @ApiOperation({ 
    summary: '微服务状态总览', 
    description: '检查所有微服务的健康状态' 
  })
  @ApiResponse({ 
    status: 200, 
    description: '微服务状态信息',
    schema: {
      example: {
        status: 'degraded',
        timestamp: '2025-07-17T02:19:06.817Z',
        summary: {
          total: 9,
          healthy: 8,
          unhealthy: 1,
          unknown: 0
        },
        services: {
          auth: { status: 'up', responseTime: '12ms', lastCheck: '2025-07-17T02:19:05.000Z' },
          character: { status: 'up', responseTime: '8ms', lastCheck: '2025-07-17T02:19:05.000Z' },
          notification: { status: 'down', error: 'Connection refused', lastCheck: '2025-07-17T02:19:05.000Z' }
        }
      }
    }
  })
  async checkAllMicroservices() {
    try {
      // 获取所有微服务的健康状态
      const microservicesStatus: any = await this.healthService.checkMicroservicesStatus();

      // 修复：checkMicroservicesStatus直接返回services对象，不需要.details
      const services = microservicesStatus || {};
      const serviceStatuses = Object.values(services).map((service: any) => service.status);
      
      const summary = {
        total: serviceStatuses.length,
        healthy: serviceStatuses.filter(status => status === 'up').length,
        unhealthy: serviceStatuses.filter(status => status === 'down').length,
        unknown: serviceStatuses.filter(status => !['up', 'down'].includes(status)).length,
      };

      // 确定总体状态
      let overallStatus = 'ok';
      if (summary.unhealthy > 0) {
        overallStatus = summary.healthy > 0 ? 'degraded' : 'error';
      }

      return {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        summary,
        services: this.formatMicroserviceStatuses(services),
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          timestamp: new Date().toISOString(),
          error: error.message || '微服务健康检查失败',
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  /**
   * 单个微服务健康检查
   * 用途：检查特定微服务的详细状态
   * 使用场景：故障排查、服务调试
   */
  @Get(':serviceName')
  @ApiParam({ 
    name: 'serviceName', 
    description: '微服务名称',
    example: 'character'
  })
  @ApiOperation({ 
    summary: '单个微服务健康检查', 
    description: '检查特定微服务的详细健康状态' 
  })
  @ApiResponse({ 
    status: 200, 
    description: '微服务详细状态',
    schema: {
      example: {
        status: 'up',
        timestamp: '2025-07-17T02:19:06.817Z',
        service: 'character',
        checks: {
          rpc: { status: 'up', responseTime: '5ms', endpoint: 'character.health' },
          http: { status: 'up', responseTime: '8ms', endpoint: '/health' },
          database: { status: 'up', responseTime: '3ms' },
          cache: { status: 'up', responseTime: '1ms' }
        },
        metadata: {
          version: '1.2.3',
          uptime: '2h 15m 30s',
          instance: 'character-service-pod-abc123'
        }
      }
    }
  })
  @ApiResponse({ 
    status: 404, 
    description: '微服务不存在' 
  })
  @ApiResponse({ 
    status: 503, 
    description: '微服务不健康' 
  })
  async checkSingleMicroservice(@Param('serviceName') serviceName: string) {
    try {
      // 检查单个微服务的详细状态
      const serviceStatus = await this.microserviceHealthIndicator.checkSingleService(serviceName);

      if (!serviceStatus) {
        throw new HttpException(
          {
            status: 'error',
            timestamp: new Date().toISOString(),
            service: serviceName,
            error: '微服务不存在或未配置',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      // 构建详细的健康检查响应
      const detailedStatus = {
        status: serviceStatus.status,
        timestamp: new Date().toISOString(),
        service: serviceName,
        checks: {
          rpc: {
            status: serviceStatus.status,
            responseTime: serviceStatus.responseTime || 'unknown',
            endpoint: `${serviceName}.health`,
            ...(serviceStatus.error && { error: serviceStatus.error }),
          },
          // 可以扩展更多检查项
          http: {
            status: serviceStatus.status,
            responseTime: serviceStatus.responseTime || 'unknown',
            endpoint: '/health',
          },
        },
        metadata: {
          version: serviceStatus.version || 'unknown',
          uptime: serviceStatus.uptime || 'unknown',
          instance: serviceStatus.instance || 'unknown',
          lastCheck: serviceStatus.lastCheck || new Date().toISOString(),
        }
      };

      // 如果服务不健康，返回503状态码
      if (serviceStatus.status !== 'up') {
        throw new HttpException(detailedStatus, HttpStatus.SERVICE_UNAVAILABLE);
      }

      return detailedStatus;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        {
          status: 'error',
          timestamp: new Date().toISOString(),
          service: serviceName,
          error: error.message || '微服务健康检查失败',
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  /**
   * 格式化微服务状态信息
   * 将内部状态格式转换为标准API响应格式
   */
  private formatMicroserviceStatuses(services: Record<string, any>): Record<string, any> {
    const formatted: Record<string, any> = {};

    Object.keys(services).forEach(serviceName => {
      const service = services[serviceName];
      formatted[serviceName] = {
        status: service.status || 'unknown',
        ...(service.responseTime && { responseTime: service.responseTime }),
        ...(service.error && { error: service.error }),
        lastCheck: service.lastCheck || new Date().toISOString(),
        ...(service.version && { version: service.version }),
        ...(service.instance && { instance: service.instance }),
      };
    });

    return formatted;
  }
}
