/**
 * Gateway 服务通用接口定义
 */

// 从共享库导入分页接口
export { PaginationQuery, PaginationResponse } from '@shared/interfaces';

// 基础响应接口
export interface BaseResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  timestamp: string;
  requestId?: string;
}

// 用户相关接口
export interface User {
  id: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  status: 'active' | 'inactive' | 'suspended';
  level?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthContext {
  authenticated: boolean;
  user?: User;
  token?: string;
  apiKey?: {
    id: string;
    name: string;
    permissions: string[];
    rateLimit?: {
      window: number;
      requests: number;
    };
  };
  session?: {
    id: string;
    expiresAt: Date;
  };
}

// 简化的路由接口（仅保留必要的）
export interface SimpleRouteConfig {
  enabled: boolean;
  timeout?: number;
  retries?: number;
}

// 服务发现接口
export interface ServiceInstance {
  id: string;
  name: string;
  host: string;
  port: number;
  protocol: 'http' | 'https';
  health: 'healthy' | 'unhealthy' | 'unknown';
  weight: number;
  metadata?: Record<string, any>;
  lastHeartbeat: Date;
}

// 限流相关接口
export interface RateLimitResult {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: Date;
  retryAfter?: number;
}

export interface RateLimitConfig {
  windowMs: number;
  max: number;
  strategy: 'fixed-window' | 'sliding-window' | 'token-bucket';
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

// 缓存相关接口
export interface CacheOptions {
  ttl?: number;
  key?: string;
  tags?: string[];
}

// 监控相关接口
export interface MetricData {
  name: string;
  value: number;
  timestamp: Date;
  labels?: Record<string, string>;
}

export interface HealthCheckResult {
  status: 'ok' | 'error' | 'shutting_down';
  info?: Record<string, any>;
  error?: Record<string, any>;
  details?: Record<string, any>;
}

// 配置相关接口
export interface GatewayConfig {
  server: {
    port: number;
    host: string;
    cors: {
      enabled: boolean;
      origins: string[];
    };
  };
  security: {
    jwtSecret: string;
    jwtExpiresIn: string;
    jwtIssuer: string;
    jwtAudience: string;
    rateLimitWindowMs: number;
    rateLimitMax: number;
  };
  redis: {
    host: string;
    port: number;
    password?: string;
    db: number;
  };
  microservices: Record<string, {
    host: string;
    port: number;
    protocol: string;
  }>;
}

// 错误相关接口
export interface ErrorResponse {
  statusCode: number;
  message: string;
  error: string;
  timestamp: string;
  path: string;
  requestId?: string;
  details?: any;
}

// 日志相关接口
export interface LogContext {
  requestId?: string;
  userId?: string;
  ip?: string;
  userAgent?: string;
  method?: string;
  url?: string;
  statusCode?: number;
  responseTime?: number;
}

// 事件相关接口
export interface GatewayEvent {
  type: string;
  payload: any;
  timestamp: Date;
  source: string;
  metadata?: Record<string, any>;
}
