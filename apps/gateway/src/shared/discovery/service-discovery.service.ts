import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LoadBalancingService, ServiceInstance } from '../../modules/load-balancing/services/load-balancing.service';

export interface ServiceRegistration {
  id: string;
  name: string;
  address: string;
  port: number;
  tags: string[];
  meta: Record<string, string>;
  check?: HealthCheck;
}

export interface HealthCheck {
  http?: string;
  tcp?: string;
  interval: string;
  timeout: string;
  deregisterCriticalServiceAfter?: string;
}

export interface ServiceDiscoveryProvider {
  name: string;
  register(service: ServiceRegistration): Promise<void>;
  deregister(serviceId: string): Promise<void>;
  discover(serviceName: string): Promise<ServiceRegistration[]>;
  watch(serviceName: string, callback: (services: ServiceRegistration[]) => void): Promise<void>;
  unwatch(serviceName: string): Promise<void>;
  getHealth(serviceId: string): Promise<boolean>;
}

@Injectable()
export class ServiceDiscoveryService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ServiceDiscoveryService.name);
  private provider: ServiceDiscoveryProvider | null = null;
  private readonly watchers = new Map<string, NodeJS.Timeout>();
  private readonly serviceCache = new Map<string, ServiceRegistration[]>();
  private readonly refreshInterval: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly loadBalancerService: LoadBalancingService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.refreshInterval = this.configService.get<number>('services.discovery.refreshInterval', 30000);
  }

  async onModuleInit() {
    const enabled = this.configService.get<boolean>('services.discovery.enabled', false);
    
    if (!enabled) {
      this.logger.log('Service discovery is disabled');
      return;
    }

    const providerType = this.configService.get<string>('services.discovery.provider', 'static');
    
    try {
      this.provider = await this.createProvider(providerType);
      await this.initializeServiceWatching();
      this.logger.log(`Service discovery initialized with provider: ${providerType}`);
    } catch (error) {
      this.logger.error('Failed to initialize service discovery:', error);
    }
  }

  async onModuleDestroy() {
    if (this.provider) {
      // 停止所有监听器
      for (const [serviceName] of this.watchers) {
        await this.stopWatching(serviceName);
      }
    }
  }

  /**
   * 注册服务
   */
  async registerService(service: ServiceRegistration): Promise<void> {
    if (!this.provider) {
      throw new Error('Service discovery provider not initialized');
    }

    try {
      await this.provider.register(service);
      this.logger.log(`Service registered: ${service.name}@${service.address}:${service.port}`);
      
      this.eventEmitter.emit('service.registered', service);
    } catch (error) {
      this.logger.error(`Failed to register service ${service.name}:`, error);
      throw error;
    }
  }

  /**
   * 注销服务
   */
  async deregisterService(serviceId: string): Promise<void> {
    if (!this.provider) {
      throw new Error('Service discovery provider not initialized');
    }

    try {
      await this.provider.deregister(serviceId);
      this.logger.log(`Service deregistered: ${serviceId}`);
      
      this.eventEmitter.emit('service.deregistered', { id: serviceId });
    } catch (error) {
      this.logger.error(`Failed to deregister service ${serviceId}:`, error);
      throw error;
    }
  }

  /**
   * 发现服务
   */
  async discoverServices(serviceName: string): Promise<ServiceRegistration[]> {
    if (!this.provider) {
      return this.getStaticServices(serviceName);
    }

    try {
      // 先尝试从缓存获取
      const cached = this.serviceCache.get(serviceName);
      if (cached) {
        return cached;
      }

      // 从服务发现提供者获取
      const services = await this.provider.discover(serviceName);
      this.serviceCache.set(serviceName, services);
      
      return services;
    } catch (error) {
      this.logger.error(`Failed to discover services for ${serviceName}:`, error);
      return this.getStaticServices(serviceName);
    }
  }

  /**
   * 开始监听服务变化
   */
  async startWatching(serviceName: string): Promise<void> {
    if (!this.provider) {
      this.logger.warn('Service discovery provider not available, using static configuration');
      return;
    }

    if (this.watchers.has(serviceName)) {
      this.logger.warn(`Already watching service: ${serviceName}`);
      return;
    }

    try {
      await this.provider.watch(serviceName, (services) => {
        this.handleServiceChange(serviceName, services);
      });

      // 立即执行一次服务发现
      try {
        const services = await this.provider.discover(serviceName);
        this.handleServiceChange(serviceName, services);
      } catch (error) {
        this.logger.error(`Failed to initial discover services for ${serviceName}:`, error);
      }

      // 设置定期刷新
      const interval = setInterval(async () => {
        try {
          const services = await this.provider!.discover(serviceName);
          this.handleServiceChange(serviceName, services);
        } catch (error) {
          this.logger.error(`Failed to refresh services for ${serviceName}:`, error);
        }
      }, this.refreshInterval);

      this.watchers.set(serviceName, interval);
      this.logger.log(`Started watching service: ${serviceName}`);
    } catch (error) {
      this.logger.error(`Failed to start watching service ${serviceName}:`, error);
    }
  }

  /**
   * 停止监听服务变化
   */
  async stopWatching(serviceName: string): Promise<void> {
    const interval = this.watchers.get(serviceName);
    if (interval) {
      clearInterval(interval);
      this.watchers.delete(serviceName);
    }

    if (this.provider) {
      try {
        await this.provider.unwatch(serviceName);
        this.logger.log(`Stopped watching service: ${serviceName}`);
      } catch (error) {
        this.logger.error(`Failed to stop watching service ${serviceName}:`, error);
      }
    }
  }

  /**
   * 获取服务健康状态
   */
  async getServiceHealth(serviceId: string): Promise<boolean> {
    if (!this.provider) {
      return true; // 静态配置默认健康
    }

    try {
      return await this.provider.getHealth(serviceId);
    } catch (error) {
      this.logger.error(`Failed to get health for service ${serviceId}:`, error);
      return false;
    }
  }

  /**
   * 获取所有监听的服务
   */
  getWatchedServices(): string[] {
    return Array.from(this.watchers.keys());
  }

  /**
   * 获取服务缓存
   */
  getServiceCache(): Map<string, ServiceRegistration[]> {
    return new Map(this.serviceCache);
  }

  // ==================== 私有方法 ====================

  private async createProvider(providerType: string): Promise<ServiceDiscoveryProvider> {
    switch (providerType) {
      case 'consul':
        return this.createConsulProvider();
      case 'etcd':
        return this.createEtcdProvider();
      case 'static':
      default:
        return this.createStaticProvider();
    }
  }

  private async createConsulProvider(): Promise<ServiceDiscoveryProvider> {
    const consulUrl = this.configService.get<string>('services.discovery.consulUrl');
    
    // 这里应该实现 Consul 提供者
    // 为了演示，返回一个模拟实现
    return {
      name: 'consul',
      async register(service: ServiceRegistration) {
        // Consul 注册逻辑
      },
      async deregister(serviceId: string) {
        // Consul 注销逻辑
      },
      async discover(serviceName: string) {
        // Consul 发现逻辑
        return [];
      },
      async watch(serviceName: string, callback: (services: ServiceRegistration[]) => void) {
        // Consul 监听逻辑
      },
      async unwatch(serviceName: string) {
        // Consul 取消监听逻辑
      },
      async getHealth(serviceId: string) {
        // Consul 健康检查逻辑
        return true;
      },
    };
  }

  private async createEtcdProvider(): Promise<ServiceDiscoveryProvider> {
    const etcdUrl = this.configService.get<string>('services.discovery.etcdUrl');
    
    // 这里应该实现 etcd 提供者
    return {
      name: 'etcd',
      async register(service: ServiceRegistration) {
        // etcd 注册逻辑
      },
      async deregister(serviceId: string) {
        // etcd 注销逻辑
      },
      async discover(serviceName: string) {
        // etcd 发现逻辑
        return [];
      },
      async watch(serviceName: string, callback: (services: ServiceRegistration[]) => void) {
        // etcd 监听逻辑
      },
      async unwatch(serviceName: string) {
        // etcd 取消监听逻辑
      },
      async getHealth(serviceId: string) {
        // etcd 健康检查逻辑
        return true;
      },
    };
  }

  private async createStaticProvider(): Promise<ServiceDiscoveryProvider> {
    const self = this; // 保存正确的上下文
    return {
      name: 'static',
      async register(service: ServiceRegistration) {
        // 静态配置不需要注册
      },
      async deregister(serviceId: string) {
        // 静态配置不需要注销
      },
      async discover(serviceName: string) {
        return self.getStaticServices(serviceName);
      },
      async watch(serviceName: string, callback: (services: ServiceRegistration[]) => void) {
        // 静态配置不需要监听
      },
      async unwatch(serviceName: string) {
        // 静态配置不需要取消监听
      },
      async getHealth(serviceId: string) {
        return true;
      },
    };
  }

  private getStaticServices(serviceName: string): ServiceRegistration[] {
    const servicesConfig = this.configService.get('services.services', {});

    // 首先尝试直接匹配服务名称
    let serviceConfig = servicesConfig[serviceName];
    let configKey = serviceName;

    // 如果没有找到，尝试通过配置中的 name 字段匹配
    if (!serviceConfig) {
      for (const [key, config] of Object.entries(servicesConfig)) {
        if ((config as any)?.name === serviceName) {
          serviceConfig = config;
          configKey = key;
          break;
        }
      }
    }

    if (!serviceConfig) {
      return [];
    }

    const url = new URL(serviceConfig.url);

    return [{
      id: `${serviceName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: serviceConfig.name || serviceName, // 使用配置中的 name 字段
      address: url.hostname,
      port: parseInt(url.port) || (url.protocol === 'https:' ? 443 : 80),
      tags: ['static'],
      meta: {
        url: serviceConfig.url,
        timeout: serviceConfig.timeout?.toString() || '5000',
        retries: serviceConfig.retries?.toString() || '3',
        configKey, // 记录配置键名用于调试
      },
    }];
  }

  private async initializeServiceWatching(): Promise<void> {
    const servicesConfig = this.configService.get('services.services', {});
    const serviceNames = Object.keys(servicesConfig);
    
    for (const serviceName of serviceNames) {
      await this.startWatching(serviceName);
    }
  }

  private handleServiceChange(serviceName: string, services: ServiceRegistration[]): void {
    this.logger.log(`Service change detected for ${serviceName}: ${services.length} instances`);
    
    // 更新缓存
    this.serviceCache.set(serviceName, services);
    
    // 更新负载均衡器
    this.updateLoadBalancer(serviceName, services);
    
    // 发出事件
    this.eventEmitter.emit('service.changed', { serviceName, services });
  }

  private updateLoadBalancer(serviceName: string, services: ServiceRegistration[]): void {
    // 获取现有实例
    const existingInstances = this.loadBalancerService.getAllInstances(serviceName);
    const existingUrls = new Set(existingInstances.map(inst => inst.url));

    this.logger.debug(`Updating load balancer for ${serviceName}:`);
    this.logger.debug(`Existing instances: ${existingInstances.length}`);
    this.logger.debug(`Existing URLs: ${Array.from(existingUrls)}`);
    this.logger.debug(`New services: ${services.length}`);

    // 先注册新实例（避免服务实例为空的时间窗口）
    const newUrls = new Set<string>();
    for (const service of services) {
      const url = `http://${service.address}:${service.port}`;
      newUrls.add(url);
      this.logger.debug(`Processing service URL: ${url}`);

      // 如果实例不存在，则注册新实例
      if (!existingUrls.has(url)) {
        this.logger.debug(`Registering new instance for ${serviceName}: ${url}`);
        const instance: Omit<ServiceInstance, 'id'> = {
          url,
          weight: 1,
          healthy: true,
          connections: 0,
          responseTime: 0,
          lastHealthCheck: new Date(),
          metadata: service.meta,
        };

        this.loadBalancerService.registerServiceInstance(serviceName, instance);
      } else {
        this.logger.debug(`Instance already exists for ${serviceName}: ${url}`);
      }
    }

    this.logger.debug(`New URLs: ${Array.from(newUrls)}`);

    // 然后删除不再存在的旧实例
    for (const instance of existingInstances) {
      if (!newUrls.has(instance.url)) {
        this.logger.debug(`Unregistering old instance for ${serviceName}: ${instance.url}`);
        this.loadBalancerService.unregisterServiceInstance(serviceName, instance.id);
      }
    }
  }
}
