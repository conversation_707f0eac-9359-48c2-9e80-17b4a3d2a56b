import { SetMetadata } from '@nestjs/common';

export const TOKEN_SCOPE_KEY = 'tokenScope';

/**
 * Token作用域装饰器
 * 用于标记接口需要的Token类型
 */
export const TokenScope = (scope: 'account' | 'character') => SetMetadata(TOKEN_SCOPE_KEY, scope);

/**
 * 账号Token装饰器
 * 标记接口只能使用账号级Token访问
 */
export const AccountToken = () => TokenScope('account');

/**
 * 角色Token装饰器
 * 标记接口只能使用角色级Token访问
 */
export const CharacterToken = () => TokenScope('character');
