import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// JWT共享模块
import { JwtSharedModule } from './jwt/jwt-shared.module';

// 微服务通信模块
import { MicroservicesModule } from './microservices/microservices.module';

// 服务发现模块
import { DiscoveryModule } from './discovery/discovery.module';

// 日志模块
import { LoggingModule } from './logging/logging.module';

// 链路追踪模块
import { TracingModule } from './tracing/tracing.module';

// 过滤器模块
import { FiltersModule } from './filters/filters.module';

// 拦截器模块
import { InterceptorsModule } from './interceptors/interceptors.module';

// 中间件模块
import { MiddlewareModule } from './middleware/middleware.module';

// 配置模块
import { ConfigModule as SharedConfigModule } from './config/config.module';

/**
 * 共享模块
 * 
 * 参考Auth服务成功经验，提供所有功能模块需要的共享资源：
 * - JWT认证服务
 * - 微服务通信
 * - 服务发现
 * - 日志服务
 * - 链路追踪
 * - 异常过滤器
 * - 拦截器
 * - 中间件
 * - 配置管理
 * 
 * 职责：
 * - 统一管理所有共享资源
 * - 避免功能模块间的直接依赖
 * - 提供基础设施服务
 */
@Module({
  imports: [
    // 全局配置模块
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    
    // 共享配置模块
    SharedConfigModule,
    
    // JWT共享模块 - 提供统一的JWT服务
    JwtSharedModule,
    
    // 微服务通信模块 - 提供微服务客户端
    MicroservicesModule,
    
    // 服务发现模块 - 提供服务注册和发现
    DiscoveryModule,
    
    // 日志模块 - 提供统一的日志服务
    LoggingModule,
    
    // 链路追踪模块 - 提供分布式追踪
    TracingModule,
    
    // 过滤器模块 - 提供异常处理
    FiltersModule,
    
    // 拦截器模块 - 提供请求/响应处理
    InterceptorsModule,
    
    // 中间件模块 - 提供HTTP中间件
    MiddlewareModule,
  ],
  exports: [
    // 导出所有共享模块供功能模块使用
    SharedConfigModule,
    JwtSharedModule,
    MicroservicesModule,
    DiscoveryModule,
    LoggingModule,
    TracingModule,
    FiltersModule,
    InterceptorsModule,
    MiddlewareModule,
  ],
})
export class SharedModule {}
