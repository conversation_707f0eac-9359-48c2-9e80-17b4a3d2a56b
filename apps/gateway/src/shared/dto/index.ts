// 从共享库导入抽象层DTO
export {
  PaginationDto,
  BaseResponseDto,
  PaginationResponseDto,
  ErrorResponseDto,
  HealthCheckResponseDto
} from '@shared/dto';

/**
 * Gateway 服务专用 DTO
 *
 * 注意：通用的响应DTO已移动到共享库 @shared/dto
 * 这里只保留网关服务特有的DTO
 */

// 网关专用DTO - 这些是网关特有的，不属于抽象层

// 指标数据 DTO
export class MetricDataDto {
  name: string;
  value: number;
  timestamp: string;
  labels?: Record<string, string>;

  constructor(name: string, value: number, labels?: Record<string, string>) {
    this.name = name;
    this.value = value;
    this.timestamp = new Date().toISOString();
    this.labels = labels;
  }
}

// 限流结果 DTO
export class RateLimitResultDto {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: string;
  retryAfter?: number;

  constructor(
    allowed: boolean,
    limit: number,
    remaining: number,
    resetTime: Date,
    retryAfter?: number
  ) {
    this.allowed = allowed;
    this.limit = limit;
    this.remaining = remaining;
    this.resetTime = resetTime.toISOString();
    this.retryAfter = retryAfter;
  }
}
