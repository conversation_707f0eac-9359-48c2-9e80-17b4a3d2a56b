/**
 * Gateway 服务常量定义
 */

// HTTP 状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const;

// 认证相关常量
export const AUTH_CONSTANTS = {
  JWT_SECRET_KEY: 'gateway.security.jwtSecret',
  JWT_EXPIRES_IN: 'gateway.security.jwtExpiresIn',
  JWT_ISSUER: 'gateway.security.jwtIssuer',
  JWT_AUDIENCE: 'gateway.security.jwtAudience',
  TOKEN_BLACKLIST_PREFIX: 'auth:blacklist:',
  REFRESH_TOKEN_PREFIX: 'auth:refresh:',
  SESSION_PREFIX: 'auth:session:',
} as const;

// 限流相关常量
export const RATE_LIMIT_CONSTANTS = {
  GLOBAL_KEY: 'rate_limit:global',
  USER_KEY_PREFIX: 'rate_limit:user:',
  IP_KEY_PREFIX: 'rate_limit:ip:',
  API_KEY_PREFIX: 'rate_limit:api:',
  DEFAULT_WINDOW_MS: 60000, // 1分钟
  DEFAULT_MAX_REQUESTS: 100,
} as const;

// 缓存相关常量
export const CACHE_CONSTANTS = {
  ROUTE_CACHE_KEY: 'gateway:routes',
  CONFIG_CACHE_KEY: 'gateway:config',
  SERVICE_DISCOVERY_KEY: 'gateway:services',
  DEFAULT_TTL: 300, // 5分钟
} as const;

// 微服务名称 - 从共享库导入
export { MICROSERVICE_NAMES } from '@shared/constants';

// 路由相关常量
export const ROUTE_CONSTANTS = {
  PROXY_PREFIX: '/api',         // 微服务代理前缀（统一使用）
  HEALTH_PREFIX: '/health',     // 健康检查
  METRICS_PREFIX: '/metrics',   // 监控指标
  WEBSOCKET_PATH: '/ws',        // WebSocket 路径
  GRAPHQL_PATH: '/graphql',     // GraphQL 路径

  // 已废弃的常量（保留兼容性）
  API_PREFIX: '/api',           // @deprecated 使用 PROXY_PREFIX
  AUTH_PREFIX: '/auth',         // @deprecated 使用 /api/auth
} as const;

// 导入微服务名称常量
import { MICROSERVICE_NAMES } from '@shared/constants';

// 错误消息
export const ERROR_MESSAGES = {
  UNAUTHORIZED: '未授权访问',
  FORBIDDEN: '禁止访问',
  NOT_FOUND: '资源未找到',
  TOO_MANY_REQUESTS: '请求过于频繁',
  INTERNAL_SERVER_ERROR: '内部服务器错误',
  BAD_GATEWAY: '网关错误',
  SERVICE_UNAVAILABLE: '服务不可用',
  GATEWAY_TIMEOUT: '网关超时',
  INVALID_TOKEN: '无效的令牌',
  TOKEN_EXPIRED: '令牌已过期',
  USER_NOT_FOUND: '用户不存在',
  INVALID_CREDENTIALS: '用户名或密码错误',
} as const;

// 日志级别
export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug',
  VERBOSE: 'verbose',
} as const;

// 环境变量
export const ENV_KEYS = {
  NODE_ENV: 'NODE_ENV',
  PORT: 'PORT',
  REDIS_HOST: 'REDIS_HOST',
  REDIS_PORT: 'REDIS_PORT',
  REDIS_PASSWORD: 'REDIS_PASSWORD',
  MONGODB_URI: 'MONGODB_URI',
  JWT_SECRET: 'JWT_SECRET',
} as const;
