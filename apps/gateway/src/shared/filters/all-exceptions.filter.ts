import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ThrottlerException } from '@nestjs/throttler';

/**
 * 全局异常过滤器
 * 
 * 捕获所有未处理的异常，统一格式化错误响应
 * 提供详细的错误日志和用户友好的错误信息
 */
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status: number;
    let message: string;
    let error: string;
    let details: any = null;

    // 处理不同类型的异常
    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      
      if (typeof exceptionResponse === 'string') {
        message = exceptionResponse;
        error = exception.name;
      } else if (typeof exceptionResponse === 'object') {
        const responseObj = exceptionResponse as any;
        message = responseObj.message || exception.message;
        error = responseObj.error || exception.name;
        details = responseObj.details || null;
      } else {
        message = exception.message;
        error = exception.name;
      }
    } else if (exception instanceof ThrottlerException) {
      status = HttpStatus.TOO_MANY_REQUESTS;
      message = 'Too many requests, please try again later';
      error = 'ThrottlerException';
    } else if (exception instanceof Error) {
      status = HttpStatus.INTERNAL_SERVER_ERROR;
      message = 'Internal server error';
      error = exception.name;
      details = process.env.NODE_ENV === 'development' ? exception.stack : null;
    } else {
      status = HttpStatus.INTERNAL_SERVER_ERROR;
      message = 'Unknown error occurred';
      error = 'UnknownError';
    }

    // 构建错误响应
    const errorResponse = {
      success: false,
      error: {
        code: this.getErrorCode(status),
        message,
        type: error,
        timestamp: new Date().toISOString(),
        path: request.url,
        method: request.method,
        ...(details && { details }),
      },
      meta: {
        requestId: request.headers['x-request-id'] || this.generateRequestId(),
        userAgent: request.headers['user-agent'],
        ip: this.getClientIp(request),
      },
    };

    // 记录错误日志
    this.logError(exception, request, status, errorResponse);

    // 设置响应头
    response.setHeader('Content-Type', 'application/json');
    response.setHeader('X-Request-ID', errorResponse.meta.requestId);

    // 发送错误响应
    response.status(status).json(errorResponse);
  }

  /**
   * 记录错误日志
   */
  private logError(
    exception: unknown,
    request: Request,
    status: number,
    errorResponse: any,
  ): void {
    const logContext = {
      method: request.method,
      url: request.url,
      userAgent: request.headers['user-agent'],
      ip: this.getClientIp(request),
      userId: (request as any).user?.id,
      requestId: errorResponse.meta.requestId,
      status,
    };

    if (status >= 500) {
      this.logger.error(
        `Internal Server Error: ${errorResponse.error.message}`,
        exception instanceof Error ? exception.stack : exception,
        JSON.stringify(logContext),
      );
    } else if (status >= 400) {
      this.logger.warn(
        `Client Error: ${errorResponse.error.message}`,
        JSON.stringify(logContext),
      );
    }
  }

  /**
   * 获取客户端 IP 地址
   */
  private getClientIp(request: Request): string {
    const forwarded = request.headers['x-forwarded-for'];
    if (forwarded) {
      const forwardedStr = Array.isArray(forwarded) ? forwarded[0] : forwarded;
      return forwardedStr.split(',')[0].trim();
    }

    const realIp = request.headers['x-real-ip'];
    if (realIp && typeof realIp === 'string') {
      return realIp;
    }

    return request.connection?.remoteAddress ||
           request.socket?.remoteAddress ||
           'unknown';
  }

  /**
   * 生成请求 ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 根据 HTTP 状态码获取错误代码
   */
  private getErrorCode(status: number): string {
    const statusToCode: Record<number, string> = {
      400: 'BAD_REQUEST',
      401: 'UNAUTHORIZED',
      403: 'FORBIDDEN',
      404: 'NOT_FOUND',
      405: 'METHOD_NOT_ALLOWED',
      409: 'CONFLICT',
      422: 'UNPROCESSABLE_ENTITY',
      429: 'TOO_MANY_REQUESTS',
      500: 'INTERNAL_SERVER_ERROR',
      502: 'BAD_GATEWAY',
      503: 'SERVICE_UNAVAILABLE',
      504: 'GATEWAY_TIMEOUT',
    };

    return statusToCode[status] || 'UNKNOWN_ERROR';
  }
}
