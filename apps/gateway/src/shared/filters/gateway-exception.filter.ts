import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { LoggingService } from '../logging/logging.service';
import { MetricsService } from '../../modules/monitoring/services/metrics.service';

@Catch()
export class GatewayExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GatewayExceptionFilter.name);

  constructor(
    private readonly loggingService?: LoggingService,
    private readonly metricsService?: MetricsService,
  ) {}

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let error = 'InternalServerError';
    let details: any = undefined;

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      
      if (typeof exceptionResponse === 'string') {
        message = exceptionResponse;
      } else if (typeof exceptionResponse === 'object') {
        message = (exceptionResponse as any).message || exception.message;
        error = (exceptionResponse as any).error || exception.name;
        details = (exceptionResponse as any).details;
      }
    } else if (exception instanceof Error) {
      message = exception.message;
      error = exception.name;
      
      // 特殊错误类型处理
      if (exception.message.includes('ECONNREFUSED')) {
        status = HttpStatus.BAD_GATEWAY;
        message = 'Service unavailable';
        error = 'ServiceUnavailable';
      } else if (exception.message.includes('ETIMEDOUT')) {
        status = HttpStatus.GATEWAY_TIMEOUT;
        message = 'Service timeout';
        error = 'ServiceTimeout';
      } else if (exception.message.includes('Rate limit')) {
        status = HttpStatus.TOO_MANY_REQUESTS;
        error = 'RateLimitExceeded';
      }
    }

    const errorResponse = {
      statusCode: status,
      message,
      error,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      requestId: (request as any).requestId,
      ...(details && { details }),
    };

    // 记录错误日志
    if (this.loggingService) {
      this.loggingService.logError(
        exception instanceof Error ? exception : new Error(String(exception)),
        'GatewayExceptionFilter',
        {
          statusCode: status,
          path: request.url,
          method: request.method,
          userAgent: request.get('user-agent'),
          ip: this.getClientIp(request),
          requestId: (request as any).requestId,
        },
      );
    }

    // 记录错误指标
    if (this.metricsService) {
      this.metricsService.recordHttpRequest(
        request.method,
        request.url,
        status,
        Date.now() - ((request as any).startTime || Date.now()),
        (request as any).user?.id,
      );
    }

    // 记录安全事件（如果是安全相关错误）
    if (this.isSecurityError(status) && this.loggingService) {
      this.loggingService.logSecurity({
        type: this.getSecurityEventType(status),
        severity: this.getSecuritySeverity(status),
        ip: this.getClientIp(request),
        userAgent: request.get('user-agent') || '',
        userId: (request as any).user?.id,
        details: {
          path: request.url,
          method: request.method,
          error: error,
          message: message,
        },
        action: `${request.method} ${request.url}`,
        resource: request.url,
        blocked: true,
      });
    }

    // 根据环境决定是否返回详细错误信息
    const isDevelopment = process.env.NODE_ENV === 'development';
    if (!isDevelopment && status === HttpStatus.INTERNAL_SERVER_ERROR) {
      errorResponse.message = 'Internal server error';
      delete errorResponse.details;
    }

    response.status(status).json(errorResponse);
  }

  private getClientIp(request: Request): string {
    return (
      request.get('x-forwarded-for')?.split(',')[0]?.trim() ||
      request.get('x-real-ip') ||
      request.connection.remoteAddress ||
      '127.0.0.1'
    );
  }

  private isSecurityError(status: number): boolean {
    return [
      HttpStatus.UNAUTHORIZED,
      HttpStatus.FORBIDDEN,
      HttpStatus.TOO_MANY_REQUESTS,
    ].includes(status);
  }

  private getSecurityEventType(status: number): 'auth_failure' | 'rate_limit' | 'access_denied' | 'suspicious_activity' | 'token_abuse' {
    switch (status) {
      case HttpStatus.UNAUTHORIZED:
        return 'auth_failure';
      case HttpStatus.FORBIDDEN:
        return 'access_denied';
      case HttpStatus.TOO_MANY_REQUESTS:
        return 'rate_limit';
      default:
        return 'suspicious_activity';
    }
  }

  private getSecuritySeverity(status: number): 'low' | 'medium' | 'high' | 'critical' {
    switch (status) {
      case HttpStatus.UNAUTHORIZED:
        return 'medium';
      case HttpStatus.FORBIDDEN:
        return 'high';
      case HttpStatus.TOO_MANY_REQUESTS:
        return 'low';
      default:
        return 'medium';
    }
  }
}
