import { Injectable, Logger, LogLevel } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request, Response } from 'express';
import { createHash } from 'crypto';
import * as fs from 'fs';
import * as path from 'path';

export interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  message: string;
  context?: string;
  requestId?: string;
  userId?: string;
  ip?: string;
  userAgent?: string;
  method?: string;
  url?: string;
  statusCode?: number;
  responseTime?: number;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
  metadata?: Record<string, any>;
  traceId?: string;
  spanId?: string;
}

export interface AccessLogEntry {
  timestamp: Date;
  requestId: string;
  method: string;
  url: string;
  statusCode: number;
  responseTime: number;
  contentLength: number;
  ip: string;
  userAgent: string;
  referer?: string;
  userId?: string;
  sessionId?: string;
  forwardedFor?: string;
  protocol: string;
  httpVersion: string;
}

export interface SecurityLogEntry {
  timestamp: Date;
  type: 'auth_failure' | 'rate_limit' | 'suspicious_activity' | 'access_denied' | 'token_abuse';
  severity: 'low' | 'medium' | 'high' | 'critical';
  ip: string;
  userAgent: string;
  userId?: string;
  details: Record<string, any>;
  action: string;
  resource?: string;
  blocked: boolean;
}

export interface LoggingConfig {
  level: LogLevel;
  format: 'json' | 'text';
  enableFile: boolean;
  enableConsole: boolean;
  enableElk: boolean;
  filePath: string;
  maxFileSize: string;
  maxFiles: number;
  enableAccessLog: boolean;
  enableSecurityLog: boolean;
  enablePerformanceLog: boolean;
  sensitiveFields: string[];
}

@Injectable()
export class LoggingService {
  private readonly logger = new Logger(LoggingService.name);
  private readonly config: LoggingConfig;
  private readonly logStreams = new Map<string, fs.WriteStream>();

  constructor(private readonly configService: ConfigService) {
    this.config = {
      level: (this.configService.get('logging.level', 'info') as string) as LogLevel,
      format: this.configService.get<'json' | 'text'>('logging.format', 'json'),
      enableFile: this.configService.get<boolean>('logging.enableFile', true),
      enableConsole: this.configService.get<boolean>('logging.enableConsole', true),
      enableElk: this.configService.get<boolean>('logging.enableElk', false),
      filePath: this.configService.get<string>('logging.filePath', './logs'),
      maxFileSize: this.configService.get<string>('logging.maxFileSize', '10m'),
      maxFiles: this.configService.get<number>('logging.maxFiles', 5),
      enableAccessLog: this.configService.get<boolean>('logging.enableAccessLog', true),
      enableSecurityLog: this.configService.get<boolean>('logging.enableSecurityLog', true),
      enablePerformanceLog: this.configService.get<boolean>('logging.enablePerformanceLog', true),
      sensitiveFields: this.configService.get<string[]>('logging.sensitiveFields', [
        'password',
        'token',
        'authorization',
        'cookie',
        'x-api-key',
      ]),
    };

    this.initializeLogStreams();
  }

  /**
   * 记录访问日志
   */
  logAccess(req: Request, res: Response, responseTime: number): void {
    if (!this.config.enableAccessLog) {
      return;
    }

    const entry: AccessLogEntry = {
      timestamp: new Date(),
      requestId: this.getRequestId(req),
      method: req.method,
      url: this.sanitizeUrl(req.originalUrl || req.url),
      statusCode: res.statusCode,
      responseTime,
      contentLength: parseInt(res.get('content-length') || '0', 10),
      ip: this.getClientIp(req),
      userAgent: req.get('user-agent') || '',
      referer: req.get('referer'),
      userId: (req as any).user?.id,
      sessionId: this.getSessionId(req),
      forwardedFor: req.get('x-forwarded-for'),
      protocol: req.protocol,
      httpVersion: req.httpVersion,
    };

    this.writeLog('access', entry);
  }

  /**
   * 记录安全日志
   */
  logSecurity(entry: Omit<SecurityLogEntry, 'timestamp'>): void {
    if (!this.config.enableSecurityLog) {
      return;
    }

    const securityEntry: SecurityLogEntry = {
      timestamp: new Date(),
      ...entry,
    };

    this.writeLog('security', securityEntry);
    
    // 高严重性事件同时记录到主日志
    if (entry.severity === 'high' || entry.severity === 'critical') {
      this.logger.warn(`Security event: ${entry.type}`, securityEntry);
    }
  }

  /**
   * 记录性能日志
   */
  logPerformance(
    operation: string,
    duration: number,
    metadata?: Record<string, any>,
  ): void {
    if (!this.config.enablePerformanceLog) {
      return;
    }

    const entry = {
      timestamp: new Date(),
      operation,
      duration,
      metadata: this.sanitizeMetadata(metadata || {}),
    };

    this.writeLog('performance', entry);
  }

  /**
   * 记录错误日志
   */
  logError(
    error: Error,
    context?: string,
    metadata?: Record<string, any>,
  ): void {
    const entry: LogEntry = {
      timestamp: new Date(),
      level: 'error',
      message: error.message,
      context,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      metadata: this.sanitizeMetadata(metadata || {}),
    };

    this.writeLog('error', entry);
    this.logger.error(error.message, error.stack, context);
  }

  /**
   * 记录应用日志
   */
  log(
    level: LogLevel,
    message: string,
    context?: string,
    metadata?: Record<string, any>,
  ): void {
    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      message,
      context,
      metadata: this.sanitizeMetadata(metadata || {}),
    };

    this.writeLog('application', entry);
    
    // 同时输出到 NestJS Logger
    switch (level) {
      case 'error':
        this.logger.error(message, context);
        break;
      case 'warn':
        this.logger.warn(message, context);
        break;
      case 'log':
        this.logger.log(message, context);
        break;
      case 'debug':
        this.logger.debug(message, context);
        break;
      case 'verbose':
        this.logger.verbose(message, context);
        break;
    }
  }

  /**
   * 记录链路追踪日志
   */
  logTrace(
    traceId: string,
    spanId: string,
    operation: string,
    duration: number,
    metadata?: Record<string, any>,
  ): void {
    const entry = {
      timestamp: new Date(),
      traceId,
      spanId,
      operation,
      duration,
      metadata: this.sanitizeMetadata(metadata || {}),
    };

    this.writeLog('trace', entry);
  }

  /**
   * 获取日志统计信息
   */
  getLogStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    for (const [logType] of this.logStreams) {
      const filePath = path.join(this.config.filePath, `${logType}.log`);
      
      try {
        const stat = fs.statSync(filePath);
        stats[logType] = {
          size: stat.size,
          modified: stat.mtime,
          lines: this.countLines(filePath),
        };
      } catch (error) {
        stats[logType] = {
          size: 0,
          modified: null,
          lines: 0,
        };
      }
    }
    
    return stats;
  }

  /**
   * 搜索日志
   */
  async searchLogs(
    logType: string,
    query: string,
    startTime?: Date,
    endTime?: Date,
    limit = 100,
  ): Promise<any[]> {
    const filePath = path.join(this.config.filePath, `${logType}.log`);
    
    if (!fs.existsSync(filePath)) {
      return [];
    }

    const results: any[] = [];
    const lines = fs.readFileSync(filePath, 'utf-8').split('\n');
    
    for (const line of lines) {
      if (!line.trim()) continue;
      
      try {
        const entry = JSON.parse(line);
        const entryTime = new Date(entry.timestamp);
        
        // 时间过滤
        if (startTime && entryTime < startTime) continue;
        if (endTime && entryTime > endTime) continue;
        
        // 内容过滤
        if (query && !this.matchesQuery(entry, query)) continue;
        
        results.push(entry);
        
        if (results.length >= limit) break;
      } catch (error) {
        // 忽略解析错误的行
      }
    }
    
    return results.reverse(); // 最新的在前
  }

  /**
   * 清理旧日志
   */
  async cleanupLogs(olderThanDays: number): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    
    for (const [logType] of this.logStreams) {
      const filePath = path.join(this.config.filePath, `${logType}.log`);
      
      if (fs.existsSync(filePath)) {
        const stat = fs.statSync(filePath);
        
        if (stat.mtime < cutoffDate) {
          fs.unlinkSync(filePath);
          this.logger.log(`Cleaned up old log file: ${filePath}`);
        }
      }
    }
  }

  // ==================== 私有方法 ====================

  private initializeLogStreams(): void {
    if (!this.config.enableFile) {
      return;
    }

    // 确保日志目录存在
    if (!fs.existsSync(this.config.filePath)) {
      fs.mkdirSync(this.config.filePath, { recursive: true });
    }

    const logTypes = ['access', 'security', 'performance', 'error', 'application', 'trace'];
    
    for (const logType of logTypes) {
      const filePath = path.join(this.config.filePath, `${logType}.log`);
      const stream = fs.createWriteStream(filePath, { flags: 'a' });
      
      stream.on('error', (error) => {
        this.logger.error(`Log stream error for ${logType}:`, error);
      });
      
      this.logStreams.set(logType, stream);
    }
  }

  private writeLog(logType: string, entry: any): void {
    const logData = this.formatLogEntry(entry);
    
    // 写入文件
    if (this.config.enableFile) {
      const stream = this.logStreams.get(logType);
      if (stream) {
        stream.write(logData + '\n');
      }
    }
    
    // 输出到控制台
    if (this.config.enableConsole && logType !== 'access') {
      console.log(logData);
    }
    
    // 发送到 ELK（如果启用）
    if (this.config.enableElk) {
      this.sendToElk(logType, entry);
    }
  }

  private formatLogEntry(entry: any): string {
    if (this.config.format === 'json') {
      return JSON.stringify(entry);
    } else {
      // 文本格式
      const timestamp = entry.timestamp.toISOString();
      const level = entry.level || 'info';
      const message = entry.message || JSON.stringify(entry);
      
      return `[${timestamp}] ${level.toUpperCase()}: ${message}`;
    }
  }

  private sanitizeMetadata(metadata: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(metadata)) {
      if (this.config.sensitiveFields.some(field => 
        key.toLowerCase().includes(field.toLowerCase())
      )) {
        sanitized[key] = '[REDACTED]';
      } else {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }

  private sanitizeUrl(url: string): string {
    // 移除敏感查询参数
    const urlObj = new URL(url, 'http://localhost');
    
    for (const param of this.config.sensitiveFields) {
      if (urlObj.searchParams.has(param)) {
        urlObj.searchParams.set(param, '[REDACTED]');
      }
    }
    
    return urlObj.pathname + urlObj.search;
  }

  private getRequestId(req: Request): string {
    return (req as any).id || this.generateRequestId();
  }

  private getSessionId(req: Request): string | undefined {
    return req.cookies?.sessionId || req.cookies?.session_id;
  }

  private getClientIp(req: Request): string {
    return (
      req.get('x-forwarded-for')?.split(',')[0]?.trim() ||
      req.get('x-real-ip') ||
      req.connection.remoteAddress ||
      '127.0.0.1'
    );
  }

  private generateRequestId(): string {
    return createHash('md5')
      .update(`${Date.now()}-${Math.random()}`)
      .digest('hex')
      .substring(0, 8);
  }

  private countLines(filePath: string): number {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      return content.split('\n').length - 1;
    } catch (error) {
      return 0;
    }
  }

  private matchesQuery(entry: any, query: string): boolean {
    const searchText = JSON.stringify(entry).toLowerCase();
    return searchText.includes(query.toLowerCase());
  }

  private async sendToElk(logType: string, entry: any): Promise<void> {
    // 这里可以实现发送到 Elasticsearch 的逻辑
    // 例如使用 @elastic/elasticsearch 客户端
  }
}
