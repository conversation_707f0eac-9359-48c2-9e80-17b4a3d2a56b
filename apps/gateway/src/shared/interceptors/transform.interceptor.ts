import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Request, Response } from 'express';

/**
 * 响应转换拦截器
 * 
 * 统一格式化所有 API 响应，确保响应格式的一致性
 * 添加元数据信息，如请求 ID、时间戳等
 */
@Injectable()
export class TransformInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    return next.handle().pipe(
      map((data) => {
        // 如果响应已经是标准格式，直接返回
        if (this.isStandardResponse(data)) {
          return data;
        }

        // 如果是健康检查或指标端点，直接返回原始数据
        if (this.isRawEndpoint(request.url)) {
          return data;
        }

        // 转换为标准响应格式
        return this.transformResponse(data, request, response);
      }),
    );
  }

  /**
   * 转换响应为标准格式
   */
  private transformResponse(data: any, request: Request, response: Response): any {
    const requestId = request.headers['x-request-id'] as string;
    const timestamp = new Date().toISOString();
    const statusCode = response.statusCode;

    // 基础响应结构
    const transformedResponse: any = {
      success: statusCode >= 200 && statusCode < 300,
      data: data,
      meta: {
        requestId,
        timestamp,
        path: request.url,
        method: request.method,
        statusCode,
      },
    };

    // 添加分页信息（如果存在）
    if (this.hasPaginationData(data)) {
      transformedResponse.meta.pagination = this.extractPaginationInfo(data);
    }

    // 添加用户信息（如果存在）
    const user = (request as any).user;
    if (user) {
      transformedResponse.meta.user = {
        id: user.id,
        username: user.username,
      };
    }

    return transformedResponse;
  }

  /**
   * 检查是否已经是标准响应格式
   */
  private isStandardResponse(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      typeof data.success === 'boolean' &&
      (data.hasOwnProperty('data') || data.hasOwnProperty('error'))
    );
  }

  /**
   * 检查是否是原始端点（不需要转换）
   */
  private isRawEndpoint(url: string): boolean {
    const rawEndpoints = [
      '/health',
      '/metrics',
      '/api/docs',
      '/favicon.ico',
    ];

    return rawEndpoints.some(endpoint => url.startsWith(endpoint));
  }

  /**
   * 检查是否包含分页数据
   */
  private hasPaginationData(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      (data.hasOwnProperty('total') ||
        data.hasOwnProperty('page') ||
        data.hasOwnProperty('limit') ||
        data.hasOwnProperty('totalPages'))
    );
  }

  /**
   * 提取分页信息
   */
  private extractPaginationInfo(data: any): any {
    const pagination: any = {};

    if (data.total !== undefined) pagination.total = data.total;
    if (data.page !== undefined) pagination.page = data.page;
    if (data.limit !== undefined) pagination.limit = data.limit;
    if (data.totalPages !== undefined) pagination.totalPages = data.totalPages;
    if (data.hasNext !== undefined) pagination.hasNext = data.hasNext;
    if (data.hasPrev !== undefined) pagination.hasPrev = data.hasPrev;

    // 计算缺失的分页信息
    if (pagination.total && pagination.limit) {
      pagination.totalPages = Math.ceil(pagination.total / pagination.limit);
    }

    if (pagination.page && pagination.totalPages) {
      pagination.hasNext = pagination.page < pagination.totalPages;
      pagination.hasPrev = pagination.page > 1;
    }

    return pagination;
  }
}
