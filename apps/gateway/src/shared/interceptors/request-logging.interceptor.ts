import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallH<PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';

@Injectable()
export class RequestLoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(RequestLoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    const startTime = Date.now();

    const { method, url, headers, body, query, params } = request;
    const userAgent = headers['user-agent'] || '';
    const ip = this.getClientIp(request);
    const userId = (request as any).user?.id;

    // 记录请求开始
    this.logger.log(
      `Incoming Request: ${method} ${url}`,
      {
        method,
        url,
        ip,
        userAgent,
        userId,
        query: this.sanitizeData(query),
        params: this.sanitizeData(params),
        body: this.sanitizeData(body),
        headers: this.sanitizeHeaders(headers),
      },
    );

    return next.handle().pipe(
      tap((data) => {
        const duration = Date.now() - startTime;
        const { statusCode } = response;

        // 记录成功响应
        this.logger.log(
          `Outgoing Response: ${method} ${url} ${statusCode} - ${duration}ms`,
          {
            method,
            url,
            statusCode,
            duration,
            ip,
            userId,
            responseSize: this.getResponseSize(response),
          },
        );
      }),
      catchError((error) => {
        const duration = Date.now() - startTime;
        const statusCode = error.status || 500;

        // 记录错误响应
        this.logger.error(
          `Error Response: ${method} ${url} ${statusCode} - ${duration}ms`,
          {
            method,
            url,
            statusCode,
            duration,
            ip,
            userId,
            error: {
              name: error.name,
              message: error.message,
              stack: error.stack,
            },
          },
        );

        throw error;
      }),
    );
  }

  private getClientIp(request: Request): string {
    return (
      request.get('x-forwarded-for')?.split(',')[0]?.trim() ||
      request.get('x-real-ip') ||
      request.connection.remoteAddress ||
      '127.0.0.1'
    );
  }

  private getResponseSize(response: Response): number {
    const contentLength = response.get('content-length');
    return contentLength ? parseInt(contentLength, 10) : 0;
  }

  private sanitizeData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sensitiveFields = [
      'password',
      'token',
      'authorization',
      'cookie',
      'x-api-key',
      'secret',
      'key',
      'auth',
    ];

    const sanitized = { ...data };

    for (const field of sensitiveFields) {
      for (const key in sanitized) {
        if (key.toLowerCase().includes(field.toLowerCase())) {
          sanitized[key] = '[REDACTED]';
        }
      }
    }

    return sanitized;
  }

  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers };
    const sensitiveHeaders = [
      'authorization',
      'cookie',
      'x-api-key',
      'x-auth-token',
    ];

    for (const header of sensitiveHeaders) {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    }

    return sanitized;
  }
}
