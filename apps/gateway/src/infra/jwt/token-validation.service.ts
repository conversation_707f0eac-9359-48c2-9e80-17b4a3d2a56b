import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

// 类型定义
interface TokenPayload {
  sub: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  sessionId: string;
  deviceId?: string;
  scope: 'account' | 'character';
  iat: number;
  exp: number;
  jti: string;
}

interface CharacterTokenPayload extends TokenPayload {
  characterId: string;
  serverId: string;
  scope: 'character';
}

interface AccountTokenPayload extends TokenPayload {
  scope: 'account';
}

/**
 * Token验证服务
 * 负责双层Token的验证和解析
 * 
 * 核心功能：
 * 1. 账号Token验证：验证账号级Token的有效性
 * 2. 角色Token验证：验证角色级Token的有效性
 * 3. Token类型识别：自动识别Token类型并使用对应的验证逻辑
 * 4. 权限边界检查：确保Token只在其授权范围内使用
 */
@Injectable()
export class TokenValidationService {
  private readonly logger = new Logger(TokenValidationService.name);
  private readonly accountJwtSecret: string;
  private readonly characterJwtSecret: string;

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {
    // 获取双层Token的密钥配置
    this.accountJwtSecret = this.configService.get<string>('gateway.security.jwtSecret');
    this.characterJwtSecret = this.configService.get<string>('gateway.security.characterJwtSecret') || this.accountJwtSecret;
    
    if (!this.accountJwtSecret) {
      throw new Error('JWT secret not configured');
    }
  }

  /**
   * 验证Token（自动识别类型）
   */
  async validateToken(token: string): Promise<TokenPayload> {
    try {
      // 首先尝试解析Token获取基本信息（不验证签名）
      const decoded = this.jwtService.decode(token) as any;
      
      if (!decoded || !decoded.scope) {
        throw new UnauthorizedException('Invalid token format');
      }

      // 根据scope字段选择验证方法
      switch (decoded.scope) {
        case 'account':
          return await this.validateAccountToken(token);
        case 'character':
          return await this.validateCharacterToken(token);
        default:
          throw new UnauthorizedException(`Unsupported token scope: ${decoded.scope}`);
      }

    } catch (error) {
      this.logger.warn('Token validation failed', error);
      throw new UnauthorizedException('Invalid token');
    }
  }

  /**
   * 验证账号Token
   */
  async validateAccountToken(token: string): Promise<AccountTokenPayload> {
    try {
      const payload = this.jwtService.verify(token, {
        secret: this.accountJwtSecret,
      }) as AccountTokenPayload;

      // 验证Token类型
      if (payload.scope !== 'account') {
        throw new UnauthorizedException('Expected account token');
      }

      // 验证权限边界：账号Token不应包含角色信息
      if ((payload as any).characterId || (payload as any).serverId) {
        throw new UnauthorizedException('Account token should not contain character information');
      }

      // 验证必要字段
      this.validateRequiredFields(payload, ['sub', 'username', 'email', 'sessionId']);

      return payload;

    } catch (error) {
      this.logger.warn(`Account token validation failed: ${error.message}`);
      throw new UnauthorizedException('Invalid account token');
    }
  }

  /**
   * 验证角色Token
   */
  async validateCharacterToken(token: string): Promise<CharacterTokenPayload> {
    try {
      const payload = this.jwtService.verify(token, {
        secret: this.characterJwtSecret,
      }) as CharacterTokenPayload;

      // 验证Token类型
      if (payload.scope !== 'character') {
        throw new UnauthorizedException('Expected character token');
      }

      // 验证权限边界：角色Token必须包含角色和区服信息
      if (!payload.characterId || !payload.serverId) {
        throw new UnauthorizedException('Character token must contain character and server information');
      }

      // 验证必要字段
      this.validateRequiredFields(payload, ['sub', 'username', 'email', 'sessionId', 'characterId', 'serverId']);

      return payload;

    } catch (error) {
      this.logger.warn(`Character token validation failed: ${error.message}`);
      throw new UnauthorizedException('Invalid character token');
    }
  }

  /**
   * 检查Token是否为指定类型
   */
  isTokenType(token: string, expectedScope: 'account' | 'character'): boolean {
    try {
      const decoded = this.jwtService.decode(token) as any;
      return decoded && decoded.scope === expectedScope;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取Token信息（不验证签名）
   */
  getTokenInfo(token: string): Partial<TokenPayload> | null {
    try {
      const decoded = this.jwtService.decode(token) as any;
      return decoded || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 检查Token是否过期
   */
  isTokenExpired(token: string): boolean {
    try {
      const decoded = this.jwtService.decode(token) as any;
      if (!decoded || !decoded.exp) {
        return true;
      }

      const currentTime = Math.floor(Date.now() / 1000);
      return decoded.exp < currentTime;
    } catch (error) {
      return true;
    }
  }

  /**
   * 获取Token剩余有效时间（秒）
   */
  getTokenRemainingTime(token: string): number {
    try {
      const decoded = this.jwtService.decode(token) as any;
      if (!decoded || !decoded.exp) {
        return 0;
      }

      const currentTime = Math.floor(Date.now() / 1000);
      return Math.max(0, decoded.exp - currentTime);
    } catch (error) {
      return 0;
    }
  }

  /**
   * 验证Token权限
   */
  hasPermission(payload: TokenPayload, requiredPermission: string): boolean {
    return payload.permissions && payload.permissions.includes(requiredPermission);
  }

  /**
   * 验证Token角色
   */
  hasRole(payload: TokenPayload, requiredRole: string): boolean {
    return payload.roles && payload.roles.includes(requiredRole);
  }

  /**
   * 私有方法：验证必要字段
   */
  private validateRequiredFields(payload: any, requiredFields: string[]): void {
    for (const field of requiredFields) {
      if (!payload[field]) {
        throw new UnauthorizedException(`Missing required field: ${field}`);
      }
    }
  }

  /**
   * 私有方法：验证Token格式
   */
  private validateTokenFormat(payload: any): void {
    // 验证JWT标准字段
    if (!payload.iat || !payload.exp || !payload.jti) {
      throw new UnauthorizedException('Invalid JWT format');
    }

    // 验证时间戳
    if (payload.iat > payload.exp) {
      throw new UnauthorizedException('Invalid token timestamps');
    }

    // 验证过期时间
    const currentTime = Math.floor(Date.now() / 1000);
    if (payload.exp < currentTime) {
      throw new UnauthorizedException('Token expired');
    }
  }
}

// 导出类型定义
export { TokenPayload, CharacterTokenPayload, AccountTokenPayload };
