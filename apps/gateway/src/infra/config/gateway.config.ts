import { registerAs } from '@nestjs/config';

export interface GatewayConfig {
  port: number;
  enableSwagger: boolean;
  enableMetrics: boolean;
  enableHealthCheck: boolean;
  cors: {
    origin: string | string[];
    credentials: boolean;
    methods: string[];
    allowedHeaders: string[];
  };
  security: {
    enableHelmet: boolean;
    enableCompression: boolean;
    rateLimitWindowMs: number;
    rateLimitMax: number;
    jwtSecret: string;
    jwtExpiresIn: string;
    sessionSecret: string;
    sessionMaxAge: number;
  };
  proxy: {
    timeout: number;
    retries: number;
    retryDelay: number;
    keepAlive: boolean;
    maxSockets: number;
  };
  websocket: {
    pingTimeout: number;
    pingInterval: number;
    maxConnections: number;
    enableHeartbeat: boolean;
    heartbeatInterval: number;
  };
  monitoring: {
    enablePrometheus: boolean;
    enableJaeger: boolean;
    enableElk: boolean;
    metricsPath: string;
    healthPath: string;
  };

  // 新增：区服配置
  servers: Array<{
    id: string;
    name: string;
    status: 'active' | 'maintenance' | 'closed';
    openTime: string;
    maxPlayers: number;
    region: string;
    tags: string[];
    description: string;
    endpoint: string;
  }>;

  // 新增：区服发现配置
  serverDiscovery: {
    cacheTimeout: number;
    statusCheckInterval: number;
    healthCheckInterval: number;
    recommendationCacheTimeout: number;
  };
}

export default registerAs('gateway', (): GatewayConfig => ({
  port: parseInt(process.env.GATEWAY_PORT || '3000', 10),
  enableSwagger: process.env.GATEWAY_ENABLE_SWAGGER !== 'false',
  enableMetrics: process.env.GATEWAY_ENABLE_METRICS !== 'false',
  enableHealthCheck: process.env.GATEWAY_ENABLE_HEALTH_CHECK !== 'false',
  
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
    credentials: process.env.CORS_CREDENTIALS === 'true',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-API-Key',
      'X-Request-ID',
      'X-Forwarded-For',
      'User-Agent',
    ],
  },
  
  security: {
    enableHelmet: process.env.SECURITY_ENABLE_HELMET !== 'false',
    enableCompression: process.env.SECURITY_ENABLE_COMPRESSION !== 'false',
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000', 10),
    rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '100', 10),
    jwtSecret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
    sessionSecret: process.env.SESSION_SECRET || 'your-super-secret-session-key',
    sessionMaxAge: parseInt(process.env.SESSION_MAX_AGE || '86400000', 10), // 24 hours
  },
  
  proxy: {
    timeout: parseInt(process.env.PROXY_TIMEOUT || '30000', 10),
    retries: parseInt(process.env.PROXY_RETRIES || '3', 10),
    retryDelay: parseInt(process.env.PROXY_RETRY_DELAY || '1000', 10),
    keepAlive: process.env.PROXY_KEEP_ALIVE !== 'false',
    maxSockets: parseInt(process.env.PROXY_MAX_SOCKETS || '100', 10),
  },
  
  websocket: {
    pingTimeout: parseInt(process.env.WS_PING_TIMEOUT || '60000', 10),
    pingInterval: parseInt(process.env.WS_PING_INTERVAL || '25000', 10),
    maxConnections: parseInt(process.env.WS_MAX_CONNECTIONS || '10000', 10),
    enableHeartbeat: process.env.WS_ENABLE_HEARTBEAT !== 'false',
    heartbeatInterval: parseInt(process.env.WS_HEARTBEAT_INTERVAL || '30000', 10),
  },
  
  monitoring: {
    enablePrometheus: process.env.MONITORING_ENABLE_PROMETHEUS !== 'false',
    enableJaeger: process.env.MONITORING_ENABLE_JAEGER === 'true',
    enableElk: process.env.MONITORING_ENABLE_ELK === 'true',
    metricsPath: process.env.MONITORING_METRICS_PATH || '/metrics',
    healthPath: process.env.MONITORING_HEALTH_PATH || '/health',
  },

  // 区服配置
  servers: [
    {
      id: process.env.SERVER_001_ID || 'server_001',
      name: process.env.SERVER_001_NAME || '新手村',
      status: (process.env.SERVER_001_STATUS as any) || 'active',
      openTime: process.env.SERVER_001_OPEN_TIME || '2024-01-01',
      maxPlayers: parseInt(process.env.SERVER_001_MAX_PLAYERS || '10000', 10),
      region: process.env.SERVER_001_REGION || 'asia',
      tags: process.env.SERVER_001_TAGS?.split(',') || ['新手友好', '活跃'],
      description: process.env.SERVER_001_DESCRIPTION || '适合新手玩家的区服',
      endpoint: process.env.SERVER_001_ENDPOINT || 'http://localhost:3001',
    },
    {
      id: process.env.SERVER_002_ID || 'server_002',
      name: process.env.SERVER_002_NAME || '勇者大陆',
      status: (process.env.SERVER_002_STATUS as any) || 'active',
      openTime: process.env.SERVER_002_OPEN_TIME || '2024-02-01',
      maxPlayers: parseInt(process.env.SERVER_002_MAX_PLAYERS || '10000', 10),
      region: process.env.SERVER_002_REGION || 'asia',
      tags: process.env.SERVER_002_TAGS?.split(',') || ['竞技', '高手'],
      description: process.env.SERVER_002_DESCRIPTION || '高手云集的竞技区服',
      endpoint: process.env.SERVER_002_ENDPOINT || 'http://localhost:3002',
    },
  ],

  // 区服发现配置
  serverDiscovery: {
    cacheTimeout: parseInt(process.env.SERVER_CACHE_TIMEOUT || '300', 10), // 5分钟
    statusCheckInterval: parseInt(process.env.STATUS_CHECK_INTERVAL || '60', 10), // 1分钟
    healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '300', 10), // 5分钟
    recommendationCacheTimeout: parseInt(process.env.RECOMMENDATION_CACHE_TIMEOUT || '1800', 10), // 30分钟
  },
}));
