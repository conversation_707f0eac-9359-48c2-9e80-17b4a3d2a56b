import { registerAs } from '@nestjs/config';
import { MICROSERVICE_NAMES } from '@shared/constants';

export interface ServiceConfig {
  name: string;
  url: string;
  timeout: number;
  retries: number;
  healthCheck: {
    enabled: boolean;
    path: string;
    interval: number;
    timeout: number;
    unhealthyThreshold: number;
    healthyThreshold: number;
  };
  loadBalancer: {
    strategy: 'round-robin' | 'weighted' | 'least-connections' | 'ip-hash' | 'health-based';
    weight: number;
  };
  circuitBreaker: {
    enabled: boolean;
    failureThreshold: number;
    resetTimeout: number;
    monitoringPeriod: number;
  };
}

export interface ServicesConfig {
  discovery: {
    enabled: boolean;
    provider: 'consul' | 'etcd' | 'static';
    consulUrl?: string;
    etcdUrl?: string;
    refreshInterval: number;
  };
  services: Record<string, ServiceConfig>;
}

export default registerAs('services', (): ServicesConfig => ({
  discovery: {
    enabled: process.env.SERVICE_DISCOVERY_ENABLED === 'true',
    provider: (process.env.SERVICE_DISCOVERY_PROVIDER as any) || 'static',
    consulUrl: process.env.CONSUL_URL || 'http://localhost:8500',
    etcdUrl: process.env.ETCD_URL || 'http://localhost:2379',
    refreshInterval: parseInt(process.env.SERVICE_DISCOVERY_REFRESH_INTERVAL || '30000', 10),
  },
  
  services: {
    [MICROSERVICE_NAMES.AUTH_SERVICE]: {
      name: MICROSERVICE_NAMES.AUTH_SERVICE,
      url: process.env.AUTH_SERVICE_URL || 'http://127.0.0.1:3001',
      timeout: parseInt(process.env.AUTH_SERVICE_TIMEOUT || '5000', 10),
      retries: parseInt(process.env.AUTH_SERVICE_RETRIES || '3', 10),
      healthCheck: {
        enabled: true,
        path: '/health',
        interval: 30000,
        timeout: 5000,
        unhealthyThreshold: 3,
        healthyThreshold: 2,
      },
      loadBalancer: {
        strategy: 'round-robin',
        weight: 1,
      },
      circuitBreaker: {
        enabled: true,
        failureThreshold: 5,
        resetTimeout: 60000,
        monitoringPeriod: 10000,
      },
    },
    
    [MICROSERVICE_NAMES.CHARACTER_SERVICE]: {
      name: MICROSERVICE_NAMES.CHARACTER_SERVICE,
      url: process.env.CHARACTER_SERVICE_URL || 'http://127.0.0.1:3002',
      timeout: parseInt(process.env.CHARACTER_SERVICE_TIMEOUT || '5000', 10),
      retries: parseInt(process.env.CHARACTER_SERVICE_RETRIES || '3', 10),
      healthCheck: {
        enabled: true,
        path: '/health',
        interval: 30000,
        timeout: 5000,
        unhealthyThreshold: 3,
        healthyThreshold: 2,
      },
      loadBalancer: {
        strategy: 'round-robin',
        weight: 1,
      },
      circuitBreaker: {
        enabled: true,
        failureThreshold: 5,
        resetTimeout: 60000,
        monitoringPeriod: 10000,
      },
    },
    
    [MICROSERVICE_NAMES.CLUB_SERVICE]: {
      name: MICROSERVICE_NAMES.CLUB_SERVICE,
      url: process.env.CLUB_SERVICE_URL || 'http://127.0.0.1:3003',
      timeout: parseInt(process.env.CLUB_SERVICE_TIMEOUT || '5000', 10),
      retries: parseInt(process.env.CLUB_SERVICE_RETRIES || '3', 10),
      healthCheck: {
        enabled: true,
        path: '/health',
        interval: 30000,
        timeout: 5000,
        unhealthyThreshold: 3,
        healthyThreshold: 2,
      },
      loadBalancer: {
        strategy: 'round-robin',
        weight: 1,
      },
      circuitBreaker: {
        enabled: true,
        failureThreshold: 5,
        resetTimeout: 60000,
        monitoringPeriod: 10000,
      },
    },
    
    [MICROSERVICE_NAMES.MATCH_SERVICE]: {
      name: MICROSERVICE_NAMES.MATCH_SERVICE,
      url: process.env.MATCH_SERVICE_URL || 'http://127.0.0.1:3005',
      timeout: parseInt(process.env.MATCH_SERVICE_TIMEOUT || '10000', 10),
      retries: parseInt(process.env.MATCH_SERVICE_RETRIES || '2', 10),
      healthCheck: {
        enabled: true,
        path: '/health',
        interval: 30000,
        timeout: 5000,
        unhealthyThreshold: 3,
        healthyThreshold: 2,
      },
      loadBalancer: {
        strategy: 'least-connections',
        weight: 2, // 比赛服务权重更高
      },
      circuitBreaker: {
        enabled: true,
        failureThreshold: 3,
        resetTimeout: 30000,
        monitoringPeriod: 5000,
      },
    },
    
    [MICROSERVICE_NAMES.HERO_SERVICE]: {
      name: MICROSERVICE_NAMES.HERO_SERVICE,
      url: process.env.HERO_SERVICE_URL || 'http://127.0.0.1:3005',
      timeout: parseInt(process.env.HERO_SERVICE_TIMEOUT || '5000', 10),
      retries: parseInt(process.env.HERO_SERVICE_RETRIES || '3', 10),
      healthCheck: {
        enabled: true,
        path: '/health',
        interval: 30000,
        timeout: 5000,
        unhealthyThreshold: 3,
        healthyThreshold: 2,
      },
      loadBalancer: {
        strategy: 'round-robin',
        weight: 1,
      },
      circuitBreaker: {
        enabled: true,
        failureThreshold: 5,
        resetTimeout: 60000,
        monitoringPeriod: 10000,
      },
    },
    
    [MICROSERVICE_NAMES.CARD_SERVICE]: {
      name: MICROSERVICE_NAMES.CARD_SERVICE,
      url: process.env.CARD_SERVICE_URL || 'http://127.0.0.1:3006',
      timeout: parseInt(process.env.CARD_SERVICE_TIMEOUT || '5000', 10),
      retries: parseInt(process.env.CARD_SERVICE_RETRIES || '3', 10),
      healthCheck: {
        enabled: true,
        path: '/health',
        interval: 30000,
        timeout: 5000,
        unhealthyThreshold: 3,
        healthyThreshold: 2,
      },
      loadBalancer: {
        strategy: 'round-robin',
        weight: 1,
      },
      circuitBreaker: {
        enabled: true,
        failureThreshold: 5,
        resetTimeout: 60000,
        monitoringPeriod: 10000,
      },
    },
    
    [MICROSERVICE_NAMES.NOTIFICATION_SERVICE]: {
      name: MICROSERVICE_NAMES.NOTIFICATION_SERVICE,
      url: process.env.NOTIFICATION_SERVICE_URL || 'http://127.0.0.1:3007',
      timeout: parseInt(process.env.NOTIFICATION_SERVICE_TIMEOUT || '3000', 10),
      retries: parseInt(process.env.NOTIFICATION_SERVICE_RETRIES || '2', 10),
      healthCheck: {
        enabled: true,
        path: '/health',
        interval: 30000,
        timeout: 3000,
        unhealthyThreshold: 3,
        healthyThreshold: 2,
      },
      loadBalancer: {
        strategy: 'round-robin',
        weight: 1,
      },
      circuitBreaker: {
        enabled: true,
        failureThreshold: 5,
        resetTimeout: 60000,
        monitoringPeriod: 10000,
      },
    },
  },
}));
