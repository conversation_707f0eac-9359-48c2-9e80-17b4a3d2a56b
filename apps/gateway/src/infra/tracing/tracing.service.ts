import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request, Response } from 'express';
import { randomBytes } from 'crypto';

export interface TraceContext {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  baggage?: Record<string, string>;
  flags: number;
  sampled: boolean;
}

export interface Span {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  operationName: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  tags: Record<string, any>;
  logs: LogEntry[];
  status: 'ok' | 'error' | 'timeout';
  service: string;
  component: string;
}

export interface LogEntry {
  timestamp: Date;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  fields?: Record<string, any>;
}

export interface TracingConfig {
  enabled: boolean;
  serviceName: string;
  jaegerEndpoint?: string;
  samplingRate: number;
  maxSpansPerTrace: number;
  enableHttpTracing: boolean;
  enableDatabaseTracing: boolean;
  enableRedisTracing: boolean;
  enableExternalServiceTracing: boolean;
}

@Injectable()
export class TracingService {
  private readonly logger = new Logger(TracingService.name);
  private readonly config: TracingConfig;
  private readonly activeSpans = new Map<string, Span>();
  private readonly traces = new Map<string, Span[]>();

  constructor(private readonly configService: ConfigService) {
    this.config = {
      enabled: this.configService.get<boolean>('tracing.enabled', false),
      serviceName: this.configService.get<string>('tracing.serviceName', 'gateway'),
      jaegerEndpoint: this.configService.get<string>('tracing.jaegerEndpoint'),
      samplingRate: this.configService.get<number>('tracing.samplingRate', 0.1),
      maxSpansPerTrace: this.configService.get<number>('tracing.maxSpansPerTrace', 100),
      enableHttpTracing: this.configService.get<boolean>('tracing.enableHttpTracing', true),
      enableDatabaseTracing: this.configService.get<boolean>('tracing.enableDatabaseTracing', true),
      enableRedisTracing: this.configService.get<boolean>('tracing.enableRedisTracing', true),
      enableExternalServiceTracing: this.configService.get<boolean>('tracing.enableExternalServiceTracing', true),
    };

    if (this.config.enabled) {
      this.logger.log('Distributed tracing enabled');
    }
  }

  /**
   * 从 HTTP 请求中提取或创建追踪上下文
   */
  extractOrCreateTraceContext(req: Request): TraceContext {
    if (!this.config.enabled) {
      return this.createEmptyContext();
    }

    // 尝试从请求头中提取追踪信息
    const traceHeader = req.get('x-trace-id') || req.get('uber-trace-id');
    
    if (traceHeader) {
      return this.parseTraceHeader(traceHeader);
    }

    // 创建新的追踪上下文
    return this.createTraceContext();
  }

  /**
   * 注入追踪上下文到 HTTP 响应
   */
  injectTraceContext(res: Response, context: TraceContext): void {
    if (!this.config.enabled || !context.sampled) {
      return;
    }

    res.setHeader('x-trace-id', context.traceId);
    res.setHeader('x-span-id', context.spanId);
  }

  /**
   * 开始一个新的 Span
   */
  startSpan(
    operationName: string,
    parentContext?: TraceContext,
    tags?: Record<string, any>,
  ): Span {
    if (!this.config.enabled) {
      return this.createEmptySpan(operationName);
    }

    const context = parentContext || this.createTraceContext();
    
    if (!context.sampled) {
      return this.createEmptySpan(operationName);
    }

    const span: Span = {
      traceId: context.traceId,
      spanId: this.generateSpanId(),
      parentSpanId: context.spanId,
      operationName,
      startTime: new Date(),
      tags: {
        'span.kind': 'server',
        'component': 'gateway',
        'service.name': this.config.serviceName,
        ...tags,
      },
      logs: [],
      status: 'ok',
      service: this.config.serviceName,
      component: 'gateway',
    };

    this.activeSpans.set(span.spanId, span);
    this.addSpanToTrace(span);

    return span;
  }

  /**
   * 完成 Span
   */
  finishSpan(span: Span, status: 'ok' | 'error' | 'timeout' = 'ok'): void {
    if (!this.config.enabled || !span.spanId) {
      return;
    }

    span.endTime = new Date();
    span.duration = span.endTime.getTime() - span.startTime.getTime();
    span.status = status;

    this.activeSpans.delete(span.spanId);

    // 发送到 Jaeger
    this.sendSpanToJaeger(span);
  }

  /**
   * 为 Span 添加标签
   */
  setSpanTag(span: Span, key: string, value: any): void {
    if (!this.config.enabled || !span.spanId) {
      return;
    }

    span.tags[key] = value;
  }

  /**
   * 为 Span 添加日志
   */
  logToSpan(
    span: Span,
    level: 'info' | 'warn' | 'error' | 'debug',
    message: string,
    fields?: Record<string, any>,
  ): void {
    if (!this.config.enabled || !span.spanId) {
      return;
    }

    span.logs.push({
      timestamp: new Date(),
      level,
      message,
      fields,
    });
  }

  /**
   * 追踪 HTTP 请求
   */
  traceHttpRequest(req: Request, res: Response): Span {
    if (!this.config.enableHttpTracing) {
      return this.createEmptySpan('http_request');
    }

    const context = this.extractOrCreateTraceContext(req);
    const span = this.startSpan('http_request', context, {
      'http.method': req.method,
      'http.url': req.originalUrl || req.url,
      'http.user_agent': req.get('user-agent'),
      'http.remote_addr': this.getClientIp(req),
    });

    // 注入追踪上下文到响应
    this.injectTraceContext(res, {
      traceId: span.traceId,
      spanId: span.spanId,
      parentSpanId: span.parentSpanId,
      flags: context.flags,
      sampled: context.sampled,
    });

    return span;
  }

  /**
   * 追踪数据库操作
   */
  traceDatabaseOperation(
    operation: string,
    table: string,
    parentSpan?: Span,
  ): Span {
    if (!this.config.enableDatabaseTracing) {
      return this.createEmptySpan('db_operation');
    }

    const context = parentSpan ? {
      traceId: parentSpan.traceId,
      spanId: parentSpan.spanId,
      flags: 1,
      sampled: true,
    } : this.createTraceContext();

    return this.startSpan('db_operation', context, {
      'db.type': 'mongodb',
      'db.operation': operation,
      'db.collection': table,
      'span.kind': 'client',
    });
  }

  /**
   * 追踪 Redis 操作
   */
  traceRedisOperation(
    command: string,
    key: string,
    parentSpan?: Span,
  ): Span {
    if (!this.config.enableRedisTracing) {
      return this.createEmptySpan('redis_operation');
    }

    const context = parentSpan ? {
      traceId: parentSpan.traceId,
      spanId: parentSpan.spanId,
      flags: 1,
      sampled: true,
    } : this.createTraceContext();

    return this.startSpan('redis_operation', context, {
      'db.type': 'redis',
      'db.statement': command,
      'db.key': key,
      'span.kind': 'client',
    });
  }

  /**
   * 追踪外部服务调用
   */
  traceExternalService(
    serviceName: string,
    operation: string,
    url: string,
    parentSpan?: Span,
  ): Span {
    if (!this.config.enableExternalServiceTracing) {
      return this.createEmptySpan('external_service');
    }

    const context = parentSpan ? {
      traceId: parentSpan.traceId,
      spanId: parentSpan.spanId,
      flags: 1,
      sampled: true,
    } : this.createTraceContext();

    return this.startSpan('external_service', context, {
      'service.name': serviceName,
      'http.url': url,
      'http.method': operation,
      'span.kind': 'client',
    });
  }

  /**
   * 获取追踪统计信息
   */
  getTracingStats(): any {
    return {
      enabled: this.config.enabled,
      activeSpans: this.activeSpans.size,
      totalTraces: this.traces.size,
      samplingRate: this.config.samplingRate,
      config: this.config,
    };
  }

  /**
   * 获取活跃的追踪
   */
  getActiveTraces(): Record<string, Span[]> {
    const activeTraces: Record<string, Span[]> = {};
    
    for (const [traceId, spans] of this.traces) {
      if (spans.some(span => !span.endTime)) {
        activeTraces[traceId] = spans;
      }
    }
    
    return activeTraces;
  }

  // ==================== 私有方法 ====================

  private createTraceContext(): TraceContext {
    const sampled = Math.random() < this.config.samplingRate;
    
    return {
      traceId: this.generateTraceId(),
      spanId: this.generateSpanId(),
      flags: sampled ? 1 : 0,
      sampled,
    };
  }

  private createEmptyContext(): TraceContext {
    return {
      traceId: '',
      spanId: '',
      flags: 0,
      sampled: false,
    };
  }

  private createEmptySpan(operationName: string): Span {
    return {
      traceId: '',
      spanId: '',
      operationName,
      startTime: new Date(),
      tags: {},
      logs: [],
      status: 'ok',
      service: this.config.serviceName,
      component: 'gateway',
    };
  }

  private parseTraceHeader(header: string): TraceContext {
    try {
      // 解析 Uber 追踪头格式: traceId:spanId:parentSpanId:flags
      const parts = header.split(':');
      
      if (parts.length >= 4) {
        return {
          traceId: parts[0],
          spanId: parts[1],
          parentSpanId: parts[2] !== '0' ? parts[2] : undefined,
          flags: parseInt(parts[3], 10),
          sampled: (parseInt(parts[3], 10) & 1) === 1,
        };
      }
    } catch (error) {
      this.logger.warn('Failed to parse trace header:', error);
    }
    
    return this.createTraceContext();
  }

  private generateTraceId(): string {
    return randomBytes(16).toString('hex');
  }

  private generateSpanId(): string {
    return randomBytes(8).toString('hex');
  }

  private addSpanToTrace(span: Span): void {
    if (!this.traces.has(span.traceId)) {
      this.traces.set(span.traceId, []);
    }
    
    const spans = this.traces.get(span.traceId)!;
    spans.push(span);
    
    // 限制每个追踪的 Span 数量
    if (spans.length > this.config.maxSpansPerTrace) {
      spans.shift();
    }
  }

  private async sendSpanToJaeger(span: Span): Promise<void> {
    if (!this.config.jaegerEndpoint) {
      return;
    }

    try {
      // 这里应该实现发送到 Jaeger 的逻辑
      // 可以使用 jaeger-client 或直接发送 HTTP 请求
      
      this.logger.debug(`Span sent to Jaeger: ${span.operationName} (${span.duration}ms)`);
    } catch (error) {
      this.logger.error('Failed to send span to Jaeger:', error);
    }
  }

  private getClientIp(req: Request): string {
    return (
      req.get('x-forwarded-for')?.split(',')[0]?.trim() ||
      req.get('x-real-ip') ||
      req.connection.remoteAddress ||
      '127.0.0.1'
    );
  }
}
