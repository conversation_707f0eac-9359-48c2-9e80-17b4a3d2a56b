import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { RedisService } from '@common/redis';
import * as fs from 'fs';
import * as path from 'path';
import { debounce } from 'lodash';

export interface ConfigChangeEvent {
  key: string;
  oldValue: any;
  newValue: any;
  timestamp: Date;
  source: 'file' | 'redis' | 'api';
}

export interface ConfigValidationRule {
  key: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  enum?: any[];
  validator?: (value: any) => boolean | string;
}

export interface ConfigSnapshot {
  id: string;
  timestamp: Date;
  config: Record<string, any>;
  description?: string;
  version: string;
}

@Injectable()
export class ConfigManagerService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ConfigManagerService.name);
  private readonly configCache = new Map<string, any>();
  private readonly watchers = new Map<string, fs.FSWatcher>();
  private readonly validationRules = new Map<string, ConfigValidationRule>();
  private readonly snapshots: ConfigSnapshot[] = [];
  private redisSubscription: any;
  private readonly debouncedReload = debounce(this.reloadConfig.bind(this), 1000);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initializeValidationRules();
  }

  async onModuleInit() {
    await this.loadInitialConfig();
    await this.setupFileWatchers();
    await this.setupRedisSubscription();
    
    this.logger.log('Configuration manager initialized');
  }

  async onModuleDestroy() {
    // 清理文件监听器
    for (const [, watcher] of this.watchers) {
      watcher.close();
    }
    
    // 清理 Redis 订阅
    if (this.redisSubscription) {
      await this.redisSubscription.unsubscribe();
    }
  }

  /**
   * 获取配置值
   */
  get<T = any>(key: string, defaultValue?: T): T {
    // 优先从缓存获取
    if (this.configCache.has(key)) {
      return this.configCache.get(key);
    }
    
    // 从 ConfigService 获取
    const value = this.configService.get<T>(key, defaultValue);
    this.configCache.set(key, value);
    
    return value;
  }

  /**
   * 设置配置值
   */
  async set(key: string, value: any, source: 'file' | 'redis' | 'api' = 'api'): Promise<void> {
    // 验证配置值
    const validationResult = this.validateConfig(key, value);
    if (!validationResult.valid) {
      throw new Error(`Configuration validation failed for ${key}: ${validationResult.errors.join(', ')}`);
    }

    const oldValue = this.configCache.get(key);
    
    // 更新缓存
    this.configCache.set(key, value);
    
    // 根据来源更新持久化存储
    switch (source) {
      case 'redis':
        await this.updateRedisConfig(key, value);
        break;
      case 'file':
        await this.updateFileConfig(key, value);
        break;
      case 'api':
        // API 更新同时更新 Redis 和文件
        await this.updateRedisConfig(key, value);
        await this.updateFileConfig(key, value);
        break;
    }
    
    // 发出配置变更事件
    const changeEvent: ConfigChangeEvent = {
      key,
      oldValue,
      newValue: value,
      timestamp: new Date(),
      source,
    };
    
    this.eventEmitter.emit('config.changed', changeEvent);
    this.logger.log(`Configuration updated: ${key} = ${JSON.stringify(value)}`);
  }

  /**
   * 批量设置配置
   */
  async setBatch(configs: Record<string, any>, source: 'file' | 'redis' | 'api' = 'api'): Promise<void> {
    const validationErrors: string[] = [];
    
    // 验证所有配置
    for (const [key, value] of Object.entries(configs)) {
      const validationResult = this.validateConfig(key, value);
      if (!validationResult.valid) {
        validationErrors.push(`${key}: ${validationResult.errors.join(', ')}`);
      }
    }
    
    if (validationErrors.length > 0) {
      throw new Error(`Configuration validation failed: ${validationErrors.join('; ')}`);
    }
    
    // 批量更新
    for (const [key, value] of Object.entries(configs)) {
      await this.set(key, value, source);
    }
  }

  /**
   * 删除配置
   */
  async delete(key: string): Promise<void> {
    const oldValue = this.configCache.get(key);
    
    this.configCache.delete(key);
    await this.deleteRedisConfig(key);
    
    const changeEvent: ConfigChangeEvent = {
      key,
      oldValue,
      newValue: undefined,
      timestamp: new Date(),
      source: 'api',
    };
    
    this.eventEmitter.emit('config.changed', changeEvent);
    this.logger.log(`Configuration deleted: ${key}`);
  }

  /**
   * 获取所有配置
   */
  getAll(): Record<string, any> {
    const config: Record<string, any> = {};
    
    for (const [key, value] of this.configCache) {
      config[key] = value;
    }
    
    return config;
  }

  /**
   * 重载配置
   */
  async reloadConfig(): Promise<void> {
    this.logger.log('Reloading configuration...');
    
    try {
      await this.loadInitialConfig();
      this.eventEmitter.emit('config.reloaded', { timestamp: new Date() });
      this.logger.log('Configuration reloaded successfully');
    } catch (error) {
      this.logger.error('Failed to reload configuration:', error);
      throw error;
    }
  }

  /**
   * 创建配置快照
   */
  createSnapshot(description?: string): ConfigSnapshot {
    const snapshot: ConfigSnapshot = {
      id: this.generateSnapshotId(),
      timestamp: new Date(),
      config: this.getAll(),
      description,
      version: this.configService.get<string>('app.version', '1.0.0'),
    };
    
    this.snapshots.push(snapshot);
    
    // 保留最近 10 个快照
    if (this.snapshots.length > 10) {
      this.snapshots.shift();
    }
    
    this.logger.log(`Configuration snapshot created: ${snapshot.id}`);
    return snapshot;
  }

  /**
   * 恢复配置快照
   */
  async restoreSnapshot(snapshotId: string): Promise<void> {
    const snapshot = this.snapshots.find(s => s.id === snapshotId);
    
    if (!snapshot) {
      throw new Error(`Snapshot not found: ${snapshotId}`);
    }
    
    await this.setBatch(snapshot.config, 'api');
    this.logger.log(`Configuration restored from snapshot: ${snapshotId}`);
  }

  /**
   * 获取配置快照列表
   */
  getSnapshots(): ConfigSnapshot[] {
    return [...this.snapshots];
  }

  /**
   * 添加验证规则
   */
  addValidationRule(rule: ConfigValidationRule): void {
    this.validationRules.set(rule.key, rule);
  }

  /**
   * 验证配置
   */
  validateConfig(key: string, value: any): { valid: boolean; errors: string[] } {
    const rule = this.validationRules.get(key);
    const errors: string[] = [];
    
    if (!rule) {
      return { valid: true, errors: [] };
    }
    
    // 必填验证
    if (rule.required && (value === undefined || value === null)) {
      errors.push('Value is required');
      return { valid: false, errors };
    }
    
    if (value === undefined || value === null) {
      return { valid: true, errors: [] };
    }
    
    // 类型验证
    if (!this.validateType(value, rule.type)) {
      errors.push(`Expected type ${rule.type}, got ${typeof value}`);
    }
    
    // 数值范围验证
    if (rule.type === 'number' && typeof value === 'number') {
      if (rule.min !== undefined && value < rule.min) {
        errors.push(`Value must be >= ${rule.min}`);
      }
      if (rule.max !== undefined && value > rule.max) {
        errors.push(`Value must be <= ${rule.max}`);
      }
    }
    
    // 字符串长度验证
    if (rule.type === 'string' && typeof value === 'string') {
      if (rule.min !== undefined && value.length < rule.min) {
        errors.push(`String length must be >= ${rule.min}`);
      }
      if (rule.max !== undefined && value.length > rule.max) {
        errors.push(`String length must be <= ${rule.max}`);
      }
    }
    
    // 正则表达式验证
    if (rule.pattern && typeof value === 'string') {
      if (!rule.pattern.test(value)) {
        errors.push(`Value does not match pattern ${rule.pattern}`);
      }
    }
    
    // 枚举验证
    if (rule.enum && !rule.enum.includes(value)) {
      errors.push(`Value must be one of: ${rule.enum.join(', ')}`);
    }
    
    // 自定义验证器
    if (rule.validator) {
      const result = rule.validator(value);
      if (typeof result === 'string') {
        errors.push(result);
      } else if (!result) {
        errors.push('Custom validation failed');
      }
    }
    
    return { valid: errors.length === 0, errors };
  }

  // ==================== 私有方法 ====================

  private async loadInitialConfig(): Promise<void> {
    // 从文件加载配置
    await this.loadFileConfig();
    
    // 从 Redis 加载配置（覆盖文件配置）
    await this.loadRedisConfig();
  }

  private async loadFileConfig(): Promise<void> {
    const configFiles = [
      'gateway.config.ts',
      'services.config.ts',
    ];
    
    for (const file of configFiles) {
      try {
        const configPath = path.join(process.cwd(), 'src/config', file);
        if (fs.existsSync(configPath)) {
          // 这里应该动态加载配置文件
          // 为了简化，直接从 ConfigService 获取
          const config = this.configService.get(file.replace('.config.ts', ''));
          if (config) {
            this.mergeConfig(config);
          }
        }
      } catch (error) {
        this.logger.error(`Failed to load config file ${file}:`, error);
      }
    }
  }

  private async loadRedisConfig(): Promise<void> {
    try {
      const keys = await this.redisService.getClient().keys('config:*');
      
      for (const key of keys) {
        const configKey = key.replace('config:', '');
        const value = await this.redisService.get(key);
        
        if (value) {
          try {
            const parsedValue = JSON.parse(value as string);
            this.configCache.set(configKey, parsedValue);
          } catch (error) {
            this.configCache.set(configKey, value);
          }
        }
      }
    } catch (error) {
      this.logger.error('Failed to load Redis config:', error);
    }
  }

  private mergeConfig(config: Record<string, any>, prefix = ''): void {
    for (const [key, value] of Object.entries(config)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        this.mergeConfig(value, fullKey);
      } else {
        this.configCache.set(fullKey, value);
      }
    }
  }

  private async setupFileWatchers(): Promise<void> {
    const configDir = path.join(process.cwd(), 'src/config');
    
    if (!fs.existsSync(configDir)) {
      return;
    }
    
    const watcher = fs.watch(configDir, { recursive: true }, (eventType, filename) => {
      if (filename && filename.endsWith('.ts')) {
        this.logger.log(`Config file changed: ${filename}`);
        this.debouncedReload();
      }
    });
    
    this.watchers.set('config', watcher);
  }

  private async setupRedisSubscription(): Promise<void> {
    try {
      const subscriber = this.redisService.getClient().duplicate();
      await subscriber.subscribe('config:changed');
      
      subscriber.on('message', (channel, message) => {
        if (channel === 'config:changed') {
          try {
            const change = JSON.parse(message);
            this.handleRedisConfigChange(change);
          } catch (error) {
            this.logger.error('Failed to parse Redis config change:', error);
          }
        }
      });
      
      this.redisSubscription = subscriber;
    } catch (error) {
      this.logger.error('Failed to setup Redis subscription:', error);
    }
  }

  private async updateRedisConfig(key: string, value: any): Promise<void> {
    try {
      const redisKey = `config:${key}`;
      const serializedValue = JSON.stringify(value);
      
      await this.redisService.set(redisKey, serializedValue);
      
      // 发布配置变更通知
      await this.redisService.getClient().publish('config:changed', JSON.stringify({
        key,
        value,
        timestamp: new Date(),
      }));
    } catch (error) {
      this.logger.error(`Failed to update Redis config for ${key}:`, error);
    }
  }

  private async updateFileConfig(key: string, value: any): Promise<void> {
    // 这里可以实现文件配置更新逻辑
    // 为了简化，暂时跳过
  }

  private async deleteRedisConfig(key: string): Promise<void> {
    try {
      const redisKey = `config:${key}`;
      await this.redisService.del(redisKey);
    } catch (error) {
      this.logger.error(`Failed to delete Redis config for ${key}:`, error);
    }
  }

  private handleRedisConfigChange(change: any): void {
    const { key, value } = change;
    
    if (this.configCache.get(key) !== value) {
      this.configCache.set(key, value);
      
      const changeEvent: ConfigChangeEvent = {
        key,
        oldValue: this.configCache.get(key),
        newValue: value,
        timestamp: new Date(),
        source: 'redis',
      };
      
      this.eventEmitter.emit('config.changed', changeEvent);
    }
  }

  private validateType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'array':
        return Array.isArray(value);
      default:
        return true;
    }
  }

  private initializeValidationRules(): void {
    // 添加默认验证规则
    this.addValidationRule({
      key: 'gateway.port',
      type: 'number',
      required: true,
      min: 1,
      max: 65535,
    });
    
    this.addValidationRule({
      key: 'gateway.security.rateLimitMax',
      type: 'number',
      required: true,
      min: 1,
    });
    
    this.addValidationRule({
      key: 'gateway.security.jwtSecret',
      type: 'string',
      required: true,
      min: 32,
    });
  }

  private generateSnapshotId(): string {
    return `snapshot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
