import {Module} from '@nestjs/common';
import {PassportModule} from '@nestjs/passport';
import {ConfigModule} from '@nestjs/config';

import {UserController} from '../../modules/gateway-auth/controllers/user.controller';
import {UserService} from '../../modules/gateway-auth/services/user.service';
import {JwtStrategy} from '../../modules/gateway-auth/strategies/jwt.strategy';
import {ApiKeyStrategy} from '../../modules/gateway-auth/strategies/api-key.strategy';
import {AuthGuard} from '@gateway/modules/gateway-auth/dto/guards/auth.guard';
import {RolesGuard} from '@gateway/modules/gateway-auth/dto/guards/roles.guard';
import {PermissionsGuard} from '@gateway/modules/gateway-auth/dto/guards/permissions.guard';

// 使用共享模块
import {JwtSharedModule} from '../../shared/jwt/jwt-shared.module';

/**
 * 用户模块
 *
 * 提供完整的用户管理和认证功能，包括：
 * - 用户认证（JWT、API Key）
 * - 用户信息管理
 * - 角色权限控制
 * - 会话管理
 * - 密码管理
 * - 安全审计
 */
@Module({
  imports: [
    ConfigModule,

    // Passport 模块
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // 使用共享模块，避免重复配置
    JwtSharedModule,
  ],
  controllers: [UserController],
  providers: [
    UserService,
    JwtStrategy,
    ApiKeyStrategy,
    AuthGuard,
    RolesGuard,
    PermissionsGuard,
  ],
  exports: [
    UserService,
    AuthGuard,
    RolesGuard,
    PermissionsGuard,
    JwtSharedModule,  // 导出共享的 JWT 模块
    PassportModule,
  ],
})
export class UserModule {}
