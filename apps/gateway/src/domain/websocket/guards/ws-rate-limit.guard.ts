import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { WsException } from '@nestjs/websockets';
import { Socket } from 'socket.io';
import { RedisService } from '@common/redis';

/**
 * WebSocket 限流配置接口
 */
export interface WsRateLimitConfig {
  windowMs: number;    // 时间窗口（毫秒）
  max: number;         // 最大请求数
  keyGenerator?: (socket: Socket, event: string) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  message?: string;
}

/**
 * WebSocket 限流守卫
 *
 * 对 WebSocket 连接和消息进行限流控制，支持：
 * - 用户级限流
 * - IP 级限流
 * - 事件级限流
 * - 自定义限流配置
 * - 滑动窗口算法
 */
@Injectable()
export class WsRateLimitGuard implements CanActivate {
  private readonly logger = new Logger(WsRateLimitGuard.name);
  private readonly RATE_LIMIT_PREFIX = 'ws:rate_limit:';

  constructor(
    private readonly redisService: RedisService,
    private readonly reflector: Reflector,
  ) {}

  /**
   * 检查是否允许访问
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const client: Socket = context.switchToWs().getClient();
      const event = context.switchToWs().getPattern();

      // 检查是否跳过限流
      const skipRateLimit = this.reflector.getAllAndOverride<boolean>('skipRateLimit', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (skipRateLimit) {
        return true;
      }

      // 获取限流配置
      const rateLimitConfig = this.reflector.getAllAndOverride<WsRateLimitConfig>('wsRateLimit', [
        context.getHandler(),
        context.getClass(),
      ]) || this.getDefaultConfig();

      // 生成限流键
      const key = this.generateRateLimitKey(client, event, rateLimitConfig);

      // 检查限流
      const allowed = await this.checkRateLimit(key, rateLimitConfig);

      if (!allowed) {
        const message = rateLimitConfig.message || 'Too many requests';
        this.logger.warn(`Rate limit exceeded for ${key}`);
        throw new WsException(message);
      }

      return true;
    } catch (error) {
      if (error instanceof WsException) {
        throw error;
      }

      this.logger.error(`Rate limit check failed: ${error.message}`);
      // 在错误情况下允许请求，避免阻塞正常用户
      return true;
    }
  }

  /**
   * 检查限流
   */
  private async checkRateLimit(key: string, config: WsRateLimitConfig): Promise<boolean> {
    try {
      const client = this.redisService.getClient();
      const now = Date.now();
      const windowStart = now - config.windowMs;

      // 使用 Redis 的 ZSET 实现滑动窗口
      const pipeline = client.pipeline();

      // 移除过期的记录
      pipeline.zremrangebyscore(key, 0, windowStart);

      // 添加当前请求
      pipeline.zadd(key, now, `${now}-${Math.random()}`);

      // 获取当前窗口内的请求数
      pipeline.zcard(key);

      // 设置过期时间
      pipeline.expire(key, Math.ceil(config.windowMs / 1000));

      const results = await pipeline.exec();

      if (!results) {
        return true; // 如果 Redis 操作失败，允许请求
      }

      const requestCount = results[2][1] as number;

      return requestCount <= config.max;
    } catch (error) {
      this.logger.error(`Rate limit check error: ${error.message}`);
      return true; // 错误情况下允许请求
    }
  }

  /**
   * 生成限流键
   */
  private generateRateLimitKey(
    client: Socket,
    event: string,
    config: WsRateLimitConfig
  ): string {
    if (config.keyGenerator) {
      return `${this.RATE_LIMIT_PREFIX}${config.keyGenerator(client, event)}`;
    }

    // 默认键生成策略
    const userId = client.data?.user?.id;
    const ip = this.getClientIp(client);

    // 优先使用用户ID，其次使用IP
    const identifier = userId ? `user:${userId}` : `ip:${ip}`;

    return `${this.RATE_LIMIT_PREFIX}${identifier}:${event}`;
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): WsRateLimitConfig {
    return {
      windowMs: 60000,  // 1分钟
      max: 100,         // 100个请求
      message: 'Too many WebSocket requests',
    };
  }

  /**
   * 获取客户端 IP 地址
   */
  private getClientIp(client: Socket): string {
    const forwarded = client.handshake.headers['x-forwarded-for'];
    const realIp = client.handshake.headers['x-real-ip'];
    const remoteAddress = client.handshake.address;

    if (forwarded && typeof forwarded === 'string') {
      return forwarded.split(',')[0].trim();
    }

    if (realIp && typeof realIp === 'string') {
      return realIp;
    }

    return remoteAddress || 'unknown';
  }
}

// ==================== 装饰器 ====================

/**
 * 跳过 WebSocket 限流装饰器
 */
export const SkipWsRateLimit = () => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    if (descriptor) {
      Reflect.defineMetadata('skipRateLimit', true, descriptor.value);
    } else {
      Reflect.defineMetadata('skipRateLimit', true, target);
    }
  };
};

/**
 * WebSocket 限流配置装饰器
 */
export const WsRateLimit = (config: Partial<WsRateLimitConfig>) => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    const fullConfig = {
      windowMs: 60000,
      max: 100,
      ...config,
    };

    if (descriptor) {
      Reflect.defineMetadata('wsRateLimit', fullConfig, descriptor.value);
    } else {
      Reflect.defineMetadata('wsRateLimit', fullConfig, target);
    }
  };
};

/**
 * 公开 WebSocket 事件装饰器（跳过认证）
 */
export const PublicWsEvent = () => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    if (descriptor) {
      Reflect.defineMetadata('isPublic', true, descriptor.value);
    } else {
      Reflect.defineMetadata('isPublic', true, target);
    }
  };
};

/**
 * WebSocket 角色权限装饰器
 */
export const WsRoles = (...roles: string[]) => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    if (descriptor) {
      Reflect.defineMetadata('roles', roles, descriptor.value);
    } else {
      Reflect.defineMetadata('roles', roles, target);
    }
  };
};
