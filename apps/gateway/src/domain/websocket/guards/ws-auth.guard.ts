import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { WsException } from '@nestjs/websockets';
import { Socket } from 'socket.io';

/**
 * WebSocket 认证守卫
 *
 * 验证 WebSocket 连接的 JWT 令牌，确保只有经过认证的用户
 * 才能访问 WebSocket 端点
 */
@Injectable()
export class WsAuthGuard implements CanActivate {
  private readonly logger = new Logger(WsAuthGuard.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly reflector: Reflector,
  ) {}

  /**
   * 验证 WebSocket 连接的认证状态
   */
  canActivate(context: ExecutionContext): boolean {
    try {
      const client: Socket = context.switchToWs().getClient();

      // 检查是否为公开事件
      const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (isPublic) {
        return true;
      }

      const token = this.extractTokenFromSocket(client);

      if (!token) {
        this.logger.warn(`WebSocket authentication failed: No token provided`);
        throw new WsException('Authentication required');
      }

      // 验证 JWT 令牌
      const payload = this.jwtService.verify(token);

      // 将用户信息附加到 socket 对象
      client.data.user = {
        id: payload.sub,
        username: payload.username,
        roles: payload.roles || [],
        permissions: payload.permissions || [],
      };

      // 检查所需权限
      const requiredRoles = this.reflector.getAllAndOverride<string[]>('roles', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (requiredRoles && requiredRoles.length > 0) {
        const userRoles = payload.roles || [];
        const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

        if (!hasRequiredRole) {
          throw new WsException('Insufficient permissions');
        }
      }

      this.logger.debug(`WebSocket authentication successful for user: ${payload.sub}`);
      return true;

    } catch (error) {
      this.logger.error(`WebSocket authentication failed: ${error.message}`);

      if (error.name === 'JsonWebTokenError') {
        throw new WsException('Invalid token');
      } else if (error.name === 'TokenExpiredError') {
        throw new WsException('Token expired');
      } else if (error instanceof WsException) {
        throw error;
      } else {
        throw new WsException('Authentication failed');
      }
    }
  }

  /**
   * 从 Socket 连接中提取 JWT 令牌
   */
  private extractTokenFromSocket(client: Socket): string | null {
    // 方法1: 从 auth 对象中获取（推荐方式）
    const authToken = client.handshake.auth?.token;
    if (authToken && typeof authToken === 'string') {
      return authToken;
    }

    // 方法2: 从查询参数中获取
    const queryToken = client.handshake.query?.token;
    if (queryToken && typeof queryToken === 'string') {
      return queryToken;
    }

    // 方法3: 从认证头中获取
    const authHeader = client.handshake.headers?.authorization;
    if (authHeader && typeof authHeader === 'string') {
      const [type, token] = authHeader.split(' ');
      if (type === 'Bearer' && token) {
        return token;
      }
    }

    // 方法4: 从 Cookie 中获取
    const cookies = client.handshake.headers?.cookie;
    if (cookies) {
      const tokenMatch = cookies.match(/token=([^;]+)/);
      if (tokenMatch) {
        return tokenMatch[1];
      }
    }

    return null;
  }
}
