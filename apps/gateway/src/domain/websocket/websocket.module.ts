import {Module} from '@nestjs/common';
import {ConfigModule} from '@nestjs/config';

import {WebSocketGateway} from './websocket.gateway';
import {SessionService} from './session.service';
import {WsAuthGuard} from './guards/ws-auth.guard';
import {WsRateLimitGuard} from './guards/ws-rate-limit.guard';

// 使用共享模块
import {JwtSharedModule} from '../../infrastructure/jwt/jwt-shared.module';
import { RedisModule } from '@common/redis';

/**
 * WebSocket 模块
 * 
 * 提供实时通信功能，包括：
 * - WebSocket 连接管理
 * - 实时消息传递
 * - 房间管理
 * - 用户会话管理
 * - 微服务集成
 */
@Module({
  imports: [
    ConfigModule,

    // 使用共享模块，避免重复配置
    // 注意：修复了配置不一致问题（原来使用 JWT_SECRET，现在统一使用 gateway.security.jwtSecret）
    JwtSharedModule,

    // Redis模块，用于分布式锁和数据结构测试
    RedisModule,
  ],
  providers: [
    WebSocketGateway,
    SessionService,
    WsAuthGuard,
    WsRateLimitGuard,
  ],
  exports: [
    WebSocketGateway,
    SessionService,
  ],
})
export class WebSocketModule {}
