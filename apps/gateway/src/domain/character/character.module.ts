import { Module } from '@nestjs/common';
import { MicroserviceKitModule } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@shared/constants';
import { JwtSharedModule } from '../../infrastructure/jwt/jwt-shared.module';
import { CharacterController } from './controllers/character.controller';
import { CharacterService } from './services/character.service';

/**
 * 网关层角色模块
 * 
 * 职责：
 * 1. 处理角色相关的业务流程编排
 * 2. 协调Auth服务和Character服务的调用
 * 3. 提供统一的角色管理API接口
 * 4. 处理角色创建、登录的安全验证
 * 
 * 设计原则：
 * - 网关层负责业务流程编排
 * - Auth服务只负责Token生成和验证
 * - Character服务只负责角色数据管理
 * - 清晰的职责边界，避免服务间的紧耦合
 */
@Module({
  imports: [
    // 导入JWT共享模块，提供认证功能
    JwtSharedModule,

    // 导入微服务客户端，用于调用Auth和Character服务
    // 注意：这里使用forClient模式，因为网关需要调用其他微服务
    MicroserviceKitModule.forClient({
      services: [
        MICROSERVICE_NAMES.AUTH_SERVICE,        // 用于生成角色Token
        MICROSERVICE_NAMES.CHARACTER_SERVICE,   // 用于角色数据操作
      ],
    }),
  ],
  controllers: [
    CharacterController,
  ],
  providers: [
    CharacterService,
  ],
  exports: [
    CharacterService,
  ],
})
export class CharacterModule {}
