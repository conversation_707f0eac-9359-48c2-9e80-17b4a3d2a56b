import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

// 接口定义
export interface HttpRequestContext {
  userId?: string;
  serverId?: string;
  characterId?: string;
  tokenScope?: 'account' | 'character';
  sessionId?: string;
  deviceId?: string;
}

/**
 * HTTP请求上下文提取器
 * 从HTTP请求中提取区服和用户上下文信息
 * 
 * 核心功能：
 * 1. JWT Token解析：解析Authorization头中的JWT Token
 * 2. 区服信息提取：从角色Token中提取区服ID
 * 3. 用户信息提取：提取用户ID和会话信息
 * 4. 上下文验证：验证提取的上下文信息有效性
 */
@Injectable()
export class HttpContextExtractorService {
  private readonly logger = new Logger(HttpContextExtractorService.name);

  constructor(
    private readonly jwtService: JwtService,
  ) {}

  /**
   * 从HTTP请求中提取上下文
   */
  extractContext(req: Request): HttpRequestContext {
    try {
      // 提取Authorization头
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        this.logger.debug('No valid Authorization header found');
        return {};
      }

      // 提取JWT Token
      const token = authHeader.substring(7); // 移除 'Bearer ' 前缀
      if (!token) {
        this.logger.debug('No JWT token found in Authorization header');
        return {};
      }

      // 解析JWT Token
      const payload = this.jwtService.decode(token) as any;
      if (!payload) {
        this.logger.debug('Failed to decode JWT token');
        return {};
      }

      // 构建上下文
      const context: HttpRequestContext = {
        userId: payload.sub || payload.userId,
        tokenScope: payload.scope,
        sessionId: payload.sessionId,
        deviceId: payload.deviceId,
      };

      // 如果是角色Token，提取区服信息
      if (payload.scope === 'character') {
        context.serverId = payload.serverId;
        context.characterId = payload.characterId;
        
        this.logger.debug(`Extracted character context: serverId=${context.serverId}, characterId=${context.characterId}`);
      } else {
        this.logger.debug(`Extracted account context: userId=${context.userId}`);
      }

      return context;

    } catch (error) {
      this.logger.error('Failed to extract context from HTTP request:', error);
      return {};
    }
  }

  /**
   * 验证上下文是否有效
   */
  validateContext(context: HttpRequestContext): boolean {
    // 至少需要有用户ID
    if (!context.userId) {
      return false;
    }

    // 如果是角色Token，需要有区服和角色信息
    if (context.tokenScope === 'character') {
      return !!(context.serverId && context.characterId);
    }

    // 账号Token只需要用户ID
    return true;
  }

  /**
   * 检查请求是否需要区服路由
   */
  requiresServerRouting(req: Request, context: HttpRequestContext): boolean {
    // 如果有角色上下文，则需要区服路由
    if (context.tokenScope === 'character' && context.serverId) {
      return true;
    }

    // 检查请求路径是否包含需要区服路由的端点
    const path = req.path.toLowerCase();
    const serverRoutingPaths = [
      '/character/',
      '/hero/',
      '/formation/',
      '/inventory/',
      '/tactic/',
      '/match/',
      '/guild/',
    ];

    return serverRoutingPaths.some(routePath => path.includes(routePath));
  }

  /**
   * 为请求添加区服路由头
   */
  addServerRoutingHeaders(req: Request, context: HttpRequestContext): void {
    if (!context.serverId) {
      return;
    }

    // 添加区服路由头，供微服务使用
    req.headers['x-server-id'] = context.serverId;
    req.headers['x-character-id'] = context.characterId || '';
    req.headers['x-user-id'] = context.userId || '';
    req.headers['x-token-scope'] = context.tokenScope || 'account';

    this.logger.debug(`Added server routing headers: serverId=${context.serverId}, characterId=${context.characterId}`);
  }

  /**
   * 获取目标服务的区服实例
   */
  getServerSpecificServiceName(serviceName: string, serverId: string): string {
    // 对于需要区服路由的服务，返回带区服标识的服务名
    const serverRoutingServices = [
      'character',
      'hero', 
      'match',
      'guild',
    ];

    if (serverRoutingServices.includes(serviceName) && serverId) {
      // 返回格式：serviceName-serverId
      return `${serviceName}-${serverId}`;
    }

    // 其他服务保持原名
    return serviceName;
  }

  /**
   * 检查服务是否支持区服路由
   */
  supportsServerRouting(serviceName: string): boolean {
    const serverRoutingServices = [
      'character',
      'hero',
      'match', 
      'guild',
    ];

    return serverRoutingServices.includes(serviceName);
  }

  /**
   * 从请求中提取区服ID（备用方法）
   */
  extractServerIdFromRequest(req: Request): string | null {
    // 1. 从查询参数中提取
    if (req.query.serverId) {
      return req.query.serverId as string;
    }

    // 2. 从请求体中提取
    if (req.body && req.body.serverId) {
      return req.body.serverId;
    }

    // 3. 从路径参数中提取（如果路径包含serverId）
    const pathMatch = req.path.match(/\/server\/([^\/]+)/);
    if (pathMatch) {
      return pathMatch[1];
    }

    // 4. 从自定义头中提取
    if (req.headers['x-server-id']) {
      return req.headers['x-server-id'] as string;
    }

    return null;
  }

  /**
   * 生成上下文摘要（用于日志）
   */
  getContextSummary(context: HttpRequestContext): string {
    const parts = [];
    
    if (context.userId) {
      parts.push(`userId=${context.userId}`);
    }
    
    if (context.serverId) {
      parts.push(`serverId=${context.serverId}`);
    }
    
    if (context.characterId) {
      parts.push(`characterId=${context.characterId}`);
    }
    
    if (context.tokenScope) {
      parts.push(`scope=${context.tokenScope}`);
    }

    return parts.length > 0 ? `[${parts.join(', ')}]` : '[no-context]';
  }

  /**
   * 检查请求是否为跨服请求
   */
  isCrossServerRequest(req: Request): boolean {
    const path = req.path.toLowerCase();
    
    // 检查路径是否包含跨服标识
    if (path.includes('/cross-server/') || path.includes('/cross/')) {
      return true;
    }

    // 检查查询参数
    if (req.query.crossServer === 'true' || req.query.cross === 'true') {
      return true;
    }

    // 检查请求头
    if (req.headers['x-cross-server'] === 'true') {
      return true;
    }

    return false;
  }

  /**
   * 检查请求是否为全服请求
   */
  isGlobalRequest(req: Request): boolean {
    const path = req.path.toLowerCase();
    
    // 检查路径是否包含全服标识
    if (path.includes('/global/') || path.includes('/all-servers/')) {
      return true;
    }

    // 检查查询参数
    if (req.query.global === 'true' || req.query.allServers === 'true') {
      return true;
    }

    // 检查请求头
    if (req.headers['x-global-request'] === 'true') {
      return true;
    }

    return false;
  }
}
