import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { ProxyController } from './proxy.controller';
import { ProxyService } from './proxy.service';
import { CoreModule } from '../../core/shared/core.module';
import { MonitoringModule } from '@gateway/app/monitoring/monitoring.module';
import { RouteResolverService } from '../../core/router/route-resolver.service';

// 新增：区服路由支持服务
import { HttpContextExtractorService } from './services/http-context-extractor.service';
import { HttpRouteEnhancerService } from './services/http-route-enhancer.service';

/**
 * HTTP代理模块（支持区服路由）
 *
 * 提供核心的HTTP代理功能：
 * - HTTP代理控制器
 * - HTTP代理服务（支持区服路由）
 * - 请求上下文提取器
 * - 路由增强器
 * - 负载均衡集成
 * - 监控指标集成
 */
@Module({
  imports: [
    ConfigModule,
    CoreModule,
    MonitoringModule,
    // JWT模块用于Token解析
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('gateway.security.jwtSecret'),
        signOptions: {
          expiresIn: configService.get<string>('gateway.security.jwtExpiresIn', '24h'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [ProxyController],
  providers: [
    ProxyService,
    RouteResolverService,
    // 新增：区服路由支持服务
    HttpContextExtractorService,
    HttpRouteEnhancerService,
  ],
  exports: [
    ProxyService,
    RouteResolverService,
    HttpContextExtractorService,
    HttpRouteEnhancerService,
  ],
})
export class ProxyModule {}
