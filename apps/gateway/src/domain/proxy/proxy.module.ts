import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ProxyController } from './proxy.controller';
import { ProxyService } from './proxy.service';
import { CoreModule } from '../../core/shared/core.module';
import { MonitoringModule } from '@gateway/app/monitoring/monitoring.module';
import { RouteResolverService } from '../../core/router/route-resolver.service';

/**
 * 简化的代理模块
 * 
 * 提供核心的HTTP代理功能：
 * - 简化的代理控制器
 * - 简化的代理服务
 * - 负载均衡集成
 * - 监控指标集成
 */
@Module({
  imports: [
    ConfigModule,
    CoreModule,
    MonitoringModule,
  ],
  controllers: [ProxyController],
  providers: [
    ProxyService,
    RouteResolverService,
  ],
  exports: [
    ProxyService,
    RouteResolverService,
  ],
})
export class ProxyModule {}
