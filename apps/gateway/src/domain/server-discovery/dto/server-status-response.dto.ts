import { ApiProperty } from '@nestjs/swagger';

/**
 * 区服状态DTO
 */
export class ServerStatusDto {
  @ApiProperty({ description: '区服ID', example: 'server_001' })
  serverId: string;

  @ApiProperty({ description: '状态', enum: ['online', 'offline', 'maintenance'] })
  status: 'online' | 'offline' | 'maintenance';

  @ApiProperty({ description: '当前玩家数', example: 5000 })
  currentPlayers: number;

  @ApiProperty({ description: '最大玩家数', example: 10000 })
  maxPlayers: number;

  @ApiProperty({ description: 'CPU使用率(%)', example: 65.5 })
  cpuUsage: number;

  @ApiProperty({ description: '内存使用率(%)', example: 78.2 })
  memoryUsage: number;

  @ApiProperty({ description: '响应时间(ms)', example: 85 })
  responseTime: number;

  @ApiProperty({ description: '最后更新时间', example: '2024-01-15T10:30:00.000Z' })
  lastUpdate: Date;

  @ApiProperty({ description: '版本号', example: '1.0.0' })
  version?: string;

  @ApiProperty({ description: '运行时长(秒)', example: 86400 })
  uptime?: number;
}

/**
 * 区服健康检查DTO
 */
export class ServerHealthCheckDto {
  @ApiProperty({ description: '区服ID', example: 'server_001' })
  serverId: string;

  @ApiProperty({ description: '是否健康', example: true })
  isHealthy: boolean;

  @ApiProperty({ description: '检查项目' })
  checks: {
    database: boolean;
    redis: boolean;
    api: boolean;
    websocket: boolean;
  };

  @ApiProperty({ description: '最后检查时间', example: '2024-01-15T10:30:00.000Z' })
  lastCheck: Date;

  @ApiProperty({ description: '错误数量', example: 0 })
  errorCount: number;
}

/**
 * 区服性能指标DTO
 */
export class ServerMetricsDto {
  @ApiProperty({ description: '区服ID', example: 'server_001' })
  serverId: string;

  @ApiProperty({ description: '时间戳', example: '2024-01-15T10:30:00.000Z' })
  timestamp: Date;

  @ApiProperty({ description: '性能指标' })
  performance: {
    avgResponseTime: number;
    requestsPerSecond: number;
    errorRate: number;
    throughput: number;
  };

  @ApiProperty({ description: '资源使用' })
  resources: {
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    networkIO: number;
  };

  @ApiProperty({ description: '连接统计' })
  connections: {
    activeConnections: number;
    websocketConnections: number;
    databaseConnections: number;
  };
}

/**
 * 区服状态数据DTO
 */
export class ServerStatusDataDto {
  @ApiProperty({ description: '区服状态列表', type: [ServerStatusDto] })
  servers: ServerStatusDto[];

  @ApiProperty({ description: '总区服数量', example: 5 })
  totalServers: number;

  @ApiProperty({ description: '数据时间戳', example: '2024-01-15T10:30:00.000Z' })
  timestamp: Date;
}

/**
 * 区服状态响应DTO
 */
export class ServerStatusResponseDto {
  @ApiProperty({ description: '请求是否成功', example: true })
  success: boolean;

  @ApiProperty({ description: '响应消息', example: '获取区服状态成功' })
  message: string;

  @ApiProperty({ description: '区服状态数据', type: ServerStatusDataDto })
  data: ServerStatusDataDto | null;
}
