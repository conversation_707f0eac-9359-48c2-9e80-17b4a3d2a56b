import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

// 核心服务
import { RedisService } from '@common/redis';

// 接口定义
import { BaseGlobalMessage, GlobalMessageType, MessagePriority } from '../interfaces/global-message.interface';

// DTO
import { CreateEventNotificationDto, UpdateEventNotificationDto } from '../dto/event-notification.dto';

// 其他服务
import { GlobalBroadcastService } from './global-broadcast.service';
import { MessagePersistenceService } from './message-persistence.service';

/**
 * 活动通知接口（严格按照设计文档定义）
 */
export interface EventNotification extends BaseGlobalMessage {
  type: GlobalMessageType.EVENT_NOTIFICATION;
  eventId: string;
  eventType: 'cross_server' | 'global' | 'seasonal';
  description: string;
  startTime: Date;
  endTime: Date;
  rewards?: any[];
  requirements?: any;
  jumpToEvent?: boolean;
  registrationRequired?: boolean;
  maxParticipants?: number;
}

/**
 * 事件通知服务
 * 专门负责游戏活动通知的创建、管理和发布
 * 
 * 核心功能：
 * 1. 活动通知创建：创建各类活动通知
 * 2. 活动通知管理：编辑、删除、查询活动通知
 * 3. 活动通知发布：发布通知到指定目标
 * 4. 活动提醒：活动开始/结束提醒
 * 5. 参与统计：活动参与度统计
 */
@Injectable()
export class EventNotificationService {
  private readonly logger = new Logger(EventNotificationService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly globalBroadcastService: GlobalBroadcastService,
    private readonly messagePersistenceService: MessagePersistenceService,
  ) {}

  /**
   * 创建活动通知
   */
  async createEventNotification(createDto: CreateEventNotificationDto): Promise<EventNotification> {
    this.logger.log(`🎉 Creating event notification: ${createDto.title}`);

    try {
      const eventNotification: EventNotification = {
        id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: GlobalMessageType.EVENT_NOTIFICATION,
        priority: createDto.priority || MessagePriority.HIGH, // 活动通知默认高优先级
        title: createDto.title,
        content: createDto.content,
        createdAt: new Date(),
        publishAt: createDto.publishAt || new Date(),
        expireAt: createDto.expireAt || createDto.endTime,
        target: {
          scope: createDto.targetScope || 'global',
          servers: createDto.targetServers,
          conditions: createDto.targetConditions,
        },
        metadata: {
          eventId: createDto.eventId,
          eventType: createDto.eventType,
          jumpToEvent: createDto.jumpToEvent,
          registrationRequired: createDto.registrationRequired,
          maxParticipants: createDto.maxParticipants,
        },
        // 活动通知特有字段
        eventId: createDto.eventId,
        eventType: createDto.eventType,
        description: createDto.description,
        startTime: createDto.startTime,
        endTime: createDto.endTime,
        rewards: createDto.rewards,
        requirements: createDto.requirements,
        jumpToEvent: createDto.jumpToEvent,
        registrationRequired: createDto.registrationRequired,
        maxParticipants: createDto.maxParticipants,
      };

      // 持久化活动通知
      await this.messagePersistenceService.persistMessage(eventNotification);

      // 如果是立即发布，则发布通知
      if (eventNotification.publishAt <= new Date()) {
        await this.publishEventNotification(eventNotification);
      }

      // 调度活动开始和结束提醒
      await this.scheduleEventReminders(eventNotification);

      this.logger.log(`✅ Event notification created: ${eventNotification.id}`);
      return eventNotification;

    } catch (error) {
      this.logger.error(`❌ Failed to create event notification: ${createDto.title}`, error);
      throw error;
    }
  }

  /**
   * 更新活动通知
   */
  async updateEventNotification(notificationId: string, updateDto: UpdateEventNotificationDto): Promise<EventNotification> {
    this.logger.log(`📝 Updating event notification: ${notificationId}`);

    try {
      const existingNotification = await this.getEventNotification(notificationId);
      if (!existingNotification) {
        throw new Error(`Event notification not found: ${notificationId}`);
      }

      // 更新通知字段
      const updatedNotification: EventNotification = {
        ...existingNotification,
        title: updateDto.title || existingNotification.title,
        content: updateDto.content || existingNotification.content,
        description: updateDto.description || existingNotification.description,
        priority: updateDto.priority || existingNotification.priority,
        publishAt: updateDto.publishAt || existingNotification.publishAt,
        expireAt: updateDto.expireAt || existingNotification.expireAt,
        startTime: updateDto.startTime || existingNotification.startTime,
        endTime: updateDto.endTime || existingNotification.endTime,
        rewards: updateDto.rewards || existingNotification.rewards,
        requirements: updateDto.requirements || existingNotification.requirements,
        jumpToEvent: updateDto.jumpToEvent !== undefined ? updateDto.jumpToEvent : existingNotification.jumpToEvent,
        registrationRequired: updateDto.registrationRequired !== undefined ? updateDto.registrationRequired : existingNotification.registrationRequired,
        maxParticipants: updateDto.maxParticipants || existingNotification.maxParticipants,
      };

      // 更新target字段
      if (updateDto.targetScope || updateDto.targetServers || updateDto.targetConditions) {
        updatedNotification.target = {
          scope: updateDto.targetScope || existingNotification.target.scope,
          servers: updateDto.targetServers || existingNotification.target.servers,
          conditions: updateDto.targetConditions || existingNotification.target.conditions,
        };
      }

      // 更新metadata
      updatedNotification.metadata = {
        ...existingNotification.metadata,
        jumpToEvent: updatedNotification.jumpToEvent,
        registrationRequired: updatedNotification.registrationRequired,
        maxParticipants: updatedNotification.maxParticipants,
      };

      // 持久化更新
      await this.messagePersistenceService.persistMessage(updatedNotification);

      // 如果时间有变化，重新调度提醒
      if (updateDto.startTime || updateDto.endTime) {
        await this.scheduleEventReminders(updatedNotification);
      }

      this.logger.log(`✅ Event notification updated: ${notificationId}`);
      return updatedNotification;

    } catch (error) {
      this.logger.error(`❌ Failed to update event notification: ${notificationId}`, error);
      throw error;
    }
  }

  /**
   * 发布活动通知
   */
  async publishEventNotification(notification: EventNotification): Promise<void> {
    this.logger.log(`📤 Publishing event notification: ${notification.id}`);

    try {
      // 验证活动时间
      const now = new Date();
      if (notification.endTime < now) {
        throw new Error('Event has already ended');
      }

      // 使用全服广播服务发布
      await this.globalBroadcastService.broadcastMessage(notification);

      // 更新发布状态
      await this.messagePersistenceService.updateMessageStatus(
        notification.id,
        'published',
        { publishedAt: new Date() }
      );

      this.logger.log(`✅ Event notification published: ${notification.id}`);

    } catch (error) {
      this.logger.error(`❌ Failed to publish event notification: ${notification.id}`, error);
      throw error;
    }
  }

  /**
   * 获取活动通知
   */
  async getEventNotification(notificationId: string): Promise<EventNotification | null> {
    try {
      const message = await this.messagePersistenceService.getMessage(notificationId);
      
      if (!message || message.type !== GlobalMessageType.EVENT_NOTIFICATION) {
        return null;
      }

      return message as EventNotification;

    } catch (error) {
      this.logger.error(`❌ Failed to get event notification: ${notificationId}`, error);
      return null;
    }
  }

  /**
   * 获取活动通知列表
   */
  async getEventNotifications(filters: {
    eventType?: 'cross_server' | 'global' | 'seasonal';
    active?: boolean;
    upcoming?: boolean;
    limit?: number;
  } = {}): Promise<EventNotification[]> {
    try {
      const messages = await this.messagePersistenceService.getMessageHistory({
        type: GlobalMessageType.EVENT_NOTIFICATION,
        limit: filters.limit || 50,
      });

      let notifications = messages.filter(msg => msg.type === GlobalMessageType.EVENT_NOTIFICATION) as EventNotification[];

      // 应用过滤条件
      if (filters.eventType) {
        notifications = notifications.filter(n => n.eventType === filters.eventType);
      }

      const now = new Date();
      if (filters.active !== undefined) {
        notifications = notifications.filter(n => {
          const isActive = n.startTime <= now && n.endTime >= now;
          return filters.active ? isActive : !isActive;
        });
      }

      if (filters.upcoming !== undefined) {
        notifications = notifications.filter(n => {
          const isUpcoming = n.startTime > now;
          return filters.upcoming ? isUpcoming : !isUpcoming;
        });
      }

      return notifications;

    } catch (error) {
      this.logger.error('❌ Failed to get event notifications', error);
      return [];
    }
  }

  /**
   * 删除活动通知
   */
  async deleteEventNotification(notificationId: string): Promise<boolean> {
    this.logger.log(`🗑️ Deleting event notification: ${notificationId}`);

    try {
      const deleted = await this.messagePersistenceService.deleteMessage(notificationId);
      
      if (deleted) {
        // 取消相关的提醒调度
        await this.cancelEventReminders(notificationId);
        this.logger.log(`✅ Event notification deleted: ${notificationId}`);
      } else {
        this.logger.warn(`⚠️ Event notification not found for deletion: ${notificationId}`);
      }

      return deleted;

    } catch (error) {
      this.logger.error(`❌ Failed to delete event notification: ${notificationId}`, error);
      throw error;
    }
  }

  /**
   * 调度活动提醒
   */
  private async scheduleEventReminders(notification: EventNotification): Promise<void> {
    try {
      const now = new Date();
      
      // 活动开始前1小时提醒
      const startReminderTime = new Date(notification.startTime.getTime() - 60 * 60 * 1000);
      if (startReminderTime > now) {
        await this.scheduleReminder(notification, 'start_reminder', startReminderTime);
      }

      // 活动结束前30分钟提醒
      const endReminderTime = new Date(notification.endTime.getTime() - 30 * 60 * 1000);
      if (endReminderTime > now) {
        await this.scheduleReminder(notification, 'end_reminder', endReminderTime);
      }

    } catch (error) {
      this.logger.error(`❌ Failed to schedule event reminders: ${notification.id}`, error);
    }
  }

  /**
   * 调度单个提醒
   */
  private async scheduleReminder(notification: EventNotification, type: string, reminderTime: Date): Promise<void> {
    const reminderKey = `event_reminder:${notification.id}:${type}`;
    const reminderData = {
      notificationId: notification.id,
      type,
      reminderTime,
      eventTitle: notification.title,
      eventType: notification.eventType,
    };

    await this.redisService.zadd(
      'event_reminders',
      reminderTime.getTime(),
      JSON.stringify(reminderData),
      'global'
    );

    this.logger.debug(`⏰ Scheduled ${type} for event: ${notification.title} at ${reminderTime.toISOString()}`);
  }

  /**
   * 取消活动提醒
   */
  private async cancelEventReminders(notificationId: string): Promise<void> {
    try {
      const reminders = await this.redisService.zrange('event_reminders', 0, -1, 'global');
      
      for (const reminderData of reminders) {
        try {
          const reminder = JSON.parse(reminderData as string);
          if (reminder.notificationId === notificationId) {
            await this.redisService.zrem('event_reminders', reminderData as string, 'global');
          }
        } catch (parseError) {
          this.logger.error('❌ Failed to parse reminder data', parseError);
        }
      }

    } catch (error) {
      this.logger.error(`❌ Failed to cancel event reminders: ${notificationId}`, error);
    }
  }

  /**
   * 获取活动统计
   */
  async getEventStats(notificationId: string): Promise<any> {
    try {
      const stats = await this.globalBroadcastService.getMessageStats(notificationId);
      
      // 添加活动特有的统计信息
      const notification = await this.getEventNotification(notificationId);
      if (notification) {
        const now = new Date();
        return {
          ...stats,
          eventId: notification.eventId,
          eventType: notification.eventType,
          duration: notification.endTime.getTime() - notification.startTime.getTime(),
          isActive: notification.startTime <= now && notification.endTime >= now,
          isUpcoming: notification.startTime > now,
          hasEnded: notification.endTime < now,
          registrationRequired: notification.registrationRequired,
          maxParticipants: notification.maxParticipants,
        };
      }

      return stats;

    } catch (error) {
      this.logger.error(`❌ Failed to get event stats: ${notificationId}`, error);
      return null;
    }
  }
}
