import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

// 核心服务
import { RedisService } from '@common/redis';

// 接口定义
import {
  BaseGlobalMessage,
  MessagePriority,
  GlobalMessageType,
} from '../interfaces/global-message.interface';

// 其他服务
import { QueueManagerService } from '../queue/queue-manager.service';

/**
 * 消息发布服务
 * 负责全服消息的发布、调度和队列管理
 * 
 * 核心功能：
 * 1. 消息发布：将消息发布到Redis队列
 * 2. 优先级管理：根据消息优先级进行排队
 * 3. 延时发布：支持定时发布消息
 * 4. 批量发布：支持批量消息发布
 * 5. 发布状态跟踪：跟踪消息发布状态
 */
@Injectable()
export class MessagePublisherService {
  private readonly logger = new Logger(MessagePublisherService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly queueManagerService: QueueManagerService,
  ) {}

  /**
   * 发布单个消息
   */
  async publishMessage(message: BaseGlobalMessage): Promise<void> {
    this.logger.log(`📤 Publishing message: ${message.type} - ${message.title}`);

    try {
      // 验证消息
      this.validateMessage(message);

      // 检查是否需要延时发布
      if (message.publishAt > new Date()) {
        await this.scheduleDelayedMessage(message);
      } else {
        await this.publishImmediately(message);
      }

      // 记录发布日志
      await this.recordPublishLog(message);

      this.logger.log(`✅ Message published successfully: ${message.id}`);

    } catch (error) {
      this.logger.error(`❌ Failed to publish message: ${message.id}`, error);
      throw error;
    }
  }

  /**
   * 批量发布消息
   */
  async publishMessages(messages: BaseGlobalMessage[]): Promise<void> {
    this.logger.log(`📤 Publishing ${messages.length} messages`);

    const results = await Promise.allSettled(
      messages.map(message => this.publishMessage(message))
    );

    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;

    this.logger.log(`📊 Batch publish completed: ${successful} successful, ${failed} failed`);

    if (failed > 0) {
      const errors = results
        .filter(r => r.status === 'rejected')
        .map(r => (r as PromiseRejectedResult).reason);
      
      this.logger.error(`❌ Batch publish errors:`, errors);
    }
  }

  /**
   * 立即发布消息
   */
  private async publishImmediately(message: BaseGlobalMessage): Promise<void> {
    // 添加到优先级队列
    await this.queueManagerService.enqueue(message);

    // 发布到Redis频道进行实时广播
    const channel = this.getChannelByMessageType(message.type);
    await this.redisService.publish(channel, JSON.stringify(message));

    this.logger.debug(`📡 Message published to channel: ${channel}`);
  }

  /**
   * 调度延时消息
   */
  private async scheduleDelayedMessage(message: BaseGlobalMessage): Promise<void> {
    const delayMs = message.publishAt.getTime() - Date.now();
    
    // 将消息添加到延时队列
    const delayedQueueKey = 'global_messages:delayed';
    await this.redisService.zadd(
      delayedQueueKey,
      message.publishAt.getTime(),
      JSON.stringify(message),
      'global'
    );

    this.logger.debug(`⏰ Message scheduled for delayed publish: ${message.id}, delay: ${delayMs}ms`);
  }

  /**
   * 取消消息发布
   */
  async cancelMessage(messageId: string): Promise<boolean> {
    this.logger.log(`🚫 Cancelling message: ${messageId}`);

    try {
      // 从延时队列中移除
      const delayedQueueKey = 'global_messages:delayed';
      const delayedMessages = await this.redisService.zrange(delayedQueueKey, 0, -1, 'global');
      
      for (const messageData of delayedMessages) {
        const message = JSON.parse(messageData as string);
        if (message.id === messageId) {
          await this.redisService.zrem(delayedQueueKey, messageData as string, 'global');
          this.logger.log(`✅ Message cancelled from delayed queue: ${messageId}`);
          return true;
        }
      }

      // 从优先级队列中移除
      const cancelled = await this.queueManagerService.removeMessage(messageId);
      if (cancelled) {
        this.logger.log(`✅ Message cancelled from priority queue: ${messageId}`);
        return true;
      }

      this.logger.warn(`⚠️ Message not found for cancellation: ${messageId}`);
      return false;

    } catch (error) {
      this.logger.error(`❌ Failed to cancel message: ${messageId}`, error);
      throw error;
    }
  }

  /**
   * 获取发布统计信息
   */
  async getPublishStats(): Promise<any> {
    try {
      const stats = {
        totalPublished: 0,
        pendingDelayed: 0,
        queueSize: 0,
        publishRate: 0,
        lastPublishTime: null,
      };

      // 获取延时队列大小
      const delayedQueueKey = 'global_messages:delayed';
      stats.pendingDelayed = await this.redisService.zcard(delayedQueueKey, 'global');

      // 获取优先级队列大小
      stats.queueSize = await this.queueManagerService.getQueueSize();

      // 获取发布统计（从Redis获取）
      const publishStatsKey = 'global_messages:publish_stats';
      const publishStatsData = await this.redisService.get(publishStatsKey, 'global');
      if (publishStatsData) {
        const publishStats = JSON.parse(publishStatsData as string);
        stats.totalPublished = publishStats.totalPublished || 0;
        stats.publishRate = publishStats.publishRate || 0;
        stats.lastPublishTime = publishStats.lastPublishTime;
      }

      return stats;

    } catch (error) {
      this.logger.error('❌ Failed to get publish stats', error);
      return null;
    }
  }

  /**
   * 验证消息格式
   */
  private validateMessage(message: BaseGlobalMessage): void {
    if (!message.id || !message.type || !message.title || !message.content) {
      throw new Error('Invalid message format: missing required fields');
    }

    if (!message.target || !message.target.scope) {
      throw new Error('Invalid message format: missing target configuration');
    }

    if (message.publishAt && message.expireAt && message.publishAt >= message.expireAt) {
      throw new Error('Invalid message format: publishAt must be before expireAt');
    }
  }

  /**
   * 根据消息类型获取频道
   */
  private getChannelByMessageType(type: GlobalMessageType): string {
    switch (type) {
      case GlobalMessageType.SYSTEM_ANNOUNCEMENT:
        return 'global_messages:announcement';
      case GlobalMessageType.EMERGENCY_ALERT:
        return 'global_messages:emergency';
      case GlobalMessageType.MAINTENANCE_NOTICE:
        return 'global_messages:maintenance';
      case GlobalMessageType.EVENT_NOTIFICATION:
        return 'global_messages:event';
      case GlobalMessageType.CROSS_SERVER_EVENT:
        return 'global_messages:cross_server';
      case GlobalMessageType.MARKETING_MESSAGE:
        return 'global_messages:marketing';
      default:
        return 'global_messages:broadcast';
    }
  }

  /**
   * 记录发布日志
   */
  private async recordPublishLog(message: BaseGlobalMessage): Promise<void> {
    const logKey = `publish_log:${message.id}`;
    const logData = {
      messageId: message.id,
      type: message.type,
      priority: message.priority,
      publishedAt: new Date(),
      publishAt: message.publishAt,
      channel: this.getChannelByMessageType(message.type),
    };

    await this.redisService.set(logKey, JSON.stringify(logData), 3600 * 24 * 30, 'global');

    // 更新发布统计
    await this.updatePublishStats();
  }

  /**
   * 更新发布统计
   */
  private async updatePublishStats(): Promise<void> {
    const publishStatsKey = 'global_messages:publish_stats';
    
    try {
      const currentStats = await this.redisService.get(publishStatsKey, 'global');
      const stats = currentStats ? JSON.parse(currentStats as string) : {
        totalPublished: 0,
        publishRate: 0,
        lastPublishTime: null,
        hourlyCount: 0,
        lastHourReset: Date.now(),
      };

      // 更新统计数据
      stats.totalPublished += 1;
      stats.lastPublishTime = new Date();

      // 计算每小时发布率
      const now = Date.now();
      if (now - stats.lastHourReset > 3600000) { // 1小时
        stats.publishRate = stats.hourlyCount;
        stats.hourlyCount = 1;
        stats.lastHourReset = now;
      } else {
        stats.hourlyCount += 1;
      }

      await this.redisService.set(publishStatsKey, JSON.stringify(stats), 3600 * 24 * 7, 'global');

    } catch (error) {
      this.logger.error('❌ Failed to update publish stats', error);
    }
  }
}
