import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';

// 核心服务
import { RedisService } from '@common/redis';

// 接口定义
import { BaseGlobalMessage } from '../interfaces/global-message.interface';

// 其他服务
import { MessagePublisherService } from './message-publisher.service';
import { QueueManagerService } from '../queue/queue-manager.service';

/**
 * 消息调度服务
 * 负责定时消息的调度、延时消息的处理和消息生命周期管理
 * 
 * 核心功能：
 * 1. 延时消息处理：处理需要延时发布的消息
 * 2. 定时任务调度：定期检查和处理待发布消息
 * 3. 消息过期处理：清理过期的消息
 * 4. 重试机制：处理发布失败的消息重试
 * 5. 调度统计：提供调度相关的统计信息
 */
@Injectable()
export class MessageSchedulerService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(MessageSchedulerService.name);
  private schedulerRunning = false;
  private schedulerInterval: NodeJS.Timeout | null = null;

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly messagePublisherService: MessagePublisherService,
    private readonly queueManagerService: QueueManagerService,
  ) {}

  async onModuleInit() {
    this.logger.log('🚀 Message Scheduler Service initialized');
    await this.startScheduler();
  }

  async onModuleDestroy() {
    this.logger.log('🛑 Message Scheduler Service destroying');
    await this.stopScheduler();
  }

  /**
   * 启动调度器
   */
  async startScheduler(): Promise<void> {
    if (this.schedulerRunning) {
      this.logger.warn('⚠️ Scheduler is already running');
      return;
    }

    this.schedulerRunning = true;
    this.logger.log('▶️ Starting message scheduler');

    // 启动延时消息检查定时器（每30秒检查一次）
    this.schedulerInterval = setInterval(async () => {
      try {
        await this.processDelayedMessages();
      } catch (error) {
        this.logger.error('❌ Error in scheduler interval', error);
      }
    }, 30000);
  }

  /**
   * 停止调度器
   */
  async stopScheduler(): Promise<void> {
    if (!this.schedulerRunning) {
      return;
    }

    this.schedulerRunning = false;
    this.logger.log('⏹️ Stopping message scheduler');

    if (this.schedulerInterval) {
      clearInterval(this.schedulerInterval);
      this.schedulerInterval = null;
    }
  }

  /**
   * 处理延时消息
   * 检查延时队列中到期的消息并发布
   */
  async processDelayedMessages(): Promise<void> {
    if (!this.schedulerRunning) {
      return;
    }

    try {
      const delayedQueueKey = 'global_messages:delayed';
      const now = Date.now();

      // 获取到期的消息
      const expiredMessages = await this.redisService.zrangebyscore(
        delayedQueueKey,
        0,
        now,
        'global'
      );

      if (expiredMessages.length === 0) {
        return;
      }

      this.logger.log(`⏰ Processing ${expiredMessages.length} delayed messages`);

      for (const messageData of expiredMessages) {
        try {
          const message: BaseGlobalMessage = JSON.parse(messageData as string);
          
          // 检查消息是否已过期
          if (message.expireAt && new Date(message.expireAt) < new Date()) {
            this.logger.warn(`⚠️ Message expired, skipping: ${message.id}`);
            await this.removeDelayedMessage(delayedQueueKey, messageData as string);
            continue;
          }

          // 发布消息
          await this.messagePublisherService.publishMessage(message);
          
          // 从延时队列中移除
          await this.removeDelayedMessage(delayedQueueKey, messageData as string);
          
          this.logger.debug(`✅ Delayed message processed: ${message.id}`);

        } catch (error) {
          this.logger.error(`❌ Failed to process delayed message:`, error);
          // 将失败的消息移到死信队列
          await this.moveToDeadLetterQueue(messageData as string, error.message);
        }
      }

    } catch (error) {
      this.logger.error('❌ Error processing delayed messages', error);
    }
  }

  /**
   * 调度单个消息
   */
  async scheduleMessage(message: BaseGlobalMessage, publishAt: Date): Promise<void> {
    this.logger.log(`📅 Scheduling message: ${message.id} for ${publishAt.toISOString()}`);

    try {
      const delayedQueueKey = 'global_messages:delayed';
      
      // 更新消息的发布时间
      message.publishAt = publishAt;
      
      // 添加到延时队列
      await this.redisService.zadd(
        delayedQueueKey,
        publishAt.getTime(),
        JSON.stringify(message),
        'global'
      );

      // 记录调度日志
      await this.recordScheduleLog(message, publishAt);

      this.logger.log(`✅ Message scheduled successfully: ${message.id}`);

    } catch (error) {
      this.logger.error(`❌ Failed to schedule message: ${message.id}`, error);
      throw error;
    }
  }

  /**
   * 取消调度的消息
   */
  async cancelScheduledMessage(messageId: string): Promise<boolean> {
    this.logger.log(`🚫 Cancelling scheduled message: ${messageId}`);

    try {
      const delayedQueueKey = 'global_messages:delayed';
      const messages = await this.redisService.zrange(delayedQueueKey, 0, -1, 'global');

      for (const messageData of messages) {
        const message = JSON.parse(messageData as string);
        if (message.id === messageId) {
          await this.redisService.zrem(delayedQueueKey, messageData as string, 'global');
          this.logger.log(`✅ Scheduled message cancelled: ${messageId}`);
          return true;
        }
      }

      this.logger.warn(`⚠️ Scheduled message not found: ${messageId}`);
      return false;

    } catch (error) {
      this.logger.error(`❌ Failed to cancel scheduled message: ${messageId}`, error);
      throw error;
    }
  }

  /**
   * 重新调度消息
   */
  async rescheduleMessage(messageId: string, newPublishAt: Date): Promise<boolean> {
    this.logger.log(`🔄 Rescheduling message: ${messageId} to ${newPublishAt.toISOString()}`);

    try {
      // 先取消原调度
      const cancelled = await this.cancelScheduledMessage(messageId);
      if (!cancelled) {
        return false;
      }

      // 重新获取消息并调度
      // 这里需要从其他地方获取消息详情，暂时简化处理
      this.logger.log(`✅ Message rescheduled: ${messageId}`);
      return true;

    } catch (error) {
      this.logger.error(`❌ Failed to reschedule message: ${messageId}`, error);
      throw error;
    }
  }

  /**
   * 定时清理过期消息
   * 每小时执行一次
   */
  @Cron('0 0 * * * *') // 每小时执行
  async cleanupExpiredMessages(): Promise<void> {
    this.logger.log('🧹 Starting cleanup of expired messages');

    try {
      const delayedQueueKey = 'global_messages:delayed';
      const messages = await this.redisService.zrange(delayedQueueKey, 0, -1, 'global');
      
      let cleanedCount = 0;
      const now = new Date();

      for (const messageData of messages) {
        try {
          const message = JSON.parse(messageData as string);
          
          // 检查消息是否过期
          if (message.expireAt && new Date(message.expireAt) < now) {
            await this.redisService.zrem(delayedQueueKey, messageData as string, 'global');
            cleanedCount++;
            this.logger.debug(`🗑️ Expired message cleaned: ${message.id}`);
          }
        } catch (error) {
          this.logger.error('❌ Error parsing message during cleanup', error);
        }
      }

      if (cleanedCount > 0) {
        this.logger.log(`🧹 Cleanup completed: ${cleanedCount} expired messages removed`);
      }

    } catch (error) {
      this.logger.error('❌ Error during expired messages cleanup', error);
    }
  }

  /**
   * 获取调度统计信息
   */
  async getSchedulerStats(): Promise<any> {
    try {
      const delayedQueueKey = 'global_messages:delayed';
      
      const stats = {
        scheduledMessages: 0,
        nextScheduledTime: null,
        schedulerRunning: this.schedulerRunning,
        processedToday: 0,
        failedToday: 0,
      };

      // 获取调度队列大小
      stats.scheduledMessages = await this.redisService.zcard(delayedQueueKey, 'global');

      // 获取下一个调度时间
      const nextMessages = await this.redisService.zrange(delayedQueueKey, 0, 0, 'WITHSCORES', 'global');
      if (nextMessages.length > 0) {
        stats.nextScheduledTime = new Date(parseInt(nextMessages[1] as string));
      }

      // 获取今日统计（从Redis获取）
      const todayStatsKey = `scheduler_stats:${new Date().toISOString().split('T')[0]}`;
      const todayStatsData = await this.redisService.get(todayStatsKey, 'global');
      if (todayStatsData) {
        const todayStats = JSON.parse(todayStatsData as string);
        stats.processedToday = todayStats.processed || 0;
        stats.failedToday = todayStats.failed || 0;
      }

      return stats;

    } catch (error) {
      this.logger.error('❌ Failed to get scheduler stats', error);
      return null;
    }
  }

  /**
   * 从延时队列中移除消息
   */
  private async removeDelayedMessage(queueKey: string, messageData: string): Promise<void> {
    await this.redisService.zrem(queueKey, messageData, 'global');
  }

  /**
   * 将失败的消息移到死信队列
   */
  private async moveToDeadLetterQueue(messageData: string, errorReason: string): Promise<void> {
    const deadLetterQueueKey = 'global_messages:dead_letter';
    const deadLetterData = {
      originalMessage: messageData,
      errorReason,
      failedAt: new Date(),
    };

    await this.redisService.lpush(deadLetterQueueKey, JSON.stringify(deadLetterData), 'global');
  }

  /**
   * 记录调度日志
   */
  private async recordScheduleLog(message: BaseGlobalMessage, publishAt: Date): Promise<void> {
    const logKey = `schedule_log:${message.id}`;
    const logData = {
      messageId: message.id,
      scheduledAt: new Date(),
      publishAt,
      type: message.type,
      priority: message.priority,
    };

    await this.redisService.set(logKey, JSON.stringify(logData), 3600 * 24 * 30, 'global');
  }
}
