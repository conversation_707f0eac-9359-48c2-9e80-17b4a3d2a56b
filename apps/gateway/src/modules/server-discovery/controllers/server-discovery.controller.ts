import { 
  Controller, 
  Get, 
  Query,
  UseGuards,
  Logger,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';

// 守卫和装饰器
import { JwtAuthGuard } from '../../gateway-auth/guards/jwt-auth.guard';
import { CurrentUser } from '../../../shared/decorators/current-user.decorator';
import { AccountToken } from '../../../shared/decorators/token-scope.decorator';

// 服务
import { ServerListService } from '../services/server-list.service';
import { ServerStatusService } from '../services/server-status.service';
import { ServerRecommendationService } from '../services/server-recommendation.service';

// DTO
import { ServerListResponseDto } from '../dto/server-list-response.dto';
import { ServerStatusResponseDto } from '../dto/server-status-response.dto';
import { ServerRecommendationResponseDto } from '../dto/server-recommendation-response.dto';

// 类型定义
interface User {
  id: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  sessionId: string;
  deviceId?: string;
  tokenScope: 'account' | 'character';
}

/**
 * 区服发现控制器
 * 提供区服列表、状态查询、智能推荐等HTTP接口
 * 
 * 接口设计原则：
 * 1. 职责清晰：专注于区服发现相关功能
 * 2. 权限控制：只允许账号Token访问，不涉及具体游戏业务
 * 3. 性能优化：合理使用缓存，避免重复计算
 * 4. 用户体验：提供个性化的推荐和详细的状态信息
 */
@ApiTags('区服发现')
@Controller('servers')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@AccountToken() // 只允许账号Token访问
export class ServerDiscoveryController {
  private readonly logger = new Logger(ServerDiscoveryController.name);

  constructor(
    private readonly serverListService: ServerListService,
    private readonly serverStatusService: ServerStatusService,
    private readonly serverRecommendationService: ServerRecommendationService,
  ) {}

  /**
   * 获取区服列表
   * GET /api/gateway/servers
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: '获取区服列表',
    description: '获取当前可用的区服列表，包含用户历史记录和推荐信息'
  })
  @ApiQuery({
    name: 'includeRecommendations',
    required: false,
    type: Boolean,
    description: '是否包含推荐信息',
  })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功',
    type: ServerListResponseDto
  })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async getServerList(
    @CurrentUser() user: User,
    @Query('includeRecommendations') includeRecommendations?: boolean
  ): Promise<ServerListResponseDto> {
    this.logger.log(`获取区服列表请求: ${user.id}, includeRecommendations=${includeRecommendations}`);

    try {
      // 获取基础区服列表
      const serverList = await this.serverListService.getServerListForUser(user.id);
      
      // 如果需要推荐信息，获取详细推荐
      let detailedRecommendations = null;
      if (includeRecommendations) {
        try {
          const recommendationContext = {
            userId: user.id,
            region: 'asia', // 可以从用户配置获取
          };
          
          detailedRecommendations = await this.serverRecommendationService.recommendServersForUser(recommendationContext);
        } catch (error) {
          this.logger.warn(`获取推荐信息失败: ${user.id}`, error);
          // 推荐失败不影响主要功能
        }
      }

      return {
        success: true,
        message: '获取区服列表成功',
        data: {
          ...serverList,
          detailedRecommendations,
        },
      };

    } catch (error) {
      this.logger.error(`获取区服列表失败: ${user.id}`, error);
      throw error;
    }
  }

  /**
   * 获取区服状态
   * GET /api/gateway/servers/status
   */
  @Get('status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: '获取区服状态',
    description: '获取所有区服的实时状态信息'
  })
  @ApiQuery({
    name: 'serverId',
    required: false,
    type: String,
    description: '指定区服ID，不指定则返回所有区服状态',
  })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功',
    type: ServerStatusResponseDto
  })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async getServerStatus(
    @CurrentUser() user: User,
    @Query('serverId') serverId?: string
  ): Promise<ServerStatusResponseDto> {
    this.logger.log(`获取区服状态请求: ${user.id}, serverId=${serverId || 'all'}`);

    try {
      let statusData;

      if (serverId) {
        // 获取指定区服状态
        const status = await this.serverStatusService.getServerStatus(serverId);
        if (!status) {
          return {
            success: false,
            message: '区服不存在或状态获取失败',
            data: null,
          };
        }

        statusData = {
          servers: [status],
          totalServers: 1,
          timestamp: new Date(),
        };
      } else {
        // 获取所有区服状态
        const allStatuses = await this.serverStatusService.getAllServerStatuses();
        const servers = Array.from(allStatuses.values());

        statusData = {
          servers,
          totalServers: servers.length,
          timestamp: new Date(),
        };
      }

      return {
        success: true,
        message: '获取区服状态成功',
        data: statusData,
      };

    } catch (error) {
      this.logger.error(`获取区服状态失败: ${user.id}`, error);
      throw error;
    }
  }

  /**
   * 获取区服推荐
   * GET /api/gateway/servers/recommendations
   */
  @Get('recommendations')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: '获取区服推荐',
    description: '基于用户画像和区服状态的智能推荐'
  })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: ['general', 'new_player', 'returning_player'],
    description: '推荐类型',
  })
  @ApiQuery({
    name: 'region',
    required: false,
    type: String,
    description: '首选区域',
  })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功',
    type: ServerRecommendationResponseDto
  })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async getServerRecommendations(
    @CurrentUser() user: User,
    @Query('type') type: 'general' | 'new_player' | 'returning_player' = 'general',
    @Query('region') region?: string
  ): Promise<ServerRecommendationResponseDto> {
    this.logger.log(`获取区服推荐请求: ${user.id}, type=${type}, region=${region}`);

    try {
      const context = {
        userId: user.id,
        region: region || 'asia',
        // 可以从请求头获取更多上下文信息
      };

      let recommendations;

      switch (type) {
        case 'new_player':
          const newPlayerRecs = await this.serverRecommendationService.recommendForNewPlayer(context);
          recommendations = {
            primary: newPlayerRecs[0] || null,
            alternatives: newPlayerRecs.slice(1, 4),
            explanation: '为新手玩家推荐的区服',
            timestamp: new Date(),
          };
          break;

        case 'returning_player':
          const returningPlayerRecs = await this.serverRecommendationService.recommendForReturningPlayer(context);
          recommendations = {
            primary: returningPlayerRecs[0] || null,
            alternatives: returningPlayerRecs.slice(1, 4),
            explanation: '为回流玩家推荐的区服',
            timestamp: new Date(),
          };
          break;

        default:
          recommendations = await this.serverRecommendationService.recommendServersForUser(context);
          break;
      }

      return {
        success: true,
        message: '获取区服推荐成功',
        data: recommendations,
      };

    } catch (error) {
      this.logger.error(`获取区服推荐失败: ${user.id}`, error);
      throw error;
    }
  }

  /**
   * 获取区服详细信息
   * GET /api/gateway/servers/:serverId
   */
  @Get(':serverId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: '获取区服详细信息',
    description: '获取指定区服的详细信息，包括状态、历史、推荐原因等'
  })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功',
    type: Object
  })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 404, description: '区服不存在' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async getServerDetails(
    @CurrentUser() user: User,
    @Query('serverId') serverId: string
  ): Promise<any> {
    this.logger.log(`获取区服详细信息: ${user.id} -> ${serverId}`);

    try {
      // 并发获取多种信息
      const [serverList, status, health, metrics] = await Promise.allSettled([
        this.serverListService.getServerListForUser(user.id),
        this.serverStatusService.getServerStatus(serverId),
        this.serverStatusService.checkServerHealth(serverId),
        this.serverStatusService.getServerMetrics(serverId),
      ]);

      // 查找目标区服
      const serverInfo = serverList.status === 'fulfilled'
        ? serverList.value.servers.find(s => s.id === serverId)
        : null;

      if (!serverInfo) {
        return {
          success: false,
          message: '区服不存在',
          data: null,
        };
      }

      // 构建详细信息
      const details = {
        basic: serverInfo,
        status: status.status === 'fulfilled' ? status.value : null,
        health: health.status === 'fulfilled' ? health.value : null,
        metrics: metrics.status === 'fulfilled' ? metrics.value : null,
        timestamp: new Date(),
      };

      return {
        success: true,
        message: '获取区服详细信息成功',
        data: details,
      };

    } catch (error) {
      this.logger.error(`获取区服详细信息失败: ${user.id} -> ${serverId}`, error);
      throw error;
    }
  }

  /**
   * 健康检查
   * GET /api/gateway/servers/health
   */
  @Get('health')
  @ApiOperation({ 
    summary: '健康检查',
    description: '检查区服发现服务的健康状态'
  })
  @ApiResponse({ 
    status: 200, 
    description: '服务正常',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string', format: 'date-time' },
        service: { type: 'string', example: 'server-discovery' },
        version: { type: 'string', example: '1.0.0' },
      },
    }
  })
  async healthCheck(): Promise<any> {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'server-discovery',
      version: '1.0.0',
      uptime: process.uptime(),
    };
  }
}
