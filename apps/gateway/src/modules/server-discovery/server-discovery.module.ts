import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// 核心模块
import { RedisModule } from '@common/redis';
import { JwtSharedModule } from '../../shared/jwt/jwt-shared.module';

// 微服务通信（暂时注释，后续集成时启用）
// import { MicroserviceKitModule } from '@common/microservice-kit';
// import { MICROSERVICE_NAMES } from '@shared/constants';

// 服务
import { ServerListService } from './services/server-list.service';
import { ServerStatusService } from './services/server-status.service';
import { ServerRecommendationService } from './services/server-recommendation.service';

// 控制器
import { ServerDiscoveryController } from './controllers/server-discovery.controller';

/**
 * 区服发现模块
 * 负责区服列表管理、状态监控、智能推荐等功能
 * 
 * 职责说明：
 * - 区服列表管理：获取、缓存、更新可用区服列表
 * - 区服状态监控：实时监控区服状态、负载、可用性
 * - 智能推荐：基于用户历史和区服状态的推荐算法
 * - 负载均衡：区服间的负载分配和路由决策
 */
@Module({
  imports: [
    ConfigModule,
    RedisModule,
    JwtSharedModule, // 用于JWT认证

    // 微服务通信：需要调用Auth服务获取用户历史（暂时注释）
    // MicroserviceKitModule.forClient({
    //   services: [
    //     MICROSERVICE_NAMES.AUTH_SERVICE,
    //     MICROSERVICE_NAMES.CHARACTER_SERVICE,
    //   ],
    // }),
  ],
  controllers: [
    ServerDiscoveryController,
  ],
  providers: [
    ServerListService,
    ServerStatusService,
    ServerRecommendationService,
  ],
  exports: [
    ServerListService,
    ServerStatusService,
    ServerRecommendationService,
  ],
})
export class ServerDiscoveryModule {}
