import { Module } from '@nestjs/common';

// 共享模块
import { SharedModule } from '../../shared/shared.module';

// 认证模块
import { GatewayAuthModule } from '../gateway-auth/gateway-auth.module';

// 控制器
import { ServerDiscoveryController } from './controllers/server-discovery.controller';

// 服务
import { ServerListService } from './services/server-list.service';
import { ServerStatusService } from './services/server-status.service';
import { ServerRecommendationService } from './services/server-recommendation.service';

/**
 * 区服发现功能模块
 *
 * 职责：
 * - 管理区服列表和状态
 * - 提供智能区服推荐
 * - 监控区服负载和可用性
 *
 * 依赖：
 * - SharedModule：微服务通信、缓存服务
 * - GatewayAuthModule：用户认证
 */
@Module({
  imports: [
    SharedModule,
    GatewayAuthModule,
  ],
  controllers: [
    ServerDiscoveryController,
  ],
  providers: [
    ServerListService,
    ServerStatusService,
    ServerRecommendationService,
  ],
  exports: [
    ServerListService,
    ServerStatusService,
    ServerRecommendationService,
  ],
})
export class ServerDiscoveryModule {}
