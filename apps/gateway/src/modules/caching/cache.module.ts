import { Module } from '@nestjs/common';

// 导入缓存服务
import { GatewayCacheService } from './services/gateway-cache.service';

/**
 * 缓存模块
 * 
 * 提供网关缓存核心功能，包括：
 * - 响应结果缓存
 * - 缓存策略管理
 * - 缓存失效和更新
 * - 分布式缓存支持
 * 
 * 职责范围：
 * - 缓存后端服务的响应结果
 * - 减少重复请求对后端的压力
 * - 提供灵活的缓存策略配置
 * - 支持缓存预热和主动失效
 */
@Module({
  providers: [
    GatewayCacheService,
  ],
  exports: [
    GatewayCacheService,
  ],
})
export class CacheModule {}
