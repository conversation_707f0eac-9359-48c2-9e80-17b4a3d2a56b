import { Module } from '@nestjs/common';

// 共享模块
import { SharedModule } from '../../shared/shared.module';

// 缓存服务
import { GatewayCacheService } from './services/gateway-cache.service';

/**
 * 缓存功能模块
 * 
 * 职责：
 * - 提供网关层缓存服务
 * - 管理缓存策略和配置
 * - 提供缓存装饰器和工具
 * 
 * 依赖：
 * - SharedModule：基础设施服务
 */
@Module({
  imports: [
    SharedModule,
  ],
  providers: [
    GatewayCacheService,
  ],
  exports: [
    GatewayCacheService,
  ],
})
export class CachingModule {}
