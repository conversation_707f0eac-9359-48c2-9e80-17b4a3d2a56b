import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RedisModule } from '@common/redis';

// 缓存服务
import { GatewayCacheService } from './services/gateway-cache.service';

/**
 * 缓存功能模块
 *
 * 职责：
 * - 提供网关层缓存服务
 * - 管理缓存策略和配置
 * - 提供缓存装饰器和工具
 *
 * 依赖分析：
 * - ConfigModule：获取缓存配置参数
 * - RedisModule：缓存存储后端
 */
@Module({
  imports: [
    ConfigModule,
    RedisModule,
  ],
  providers: [
    GatewayCacheService,
  ],
  exports: [
    GatewayCacheService,
  ],
})
export class CachingModule {}
