import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// 认证模块
import { GatewayAuthModule } from '../gateway-auth/gateway-auth.module';

// 控制器
import { MonitoringController } from './controllers/monitoring.controller';

// 服务
import { MetricsService } from './services/metrics.service';
import { AlertingService } from './services/alerting.service';
import { TracingService } from './services/tracing.service';

/**
 * 监控功能模块
 *
 * 职责：
 * - 提供系统监控和指标收集
 * - 实现告警和通知机制
 * - 支持分布式链路追踪
 *
 * 依赖：
 * - SharedModule：基础设施服务
 * - GatewayAuthModule：监控接口认证
 */
@Module({
  imports: [
    ConfigModule,
    GatewayAuthModule,
  ],
  controllers: [
    MonitoringController,
  ],
  providers: [
    MetricsService,
    AlertingService,
    TracingService,
  ],
  exports: [
    MetricsService,
    AlertingService,
    TracingService,
  ],
})
export class MonitoringModule {}
