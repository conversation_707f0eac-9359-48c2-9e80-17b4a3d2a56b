import { Module } from '@nestjs/common';

// JWT共享模块
import { JwtSharedModule } from '../../shared/jwt/jwt-shared.module';

// 认证模块
import { GatewayAuthModule } from '../gateway-auth/gateway-auth.module';

// 控制器
import { CharacterController } from './controllers/character.controller';

// 服务
import { CharacterService } from './services/character.service';

/**
 * 角色管理功能模块
 *
 * 职责：
 * - 处理角色相关的业务流程编排
 * - 协调Auth服务和Character服务的调用
 * - 提供统一的角色管理API接口
 *
 * 依赖分析：
 * - JwtSharedModule：JWT服务
 * - GatewayAuthModule：用户认证
 */
@Module({
  imports: [
    JwtSharedModule,
    GatewayAuthModule,
  ],
  controllers: [
    CharacterController,
  ],
  providers: [
    CharacterService,
  ],
  exports: [
    CharacterService,
  ],
})
export class CharacterModule {}
