import { Injectable, Logger, UnauthorizedException, ForbiddenException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@common/redis';
import {
  User,
  AuthToken,
  AuthContext,
  AuthSession,
  ApiKey,
  AuthRequest,
  AuthResponse,
  Permission,
  Role,
  SecurityEvent,
  AuthMetrics,
  DeviceInfo,
} from '../../../shared/interfaces/auth.interface';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly jwtSecret: string;
  private readonly jwtExpiresIn: string;
  private readonly sessionMaxAge: number;

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {
    this.jwtSecret = this.configService.get<string>('gateway.security.jwtSecret');
    this.jwtExpiresIn = this.configService.get<string>('gateway.security.jwtExpiresIn');
    this.sessionMaxAge = this.configService.get<number>('gateway.security.sessionMaxAge');
  }

  /**
   * 验证 JWT Token
   */
  async validateJwtToken(token: string): Promise<AuthContext> {
    try {
      const payload = this.jwtService.verify(token, { secret: this.jwtSecret });
      
      // 检查 token 是否在黑名单中
      const isBlacklisted = await this.isTokenBlacklisted(token);
      if (isBlacklisted) {
        throw new UnauthorizedException('Token has been revoked');
      }

      // 获取用户信息
      const user = await this.getUserById(payload.sub);
      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      // 检查用户状态
      if (user.status !== 'active') {
        throw new UnauthorizedException(`User account is ${user.status}`);
      }

      return {
        user,
        token: {
          accessToken: token,
          tokenType: 'Bearer',
          expiresIn: payload.exp - Math.floor(Date.now() / 1000),
          expiresAt: new Date(payload.exp * 1000),
          scope: payload.scope || [],
        },
        authenticated: true,
        authMethod: 'jwt',
        permissions: user.permissions,
        roles: user.roles,
        metadata: {
          tokenId: payload.jti,
          issuedAt: new Date(payload.iat * 1000),
          deviceId: payload.deviceId,
        },
      };
    } catch (error) {
      this.logger.warn(`JWT validation failed: ${error.message}`);
      throw new UnauthorizedException('Invalid token');
    }
  }

  /**
   * 验证 API Key
   */
  async validateApiKey(apiKey: string, ipAddress: string): Promise<AuthContext> {
    try {
      const keyInfo = await this.getApiKeyInfo(apiKey);
      
      if (!keyInfo || !keyInfo.enabled) {
        throw new UnauthorizedException('Invalid API key');
      }

      // 检查过期时间
      if (keyInfo.expiresAt && keyInfo.expiresAt < new Date()) {
        throw new UnauthorizedException('API key has expired');
      }

      // 检查 IP 白名单
      if (keyInfo.ipWhitelist && keyInfo.ipWhitelist.length > 0) {
        if (!keyInfo.ipWhitelist.includes(ipAddress)) {
          throw new UnauthorizedException('IP address not allowed');
        }
      }

      // 更新最后使用时间
      await this.updateApiKeyLastUsed(keyInfo.id);

      // 获取用户信息（如果有关联用户）
      let user: User | undefined;
      if (keyInfo.userId) {
        user = await this.getUserById(keyInfo.userId);
      }

      return {
        user,
        apiKey: keyInfo,
        authenticated: true,
        authMethod: 'apikey',
        permissions: keyInfo.permissions,
        roles: user?.roles || [],
        metadata: {
          apiKeyId: keyInfo.id,
          scopes: keyInfo.scopes,
        },
      };
    } catch (error) {
      this.logger.warn(`API key validation failed: ${error.message}`);
      throw new UnauthorizedException('Invalid API key');
    }
  }

  /**
   * 验证会话
   */
  async validateSession(sessionId: string, ipAddress: string, userAgent: string): Promise<AuthContext> {
    try {
      const session = await this.getSession(sessionId);
      
      if (!session) {
        throw new UnauthorizedException('Invalid session');
      }

      // 检查会话是否过期
      if (session.expiresAt < new Date()) {
        await this.deleteSession(sessionId);
        throw new UnauthorizedException('Session has expired');
      }

      // 检查 IP 地址（可选的安全检查）
      const strictIpCheck = this.configService.get<boolean>('gateway.security.strictIpCheck', false);
      if (strictIpCheck && session.ipAddress !== ipAddress) {
        await this.logSecurityEvent({
          type: 'suspicious_activity',
          severity: 'medium',
          userId: session.userId,
          ipAddress,
          userAgent,
          details: {
            reason: 'IP address mismatch',
            sessionIp: session.ipAddress,
            requestIp: ipAddress,
          },
        });
        throw new UnauthorizedException('Session IP mismatch');
      }

      // 获取用户信息
      const user = await this.getUserById(session.userId);
      if (!user) {
        await this.deleteSession(sessionId);
        throw new UnauthorizedException('User not found');
      }

      // 检查用户状态
      if (user.status !== 'active') {
        await this.deleteSession(sessionId);
        throw new UnauthorizedException(`User account is ${user.status}`);
      }

      // 更新会话最后访问时间
      await this.updateSessionLastAccess(sessionId);

      return {
        user,
        session,
        authenticated: true,
        authMethod: 'session',
        permissions: user.permissions,
        roles: user.roles,
        metadata: {
          sessionId: session.id,
          deviceId: session.deviceId,
          deviceInfo: session.deviceInfo,
        },
      };
    } catch (error) {
      this.logger.warn(`Session validation failed: ${error.message}`);
      throw new UnauthorizedException('Invalid session');
    }
  }

  /**
   * 检查权限
   */
  async checkPermission(
    context: AuthContext,
    resource: string,
    action: string,
    conditions?: Record<string, any>,
  ): Promise<boolean> {
    if (!context.authenticated) {
      return false;
    }

    // 超级管理员拥有所有权限
    if (context.roles.includes('super_admin')) {
      return true;
    }

    // 检查直接权限
    const requiredPermission = `${resource}:${action}`;
    if (context.permissions.includes(requiredPermission) || context.permissions.includes('*')) {
      return true;
    }

    // 检查角色权限
    for (const roleName of context.roles) {
      const role = await this.getRole(roleName);
      if (role && role.permissions.includes(requiredPermission)) {
        return true;
      }
    }

    // 检查条件权限（ABAC）
    if (conditions) {
      return await this.checkConditionalPermission(context, resource, action, conditions);
    }

    return false;
  }

  /**
   * 检查角色
   */
  checkRole(context: AuthContext, requiredRoles: string[]): boolean {
    if (!context.authenticated) {
      return false;
    }

    // 超级管理员拥有所有角色
    if (context.roles.includes('super_admin')) {
      return true;
    }

    return requiredRoles.some(role => context.roles.includes(role));
  }

  /**
   * 生成 JWT Token
   */
  async generateJwtToken(user: User, deviceInfo?: DeviceInfo): Promise<AuthToken> {
    const payload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      roles: user.roles,
      permissions: user.permissions,
      level: user.level,
      deviceId: deviceInfo?.fingerprint,
      iat: Math.floor(Date.now() / 1000),
      jti: this.generateTokenId(),
    };

    const accessToken = this.jwtService.sign(payload, {
      secret: this.jwtSecret,
      expiresIn: this.jwtExpiresIn,
    });

    const refreshToken = this.jwtService.sign(
      { sub: user.id, type: 'refresh', jti: payload.jti },
      { secret: this.jwtSecret, expiresIn: '7d' },
    );

    const expiresIn = this.parseExpiresIn(this.jwtExpiresIn);
    const expiresAt = new Date(Date.now() + expiresIn * 1000);

    // 存储 token 信息到 Redis
    await this.storeTokenInfo(payload.jti, {
      userId: user.id,
      deviceId: deviceInfo?.fingerprint,
      expiresAt,
    });

    return {
      accessToken,
      refreshToken,
      tokenType: 'Bearer',
      expiresIn,
      expiresAt,
      scope: user.permissions,
      metadata: {
        tokenId: payload.jti,
        deviceId: deviceInfo?.fingerprint,
      },
    };
  }

  /**
   * 创建会话
   */
  async createSession(
    user: User,
    ipAddress: string,
    userAgent: string,
    deviceInfo?: DeviceInfo,
  ): Promise<AuthSession> {
    const sessionId = this.generateSessionId();
    const expiresAt = new Date(Date.now() + this.sessionMaxAge);

    const session: AuthSession = {
      id: sessionId,
      userId: user.id,
      deviceId: deviceInfo?.fingerprint,
      deviceInfo,
      ipAddress,
      userAgent,
      createdAt: new Date(),
      lastAccessAt: new Date(),
      expiresAt,
      metadata: {},
    };

    await this.storeSession(session);

    return session;
  }

  /**
   * 撤销 token
   */
  async revokeToken(tokenId: string): Promise<void> {
    await this.addTokenToBlacklist(tokenId);
    await this.deleteTokenInfo(tokenId);
  }

  /**
   * 删除会话
   */
  async deleteSession(sessionId: string): Promise<void> {
    await this.redisService.del(`session:${sessionId}`);
  }

  /**
   * 记录安全事件
   */
  async logSecurityEvent(event: Omit<SecurityEvent, 'id' | 'timestamp' | 'resolved'>): Promise<void> {
    const securityEvent: SecurityEvent = {
      id: this.generateEventId(),
      ...event,
      timestamp: new Date(),
      resolved: false,
    };

    await this.redisService.lpush('security:events', JSON.stringify(securityEvent));
    
    // 保留最近 1000 个事件
    await this.redisService.getClient().ltrim('security:events', 0, 999);

    this.logger.warn(`Security event: ${event.type}`, securityEvent);
  }

  /**
   * 获取认证指标
   */
  async getAuthMetrics(): Promise<AuthMetrics> {
    const now = new Date();
    const dayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // 这里应该从实际的数据源获取指标
    // 为了演示，返回模拟数据
    return {
      totalUsers: await this.getTotalUsersCount(),
      activeUsers: await this.getActiveUsersCount(),
      loginAttempts: await this.getLoginAttemptsCount(dayStart),
      successfulLogins: await this.getSuccessfulLoginsCount(dayStart),
      failedLogins: await this.getFailedLoginsCount(dayStart),
      blockedAttempts: await this.getBlockedAttemptsCount(dayStart),
      averageSessionDuration: await this.getAverageSessionDuration(),
      tokenRefreshRate: await this.getTokenRefreshRate(dayStart),
      securityEvents: await this.getSecurityEventsCount(dayStart),
      timestamp: now,
    };
  }

  // ==================== 私有方法 ====================

  private async getUserById(userId: string): Promise<User | null> {
    // 这里应该从用户服务获取用户信息
    // 为了演示，返回模拟数据
    const userData: string | null = await this.redisService.get(`user:${userId}`);
    return userData ? JSON.parse(userData) : null;
  }

  private async getApiKeyInfo(apiKey: string): Promise<ApiKey | null> {
    const keyData: string | null = await this.redisService.get(`apikey:${apiKey}`);
    return keyData ? JSON.parse(keyData) : null;
  }

  private async getSession(sessionId: string): Promise<AuthSession | null> {
    const sessionData: string | null = await this.redisService.get(`session:${sessionId}`);
    return sessionData ? JSON.parse(sessionData) : null;
  }

  private async getRole(roleName: string): Promise<Role | null> {
    const roleData: string | null = await this.redisService.get(`role:${roleName}`);
    return roleData ? JSON.parse(roleData) : null;
  }

  private async isTokenBlacklisted(token: string): Promise<boolean> {
    return await this.redisService.exists(`blacklist:${token}`);
  }

  private async addTokenToBlacklist(tokenId: string): Promise<void> {
    await this.redisService.set(`blacklist:${tokenId}`, 'true', 86400); // 24小时
  }

  private async storeTokenInfo(tokenId: string, info: any): Promise<void> {
    await this.redisService.set(`token:${tokenId}`, JSON.stringify(info), 86400);
  }

  private async deleteTokenInfo(tokenId: string): Promise<void> {
    await this.redisService.del(`token:${tokenId}`);
  }

  private async storeSession(session: AuthSession): Promise<void> {
    const ttl = Math.floor((session.expiresAt.getTime() - Date.now()) / 1000);
    await this.redisService.set(`session:${session.id}`, JSON.stringify(session), ttl);
  }

  private async updateSessionLastAccess(sessionId: string): Promise<void> {
    const session = await this.getSession(sessionId);
    if (session) {
      session.lastAccessAt = new Date();
      await this.storeSession(session);
    }
  }

  private async updateApiKeyLastUsed(apiKeyId: string): Promise<void> {
    // 更新 API Key 最后使用时间
    await this.redisService.set(`apikey:${apiKeyId}:lastused`, Date.now().toString());
  }

  private async checkConditionalPermission(
    context: AuthContext,
    resource: string,
    action: string,
    conditions: Record<string, any>,
  ): Promise<boolean> {
    // 实现基于属性的访问控制 (ABAC)
    // 这里是简化的实现
    return false;
  }

  private generateTokenId(): string {
    return `tok_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSessionId(): string {
    return `ses_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private parseExpiresIn(expiresIn: string): number {
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    if (!match) return 3600; // 默认1小时

    const value = parseInt(match[1], 10);
    const unit = match[2];

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 3600;
      case 'd': return value * 86400;
      default: return 3600;
    }
  }

  // 模拟的指标获取方法
  private async getTotalUsersCount(): Promise<number> { return 10000; }
  private async getActiveUsersCount(): Promise<number> { return 1500; }
  private async getLoginAttemptsCount(since: Date): Promise<number> { return 500; }
  private async getSuccessfulLoginsCount(since: Date): Promise<number> { return 450; }
  private async getFailedLoginsCount(since: Date): Promise<number> { return 50; }
  private async getBlockedAttemptsCount(since: Date): Promise<number> { return 25; }
  private async getAverageSessionDuration(): Promise<number> { return 1800; }
  private async getTokenRefreshRate(since: Date): Promise<number> { return 0.1; }
  private async getSecurityEventsCount(since: Date): Promise<number> { return 5; }
}
