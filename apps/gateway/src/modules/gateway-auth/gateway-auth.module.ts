import { Module } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule } from '@nestjs/config';
import { RedisModule } from '@common/redis';
import { MicroserviceKitModule } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@shared/constants';

// JWT共享模块
import { JwtSharedModule } from '../../shared/jwt/jwt-shared.module';

// 限流模块
import { RateLimitingModule } from '../rate-limiting/rate-limit.module';

// 控制器
import { UserController } from './controllers/user.controller';

// 服务
import { UserService } from './services/user.service';
import { AuthService } from './services/auth.service';

// 策略
import { JwtStrategy } from './strategies/jwt.strategy';
import { ApiKeyStrategy } from './strategies/api-key.strategy';

// 守卫 (只导入存在的文件)
import { AuthGuard } from './guards/auth.guard';
import { RolesGuard } from './guards/roles.guard';
import { PermissionsGuard } from './guards/permissions.guard';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RateLimitGuard } from './guards/rate-limit.guard';

/**
 * 网关认证功能模块
 *
 * 职责：
 * - 提供用户认证和授权
 * - 管理JWT令牌和API密钥
 * - 实现角色和权限控制
 * - 集成限流保护
 *
 * 依赖分析：
 * - ConfigModule：获取认证配置
 * - RedisModule：存储会话和黑名单
 * - JwtSharedModule：JWT令牌服务
 * - RateLimitingModule：认证接口限流
 */
@Module({
  imports: [
    ConfigModule,
    RedisModule,
    JwtSharedModule,
    RateLimitingModule,
    PassportModule.register({ defaultStrategy: 'jwt' }),
  ],
  controllers: [
    UserController,
  ],
  providers: [
    // 服务
    UserService,
    AuthService,

    // 策略
    JwtStrategy,
    ApiKeyStrategy,

    // 守卫 (只注册存在的)
    AuthGuard,
    // RolesGuard,
    // PermissionsGuard,
    // JwtAuthGuard,
    // RateLimitGuard,
  ],
  exports: [
    // 导出认证相关服务供其他模块使用
    UserService,
    AuthService,

    // 导出守卫供其他模块使用
    AuthGuard,
    // RolesGuard,
    // PermissionsGuard,
    // JwtAuthGuard,
    // RateLimitGuard,
  ],
})
export class GatewayAuthModule {}
