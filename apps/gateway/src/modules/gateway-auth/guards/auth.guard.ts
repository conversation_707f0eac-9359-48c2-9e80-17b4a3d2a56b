import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthService } from '../../modules/gateway-auth/services/auth.service';

/**
 * 认证守卫
 * 
 * 验证请求的认证状态，确保只有经过认证的用户才能访问受保护的资源
 */
@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private readonly authService: AuthService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 检查是否需要认证
    const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromRequest(request);

    if (!token) {
      throw new UnauthorizedException('Authentication token is required');
    }

    try {
      const authContext = await this.authService.validateJwtToken(token);
      request.user = authContext.user;
      return true;
    } catch (error) {
      throw new UnauthorizedException('Invalid authentication token');
    }
  }

  /**
   * 从请求中提取认证令牌
   */
  private extractTokenFromRequest(request: any): string | null {
    // 从 Authorization 头中提取
    const authHeader = request.headers?.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // 从查询参数中提取
    if (request.query?.token) {
      return request.query.token;
    }

    // 从 Cookie 中提取
    if (request.cookies?.token) {
      return request.cookies.token;
    }

    return null;
  }
}
