import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

// 服务
import { TokenValidationService } from '../../../shared/jwt/token-validation.service';

// 装饰器
import { TOKEN_SCOPE_KEY } from '@gateway/common/decorators/token-scope.decorator';

/**
 * JWT认证守卫
 * 支持双层Token机制的统一认证守卫
 * 
 * 功能特性：
 * 1. 自动Token类型识别：根据Token内容自动识别账号Token或角色Token
 * 2. 作用域验证：确保Token类型与接口要求匹配
 * 3. 权限边界控制：严格控制Token的使用范围
 * 4. 用户上下文注入：将用户信息注入到请求对象中
 */
@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(
    private readonly tokenValidationService: TokenValidationService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException('缺少认证Token');
    }

    try {
      // 获取接口要求的Token作用域
      const requiredScope = this.reflector.get<'account' | 'character'>(
        TOKEN_SCOPE_KEY,
        context.getHandler()
      ) || this.reflector.get<'account' | 'character'>(
        TOKEN_SCOPE_KEY,
        context.getClass()
      );

      // 验证Token
      const payload = await this.tokenValidationService.validateToken(token);

      // 检查作用域匹配
      if (requiredScope && payload.scope !== requiredScope) {
        throw new UnauthorizedException(
          `Token作用域不匹配，期望: ${requiredScope}, 实际: ${payload.scope}`
        );
      }

      // 将用户信息注入到请求对象
      request.user = {
        id: payload.sub,
        username: payload.username,
        email: payload.email,
        roles: payload.roles,
        permissions: payload.permissions,
        sessionId: payload.sessionId,
        deviceId: payload.deviceId,
        tokenScope: payload.scope,
      };

      // 如果是角色Token，注入角色上下文
      if (payload.scope === 'character') {
        const characterPayload = payload as any;
        request.character = {
          characterId: characterPayload.characterId,
          serverId: characterPayload.serverId,
        };
      }

      return true;

    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Token验证失败');
    }
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
