import {Body, Controller, Get, HttpCode, HttpStatus, Logger, Post, Request, UseGuards,} from '@nestjs/common';
import {ApiBearerAuth, ApiOperation, ApiResponse, ApiTags} from '@nestjs/swagger';

import {UserService} from '../services/user.service';
import {AuthGuard} from '../guards/auth.guard';
import {LoginDto} from '../dto/login.dto';
import {RefreshTokenDto} from '../dto/refresh-token.dto';
import {ChangePasswordDto} from '../dto/change-password.dto';

/**
 * 用户控制器
 *
 * 处理用户相关的 HTTP 请求，包括：
 * - 用户登录
 * - 令牌刷新
 * - 用户登出
 * - 密码修改
 * - 用户信息获取
 * - 用户资料管理
 */
@ApiTags('user')
@Controller('user')
export class UserController {
  private readonly logger = new Logger(UserController.name);

  constructor(private readonly userService: UserService) {}

  /**
   * 用户登录
   */
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '用户登录' })
  @ApiResponse({ status: 200, description: '登录成功' })
  @ApiResponse({ status: 401, description: '认证失败' })
  async login(@Body() loginDto: LoginDto) {
    this.logger.log(`Login attempt for user: ${loginDto.username}`);
    
    try {
      const result = await this.userService.login(loginDto);
      
      this.logger.log(`Login successful for user: ${loginDto.username}`);
      return {
        message: '登录成功',
        ...result,
      };
    } catch (error) {
      this.logger.error(`Login failed for user: ${loginDto.username}`, error.stack);
      throw error;
    }
  }

  /**
   * 刷新访问令牌
   */
  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '刷新访问令牌' })
  @ApiResponse({ status: 200, description: '令牌刷新成功' })
  @ApiResponse({ status: 401, description: '刷新令牌无效' })
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto) {
    this.logger.log('Token refresh attempt');
    
    try {
      const result = await this.userService.refreshToken(refreshTokenDto.refreshToken);
      
      this.logger.log('Token refresh successful');
      return {
        message: '令牌刷新成功',
        ...result,
      };
    } catch (error) {
      this.logger.error('Token refresh failed', error.stack);
      throw error;
    }
  }

  /**
   * 用户登出
   */
  @Post('logout')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '用户登出' })
  @ApiResponse({ status: 200, description: '登出成功' })
  async logout(@Request() req) {
    const userId = req.user.id;
    this.logger.log(`Logout attempt for user: ${userId}`);
    
    try {
      await this.userService.logout(userId, req.token);
      
      this.logger.log(`Logout successful for user: ${userId}`);
      return {
        message: '登出成功',
      };
    } catch (error) {
      this.logger.error(`Logout failed for user: ${userId}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取当前用户信息
   */
  @Get('me')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取当前用户信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getCurrentUser(@Request() req) {
    const userId = req.user.id;
    this.logger.log(`Get current user info for: ${userId}`);
    
    try {
      const user = await this.userService.getCurrentUser(userId);
      
      return {
        message: '获取用户信息成功',
        user,
      };
    } catch (error) {
      this.logger.error(`Get current user failed for: ${userId}`, error.stack);
      throw error;
    }
  }

  /**
   * 修改密码
   */
  @Post('change-password')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '修改密码' })
  @ApiResponse({ status: 200, description: '密码修改成功' })
  @ApiResponse({ status: 400, description: '原密码错误' })
  async changePassword(@Request() req, @Body() changePasswordDto: ChangePasswordDto) {
    const userId = req.user.id;
    this.logger.log(`Change password attempt for user: ${userId}`);
    
    try {
      await this.userService.changePassword(userId, changePasswordDto);
      
      this.logger.log(`Password changed successfully for user: ${userId}`);
      return {
        message: '密码修改成功',
      };
    } catch (error) {
      this.logger.error(`Change password failed for user: ${userId}`, error.stack);
      throw error;
    }
  }

  /**
   * 验证令牌
   */
  @Post('verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '验证令牌' })
  @ApiResponse({ status: 200, description: '令牌有效' })
  @ApiResponse({ status: 401, description: '令牌无效' })
  async verifyToken(@Body() body: { token: string }) {
    this.logger.log('Token verification attempt');
    
    try {
      const result = await this.userService.verifyToken(body.token);
      
      this.logger.log('Token verification successful');
      return {
        message: '令牌验证成功',
        valid: true,
        payload: result,
      };
    } catch (error) {
      this.logger.error('Token verification failed', error.stack);
      throw error;
    }
  }
}
