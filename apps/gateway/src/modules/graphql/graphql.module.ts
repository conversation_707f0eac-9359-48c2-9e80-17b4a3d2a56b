import { Module } from '@nestjs/common';
import { MicroserviceKitModule } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@shared/constants';

// 认证模块
import { GatewayAuthModule } from '../gateway-auth/gateway-auth.module';

// GraphQL网关
import { GraphQLGateway } from './gateways/graphql.gateway';

/**
 * GraphQL功能模块
 * 
 * 职责：
 * - 提供GraphQL查询接口
 * - 整合多个微服务的数据
 * - 提供统一的数据查询入口
 * 
 * 依赖：
 * - SharedModule：微服务通信
 * - GatewayAuthModule：GraphQL认证
 */
@Module({
  imports: [
    GatewayAuthModule,
  ],
  providers: [
    GraphQLGateway,
  ],
  exports: [
    GraphQLGateway,
  ],
})
export class GraphQLModule {}
