import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// 认证模块
import { GatewayAuthModule } from '../gateway-auth/gateway-auth.module';

// 负载均衡模块
import { LoadBalancingModule } from '../load-balancing/load-balancing.module';

// 限流模块
import { RateLimitingModule } from '../rate-limiting/rate-limit.module';

// GraphQL网关
import { GraphQLGateway } from './gateways/graphql.gateway';

/**
 * GraphQL功能模块
 * 
 * 职责：
 * - 提供GraphQL查询接口
 * - 整合多个微服务的数据
 * - 提供统一的数据查询入口
 * 
 * 依赖：
 * - SharedModule：微服务通信
 * - GatewayAuthModule：GraphQL认证
 */
@Module({
  imports: [
    ConfigModule,
    GatewayAuthModule,
    LoadBalancingModule,
    RateLimitingModule,
  ],
  providers: [
    GraphQLGateway,
  ],
  exports: [
    GraphQLGateway,
  ],
})
export class GraphQLModule {}
