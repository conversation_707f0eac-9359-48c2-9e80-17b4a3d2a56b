import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GraphQLSchema, buildSchema, execute, parse, validate } from 'graphql';
import { stitchSchemas } from '@graphql-tools/stitch';
import { wrapSchema } from '@graphql-tools/wrap';
import { buildHTTPExecutor } from '@graphql-tools/executor-http';
import { UserService } from '../../gateway-auth/services/user.service';
import { RateLimitService } from '../../rate-limiting/services/rate-limit.service';
import { LoadBalancingService } from '../../load-balancing/services/load-balancing.service';
import { MICROSERVICE_NAMES } from '@shared/constants';

export interface GraphQLServiceConfig {
  name: string;
  url: string;
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
}

export interface GraphQLContext {
  user?: any;
  auth?: any;
  request: any;
  response: any;
  dataSources?: any;
}

@Injectable()
export class GraphQLGateway {
  private readonly logger = new Logger(GraphQLGateway.name);
  private schema: GraphQLSchema | null = null;
  private serviceSchemas = new Map<string, GraphQLSchema>();
  private readonly services: GraphQLServiceConfig[] = [];

  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly rateLimitService: RateLimitService,
    private readonly loadBalancerService: LoadBalancingService,
  ) {
    this.initializeServices();
  }

  /**
   * 初始化 GraphQL 服务
   */
  private initializeServices(): void {
    // 从配置中获取 GraphQL 服务列表
    const services = this.configService.get<GraphQLServiceConfig[]>('graphql.services', []);
    
    this.services.push(...services);
    
    // 默认服务配置
    if (this.services.length === 0) {
      this.services.push(
        {
          name: MICROSERVICE_NAMES.CHARACTER_SERVICE,
          url: this.configService.get<string>('CHARACTER_SERVICE_URL', 'http://localhost:3002') + '/graphql',
        },
        {
          name: MICROSERVICE_NAMES.CLUB_SERVICE,
          url: this.configService.get<string>('CLUB_SERVICE_URL', 'http://localhost:3003') + '/graphql',
        },
        {
          name: MICROSERVICE_NAMES.MATCH_SERVICE,
          url: this.configService.get<string>('MATCH_SERVICE_URL', 'http://localhost:3004') + '/graphql',
        },
        {
          name: MICROSERVICE_NAMES.HERO_SERVICE,
          url: this.configService.get<string>('HERO_SERVICE_URL', 'http://localhost:3005') + '/graphql',
        },
      );
    }
  }

  /**
   * 构建联合 Schema
   */
  async buildFederatedSchema(): Promise<GraphQLSchema> {
    try {
      const subschemas = await Promise.all(
        this.services.map(async (service) => {
          try {
            const schema = await this.introspectServiceSchema(service);
            this.serviceSchemas.set(service.name, schema);
            
            return wrapSchema({
              schema,
              executor: this.createServiceExecutor(service),
              transforms: [
                // 可以添加 schema 转换
              ],
            });
          } catch (error) {
            this.logger.error(`Failed to introspect schema for ${service.name}:`, error);
            return null;
          }
        }),
      );

      const validSubschemas = subschemas.filter(Boolean);
      
      if (validSubschemas.length === 0) {
        throw new Error('No valid GraphQL services found');
      }

      this.schema = stitchSchemas({
        subschemas: validSubschemas,
        resolvers: {
          // 自定义解析器
          Query: {
            // 网关级别的查询
            gateway: () => ({
              version: '1.0.0',
              services: this.services.map(s => s.name),
              uptime: process.uptime(),
            }),
          },
        },
        typeDefs: `
          extend type Query {
            gateway: GatewayInfo
          }
          
          type GatewayInfo {
            version: String!
            services: [String!]!
            uptime: Float!
          }
        `,
      });

      this.logger.log('GraphQL federated schema built successfully');
      return this.schema;
    } catch (error) {
      this.logger.error('Failed to build federated schema:', error);
      throw error;
    }
  }

  /**
   * 执行 GraphQL 查询
   */
  async executeQuery(
    query: string,
    variables?: any,
    context?: GraphQLContext,
    operationName?: string,
  ): Promise<any> {
    if (!this.schema) {
      await this.buildFederatedSchema();
    }

    try {
      // 解析查询
      const document = parse(query);
      
      // 验证查询
      const validationErrors = validate(this.schema!, document);
      if (validationErrors.length > 0) {
        return {
          errors: validationErrors.map(error => ({
            message: error.message,
            locations: error.locations,
            path: error.path,
          })),
        };
      }

      // 应用认证和授权
      const authContext = await this.applyAuthentication(context);
      
      // 应用限流
      await this.applyRateLimit(authContext);

      // 执行查询
      const result = await execute({
        schema: this.schema!,
        document,
        variableValues: variables,
        contextValue: authContext,
        operationName,
      });

      // 记录指标
      this.recordMetrics(query, result, authContext);

      return result;
    } catch (error) {
      this.logger.error('GraphQL execution error:', error);
      return {
        errors: [{
          message: error.message,
          extensions: {
            code: 'INTERNAL_ERROR',
          },
        }],
      };
    }
  }

  /**
   * 获取 Schema SDL
   */
  getSchemaSDL(): string {
    if (!this.schema) {
      throw new Error('Schema not built yet');
    }
    
    return this.schema.toString();
  }

  /**
   * 刷新 Schema
   */
  async refreshSchema(): Promise<void> {
    this.logger.log('Refreshing GraphQL schema...');
    this.schema = null;
    this.serviceSchemas.clear();
    await this.buildFederatedSchema();
  }

  /**
   * 获取服务健康状态
   */
  async getServicesHealth(): Promise<Record<string, boolean>> {
    const health: Record<string, boolean> = {};
    
    await Promise.all(
      this.services.map(async (service) => {
        try {
          const response = await fetch(service.url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              ...service.headers,
            },
            body: JSON.stringify({
              query: '{ __typename }',
            }),
          });
          
          health[service.name] = response.ok;
        } catch (error) {
          health[service.name] = false;
        }
      }),
    );
    
    return health;
  }

  // ==================== 私有方法 ====================

  private async introspectServiceSchema(service: GraphQLServiceConfig): Promise<GraphQLSchema> {
    // 简化的 schema 内省，直接返回基础 schema
    // 在实际项目中，这里应该通过 HTTP 请求获取远程 schema
    return buildSchema(`
      type Query {
        hello: String
      }
    `);
  }

  private createServiceExecutor(service: GraphQLServiceConfig) {
    return async ({ document, variables, context }: any) => {
      try {
        // 获取服务实例
        const instance = this.loadBalancerService.getServiceInstance(service.name);
        const serviceUrl = instance ? instance.url + '/graphql' : service.url;

        // 构建请求头
        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
          ...service.headers,
        };

        // 添加认证头
        if (context?.auth?.token) {
          headers['Authorization'] = `Bearer ${context.auth.token.accessToken}`;
        }

        // 添加用户信息头
        if (context?.user) {
          headers['X-User-ID'] = context.user.id;
          headers['X-User-Roles'] = context.user.roles.join(',');
        }

        // 发送请求
        const response = await fetch(serviceUrl, {
          method: 'POST',
          headers,
          body: JSON.stringify({
            query: document,
            variables,
          }),
          // timeout 不是标准的 fetch 选项，这里移除
        });

        if (!response.ok) {
          throw new Error(`Service ${service.name} returned ${response.status}`);
        }

        const result = await response.json();
        
        // 更新服务健康状态
        this.loadBalancerService.updateInstanceHealth(service.name, instance?.id || 'default', true);
        
        return result;
      } catch (error) {
        this.logger.error(`Error executing query on ${service.name}:`, error);
        
        // 更新服务健康状态
        const instance = this.loadBalancerService.getServiceInstance(service.name);
        if (instance) {
          this.loadBalancerService.updateInstanceHealth(service.name, instance.id, false);
        }
        
        throw error;
      }
    };
  }

  private async applyAuthentication(context?: GraphQLContext): Promise<GraphQLContext> {
    if (!context) {
      return { request: null, response: null };
    }

    try {
      // 从请求中提取认证信息
      const authHeader = context.request?.headers?.authorization;
      
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const payload = await this.userService.verifyToken(token);
        const authContext = { user: payload, authenticated: true };
        
        return {
          ...context,
          auth: authContext,
          user: authContext.user,
        };
      }
    } catch (error) {
      this.logger.warn('GraphQL authentication failed:', error);
    }

    return context;
  }

  private async applyRateLimit(context: GraphQLContext): Promise<void> {
    if (!context.request) return;

    const userId = context.user?.id;
    const ip = this.getClientIp(context.request);
    
    // 生成限流键
    const key = userId
      ? `user:${userId}:graphql`
      : `ip:${ip}:graphql`;

    // 检查限流
    const result = await this.rateLimitService.checkRateLimit(key, {
      windowMs: 60000, // 1分钟
      max: 100, // 100次查询
    });

    if (!result.allowed) {
      throw new Error('Rate limit exceeded');
    }
  }

  private recordMetrics(query: string, result: any, context: GraphQLContext): void {
    // 记录 GraphQL 指标
    const hasErrors = result.errors && result.errors.length > 0;
    const operationType = this.extractOperationType(query);
    
    // 这里可以集成到 MetricsService
    this.logger.debug(`GraphQL ${operationType} executed`, {
      hasErrors,
      userId: context.user?.id,
      errorCount: result.errors?.length || 0,
    });
  }

  private extractOperationType(query: string): string {
    const trimmed = query.trim();
    if (trimmed.startsWith('mutation')) return 'mutation';
    if (trimmed.startsWith('subscription')) return 'subscription';
    return 'query';
  }

  private getClientIp(request: any): string {
    return (
      request.headers?.['x-forwarded-for']?.split(',')[0]?.trim() ||
      request.headers?.['x-real-ip'] ||
      request.connection?.remoteAddress ||
      '127.0.0.1'
    );
  }
}
