import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RedisModule } from '@common/redis';

// 限流服务
import { RateLimitService } from './services/rate-limit.service';

/**
 * 限流功能模块
 *
 * 职责：
 * - 提供多维度限流控制
 * - 实现多种限流算法
 * - 防止系统过载和恶意攻击
 *
 * 依赖分析：
 * - ConfigModule：获取限流配置参数
 * - RedisModule：分布式限流计数器存储
 */
@Module({
  imports: [
    ConfigModule,
    RedisModule,
  ],
  providers: [
    RateLimitService,
  ],
  exports: [
    RateLimitService,
  ],
})
export class RateLimitingModule {}
