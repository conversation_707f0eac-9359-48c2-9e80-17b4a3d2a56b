import { Module } from '@nestjs/common';

// 导入限流服务
import { RateLimitService } from './services/rate-limit.service';

/**
 * 限流模块
 * 
 * 提供网关限流核心功能，包括：
 * - 多维度限流控制（IP、用户、API等）
 * - 多种限流算法（令牌桶、滑动窗口等）
 * - 动态限流规则配置
 * - 限流统计和监控
 * 
 * 职责范围：
 * - 控制客户端的请求频率
 * - 防止系统过载和恶意攻击
 * - 提供灵活的限流策略配置
 * - 支持分布式限流和本地限流
 */
@Module({
  providers: [
    RateLimitService,
  ],
  exports: [
    RateLimitService,
  ],
})
export class RateLimitModule {}
