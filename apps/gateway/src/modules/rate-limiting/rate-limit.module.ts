import { Module } from '@nestjs/common';

// 共享模块
import { SharedModule } from '../../shared/shared.module';

// 限流服务
import { RateLimitService } from './services/rate-limit.service';

/**
 * 限流功能模块
 *
 * 职责：
 * - 提供多维度限流控制
 * - 实现多种限流算法
 * - 防止系统过载和恶意攻击
 *
 * 依赖：
 * - SharedModule：基础设施服务
 */
@Module({
  imports: [
    SharedModule,
  ],
  providers: [
    RateLimitService,
  ],
  exports: [
    RateLimitService,
  ],
})
export class RateLimitingModule {}
