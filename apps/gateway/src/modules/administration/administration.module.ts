import { Module } from '@nestjs/common';

// 共享模块
import { SharedModule } from '../../shared/shared.module';

// 认证模块
import { GatewayAuthModule } from '../gateway-auth/gateway-auth.module';

// 监控模块
import { MonitoringModule } from '../monitoring/monitoring.module';

// 控制器
import { ServiceRegistryController } from './controllers/service-registry.controller';
import { InstanceManagementController } from './controllers/instance-management.controller';

/**
 * 管理功能模块
 * 
 * 职责：
 * - 提供服务注册和管理接口
 * - 管理服务实例生命周期
 * - 提供系统管理功能
 * 
 * 依赖：
 * - SharedModule：服务发现、微服务通信
 * - GatewayAuthModule：管理员认证
 * - MonitoringModule：系统监控
 */
@Module({
  imports: [
    SharedModule,
    GatewayAuthModule,
    MonitoringModule,
  ],
  controllers: [
    ServiceRegistryController,
    InstanceManagementController,
  ],
  exports: [],
})
export class AdministrationModule {}
