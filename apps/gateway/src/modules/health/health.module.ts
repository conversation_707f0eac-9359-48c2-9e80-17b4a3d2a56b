import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';

// 负载均衡模块
import { LoadBalancingModule } from '../load-balancing/load-balancer.module';

// 控制器
import { StandardHealthController } from './controllers/standard-health.controller';
import { MicroserviceHealthController } from './controllers/microservice-health.controller';

// 服务
import { HealthService } from './services/health.service';

// 健康指标
import { RedisHealthIndicator } from './indicators/redis-health.indicator';
import { MicroserviceHealthIndicator } from './indicators/microservice-health.indicator';

/**
 * 健康检查功能模块
 *
 * 职责：
 * - 提供标准健康检查接口
 * - 监控微服务健康状态
 * - 支持Kubernetes探针标准
 *
 * 依赖：
 * - SharedModule：基础设施服务
 * - LoadBalancingModule：负载均衡服务
 */
@Module({
  imports: [
    ConfigModule,
    LoadBalancingModule,
    TerminusModule,
    HttpModule,
  ],
  controllers: [
    StandardHealthController,
    MicroserviceHealthController,
  ],
  providers: [
    HealthService,
    RedisHealthIndicator,
    MicroserviceHealthIndicator,
  ],
  exports: [
    HealthService,
    RedisHealthIndicator,
    MicroserviceHealthIndicator,
  ],
})
export class HealthModule {}
