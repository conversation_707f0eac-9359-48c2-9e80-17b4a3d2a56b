import { Injectable } from '@nestjs/common';
import { HealthIndicator, HealthIndicatorResult, HealthCheckError } from '@nestjs/terminus';
import { HttpService } from '@nestjs/axios';
import { timeout, firstValueFrom } from 'rxjs';
import { MicroserviceClientService } from '@common/microservice-kit';
import { LoadBalancingService } from '../../load-balancing/services/load-balancing.service';

/**
 * 微服务健康检查指示器 - 增强版双重检查
 */
@Injectable()
export class MicroserviceHealthIndicator extends HealthIndicator {
  constructor(
    // 使用统一的微服务客户端
    private readonly microserviceClient: MicroserviceClientService,
    // 使用HTTP客户端进行HTTP健康检查
    private readonly httpService: HttpService,
    private readonly loadBalancerService: LoadBalancingService,
  ) {
    super();
  }

  /**
   * 统一的微服务健康检查接口
   * @param serviceName 微服务名称
   * @param rpcPattern 自定义RPC模式，不提供则使用 {serviceName}.health
   * @param httpPath 自定义HTTP路径，不提供则使用 /health
   */
  async checkMicroservice(
    serviceName: string,
    rpcPattern?: string,
    httpPath?: string,
  ): Promise<HealthIndicatorResult> {
    const key = serviceName;
    const pattern = rpcPattern || `${serviceName}.health`;
    const path = httpPath || '/health';

    return this.checkServiceHealth(key, serviceName, pattern, path);
  }

  /**
   * 批量检查多个微服务
   * @param serviceNames 微服务名称数组
   */
  async checkMultipleMicroservices(serviceNames: string[]): Promise<HealthIndicatorResult[]> {
    const checks = serviceNames.map(serviceName =>
      this.checkMicroservice(serviceName)
    );

    return Promise.all(checks);
  }



  /**
   * 综合健康检查：同时检查RPC和HTTP连接
   */
  private async checkServiceHealth(
    key: string,
    serviceName: string,
    rpcPattern: string,
    httpPath: string,
  ): Promise<HealthIndicatorResult> {
    const results = await Promise.allSettled([
      this.checkRpcHealth(serviceName, rpcPattern),
      this.checkHttpHealth(serviceName, httpPath),
    ]);

    return this.aggregateHealthResults(key, results);
  }

  /**
   * RPC连接健康检查 - 仅作为参考，异常不影响整体判定
   */
  private async checkRpcHealth(serviceName: string, pattern: string): Promise<HealthResult> {
    try {
      const startTime = Date.now();

      // 使用统一的微服务客户端，设置较短的超时时间
      const response = await Promise.race([
        this.microserviceClient.call(serviceName as any, pattern, {}),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('RPC timeout')), 3000)
        )
      ]);

      const responseTime = Date.now() - startTime;

      return {
        type: 'rpc',
        status: response?.success ? 'up' : 'down',
        responseTime,
        details: {
          version: response?.version || 'unknown',
          timestamp: response?.timestamp || new Date().toISOString(),
          note: 'RPC check is for reference only',
        },
      };
    } catch (error) {
      // RPC异常是预期的，因为大多数微服务没有实现health MessagePattern
      // 不输出错误日志，避免污染日志
      return {
        type: 'rpc',
        status: 'down',
        error: `RPC check failed (expected): ${error.name || 'TimeoutError'}`,
        responseTime: 3000,
        details: {
          note: 'RPC health check is not implemented in most microservices - this is normal',
          errorType: error.name || 'Unknown',
        },
      };
    }
  }

  /**
   * HTTP连接健康检查
   */
  private async checkHttpHealth(serviceName: string, path: string): Promise<HealthResult> {
    try {
      const serviceInstance = await this.loadBalancerService.getServiceInstance(serviceName);
      if (!serviceInstance) {
        throw new Error('No service instance available');
      }

      const url = `${serviceInstance.url}${path}`;
      const startTime = Date.now();

      const response = await firstValueFrom(
        this.httpService.get(url).pipe(timeout(5000))
      );

      const responseTime = Date.now() - startTime;

      return {
        type: 'http',
        status: response.status === 200 ? 'up' : 'down',
        responseTime,
        details: {
          statusCode: response.status,
          data: response.data,
        },
      };
    } catch (error) {
      return {
        type: 'http',
        status: 'down',
        error: error.message,
        responseTime: 5000,
      };
    }
  }

  /**
   * 聚合健康检查结果
   */
  private aggregateHealthResults(
    key: string,
    results: PromiseSettledResult<HealthResult>[]
  ): HealthIndicatorResult {
    const healthData: any = {
      rpc: null,
      http: null,
      overall: 'unknown',
    };

    let rpcStatus = false;
    let httpStatus = false;

    // 处理RPC检查结果
    if (results[0].status === 'fulfilled') {
      healthData.rpc = results[0].value;
      rpcStatus = results[0].value.status === 'up';
    } else {
      healthData.rpc = {
        status: 'down',
        error: results[0].reason?.message || 'RPC check failed',
      };
    }

    // 处理HTTP检查结果
    if (results[1].status === 'fulfilled') {
      healthData.http = results[1].value;
      httpStatus = results[1].value.status === 'up';
    } else {
      healthData.http = {
        status: 'down',
        error: results[1].reason?.message || 'HTTP check failed',
      };
    }

    // HTTP作为主要判定标准，RPC只作为参考
    const overallStatus = httpStatus; // 只基于HTTP状态判定
    healthData.overall = overallStatus ? 'up' : 'down';

    // 添加诊断信息
    healthData.diagnosis = this.generateDiagnosis(rpcStatus, httpStatus);

    const finalResult = this.getStatus(key, overallStatus, healthData);

    // 不抛出错误，即使HTTP失败也返回状态信息
    // 这样健康检查不会因为单个微服务不可用而失败
    return finalResult;
  }

  /**
   * 生成诊断信息 - RPC作为参考，HTTP作为主要判定
   */
  private generateDiagnosis(rpcStatus: boolean, httpStatus: boolean): string {
    if (httpStatus) {
      if (rpcStatus) {
        return 'Service healthy - both HTTP and RPC connections working';
      } else {
        return 'Service healthy - HTTP connection working (RPC check failed but this is expected)';
      }
    } else {
      if (rpcStatus) {
        return 'Service warning - RPC working but HTTP failed (unusual configuration)';
      } else {
        return 'Service not available - HTTP connection failed (this is expected if microservice is not running)';
      }
    }
  }

  // ==================== 新增方法：支持单个服务检查 ====================

  /**
   * 检查单个微服务的健康状态
   * 用于微服务健康检查控制器
   */
  async checkSingleService(serviceName: string): Promise<any> {
    try {
      // 使用现有的checkMicroservice方法
      const result = await this.checkMicroservice(serviceName);

      // 修复：正确提取服务状态信息
      // checkMicroservice返回的格式是直接的 { [serviceName]: { status, rpc, http, overall, ... } }
      // 而不是 { details: { [serviceName]: ... } }
      let serviceData: any = result[serviceName];

      if (!serviceData) {
        // 如果直接匹配失败，尝试获取第一个键
        const keys = Object.keys(result);
        if (keys.length > 0) {
          serviceData = result[keys[0]];
        }
      }

      serviceData = serviceData || {};
      const isHealthy = serviceData.overall === 'up' || serviceData.status === 'up';

      // 从HTTP检查结果中提取详细信息
      const httpData = serviceData.http?.details?.data || {};
      const responseTime = serviceData.http?.responseTime || serviceData.rpc?.responseTime || 'unknown';

      return {
        status: isHealthy ? 'up' : 'down',
        responseTime: responseTime,
        lastCheck: new Date().toISOString(),
        version: httpData.version || 'unknown',
        uptime: httpData.uptime || 'unknown',
        instance: httpData.instance || 'unknown',
        error: isHealthy ? undefined : (serviceData.error || 'Service unavailable'),
        details: serviceData,
      };
    } catch (error) {
      return {
        status: 'down',
        error: error.message || 'Service unavailable',
        lastCheck: new Date().toISOString(),
        responseTime: 'unknown',
        version: 'unknown',
        uptime: 'unknown',
        instance: 'unknown',
      };
    }
  }

  /**
   * 检查所有微服务的健康状态（兼容旧接口）
   * 用于保持向后兼容性
   */
  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      // 获取所有微服务名称
      const MICROSERVICE_NAMES = {
        AUTH_SERVICE: 'auth',
        CHARACTER_SERVICE: 'character',
        GAME_SERVICE: 'game',
        CLUB_SERVICE: 'club',
        MATCH_SERVICE: 'match',
        CARD_SERVICE: 'card',
        HERO_SERVICE: 'hero',
        ECONOMY_SERVICE: 'economy',
        SOCIAL_SERVICE: 'social',
        ACTIVITY_SERVICE: 'activity',
        NOTIFICATION_SERVICE: 'notification',
      };

      const healthyServices: Record<string, any> = {};
      const unhealthyServices: Record<string, any> = {};

      // 检查所有微服务的健康状态
      for (const serviceName of Object.values(MICROSERVICE_NAMES)) {
        try {
          const serviceStatus = await this.checkSingleService(serviceName);

          if (serviceStatus.status === 'up') {
            healthyServices[serviceName] = serviceStatus;
          } else {
            unhealthyServices[serviceName] = serviceStatus;
          }
        } catch (error) {
          unhealthyServices[serviceName] = {
            status: 'down',
            error: error.message || 'Service unavailable',
            lastCheck: new Date().toISOString(),
          };
        }
      }

      const totalServices = Object.keys(healthyServices).length + Object.keys(unhealthyServices).length;
      const healthyCount = Object.keys(healthyServices).length;
      const isOverallHealthy = healthyCount === totalServices;

      const result = this.getStatus(key, isOverallHealthy, {
        summary: {
          total: totalServices,
          healthy: healthyCount,
          unhealthy: Object.keys(unhealthyServices).length,
        },
        services: {
          ...healthyServices,
          ...unhealthyServices,
        },
      });

      if (!isOverallHealthy) {
        throw new HealthCheckError('Some microservices are unhealthy', result);
      }

      return result;
    } catch (error) {
      const result = this.getStatus(key, false, {
        error: error.message || 'Microservice health check failed',
        timestamp: new Date().toISOString(),
      });

      throw new HealthCheckError('Microservice health check failed', result);
    }
  }
}

interface HealthResult {
  type: 'rpc' | 'http';
  status: 'up' | 'down';
  responseTime?: number;
  error?: string;
  details?: any;
}
