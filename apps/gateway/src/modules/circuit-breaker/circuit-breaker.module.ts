import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';

// 熔断器服务
import { CircuitBreakerService } from './services/circuit-breaker.service';

/**
 * 熔断器功能模块
 *
 * 职责：
 * - 提供服务熔断保护
 * - 监控服务健康状态
 * - 实现熔断策略和恢复机制
 *
 * 依赖分析：
 * - ConfigModule：获取熔断器配置参数
 * - EventEmitterModule：发送熔断状态变更事件
 * - 无需其他依赖，保持模块轻量化
 */
@Module({
  imports: [
    ConfigModule,
    EventEmitterModule,
  ],
  providers: [
    CircuitBreakerService,
  ],
  exports: [
    CircuitBreakerService,
  ],
})
export class CircuitBreakerModule {}
