import { Module } from '@nestjs/common';

// 导入熔断器服务
import { CircuitBreakerService } from './services/circuit-breaker.service';

/**
 * 熔断器模块
 * 
 * 提供网关熔断器核心功能，包括：
 * - 服务调用失败监控
 * - 自动熔断和恢复
 * - 降级策略执行
 * - 熔断状态管理
 * 
 * 职责范围：
 * - 监控后端服务的调用成功率
 * - 在服务异常时自动开启熔断保护
 * - 提供降级响应和备用方案
 * - 支持半开状态的自动恢复检测
 */
@Module({
  providers: [
    CircuitBreakerService,
  ],
  exports: [
    CircuitBreakerService,
  ],
})
export class CircuitBreakerModule {}
