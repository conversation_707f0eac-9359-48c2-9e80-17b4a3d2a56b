import { Module } from '@nestjs/common';

// 共享模块
import { SharedModule } from '../../shared/shared.module';

// 熔断器服务
import { CircuitBreakerService } from './services/circuit-breaker.service';

/**
 * 熔断器功能模块
 *
 * 职责：
 * - 提供服务熔断保护
 * - 监控服务健康状态
 * - 实现熔断策略和恢复机制
 *
 * 依赖：
 * - SharedModule：基础设施服务
 */
@Module({
  imports: [
    SharedModule,
  ],
  providers: [
    CircuitBreakerService,
  ],
  exports: [
    CircuitBreakerService,
  ],
})
export class CircuitBreakerModule {}
