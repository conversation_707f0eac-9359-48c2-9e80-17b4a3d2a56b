import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@common/redis';
import { ServiceConfig } from '../../../shared/config/services.config';

export interface ServiceInstance {
  id: string;
  url: string;
  weight: number;
  healthy: boolean;
  connections: number;
  responseTime: number;
  lastHealthCheck: Date;
  metadata: Record<string, any>;
}

export interface LoadBalancerStrategy {
  name: string;
  selectInstance(instances: ServiceInstance[], context?: any): ServiceInstance | null;
}

export interface HealthCheckResult {
  healthy: boolean;
  responseTime: number;
  error?: string;
  timestamp: Date;
}

@Injectable()
export class LoadBalancerService {
  private readonly logger = new Logger(LoadBalancerService.name);
  private readonly serviceInstances = new Map<string, ServiceInstance[]>();
  private readonly roundRobinCounters = new Map<string, number>();
  private readonly strategies = new Map<string, LoadBalancerStrategy>();

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {
    this.initializeStrategies();
  }

  /**
   * 注册服务实例
   */
  registerServiceInstance(serviceName: string, instance: Omit<ServiceInstance, 'id'>): string {
    const instanceId = this.generateInstanceId(serviceName);
    const serviceInstance: ServiceInstance = {
      id: instanceId,
      ...instance,
    };

    if (!this.serviceInstances.has(serviceName)) {
      this.serviceInstances.set(serviceName, []);
    }

    const instances = this.serviceInstances.get(serviceName)!;
    instances.push(serviceInstance);

    this.logger.log(`Registered service instance: ${serviceName}/${instanceId}`);
    return instanceId;
  }

  /**
   * 注销服务实例
   */
  unregisterServiceInstance(serviceName: string, instanceId: string): boolean {
    const instances = this.serviceInstances.get(serviceName);
    if (!instances) return false;

    const index = instances.findIndex(instance => instance.id === instanceId);
    if (index === -1) return false;

    instances.splice(index, 1);
    this.logger.log(`Unregistered service instance: ${serviceName}/${instanceId}`);
    return true;
  }

  /**
   * 获取服务实例
   */
  getServiceInstance(serviceName: string, strategy: string = 'round-robin', context?: any): ServiceInstance | null {
    const instances = this.getHealthyInstances(serviceName);
    if (instances.length === 0) {
      this.logger.warn(`No healthy instances available for service: ${serviceName}`);
      return null;
    }

    const loadBalancer = this.strategies.get(strategy);
    if (!loadBalancer) {
      this.logger.warn(`Unknown load balancer strategy: ${strategy}, falling back to round-robin`);
      return this.strategies.get('round-robin')!.selectInstance(instances, context);
    }

    const selectedInstance = loadBalancer.selectInstance(instances, context);
    if (selectedInstance) {
      this.incrementConnectionCount(serviceName, selectedInstance.id);
    }

    return selectedInstance;
  }

  /**
   * 获取健康的服务实例
   */
  getHealthyInstances(serviceName: string): ServiceInstance[] {
    const instances = this.serviceInstances.get(serviceName) || [];
    return instances.filter(instance => instance.healthy);
  }

  /**
   * 获取所有服务实例
   */
  getAllInstances(serviceName: string): ServiceInstance[] {
    return this.serviceInstances.get(serviceName) || [];
  }

  /**
   * 更新实例健康状态
   */
  updateInstanceHealth(serviceName: string, instanceId: string, healthy: boolean, responseTime?: number): void {
    const instances = this.serviceInstances.get(serviceName);
    if (!instances) return;

    const instance = instances.find(inst => inst.id === instanceId);
    if (!instance) return;

    instance.healthy = healthy;
    instance.lastHealthCheck = new Date();
    
    if (responseTime !== undefined) {
      instance.responseTime = responseTime;
    }

    this.logger.debug(`Updated health status for ${serviceName}/${instanceId}: ${healthy ? 'healthy' : 'unhealthy'}`);
  }

  /**
   * 增加连接计数
   */
  incrementConnectionCount(serviceName: string, instanceId: string): void {
    const instances = this.serviceInstances.get(serviceName);
    if (!instances) return;

    const instance = instances.find(inst => inst.id === instanceId);
    if (instance) {
      instance.connections++;
    }
  }

  /**
   * 减少连接计数
   */
  decrementConnectionCount(serviceName: string, instanceId: string): void {
    const instances = this.serviceInstances.get(serviceName);
    if (!instances) return;

    const instance = instances.find(inst => inst.id === instanceId);
    if (instance && instance.connections > 0) {
      instance.connections--;
    }
  }

  /**
   * 执行健康检查
   */
  async performHealthCheck(serviceName: string): Promise<void> {
    const instances = this.serviceInstances.get(serviceName) || [];
    
    const healthCheckPromises = instances.map(async (instance) => {
      try {
        const result = await this.checkInstanceHealth(instance);
        this.updateInstanceHealth(serviceName, instance.id, result.healthy, result.responseTime);
        
        // 记录健康检查结果
        await this.recordHealthCheckResult(serviceName, instance.id, result);
      } catch (error) {
        this.logger.error(`Health check failed for ${serviceName}/${instance.id}:`, error);
        this.updateInstanceHealth(serviceName, instance.id, false);
      }
    });

    await Promise.allSettled(healthCheckPromises);
  }

  /**
   * 获取服务统计信息
   */
  getServiceStats(serviceName: string): any {
    const instances = this.serviceInstances.get(serviceName) || [];
    const healthyInstances = instances.filter(inst => inst.healthy);
    
    return {
      serviceName,
      totalInstances: instances.length,
      healthyInstances: healthyInstances.length,
      unhealthyInstances: instances.length - healthyInstances.length,
      totalConnections: instances.reduce((sum, inst) => sum + inst.connections, 0),
      averageResponseTime: this.calculateAverageResponseTime(instances),
      instances: instances.map(inst => ({
        id: inst.id,
        url: inst.url,
        healthy: inst.healthy,
        connections: inst.connections,
        responseTime: inst.responseTime,
        weight: inst.weight,
        lastHealthCheck: inst.lastHealthCheck,
      })),
    };
  }

  // ==================== 私有方法 ====================

  private initializeStrategies(): void {
    // Round Robin 策略
    this.strategies.set('round-robin', {
      name: 'round-robin',
      selectInstance: (instances: ServiceInstance[]) => {
        if (instances.length === 0) return null;
        
        const serviceName = 'default'; // 这里应该传入实际的服务名
        const counter = this.roundRobinCounters.get(serviceName) || 0;
        const selectedInstance = instances[counter % instances.length];
        
        this.roundRobinCounters.set(serviceName, counter + 1);
        return selectedInstance;
      },
    });

    // Weighted Round Robin 策略
    this.strategies.set('weighted', {
      name: 'weighted',
      selectInstance: (instances: ServiceInstance[]) => {
        if (instances.length === 0) return null;
        
        const totalWeight = instances.reduce((sum, inst) => sum + inst.weight, 0);
        if (totalWeight === 0) return instances[0];
        
        const random = Math.random() * totalWeight;
        let currentWeight = 0;
        
        for (const instance of instances) {
          currentWeight += instance.weight;
          if (random <= currentWeight) {
            return instance;
          }
        }
        
        return instances[instances.length - 1];
      },
    });

    // Least Connections 策略
    this.strategies.set('least-connections', {
      name: 'least-connections',
      selectInstance: (instances: ServiceInstance[]) => {
        if (instances.length === 0) return null;
        
        return instances.reduce((min, current) => 
          current.connections < min.connections ? current : min
        );
      },
    });

    // IP Hash 策略
    this.strategies.set('ip-hash', {
      name: 'ip-hash',
      selectInstance: (instances: ServiceInstance[], context?: any) => {
        if (instances.length === 0) return null;
        
        const ip = context?.ip || '127.0.0.1';
        const hash = this.hashString(ip);
        const index = hash % instances.length;
        
        return instances[index];
      },
    });

    // Health-based 策略
    this.strategies.set('health-based', {
      name: 'health-based',
      selectInstance: (instances: ServiceInstance[]) => {
        if (instances.length === 0) return null;
        
        // 按响应时间和连接数排序
        const sortedInstances = [...instances].sort((a, b) => {
          const scoreA = a.responseTime + (a.connections * 10);
          const scoreB = b.responseTime + (b.connections * 10);
          return scoreA - scoreB;
        });
        
        return sortedInstances[0];
      },
    });
  }

  private async checkInstanceHealth(instance: ServiceInstance): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // 这里应该实际发送健康检查请求
      // 为了演示，我们模拟一个健康检查
      const response = await this.sendHealthCheckRequest(instance.url);
      const responseTime = Date.now() - startTime;
      
      return {
        healthy: response.status === 200,
        responseTime,
        timestamp: new Date(),
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        healthy: false,
        responseTime,
        error: error.message,
        timestamp: new Date(),
      };
    }
  }

  private async sendHealthCheckRequest(url: string): Promise<{ status: number }> {
    // 实际的HTTP健康检查请求
    try {
      const axios = require('axios');
      const response = await axios.get(`${url}/health`, {
        timeout: 3000,
        validateStatus: (status) => status < 500, // 接受所有非5xx状态码
      });
      return { status: response.status };
    } catch (error) {
      // 连接失败或超时，返回503状态码
      if (error.code === 'ECONNREFUSED') {
        throw new Error(`Service unavailable: Connection refused to ${url}`);
      } else if (error.code === 'ETIMEDOUT') {
        throw new Error(`Service unavailable: Timeout connecting to ${url}`);
      } else if (error.response) {
        // 服务返回了错误状态码
        return { status: error.response.status };
      } else {
        throw new Error(`Health check failed: ${error.message}`);
      }
    }
  }

  private async recordHealthCheckResult(
    serviceName: string,
    instanceId: string,
    result: HealthCheckResult,
  ): Promise<void> {
    const key = `health_check:${serviceName}:${instanceId}`;
    const data = JSON.stringify(result);
    
    // 保存最近的健康检查结果
    await this.redisService.lpush(key, data);
    await this.redisService.getClient().ltrim(key, 0, 99); // 保留最近100条记录
    await this.redisService.expire(key, 86400); // 24小时过期
  }

  private calculateAverageResponseTime(instances: ServiceInstance[]): number {
    if (instances.length === 0) return 0;
    
    const totalResponseTime = instances.reduce((sum, inst) => sum + inst.responseTime, 0);
    return Math.round(totalResponseTime / instances.length);
  }

  private generateInstanceId(serviceName: string): string {
    return `${serviceName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }
}
