import { Module } from '@nestjs/common';

// 共享模块
import { SharedModule } from '../../shared/shared.module';

// 负载均衡服务
import { LoadBalancerService } from './services/load-balancer.service';

/**
 * 负载均衡功能模块
 *
 * 职责：
 * - 提供多种负载均衡算法
 * - 监控服务实例健康状态
 * - 实现故障转移和自动恢复
 *
 * 依赖：
 * - SharedModule：基础设施服务
 */
@Module({
  imports: [
    SharedModule,
  ],
  providers: [
    LoadBalancerService,
  ],
  exports: [
    LoadBalancerService,
  ],
})
export class LoadBalancingModule {}
