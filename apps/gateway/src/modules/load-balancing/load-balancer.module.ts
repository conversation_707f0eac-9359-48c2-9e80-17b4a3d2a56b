import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RedisModule } from '@common/redis';

// 负载均衡服务
import { LoadBalancerService } from './services/load-balancer.service';

/**
 * 负载均衡功能模块
 *
 * 职责：
 * - 提供多种负载均衡算法
 * - 监控服务实例健康状态
 * - 实现故障转移和自动恢复
 *
 * 依赖分析：
 * - ConfigModule：获取服务配置
 * - RedisModule：存储服务实例状态和健康信息
 */
@Module({
  imports: [
    ConfigModule,
    RedisModule,
  ],
  providers: [
    LoadBalancerService,
  ],
  exports: [
    LoadBalancerService,
  ],
})
export class LoadBalancingModule {}
