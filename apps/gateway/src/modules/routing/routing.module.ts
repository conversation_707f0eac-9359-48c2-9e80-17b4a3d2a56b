import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// JWT共享模块
import { JwtSharedModule } from '@gateway/infra/jwt/jwt-shared.module';

// 基础功能模块
import { LoadBalancingModule } from '../load-balancing/load-balancing.module';
import { CircuitBreakerModule } from '../circuit-breaker/circuit-breaker.module';
// CachingModule已删除 - 使用libs/common/src/redis公共库的@Cacheable装饰器

// 认证模块
import { GatewayAuthModule } from '../gateway-auth/gateway-auth.module';

// 监控模块
import { MonitoringModule } from '../monitoring/monitoring.module';

// 控制器
import { ProxyController } from './controllers/proxy.controller';

// 服务
import { ProxyService } from './services/proxy.service';
import { RouteResolverService } from './services/route-resolver.service';
import { HttpContextExtractorService } from './services/http-context-extractor.service';
import { HttpRouteEnhancerService } from './services/http-route-enhancer.service';

/**
 * 路由功能模块
 * 
 * 职责：
 * - 处理HTTP请求路由和代理
 * - 解析路由规则和目标服务
 * - 增强HTTP上下文信息
 * - 集成负载均衡、熔断器、Redis缓存
 * 
 * 依赖：
 * - SharedModule：基础设施服务
 * - LoadBalancingModule：负载均衡
 * - CircuitBreakerModule：熔断保护
 * - RedisModule：声明式缓存（@Cacheable装饰器）
 * - GatewayAuthModule：认证守卫
 * 
 * 注意：这是网关的核心模块，解决了RouteResolverService重复注册问题
 */
@Module({
  imports: [
    ConfigModule,
    JwtSharedModule,
    LoadBalancingModule,
    CircuitBreakerModule,
    // CachingModule已删除 - 使用Redis公共库的声明式缓存
    GatewayAuthModule,
    MonitoringModule,
  ],
  controllers: [
    ProxyController,
  ],
  providers: [
    // 核心路由服务 - 只在这里注册，避免重复注册
    RouteResolverService,
    
    // 代理服务
    ProxyService,
    
    // HTTP上下文服务
    HttpContextExtractorService,
    HttpRouteEnhancerService,
  ],
  exports: [
    // 导出路由服务供其他模块使用
    RouteResolverService,
    ProxyService,
    HttpContextExtractorService,
    HttpRouteEnhancerService,
  ],
})
export class RoutingModule {}
