import { Module } from '@nestjs/common';

// 共享模块
import { SharedModule } from '../../shared/shared.module';

// 基础功能模块
import { LoadBalancingModule } from '../load-balancing/load-balancing.module';
import { CircuitBreakerModule } from '../circuit-breaker/circuit-breaker.module';
import { CachingModule } from '../caching/caching.module';

// 认证模块
import { GatewayAuthModule } from '../gateway-auth/gateway-auth.module';

// 控制器
import { ProxyController } from './controllers/proxy.controller';

// 服务
import { ProxyService } from './services/proxy.service';
import { RouteResolverService } from './services/route-resolver.service';
import { HttpContextExtractorService } from './services/http-context-extractor.service';
import { HttpRouteEnhancerService } from './services/http-route-enhancer.service';

/**
 * 路由功能模块
 * 
 * 职责：
 * - 处理HTTP请求路由和代理
 * - 解析路由规则和目标服务
 * - 增强HTTP上下文信息
 * - 集成负载均衡、熔断器、缓存
 * 
 * 依赖：
 * - SharedModule：基础设施服务
 * - LoadBalancingModule：负载均衡
 * - CircuitBreakerModule：熔断保护
 * - CachingModule：响应缓存
 * - GatewayAuthModule：认证守卫
 * 
 * 注意：这是网关的核心模块，解决了RouteResolverService重复注册问题
 */
@Module({
  imports: [
    SharedModule,
    LoadBalancingModule,
    CircuitBreakerModule,
    CachingModule,
    GatewayAuthModule,
  ],
  controllers: [
    ProxyController,
  ],
  providers: [
    // 核心路由服务 - 只在这里注册，避免重复注册
    RouteResolverService,
    
    // 代理服务
    ProxyService,
    
    // HTTP上下文服务
    HttpContextExtractorService,
    HttpRouteEnhancerService,
  ],
  exports: [
    // 导出路由服务供其他模块使用
    RouteResolverService,
    ProxyService,
    HttpContextExtractorService,
    HttpRouteEnhancerService,
  ],
})
export class RoutingModule {}
