import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request, Response } from 'express';
import { createProxyMiddleware } from 'http-proxy-middleware';
import { MetricsService } from '../../monitoring/services/metrics.service';
import { ROUTE_CONSTANTS } from '../../../shared/constants';

// 新增：区服路由支持
import { HttpRouteEnhancerService, EnhancedRouteResult } from './http-route-enhancer.service';

/**
 * 简化的代理服务
 * 
 * 专注于核心功能：
 * - HTTP 请求代理
 * - 请求体正确处理
 * - 基础错误处理
 * - 监控指标
 */
@Injectable()
export class ProxyService {
  private readonly logger = new Logger(ProxyService.name);
  private readonly proxyCache = new Map<string, any>();

  constructor(
    private readonly configService: ConfigService,
    private readonly metricsService: MetricsService,
    // 新增：区服路由增强器
    private readonly httpRouteEnhancer: HttpRouteEnhancerService,
  ) {}

  /**
   * 代理请求到目标服务（支持区服路由）
   */
  async proxyRequest(
    req: Request,
    res: Response,
    targetUrl: string,
    serviceName: string,
  ): Promise<void> {
    const startTime = Date.now();

    try {
      this.logger.debug(`🔄 Proxying ${req.method} ${req.path} -> ${targetUrl}`);

      // 🔧 使用区服路由增强器增强路由
      const enhancedRoute = await this.httpRouteEnhancer.enhanceRoute(req, serviceName, targetUrl);

      // 验证增强结果
      if (!this.httpRouteEnhancer.validateEnhancedResult(enhancedRoute)) {
        throw new Error('Invalid enhanced route result');
      }

      const routeSummary = this.httpRouteEnhancer.getRouteSummary(enhancedRoute);
      this.logger.debug(`🎯 Enhanced route: ${routeSummary}`);

      // 检查认证要求
      if (enhancedRoute.requiresAuth && !enhancedRoute.context.userId) {
        this.logger.warn(`⚠️ Authentication required but no user context found`);
        res.status(401).json({
          statusCode: 401,
          message: 'Unauthorized',
          error: 'Authentication required',
          timestamp: new Date().toISOString(),
        });
        return;
      }

      // 获取或创建代理中间件
      const proxy = this.getEnhancedProxyMiddleware(enhancedRoute);

      // 执行代理
      await new Promise<void>((resolve, reject) => {
        proxy(req, res, (error) => {
          if (error) {
            reject(error);
          } else {
            resolve();
          }
        });
      });

      // 记录成功指标
      const duration = Date.now() - startTime;
      this.recordEnhancedMetrics(req.method, req.path, enhancedRoute, 'success', duration);

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`❌ Proxy error for ${req.method} ${req.path}:`, error);
      this.recordMetrics(req.method, req.path, 'error', duration);

      // 发送错误响应
      if (!res.headersSent) {
        res.status(502).json({
          statusCode: 502,
          message: 'Bad Gateway',
          error: 'Proxy Error',
          timestamp: new Date().toISOString(),
        });
      }
    }
  }



  /**
   * 获取增强的代理中间件（支持区服路由）
   */
  private getEnhancedProxyMiddleware(enhancedRoute: EnhancedRouteResult) {
    const cacheKey = `${enhancedRoute.serviceName}_${enhancedRoute.routingStrategy}`;

    if (!this.proxyCache.has(cacheKey)) {
      const proxy = createProxyMiddleware({
        target: enhancedRoute.targetUrl,
        changeOrigin: true,
        timeout: this.configService.get<number>('gateway.proxy.timeout', 30000),

        // 🔧 添加区服路由头部
        onProxyReq: (proxyReq, req, res) => {
          // 添加增强头部
          Object.entries(enhancedRoute.headers).forEach(([key, value]) => {
            proxyReq.setHeader(key, value);
          });

          this.logger.debug(`📤 Proxy request headers added for ${enhancedRoute.serviceName}`);
        },

        onProxyRes: (proxyRes, req, res) => {
          // 添加响应头部标识
          if (proxyRes.headers) {
            proxyRes.headers['x-proxied-by'] = 'gateway';
            proxyRes.headers['x-routing-strategy'] = enhancedRoute.routingStrategy;
            proxyRes.headers['x-service-name'] = enhancedRoute.serviceName;
          }

          this.logger.debug(`📥 Proxy response from ${enhancedRoute.serviceName}: ${proxyRes.statusCode}`);
        },

        onError: (err, req, res) => {
          this.logger.error(`❌ Proxy error for ${enhancedRoute.serviceName}:`, err);
        },

        // 路径重写（如果需要）
        pathRewrite: this.getPathRewriteRules(enhancedRoute),
      });

      this.proxyCache.set(cacheKey, proxy);
    }

    return this.proxyCache.get(cacheKey);
  }

  /**
   * 获取或创建代理中间件（原有方法，保持兼容性）
   */
  private getProxyMiddleware(targetUrl: string, serviceName: string) {
    const cacheKey = `${targetUrl}:${serviceName}`;
    
    if (this.proxyCache.has(cacheKey)) {
      return this.proxyCache.get(cacheKey);
    }

    const proxy = createProxyMiddleware({
      target: targetUrl,
      changeOrigin: true,
      timeout: 30000,

      // 路径重写：/api/auth/health -> /api/health
      pathRewrite: {
        [`^${ROUTE_CONSTANTS.PROXY_PREFIX}/${serviceName}`]: '',
      },

      // 请求处理
      onProxyReq: (proxyReq, req, res) => {
        this.handleProxyRequest(proxyReq, req, res, serviceName);
      },

      // 响应处理
      onProxyRes: (proxyRes, req, res) => {
        this.handleProxyResponse(proxyRes, req, res, serviceName);
      },

      // 错误处理
      onError: (err, req, res) => {
        this.handleProxyError(err, req, res, serviceName);
      },
    });

    this.proxyCache.set(cacheKey, proxy);
    return proxy;
  }

  /**
   * 处理代理请求
   */
  private handleProxyRequest(proxyReq: any, req: Request, res: Response, serviceName: string): void {
    // 添加网关标识头部
    proxyReq.setHeader('X-Gateway', 'football-manager-gateway');
    proxyReq.setHeader('X-Gateway-Version', '1.0.0');
    proxyReq.setHeader('X-Forwarded-By', 'gateway');
    proxyReq.setHeader('X-Target-Service', serviceName);
    
    // 添加请求 ID
    const requestId = (req as any).requestId;
    if (requestId) {
      proxyReq.setHeader('X-Request-ID', requestId);
    }

    // 添加用户信息
    const user = (req as any).user;
    if (user) {
      proxyReq.setHeader('X-User-ID', user.id);
      if (user.roles) {
        proxyReq.setHeader('X-User-Roles', user.roles.join(','));
      }
    }

    // 处理请求体（修复 body parser 问题）
    if (req.body && (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH')) {
      const bodyData = JSON.stringify(req.body);
      
      // 设置正确的 Content-Type 和 Content-Length
      proxyReq.setHeader('Content-Type', 'application/json');
      proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
      
      // 写入请求体
      proxyReq.write(bodyData);
    }
  }

  /**
   * 处理代理响应
   */
  private handleProxyResponse(proxyRes: any, req: Request, res: Response, serviceName: string): void {
    // 添加响应头部
    proxyRes.headers['X-Proxied-By'] = 'football-manager-gateway';
    proxyRes.headers['X-Service'] = serviceName;
    
    // 记录响应日志
    this.logger.debug(`Response from ${serviceName}: ${proxyRes.statusCode}`);
  }

  /**
   * 处理代理错误
   */
  private handleProxyError(err: any, req: Request, res: Response, serviceName: string): void {
    this.logger.error(`Proxy error for service ${serviceName}:`, err);
    
    if (!res.headersSent) {
      res.status(502).json({
        statusCode: 502,
        message: 'Bad Gateway',
        error: 'Service Unavailable',
        service: serviceName,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * 记录监控指标
   */
  private recordMetrics(method: string, path: string, status: string, duration: number): void {
    try {
      // 使用现有的 recordHttpRequest 方法
      this.metricsService.recordHttpRequest(
        method,
        path,
        status === 'success' ? 200 : 500,
        duration,
      );
    } catch (error) {
      this.logger.warn('Failed to record metrics:', error);
    }
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.proxyCache.clear();
    this.logger.log('Proxy cache cleared');
  }

  /**
   * 记录增强指标（包含区服路由信息）
   */
  private recordEnhancedMetrics(
    method: string,
    path: string,
    enhancedRoute: EnhancedRouteResult,
    status: 'success' | 'error',
    duration: number
  ): void {
    try {
      // 记录基础指标
      this.recordMetrics(method, path, status, duration);

      // 记录区服路由相关指标（如果指标服务支持扩展参数）
      this.logger.debug(`📊 Enhanced metrics: ${enhancedRoute.serviceName} (${enhancedRoute.routingStrategy}) - ${status} in ${duration}ms`);
    } catch (error) {
      this.logger.error('Failed to record enhanced metrics:', error);
    }
  }

  /**
   * 获取路径重写规则
   */
  private getPathRewriteRules(enhancedRoute: EnhancedRouteResult): Record<string, string> | undefined {
    const rules: Record<string, string> = {};

    // 根据路由策略设置路径重写规则
    switch (enhancedRoute.routingStrategy) {
      case 'server_specific':
        // 移除路径中的区服标识（如果有）
        rules['^/api/server/[^/]+/(.*)'] = '/api/$1';
        break;

      case 'cross_server':
        // 移除跨服标识
        rules['^/api/cross-server/(.*)'] = '/api/$1';
        rules['^/api/cross/(.*)'] = '/api/$1';
        break;

      case 'global':
        // 移除全服标识
        rules['^/api/global/(.*)'] = '/api/$1';
        rules['^/api/all-servers/(.*)'] = '/api/$1';
        break;
    }

    return Object.keys(rules).length > 0 ? rules : undefined;
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.proxyCache.size,
      keys: Array.from(this.proxyCache.keys()),
    };
  }
}
