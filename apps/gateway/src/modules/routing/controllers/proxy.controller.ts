import { All, Controller, HttpException, HttpStatus, Logger, Req, <PERSON>s, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { ProxyService } from '../services/proxy.service';
import { LoadBalancingService } from '../../load-balancing/services/load-balancing.service';
import { MetricsService } from '../../monitoring/services/metrics.service';
import { RouteResolverService } from '../services/route-resolver.service';
import { ProxyRequestInterceptor } from '../interceptors/proxy-request.interceptor';
import { ROUTE_CONSTANTS } from '../../../common/constants';

/**
 * 智能代理控制器
 *
 * 专注于微服务代理：
 * - 只处理 /PROXY_PREFIX/* 路由
 * - 智能路由解析
 * - 服务发现和负载均衡
 * - 请求代理和错误处理
 */
@ApiTags('Proxy')
@Controller(ROUTE_CONSTANTS.PROXY_PREFIX.slice(1)) // 去掉前导斜杠
@UseInterceptors(ProxyRequestInterceptor)
export class ProxyController {
  private readonly logger = new Logger(ProxyController.name);
  
  // 移除硬编码的系统路由和服务路由映射，现在使用 IntelligentRouteResolverService

  constructor(
    private readonly proxyService: ProxyService,
    private readonly loadBalancerService: LoadBalancingService,
    private readonly configService: ConfigService,
    private readonly metricsService: MetricsService,
    private readonly intelligentRouteResolver: RouteResolverService,
  ) {}

  @All('*')
  async handleRequest(
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    const startTime = Date.now();
    const requestId = (req as any).requestId || this.generateRequestId();
    
    try {
      // 使用原始URL路径进行路由解析
      // req.originalUrl 包含完整的路径，包括 PROXY_PREFIX 前缀
      const fullPath = req.originalUrl.split('?')[0]; // 去掉查询参数

      this.logger.debug(`Processing request: ${req.method} ${req.originalUrl} -> req.path: ${req.path} -> fullPath: ${fullPath}`);

      // 使用智能路由解析器
      const routeResult = this.intelligentRouteResolver.resolveRoute(fullPath);

      // 处理不同的路由结果类型
      if (routeResult.type === 'not_found') {
        // 未知服务
        this.logger.debug(`Unknown service: ${fullPath}`);
        this.recordRouteResult(fullPath, 'unknown_service');

        throw new HttpException(
          {
            statusCode: routeResult.statusCode || HttpStatus.NOT_FOUND,
            message: routeResult.error || 'Unknown service',
            error: 'Not Found',
            path: fullPath,
            method: req.method,
            timestamp: new Date().toISOString(),
          },
          routeResult.statusCode || HttpStatus.NOT_FOUND,
        );
      }

      if (routeResult.type === 'service_unavailable') {
        // 服务不可用
        this.logger.debug(`Service unavailable: ${fullPath}`);
        this.recordRouteResult(fullPath, 'service_unavailable');

        throw new HttpException(
          {
            statusCode: routeResult.statusCode || HttpStatus.SERVICE_UNAVAILABLE,
            message: routeResult.error || 'Service unavailable',
            error: 'Service Unavailable',
            path: fullPath,
            method: req.method,
            timestamp: new Date().toISOString(),
          },
          routeResult.statusCode || HttpStatus.SERVICE_UNAVAILABLE,
        );
      }

      if (routeResult.type === 'internal') {
        // 记录路由处理结果指标
        this.logger.debug(`Fallback to system route: ${fullPath}`);
        this.recordRouteResult(fullPath, 'fallback');

        // 返回 404，让 NestJS 的其他路由处理
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: 'API endpoint not found',
            error: 'Not Found',
            path: fullPath,
            method: req.method,
            timestamp: new Date().toISOString(),
          },
          HttpStatus.NOT_FOUND,
        );
      }

      // 微服务代理路由
      const serviceName = routeResult.targetService;
      if (!serviceName) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: 'Service not found',
            error: 'Not Found',
            path: fullPath,
            method: req.method,
            timestamp: new Date().toISOString(),
          },
          HttpStatus.NOT_FOUND,
        );
      }

      // 获取服务实例
      const serviceInstance = await this.getServiceInstance(serviceName);
      if (!serviceInstance) {
        throw new HttpException(
          {
            statusCode: HttpStatus.SERVICE_UNAVAILABLE,
            message: 'Service unavailable',
            error: 'Service Unavailable',
            service: serviceName,
            timestamp: new Date().toISOString(),
          },
          HttpStatus.SERVICE_UNAVAILABLE,
        );
      }

      // 构建目标 URL（只包含基础 URL，路径由 http-proxy-middleware 自动处理）
      const targetUrl = this.buildTargetUrl(serviceInstance);

      // 记录请求日志
      this.logger.log(`[${requestId}] ${req.method} ${fullPath} -> ${serviceName} (${targetUrl})`);

      // 执行代理请求
      await this.proxyService.proxyRequest(req, res, targetUrl, serviceName);

      // 记录成功的请求
      const duration = Date.now() - startTime;
      this.recordRequestMetrics(req.method, fullPath, serviceName, 'success', duration);

    } catch (error) {
      const duration = Date.now() - startTime;
      this.handleError(error, req, res, requestId, duration);
    }
  }

  // 移除旧的路由匹配方法，现在使用 IntelligentRouteResolverService

  /**
   * 获取服务实例
   */
  private async getServiceInstance(serviceName: string): Promise<any> {
    try {
      // 获取所有实例进行调试
      const allInstances = this.loadBalancerService.getAllInstances(serviceName);
      this.logger.debug(`All instances for ${serviceName}:`, allInstances.map(inst => ({
        id: inst.id,
        url: inst.url,
        healthy: inst.healthy,
        lastHealthCheck: inst.lastHealthCheck,
      })));

      const healthyInstances = this.loadBalancerService.getHealthyInstances(serviceName);
      this.logger.debug(`Healthy instances for ${serviceName}:`, healthyInstances.map(inst => ({
        id: inst.id,
        url: inst.url,
        healthy: inst.healthy,
      })));

      if (healthyInstances.length === 0) {
        this.logger.warn(`No healthy instances found for service: ${serviceName}`);
        this.logger.warn(`Total instances: ${allInstances.length}, Healthy: ${healthyInstances.length}`);

        // 如果有实例但都不健康，尝试使用第一个实例
        if (allInstances.length > 0) {
          this.logger.warn(`Using first available instance despite health status`);
          return allInstances[0];
        }

        return null;
      }

      // 使用负载均衡选择实例
      return this.loadBalancerService.getServiceInstance(serviceName, 'round-robin');
    } catch (error) {
      this.logger.error(`Failed to get service instance for ${serviceName}:`, error);
      return null;
    }
  }

  /**
   * 构建目标 URL（只返回基础 URL，路径由 http-proxy-middleware 自动处理）
   */
  private buildTargetUrl(instance: any): string {
    const baseUrl = instance.url || `http://${instance.host}:${instance.port}`;

    // 确保 baseUrl 不以 / 结尾
    return baseUrl.replace(/\/$/, '');
  }

  /**
   * 处理错误
   */
  private handleError(
    error: any,
    req: Request,
    res: Response,
    requestId: string,
    duration: number,
  ): void {
    this.logger.error(`[${requestId}] Request failed after ${duration}ms:`, error);

    // 记录错误指标
    this.recordRequestMetrics(req.method, req.path, 'unknown', 'error', duration);

    // 如果响应已经发送，则不再处理
    if (res.headersSent) {
      return;
    }

    // 根据错误类型返回相应的状态码
    if (error instanceof HttpException) {
      const status = error.getStatus();
      const response = error.getResponse();
      res.status(status).json(response);
    } else {
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Internal Server Error',
        error: 'Gateway Error',
        requestId,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * 记录路由处理结果指标
   */
  private recordRouteResult(path: string, resultType: 'unknown_service' | 'service_unavailable' | 'fallback'): void {
    try {
      // 根据结果类型记录不同的指标
      switch (resultType) {
        case 'unknown_service':
          // 记录未知服务错误 - 使用代理错误记录
          this.metricsService.recordProxyError('GET', path, 404, 0);
          break;
        case 'service_unavailable':
          // 记录服务不可用错误 - 使用代理错误记录
          this.metricsService.recordProxyError('GET', path, 503, 0);
          break;
        case 'fallback':
          // 记录系统路由回退
          this.metricsService.recordSystemRouteSkip(path, 'fallback');
          break;
        default:
          this.logger.warn(`Unknown route result type: ${resultType}`);
      }
    } catch (error) {
      this.logger.warn('Failed to record route result metrics:', error);
    }
  }

  /**
   * 记录请求指标
   */
  private recordRequestMetrics(
    method: string,
    path: string,
    service: string,
    status: string,
    duration: number,
  ): void {
    try {
      // 使用现有的 recordHttpRequest 方法
      this.metricsService.recordHttpRequest(
        method,
        path,
        status === 'success' ? 200 : 500,
        duration,
      );
    } catch (error) {
      this.logger.warn('Failed to record request metrics:', error);
    }
  }

  /**
   * 生成请求 ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
