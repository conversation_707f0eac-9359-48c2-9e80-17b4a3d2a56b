import { Controller, Post, Get, Put, Delete, Body, Param, Query, UseGuards, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';

// 守卫
import { JwtAuthGuard } from '@gateway/modules/gateway-auth/guards/jwt-auth.guard';

// 服务
import { AnnouncementService } from '../services/announcement.service';
import { EventNotificationService } from '../services/event-notification.service';
import { GlobalBroadcastService } from '../services/global-broadcast.service';
import { MessagePersistenceService } from '../services/message-persistence.service';
import { QueueManagerService } from '../queue/queue-manager.service';
import { QueueMonitorService } from '../queue/queue-monitor.service';

// DTO
import { 
  CreateAnnouncementDto, 
  UpdateAnnouncementDto, 
  QueryAnnouncementDto,
  AnnouncementResponseDto 
} from '../dto/announcement.dto';
import { 
  CreateEventNotificationDto, 
  UpdateEventNotificationDto, 
  QueryEventNotificationDto,
  EventNotificationResponseDto 
} from '../dto/event-notification.dto';
import { 
  CreateBroadcastMessageDto, 
  UpdateBroadcastMessageDto, 
  QueryBroadcastMessageDto,
  BroadcastMessageResponseDto,
  BatchOperationDto,
  MessageStatsQueryDto,
  MessageStatsResponseDto,
  MessageAcknowledgmentDto
} from '../dto/broadcast-message.dto';

/**
 * 全服消息控制器
 * 严格按照设计文档定义的API接口
 * 
 * 核心功能：
 * 1. 系统公告管理：创建、更新、删除、查询系统公告
 * 2. 活动通知管理：创建、更新、删除、查询活动通知
 * 3. 广播消息管理：通用的全服消息管理
 * 4. 消息统计分析：消息投递统计和效果分析
 * 5. 系统监控：队列状态和系统健康监控
 */
@ApiTags('Global Messaging')
@ApiBearerAuth()
@Controller('api/global-messaging')
@UseGuards(JwtAuthGuard)
export class GlobalMessagingController {
  constructor(
    private readonly announcementService: AnnouncementService,
    private readonly eventNotificationService: EventNotificationService,
    private readonly globalBroadcastService: GlobalBroadcastService,
    private readonly messagePersistenceService: MessagePersistenceService,
    private readonly queueManagerService: QueueManagerService,
    private readonly queueMonitorService: QueueMonitorService,
  ) {}

  // ==================== 系统公告管理 ====================

  /**
   * 创建系统公告
   */
  @Post('announcements')
  @ApiOperation({ summary: '创建系统公告' })
  @ApiResponse({ status: HttpStatus.CREATED, description: '公告创建成功', type: AnnouncementResponseDto })
  async createAnnouncement(@Body() createDto: CreateAnnouncementDto): Promise<any> {
    const announcement = await this.announcementService.createAnnouncement(createDto);
    
    return {
      success: true,
      message: '系统公告创建成功',
      data: announcement,
    };
  }

  /**
   * 获取系统公告列表
   */
  @Get('announcements')
  @ApiOperation({ summary: '获取系统公告列表' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功', type: [AnnouncementResponseDto] })
  async getAnnouncements(@Query() queryDto: QueryAnnouncementDto): Promise<any> {
    const announcements = await this.announcementService.getAnnouncements(queryDto);
    
    return {
      success: true,
      message: '获取系统公告列表成功',
      data: {
        announcements,
        total: announcements.length,
        filters: queryDto,
      },
    };
  }

  /**
   * 获取系统公告详情
   */
  @Get('announcements/:id')
  @ApiOperation({ summary: '获取系统公告详情' })
  @ApiParam({ name: 'id', description: '公告ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功', type: AnnouncementResponseDto })
  async getAnnouncement(@Param('id') id: string): Promise<any> {
    const announcement = await this.announcementService.getAnnouncement(id);
    
    if (!announcement) {
      return {
        success: false,
        message: '系统公告不存在',
        data: null,
      };
    }

    return {
      success: true,
      message: '获取系统公告详情成功',
      data: announcement,
    };
  }

  /**
   * 更新系统公告
   */
  @Put('announcements/:id')
  @ApiOperation({ summary: '更新系统公告' })
  @ApiParam({ name: 'id', description: '公告ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '更新成功', type: AnnouncementResponseDto })
  async updateAnnouncement(
    @Param('id') id: string,
    @Body() updateDto: UpdateAnnouncementDto
  ): Promise<any> {
    const announcement = await this.announcementService.updateAnnouncement(id, updateDto);
    
    return {
      success: true,
      message: '系统公告更新成功',
      data: announcement,
    };
  }

  /**
   * 删除系统公告
   */
  @Delete('announcements/:id')
  @ApiOperation({ summary: '删除系统公告' })
  @ApiParam({ name: 'id', description: '公告ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '删除成功' })
  async deleteAnnouncement(@Param('id') id: string): Promise<any> {
    const deleted = await this.announcementService.deleteAnnouncement(id);
    
    return {
      success: deleted,
      message: deleted ? '系统公告删除成功' : '系统公告不存在',
      data: { id },
    };
  }

  /**
   * 发布系统公告
   */
  @Post('announcements/:id/publish')
  @ApiOperation({ summary: '发布系统公告' })
  @ApiParam({ name: 'id', description: '公告ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '发布成功' })
  async publishAnnouncement(@Param('id') id: string): Promise<any> {
    const announcement = await this.announcementService.getAnnouncement(id);
    
    if (!announcement) {
      return {
        success: false,
        message: '系统公告不存在',
        data: null,
      };
    }

    await this.announcementService.publishAnnouncement(announcement);
    
    return {
      success: true,
      message: '系统公告发布成功',
      data: { id },
    };
  }

  // ==================== 活动通知管理 ====================

  /**
   * 创建活动通知
   */
  @Post('events')
  @ApiOperation({ summary: '创建活动通知' })
  @ApiResponse({ status: HttpStatus.CREATED, description: '活动通知创建成功', type: EventNotificationResponseDto })
  async createEventNotification(@Body() createDto: CreateEventNotificationDto): Promise<any> {
    const eventNotification = await this.eventNotificationService.createEventNotification(createDto);
    
    return {
      success: true,
      message: '活动通知创建成功',
      data: eventNotification,
    };
  }

  /**
   * 获取活动通知列表
   */
  @Get('events')
  @ApiOperation({ summary: '获取活动通知列表' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功', type: [EventNotificationResponseDto] })
  async getEventNotifications(@Query() queryDto: QueryEventNotificationDto): Promise<any> {
    const notifications = await this.eventNotificationService.getEventNotifications(queryDto);
    
    return {
      success: true,
      message: '获取活动通知列表成功',
      data: {
        notifications,
        total: notifications.length,
        filters: queryDto,
      },
    };
  }

  /**
   * 获取活动通知详情
   */
  @Get('events/:id')
  @ApiOperation({ summary: '获取活动通知详情' })
  @ApiParam({ name: 'id', description: '通知ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功', type: EventNotificationResponseDto })
  async getEventNotification(@Param('id') id: string): Promise<any> {
    const notification = await this.eventNotificationService.getEventNotification(id);
    
    if (!notification) {
      return {
        success: false,
        message: '活动通知不存在',
        data: null,
      };
    }

    return {
      success: true,
      message: '获取活动通知详情成功',
      data: notification,
    };
  }

  /**
   * 更新活动通知
   */
  @Put('events/:id')
  @ApiOperation({ summary: '更新活动通知' })
  @ApiParam({ name: 'id', description: '通知ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '更新成功', type: EventNotificationResponseDto })
  async updateEventNotification(
    @Param('id') id: string,
    @Body() updateDto: UpdateEventNotificationDto
  ): Promise<any> {
    const notification = await this.eventNotificationService.updateEventNotification(id, updateDto);
    
    return {
      success: true,
      message: '活动通知更新成功',
      data: notification,
    };
  }

  /**
   * 删除活动通知
   */
  @Delete('events/:id')
  @ApiOperation({ summary: '删除活动通知' })
  @ApiParam({ name: 'id', description: '通知ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '删除成功' })
  async deleteEventNotification(@Param('id') id: string): Promise<any> {
    const deleted = await this.eventNotificationService.deleteEventNotification(id);
    
    return {
      success: deleted,
      message: deleted ? '活动通知删除成功' : '活动通知不存在',
      data: { id },
    };
  }

  // ==================== 广播消息管理 ====================

  /**
   * 创建广播消息
   */
  @Post('messages')
  @ApiOperation({ summary: '创建广播消息' })
  @ApiResponse({ status: HttpStatus.CREATED, description: '消息创建成功', type: BroadcastMessageResponseDto })
  async createBroadcastMessage(@Body() createDto: CreateBroadcastMessageDto): Promise<any> {
    const message = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...createDto,
      createdAt: new Date(),
      publishAt: createDto.publishAt || new Date(),
    };

    await this.messagePersistenceService.persistMessage(message as any);
    
    // 如果是立即发布，则广播消息
    if (message.publishAt <= new Date()) {
      await this.globalBroadcastService.broadcastMessage(message as any);
    }
    
    return {
      success: true,
      message: '广播消息创建成功',
      data: message,
    };
  }

  /**
   * 获取消息统计
   */
  @Get('messages/:id/stats')
  @ApiOperation({ summary: '获取消息统计' })
  @ApiParam({ name: 'id', description: '消息ID' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功', type: MessageStatsResponseDto })
  async getMessageStats(@Param('id') id: string): Promise<any> {
    const stats = await this.globalBroadcastService.getMessageStats(id);
    
    return {
      success: true,
      message: '获取消息统计成功',
      data: stats,
    };
  }

  // ==================== 系统监控 ====================

  /**
   * 获取系统健康状态
   */
  @Get('system/health')
  @ApiOperation({ summary: '获取系统健康状态' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  async getSystemHealth(): Promise<any> {
    const healthReport = await this.queueMonitorService.generateHealthReport();
    const performanceMetrics = await this.queueMonitorService.getPerformanceMetrics();
    
    return {
      success: true,
      message: '获取系统健康状态成功',
      data: {
        health: healthReport,
        performance: performanceMetrics,
      },
    };
  }

  /**
   * 获取队列状态
   */
  @Get('system/queue-status')
  @ApiOperation({ summary: '获取队列状态' })
  @ApiResponse({ status: HttpStatus.OK, description: '获取成功' })
  async getQueueStatus(): Promise<any> {
    const queueStats = await this.queueManagerService.getQueueStats();
    
    return {
      success: true,
      message: '获取队列状态成功',
      data: queueStats,
    };
  }
}
