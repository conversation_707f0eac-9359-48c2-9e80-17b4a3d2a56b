import { Controller, Post, Get, Put, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

// 守卫
import { JwtAuthGuard } from '@gateway/modules/gateway-auth/guards/jwt-auth.guard';

// 服务
import { GlobalBroadcastService } from '../services/global-broadcast.service';
import { MessagePublisherService } from '../services/message-publisher.service';
import { MessageSchedulerService } from '../services/message-scheduler.service';
import { MessagePersistenceService } from '../services/message-persistence.service';
import { QueueManagerService } from '../queue/queue-manager.service';
import { QueueMonitorService } from '../queue/queue-monitor.service';

// 接口定义
import { BaseGlobalMessage, GlobalMessageType, MessagePriority } from '../interfaces/global-message.interface';

/**
 * 管理员消息控制器
 * 提供全服消息的管理接口，仅限管理员使用
 * 
 * 核心功能：
 * 1. 消息发布管理：创建、发布、取消全服消息
 * 2. 消息调度管理：调度、重新调度消息
 * 3. 队列管理：监控和管理消息队列
 * 4. 统计分析：提供消息系统的统计数据
 * 5. 系统监控：监控消息系统的健康状态
 */
@ApiTags('Admin Global Messaging')
@ApiBearerAuth()
@Controller('admin/global-messages')
@UseGuards(JwtAuthGuard)
export class AdminMessagingController {
  constructor(
    private readonly globalBroadcastService: GlobalBroadcastService,
    private readonly messagePublisherService: MessagePublisherService,
    private readonly messageSchedulerService: MessageSchedulerService,
    private readonly messagePersistenceService: MessagePersistenceService,
    private readonly queueManagerService: QueueManagerService,
    private readonly queueMonitorService: QueueMonitorService,
  ) {}

  /**
   * 发布全服消息
   */
  @Post()
  @ApiOperation({ summary: '发布全服消息' })
  @ApiResponse({ status: 201, description: '消息发布成功' })
  async publishMessage(@Body() messageData: any): Promise<any> {
    const message: BaseGlobalMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: messageData.type || GlobalMessageType.SYSTEM_ANNOUNCEMENT,
      priority: messageData.priority || MessagePriority.MEDIUM,
      title: messageData.title,
      content: messageData.content,
      createdAt: new Date(),
      publishAt: messageData.publishAt ? new Date(messageData.publishAt) : new Date(),
      expireAt: messageData.expireAt ? new Date(messageData.expireAt) : undefined,
      target: messageData.target,
      metadata: messageData.metadata || {},
    };

    await this.messagePublisherService.publishMessage(message);

    return {
      success: true,
      message: '消息发布成功',
      data: { messageId: message.id },
    };
  }

  /**
   * 获取消息列表
   */
  @Get()
  @ApiOperation({ summary: '获取消息列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getMessages(
    @Query('type') type?: GlobalMessageType,
    @Query('priority') priority?: MessagePriority,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('limit') limit?: number,
  ): Promise<any> {
    const filters = {
      type,
      priority,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      limit: limit || 50,
    };

    const messages = await this.messagePersistenceService.getMessageHistory(filters);

    return {
      success: true,
      message: '获取消息列表成功',
      data: {
        messages,
        total: messages.length,
        filters,
      },
    };
  }

  /**
   * 获取消息详情
   */
  @Get(':messageId')
  @ApiOperation({ summary: '获取消息详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getMessage(@Param('messageId') messageId: string): Promise<any> {
    const message = await this.messagePersistenceService.getMessage(messageId);
    
    if (!message) {
      return {
        success: false,
        message: '消息不存在',
        data: null,
      };
    }

    // 获取投递记录
    const deliveryRecords = await this.messagePersistenceService.getDeliveryRecords(messageId);

    return {
      success: true,
      message: '获取消息详情成功',
      data: {
        message,
        deliveryRecords,
      },
    };
  }

  /**
   * 取消消息
   */
  @Delete(':messageId')
  @ApiOperation({ summary: '取消消息' })
  @ApiResponse({ status: 200, description: '取消成功' })
  async cancelMessage(@Param('messageId') messageId: string): Promise<any> {
    // 尝试从发布队列取消
    const cancelledFromPublisher = await this.messagePublisherService.cancelMessage(messageId);
    
    // 尝试从调度队列取消
    const cancelledFromScheduler = await this.messageSchedulerService.cancelScheduledMessage(messageId);
    
    // 尝试从消息队列取消
    const cancelledFromQueue = await this.queueManagerService.removeMessage(messageId);

    const cancelled = cancelledFromPublisher || cancelledFromScheduler || cancelledFromQueue;

    if (cancelled) {
      // 更新消息状态
      await this.messagePersistenceService.updateMessageStatus(messageId, 'cancelled');
    }

    return {
      success: cancelled,
      message: cancelled ? '消息取消成功' : '消息未找到或已处理',
      data: { messageId },
    };
  }

  /**
   * 调度消息
   */
  @Post(':messageId/schedule')
  @ApiOperation({ summary: '调度消息' })
  @ApiResponse({ status: 200, description: '调度成功' })
  async scheduleMessage(
    @Param('messageId') messageId: string,
    @Body() scheduleData: { publishAt: string },
  ): Promise<any> {
    const message = await this.messagePersistenceService.getMessage(messageId);
    
    if (!message) {
      return {
        success: false,
        message: '消息不存在',
        data: null,
      };
    }

    const publishAt = new Date(scheduleData.publishAt);
    await this.messageSchedulerService.scheduleMessage(message, publishAt);

    return {
      success: true,
      message: '消息调度成功',
      data: { messageId, publishAt },
    };
  }

  /**
   * 获取消息统计
   */
  @Get(':messageId/stats')
  @ApiOperation({ summary: '获取消息统计' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getMessageStats(@Param('messageId') messageId: string): Promise<any> {
    const stats = await this.globalBroadcastService.getMessageStats(messageId);

    return {
      success: true,
      message: '获取消息统计成功',
      data: stats,
    };
  }

  /**
   * 获取队列状态
   */
  @Get('queue/status')
  @ApiOperation({ summary: '获取队列状态' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getQueueStatus(): Promise<any> {
    const queueStats = await this.queueManagerService.getQueueStats();
    const schedulerStats = await this.messageSchedulerService.getSchedulerStats();
    const publishStats = await this.messagePublisherService.getPublishStats();

    return {
      success: true,
      message: '获取队列状态成功',
      data: {
        queue: queueStats,
        scheduler: schedulerStats,
        publisher: publishStats,
      },
    };
  }

  /**
   * 获取系统健康状态
   */
  @Get('system/health')
  @ApiOperation({ summary: '获取系统健康状态' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getSystemHealth(): Promise<any> {
    const healthReport = await this.queueMonitorService.generateHealthReport();
    const performanceMetrics = await this.queueMonitorService.getPerformanceMetrics();
    const scalingNeeds = await this.queueMonitorService.checkScalingNeeds();

    return {
      success: true,
      message: '获取系统健康状态成功',
      data: {
        health: healthReport,
        performance: performanceMetrics,
        scaling: scalingNeeds,
      },
    };
  }

  /**
   * 清空队列
   */
  @Delete('queue/clear')
  @ApiOperation({ summary: '清空队列' })
  @ApiResponse({ status: 200, description: '清空成功' })
  async clearQueue(@Query('priority') priority?: MessagePriority): Promise<any> {
    const clearedCount = await this.queueManagerService.clearQueue(priority);

    return {
      success: true,
      message: '队列清空成功',
      data: {
        clearedCount,
        priority: priority || 'all',
      },
    };
  }

  /**
   * 批量重试死信消息
   */
  @Post('dead-letter/retry-all')
  @ApiOperation({ summary: '批量重试死信消息' })
  @ApiResponse({ status: 200, description: '重试完成' })
  async retryAllDeadLetterMessages(): Promise<any> {
    // 这里需要注入DeadLetterQueueService
    // const results = await this.deadLetterQueueService.retryAllDeadLetterMessages();

    return {
      success: true,
      message: '死信消息重试完成',
      data: {
        success: 0,
        failed: 0,
      },
    };
  }
}
