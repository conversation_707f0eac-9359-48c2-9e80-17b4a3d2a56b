import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// 核心模块
import { RedisModule } from '@common/redis';

// 服务
import { GlobalBroadcastService } from './services/global-broadcast.service';
import { MessagePublisherService } from './services/message-publisher.service';
import { MessageSchedulerService } from './services/message-scheduler.service';
import { MessagePersistenceService } from './services/message-persistence.service';
import { AnnouncementService } from './services/announcement.service';
import { EventNotificationService } from './services/event-notification.service';

// 队列系统
import { QueueManagerService } from './queue/queue-manager.service';
import { PriorityQueueService } from './queue/priority-queue.service';
import { DeadLetterQueueService } from './queue/dead-letter-queue.service';
import { QueueMonitorService } from './queue/queue-monitor.service';

// 控制器
import { GlobalMessagingController } from './controllers/global-messaging.controller';
import { AdminMessagingController } from './controllers/admin-messaging.controller';
import { UserMessagingController } from './controllers/user-messaging.controller';

/**
 * 全服消息模块
 * 负责全服消息的发布、路由、投递和管理
 * 
 * 核心功能：
 * - 系统公告全服广播
 * - 活动通知推送
 * - 紧急维护通知
 * - 跨服事件广播
 * - 消息优先级管理
 * - 消息持久化和重发机制
 */
@Module({
  imports: [
    ConfigModule,
    RedisModule,
  ],
  controllers: [
    GlobalMessagingController,
    AdminMessagingController,
    UserMessagingController,
  ],
  providers: [
    // 核心服务
    GlobalBroadcastService,
    MessagePublisherService,
    MessageSchedulerService,
    MessagePersistenceService,
    AnnouncementService,
    EventNotificationService,
    
    // 队列系统
    QueueManagerService,
    PriorityQueueService,
    DeadLetterQueueService,
    QueueMonitorService,
  ],
  exports: [
    GlobalBroadcastService,
    MessagePublisherService,
    AnnouncementService,
    EventNotificationService,
    QueueManagerService,
  ],
})
export class GlobalMessagingModule {}
