import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';

export interface CircuitBreakerConfig {
  failureThreshold: number;
  resetTimeout: number;
  monitoringPeriod: number;
  volumeThreshold: number;
  errorThresholdPercentage: number;
  fallback?: {
    enabled: boolean;
    response?: any;
    redirect?: string;
  };
}

export enum CircuitBreakerState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half_open',
}

export interface CircuitBreakerStats {
  state: CircuitBreakerState;
  failureCount: number;
  successCount: number;
  totalRequests: number;
  errorRate: number;
  lastFailureTime?: Date;
  lastSuccessTime?: Date;
  nextAttemptTime?: Date;
  stateChangedAt: Date;
}

export interface CircuitBreakerResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  fromFallback: boolean;
  stats: CircuitBreakerStats;
}

@Injectable()
export class CircuitBreakerService {
  private readonly logger = new Logger(CircuitBreakerService.name);
  private readonly circuits = new Map<string, CircuitBreaker>();
  private readonly defaultConfig: CircuitBreakerConfig;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.defaultConfig = {
      failureThreshold: this.configService.get<number>('circuitBreaker.failureThreshold', 5),
      resetTimeout: this.configService.get<number>('circuitBreaker.resetTimeout', 60000),
      monitoringPeriod: this.configService.get<number>('circuitBreaker.monitoringPeriod', 10000),
      volumeThreshold: this.configService.get<number>('circuitBreaker.volumeThreshold', 10),
      errorThresholdPercentage: this.configService.get<number>('circuitBreaker.errorThresholdPercentage', 50),
      fallback: {
        enabled: this.configService.get<boolean>('circuitBreaker.fallback.enabled', true),
      },
    };
  }

  /**
   * 执行受熔断器保护的操作
   */
  async execute<T>(
    circuitName: string,
    operation: () => Promise<T>,
    config?: Partial<CircuitBreakerConfig>,
  ): Promise<CircuitBreakerResult<T>> {
    const circuit = this.getOrCreateCircuit(circuitName, config);
    
    return await circuit.execute(operation);
  }

  /**
   * 获取熔断器状态
   */
  getCircuitState(circuitName: string): CircuitBreakerState {
    const circuit = this.circuits.get(circuitName);
    return circuit ? circuit.getState() : CircuitBreakerState.CLOSED;
  }

  /**
   * 获取熔断器统计信息
   */
  getCircuitStats(circuitName: string): CircuitBreakerStats | null {
    const circuit = this.circuits.get(circuitName);
    return circuit ? circuit.getStats() : null;
  }

  /**
   * 获取所有熔断器统计信息
   */
  getAllCircuitStats(): Record<string, CircuitBreakerStats> {
    const stats: Record<string, CircuitBreakerStats> = {};
    
    for (const [name, circuit] of this.circuits) {
      stats[name] = circuit.getStats();
    }
    
    return stats;
  }

  /**
   * 手动打开熔断器
   */
  openCircuit(circuitName: string): void {
    const circuit = this.circuits.get(circuitName);
    if (circuit) {
      circuit.forceOpen();
    }
  }

  /**
   * 手动关闭熔断器
   */
  closeCircuit(circuitName: string): void {
    const circuit = this.circuits.get(circuitName);
    if (circuit) {
      circuit.forceClose();
    }
  }

  /**
   * 重置熔断器
   */
  resetCircuit(circuitName: string): void {
    const circuit = this.circuits.get(circuitName);
    if (circuit) {
      circuit.reset();
    }
  }

  /**
   * 移除熔断器
   */
  removeCircuit(circuitName: string): boolean {
    return this.circuits.delete(circuitName);
  }

  // ==================== 私有方法 ====================

  private getOrCreateCircuit(circuitName: string, config?: Partial<CircuitBreakerConfig>): CircuitBreaker {
    let circuit = this.circuits.get(circuitName);
    
    if (!circuit) {
      const finalConfig = { ...this.defaultConfig, ...config };
      circuit = new CircuitBreaker(circuitName, finalConfig, this.eventEmitter, this.logger);
      this.circuits.set(circuitName, circuit);
    }
    
    return circuit;
  }
}

class CircuitBreaker {
  private state: CircuitBreakerState = CircuitBreakerState.CLOSED;
  private failureCount = 0;
  private successCount = 0;
  private totalRequests = 0;
  private lastFailureTime?: Date;
  private lastSuccessTime?: Date;
  private stateChangedAt = new Date();
  private nextAttemptTime?: Date;
  private readonly requestHistory: { timestamp: Date; success: boolean }[] = [];

  constructor(
    private readonly name: string,
    private readonly config: CircuitBreakerConfig,
    private readonly eventEmitter: EventEmitter2,
    private readonly logger: Logger,
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<CircuitBreakerResult<T>> {
    this.cleanupHistory();
    
    if (this.state === CircuitBreakerState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.setState(CircuitBreakerState.HALF_OPEN);
      } else {
        return this.handleOpenCircuit();
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      
      return {
        success: true,
        data: result,
        fromFallback: false,
        stats: this.getStats(),
      };
    } catch (error) {
      this.onFailure(error as Error);
      
      if (this.state === CircuitBreakerState.OPEN) {
        return this.handleOpenCircuit();
      }
      
      return {
        success: false,
        error: error as Error,
        fromFallback: false,
        stats: this.getStats(),
      };
    }
  }

  getState(): CircuitBreakerState {
    return this.state;
  }

  getStats(): CircuitBreakerStats {
    return {
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      totalRequests: this.totalRequests,
      errorRate: this.calculateErrorRate(),
      lastFailureTime: this.lastFailureTime,
      lastSuccessTime: this.lastSuccessTime,
      nextAttemptTime: this.nextAttemptTime,
      stateChangedAt: this.stateChangedAt,
    };
  }

  forceOpen(): void {
    this.setState(CircuitBreakerState.OPEN);
    this.nextAttemptTime = new Date(Date.now() + this.config.resetTimeout);
  }

  forceClose(): void {
    this.setState(CircuitBreakerState.CLOSED);
    this.reset();
  }

  reset(): void {
    this.failureCount = 0;
    this.successCount = 0;
    this.totalRequests = 0;
    this.lastFailureTime = undefined;
    this.lastSuccessTime = undefined;
    this.nextAttemptTime = undefined;
    this.requestHistory.length = 0;
    this.setState(CircuitBreakerState.CLOSED);
  }

  private onSuccess(): void {
    this.successCount++;
    this.totalRequests++;
    this.lastSuccessTime = new Date();
    this.addToHistory(true);

    if (this.state === CircuitBreakerState.HALF_OPEN) {
      this.setState(CircuitBreakerState.CLOSED);
      this.failureCount = 0; // 重置失败计数
    }
  }

  private onFailure(error: Error): void {
    this.failureCount++;
    this.totalRequests++;
    this.lastFailureTime = new Date();
    this.addToHistory(false);

    if (this.shouldOpenCircuit()) {
      this.setState(CircuitBreakerState.OPEN);
      this.nextAttemptTime = new Date(Date.now() + this.config.resetTimeout);
    }
  }

  private shouldOpenCircuit(): boolean {
    // 检查请求量是否达到阈值
    if (this.totalRequests < this.config.volumeThreshold) {
      return false;
    }

    // 检查失败次数是否达到阈值
    if (this.failureCount >= this.config.failureThreshold) {
      return true;
    }

    // 检查错误率是否达到阈值
    const errorRate = this.calculateErrorRate();
    return errorRate >= this.config.errorThresholdPercentage;
  }

  private shouldAttemptReset(): boolean {
    return this.nextAttemptTime ? Date.now() >= this.nextAttemptTime.getTime() : false;
  }

  private calculateErrorRate(): number {
    if (this.totalRequests === 0) return 0;
    return (this.failureCount / this.totalRequests) * 100;
  }

  private setState(newState: CircuitBreakerState): void {
    if (this.state !== newState) {
      const oldState = this.state;
      this.state = newState;
      this.stateChangedAt = new Date();
      
      this.logger.log(`Circuit breaker ${this.name} state changed: ${oldState} -> ${newState}`);
      
      this.eventEmitter.emit('circuit-breaker.state-changed', {
        name: this.name,
        oldState,
        newState,
        timestamp: this.stateChangedAt,
        stats: this.getStats(),
      });
    }
  }

  private addToHistory(success: boolean): void {
    this.requestHistory.push({
      timestamp: new Date(),
      success,
    });
  }

  private cleanupHistory(): void {
    const cutoff = Date.now() - this.config.monitoringPeriod;
    
    while (this.requestHistory.length > 0 && this.requestHistory[0].timestamp.getTime() < cutoff) {
      this.requestHistory.shift();
    }
  }

  private handleOpenCircuit<T>(): CircuitBreakerResult<T> {
    if (this.config.fallback?.enabled) {
      return {
        success: true,
        data: this.config.fallback.response,
        fromFallback: true,
        stats: this.getStats(),
      };
    }
    
    return {
      success: false,
      error: new Error(`Circuit breaker ${this.name} is open`),
      fromFallback: false,
      stats: this.getStats(),
    };
  }
}
