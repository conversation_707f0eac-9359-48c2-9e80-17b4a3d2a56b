import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@common/redis';
import { Request, Response } from 'express';
import { createHash } from 'crypto';

export interface CacheConfig {
  enabled: boolean;
  ttl: number;
  key?: string;
  vary?: string[];
  skipCache?: (req: Request) => boolean;
  skipCacheOnError?: boolean;
  compress?: boolean;
  tags?: string[];
}

export interface CacheEntry {
  data: any;
  headers: Record<string, string>;
  statusCode: number;
  timestamp: Date;
  ttl: number;
  tags: string[];
  compressed: boolean;
}

export interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  hitRate: number;
  totalSize: number;
  keyCount: number;
}

@Injectable()
export class GatewayCacheService {
  private readonly logger = new Logger(GatewayCacheService.name);
  private readonly defaultConfig: CacheConfig;
  private readonly stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    hitRate: 0,
    totalSize: 0,
    keyCount: 0,
  };

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {
    this.defaultConfig = {
      enabled: this.configService.get<boolean>('cache.enabled', true),
      ttl: this.configService.get<number>('cache.ttl', 3600),
      compress: this.configService.get<boolean>('cache.compress', true),
      tags: [],
    };
  }

  /**
   * 获取缓存数据
   */
  async get(key: string): Promise<CacheEntry | null> {
    if (!this.defaultConfig.enabled) {
      return null;
    }

    try {
      const cacheKey = this.buildCacheKey(key);
      const data = await this.redisService.get(cacheKey);
      
      if (!data) {
        this.stats.misses++;
        this.updateHitRate();
        return null;
      }

      const entry: CacheEntry = data as CacheEntry;
      
      // 检查是否过期
      if (this.isExpired(entry)) {
        await this.delete(key);
        this.stats.misses++;
        this.updateHitRate();
        return null;
      }

      this.stats.hits++;
      this.updateHitRate();
      
      return entry;
    } catch (error) {
      this.logger.error(`Cache get error for key ${key}:`, error);
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }
  }

  /**
   * 设置缓存数据
   */
  async set(
    key: string,
    data: any,
    headers: Record<string, string>,
    statusCode: number,
    config?: Partial<CacheConfig>,
  ): Promise<void> {
    if (!this.defaultConfig.enabled) {
      return;
    }

    try {
      const finalConfig = { ...this.defaultConfig, ...config };
      const cacheKey = this.buildCacheKey(key);
      
      const entry: CacheEntry = {
        data,
        headers: this.filterCacheableHeaders(headers),
        statusCode,
        timestamp: new Date(),
        ttl: finalConfig.ttl,
        tags: finalConfig.tags || [],
        compressed: finalConfig.compress || false,
      };

      // 压缩数据（如果启用）
      if (entry.compressed) {
        entry.data = await this.compressData(entry.data);
      }

      // RedisService会自动处理序列化
      await this.redisService.set(cacheKey, entry, finalConfig.ttl);
      
      // 设置标签索引
      if (entry.tags.length > 0) {
        await this.setTagIndex(entry.tags, cacheKey);
      }

      this.stats.sets++;
      this.stats.keyCount++;
      const entrySize = JSON.stringify(entry).length;
      this.stats.totalSize += entrySize;

      this.logger.debug(`Cache set for key: ${key}, size: ${entrySize} bytes`);
    } catch (error) {
      this.logger.error(`Cache set error for key ${key}:`, error);
    }
  }

  /**
   * 删除缓存数据
   */
  async delete(key: string): Promise<boolean> {
    try {
      const cacheKey = this.buildCacheKey(key);
      const result = await this.redisService.del(cacheKey);
      
      if (result > 0) {
        this.stats.deletes++;
        this.stats.keyCount = Math.max(0, this.stats.keyCount - 1);
        return true;
      }
      
      return false;
    } catch (error) {
      this.logger.error(`Cache delete error for key ${key}:`, error);
      return false;
    }
  }

  /**
   * 根据标签删除缓存
   */
  async deleteByTags(tags: string[]): Promise<number> {
    try {
      let deletedCount = 0;
      
      for (const tag of tags) {
        const tagKey = this.buildTagKey(tag);
        const keys = await this.redisService.getClient().smembers(tagKey);
        
        if (keys.length > 0) {
          const deleted = await this.redisService.getClient().del(...keys);
          deletedCount += deleted;
          
          // 清理标签索引
          await this.redisService.del(tagKey);
        }
      }
      
      this.stats.deletes += deletedCount;
      this.stats.keyCount = Math.max(0, this.stats.keyCount - deletedCount);
      
      return deletedCount;
    } catch (error) {
      this.logger.error(`Cache delete by tags error:`, error);
      return 0;
    }
  }

  /**
   * 清空所有缓存
   */
  async clear(): Promise<void> {
    try {
      const pattern = this.buildCacheKey('*');
      const keys = await this.redisService.getClient().keys(pattern);
      
      if (keys.length > 0) {
        await this.redisService.getClient().del(...keys);
        this.stats.deletes += keys.length;
        this.stats.keyCount = 0;
        this.stats.totalSize = 0;
      }
      
      this.logger.log(`Cache cleared: ${keys.length} keys deleted`);
    } catch (error) {
      this.logger.error('Cache clear error:', error);
    }
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(req: Request, config?: Partial<CacheConfig>): string {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    if (finalConfig.key) {
      return finalConfig.key;
    }

    // 基础键：方法 + 路径
    let key = `${req.method}:${req.path}`;
    
    // 添加查询参数
    if (Object.keys(req.query).length > 0) {
      const sortedQuery = Object.keys(req.query)
        .sort()
        .map(k => `${k}=${req.query[k]}`)
        .join('&');
      key += `?${sortedQuery}`;
    }
    
    // 添加 Vary 头部
    if (finalConfig.vary && finalConfig.vary.length > 0) {
      const varyValues = finalConfig.vary
        .map(header => `${header}:${req.get(header) || ''}`)
        .join('|');
      key += `|${varyValues}`;
    }
    
    // 添加用户信息（如果已认证）
    const user = (req as any).user;
    if (user?.id) {
      key += `|user:${user.id}`;
    }
    
    return this.hashKey(key);
  }

  /**
   * 检查是否应该缓存响应
   */
  shouldCache(req: Request, res: Response, config?: Partial<CacheConfig>): boolean {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    if (!finalConfig.enabled) {
      return false;
    }
    
    // 检查 HTTP 方法
    if (req.method !== 'GET' && req.method !== 'HEAD') {
      return false;
    }
    
    // 检查状态码
    if (res.statusCode < 200 || res.statusCode >= 300) {
      return false;
    }
    
    // 检查自定义跳过条件
    if (finalConfig.skipCache && finalConfig.skipCache(req)) {
      return false;
    }
    
    // 检查 Cache-Control 头部
    const cacheControl = res.get('Cache-Control');
    if (cacheControl && (cacheControl.includes('no-cache') || cacheControl.includes('no-store'))) {
      return false;
    }
    
    return true;
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats.hits = 0;
    this.stats.misses = 0;
    this.stats.sets = 0;
    this.stats.deletes = 0;
    this.stats.hitRate = 0;
  }

  // ==================== 私有方法 ====================

  private buildCacheKey(key: string): string {
    return `gateway:cache:${key}`;
  }

  private buildTagKey(tag: string): string {
    return `gateway:cache:tag:${tag}`;
  }

  private hashKey(key: string): string {
    return createHash('md5').update(key).digest('hex');
  }

  private isExpired(entry: CacheEntry): boolean {
    const now = Date.now();
    const entryTime = entry.timestamp.getTime();
    const ttlMs = entry.ttl * 1000;
    
    return (now - entryTime) > ttlMs;
  }

  private filterCacheableHeaders(headers: Record<string, string>): Record<string, string> {
    const cacheableHeaders: Record<string, string> = {};
    const allowedHeaders = [
      'content-type',
      'content-encoding',
      'content-language',
      'cache-control',
      'expires',
      'etag',
      'last-modified',
    ];
    
    for (const [key, value] of Object.entries(headers)) {
      if (allowedHeaders.includes(key.toLowerCase())) {
        cacheableHeaders[key] = value;
      }
    }
    
    return cacheableHeaders;
  }

  private async compressData(data: any): Promise<string> {
    // 这里可以实现数据压缩逻辑
    // 为了简化，直接返回 JSON 字符串
    return typeof data === 'string' ? data : JSON.stringify(data);
  }

  private async setTagIndex(tags: string[], cacheKey: string): Promise<void> {
    for (const tag of tags) {
      const tagKey = this.buildTagKey(tag);
      await this.redisService.getClient().sadd(tagKey, cacheKey);
      await this.redisService.expire(tagKey, 86400); // 24小时过期
    }
  }

  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }
}
