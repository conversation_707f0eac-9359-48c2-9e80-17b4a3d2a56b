import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// 导入核心认证服务
import { AuthService } from './auth.service';

// 导入依赖模块
import { JwtSharedModule } from '../../infrastructure/jwt/jwt-shared.module';

/**
 * 核心认证模块
 * 
 * 提供网关核心认证功能，包括：
 * - 令牌验证和解析
 * - 用户身份识别
 * - 权限检查
 * - 认证状态管理
 * 
 * 职责范围：
 * - 验证请求中的认证令牌
 * - 解析用户身份和权限信息
 * - 提供统一的认证接口
 * - 与认证服务进行交互
 * 
 * 注意：这是核心层的认证服务，与业务层的 AuthModule 不同
 * - 核心层：提供认证的基础能力和接口
 * - 业务层：提供完整的认证业务流程
 */
@Module({
  imports: [
    ConfigModule,
    JwtSharedModule,
  ],
  providers: [
    AuthService,
  ],
  exports: [
    AuthService,
  ],
})
export class CoreAuthModule {}
