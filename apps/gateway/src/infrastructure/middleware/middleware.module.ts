import { Module } from '@nestjs/common';

/**
 * 中间件模块
 *
 * 提供网关中间件组件，包括：
 * - 认证中间件
 * - 日志中间件
 * - 限流中间件
 * - 安全中间件
 *
 * 职责范围：
 * - 在请求处理管道的早期阶段进行拦截
 * - 实现跨切面的功能（认证、日志、安全等）
 * - 提供可复用的中间件组件
 * - 支持中间件的顺序配置和组合
 *
 * 注意：中间件是函数式组件，不能作为 Provider 注册
 * 它们通过 AppModule 的 configure 方法进行配置
 */
@Module({
  // 中间件模块不需要 providers，因为中间件是函数式的
  // 它们在 AppModule 中通过 MiddlewareConsumer 进行配置
})
export class MiddlewareModule {
  // 导出中间件函数供 AppModule 使用
  static getAuthMiddleware() {
    return require('../../core/middleware/auth.middleware').AuthMiddleware;
  }

  static getLoggingMiddleware() {
    return require('../../core/middleware/logging.middleware').LoggingMiddleware;
  }

  static getRateLimitMiddleware() {
    return require('../../core/middleware/rate-limit.middleware').RateLimitMiddleware;
  }

  static getSecurityMiddleware() {
    return require('../../core/middleware/security.middleware').SecurityMiddleware;
  }
}
