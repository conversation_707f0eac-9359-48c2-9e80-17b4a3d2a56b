import { Module } from '@nestjs/common';

// 导入核心拦截器
import { ProxyRequestInterceptor } from '../../modules/routing/interceptors/proxy-request.interceptor';
import { RequestLoggingInterceptor } from './request-logging.interceptor';
import { ResponseTransformInterceptor } from './response-transform.interceptor';

// 导入全局拦截器
import { LoggingInterceptor } from './logging.interceptor';
import { TransformInterceptor } from './transform.interceptor';

/**
 * 拦截器模块
 * 
 * 提供网关拦截器组件，包括：
 * - 代理请求拦截器
 * - 请求日志拦截器
 * - 响应转换拦截器
 * - 全局日志拦截器
 * - 全局转换拦截器
 * 
 * 职责范围：
 * - 在请求/响应处理过程中进行拦截
 * - 实现日志记录、数据转换、性能监控等功能
 * - 提供可复用的拦截器组件
 * - 支持拦截器的链式组合
 */
@Module({
  providers: [
    // 核心拦截器
    ProxyRequestInterceptor,
    RequestLoggingInterceptor,
    ResponseTransformInterceptor,

    // 全局拦截器
    LoggingInterceptor,
    TransformInterceptor,
  ],
  exports: [
    // 核心拦截器
    ProxyRequestInterceptor,
    RequestLoggingInterceptor,
    ResponseTransformInterceptor,

    // 全局拦截器
    LoggingInterceptor,
    TransformInterceptor,
  ],
})
export class InterceptorsModule {}
