import { Module } from '@nestjs/common';

// 导入守卫组件
import { AuthGuard } from './auth.guard';
import { RateLimitGuard } from '@gateway/modules/gateway-auth/dto/guards/rate-limit.guard';

/**
 * 守卫模块
 * 
 * 提供网关守卫组件，包括：
 * - 认证守卫
 * - 限流守卫
 * - 权限守卫
 * - 自定义守卫
 * 
 * 职责范围：
 * - 在请求处理前进行拦截和验证
 * - 实现认证、授权、限流等安全控制
 * - 提供可复用的守卫组件
 * - 支持守卫的组合和扩展
 */
@Module({
  providers: [
    AuthGuard,
    RateLimitGuard,
  ],
  exports: [
    AuthGuard,
    RateLimitGuard,
  ],
})
export class GuardsModule {}
