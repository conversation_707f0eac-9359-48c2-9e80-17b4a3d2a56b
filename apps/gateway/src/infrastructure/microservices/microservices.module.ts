import { Module } from '@nestjs/common';
import { MicroserviceKitModule } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@shared/constants';

/**
 * 微服务模块
 * 
 * 统一管理所有微服务客户端连接，解决重复注册问题
 * 
 * 职责：
 * - 统一配置所有微服务客户端
 * - 提供微服务通信能力
 * - 避免重复注册和配置不一致
 * 
 * 使用方式：
 * - 其他模块通过导入此模块获得微服务通信能力
 * - 不需要在各个模块中重复配置微服务客户端
 */
@Module({
  imports: [
    // 统一的微服务公共库 - 客户端模式，连接所有需要的服务
    MicroserviceKitModule.forClient({
      services: [
        MICROSERVICE_NAMES.AUTH_SERVICE,
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        MICROSERVICE_NAMES.HERO_SERVICE,
        MICROSERVICE_NAMES.GAME_SERVICE,
        MICROSERVICE_NAMES.CLUB_SERVICE,
        MICROSERVICE_NAMES.MATCH_SERVICE,
        MICROSERVICE_NAMES.CARD_SERVICE,
        MICROSERVICE_NAMES.ECONOMY_SERVICE,
        MICROSERVICE_NAMES.SOCIAL_SERVICE,
        MICROSERVICE_NAMES.ACTIVITY_SERVICE,
        MICROSERVICE_NAMES.NOTIFICATION_SERVICE,
      ],
    }),
  ],
  exports: [
    // 导出微服务模块供其他模块使用
    MicroserviceKitModule,
  ],
})
export class MicroservicesModule {}
