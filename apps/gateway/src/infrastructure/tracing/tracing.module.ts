import { Module } from '@nestjs/common';

// 导入链路追踪服务
import { TracingService } from '../../infrastructure/tracing/tracing.service';

/**
 * 链路追踪模块
 * 
 * 提供网关链路追踪功能，包括：
 * - 分布式请求追踪
 * - 调用链分析
 * - 性能瓶颈识别
 * - 错误定位和诊断
 * 
 * 职责范围：
 * - 跟踪请求在微服务间的调用链路
 * - 记录每个服务的处理时间和状态
 * - 提供调用链的可视化分析
 * - 支持性能优化和问题诊断
 */
@Module({
  providers: [
    TracingService,
  ],
  exports: [
    TracingService,
  ],
})
export class TracingModule {}
