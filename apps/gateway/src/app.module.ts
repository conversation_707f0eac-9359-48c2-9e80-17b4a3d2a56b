import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { HttpModule } from '@nestjs/axios';
import { PrometheusModule } from '@willsoto/nestjs-prometheus';
import { TerminusModule } from '@nestjs/terminus';

// 配置
import gatewayConfig from './shared/config/gateway.config';
import servicesConfig from './shared/config/services.config';

// 公共模块
import { RedisModule } from '@common/redis';
import { MicroserviceKitModule } from '@common/microservice-kit';
import { BackupModule } from '@common/backup';
import { NetworkSecurityModule, IPWhitelistMiddleware, ServiceAuthMiddleware } from '@common/network-security';
import { EnvDebugUtil } from '@common/utils/env-debug.util';
import { MICROSERVICE_NAMES } from '@shared/constants';

// 服务注册与发现
import { ServiceRegistryModule } from '@libs/service-registry';

// 注意：移除了过度设计的SharedModule，改为按需导入具体模块

// 共享服务模块
import { LoggingModule } from './shared/logging/logging.module';
import { TracingModule } from './shared/tracing/tracing.module';
import { ConfigManagerModule } from './shared/config/config.module';

// 功能模块 (按功能职责划分)
import { RoutingModule } from './modules/routing/routing.module';
import { GatewayAuthModule } from './modules/gateway-auth/gateway-auth.module';
import { LoadBalancingModule } from './modules/load-balancing/load-balancing.module';
import { CircuitBreakerModule } from './modules/circuit-breaker/circuit-breaker.module';
import { RateLimitingModule } from './modules/rate-limiting/rate-limit.module';
import { CachingModule } from './modules/caching/caching.module';
import { WebSocketModule } from './modules/websocket/websocket.module';
import { ServerDiscoveryModule } from './modules/server-discovery/server-discovery.module';
import { CharacterModule } from './modules/character/character.module';
import { GlobalMessagingModule } from './modules/global-messaging/global-messaging.module';
import { GraphQLModule } from './modules/graphql/graphql.module';
import { HealthModule } from './modules/health/health.module';
import { MonitoringModule } from './modules/monitoring/monitoring.module';
import { AdminModule } from './modules/admin/admin.module';

// 中间件
import { AuthMiddleware } from './modules/gateway-auth/middleware/auth.middleware';
import { RateLimitMiddleware } from './modules/rate-limiting/middleware/rate-limit.middleware';

/**
 * Gateway应用主模块
 *
 * 基于Auth服务成功经验的功能模块化架构：
 * - 按功能职责划分模块，而非技术层次
 * - 消除重复注册和循环依赖
 * - 清晰的模块边界和依赖关系
 */
@Module({
  imports: [
    // Configuration - 使用标准化环境变量管理
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      expandVariables: true,
      load: [gatewayConfig, servicesConfig],
      envFilePath: [
        '.env',                                           // 1. 基础配置
        `.env.${process.env.NODE_ENV || 'development'}`, // 2. 环境特定配置
        '.env.local',                                     // 3. 本地覆盖

        // 业务模块特定配置
        'apps/gateway/.env.redis',                       // 4. Redis业务配置
        'apps/gateway/.env.routing',                     // 5. 路由业务配置
        'apps/gateway/.env.security',                    // 6. 安全业务配置

        // 环境特定的业务配置
        `apps/gateway/.env.redis.${process.env.NODE_ENV || 'development'}`,
        `apps/gateway/.env.routing.${process.env.NODE_ENV || 'development'}`,
        `apps/gateway/.env.security.${process.env.NODE_ENV || 'development'}`,

        // 最终覆盖
        'apps/gateway/.env.local',                       // 7. 服务本地覆盖
        '.env.backup',                                   // 8. 备份配置（保持兼容）
      ],
    }),

    // 注意：移除了过度设计的SharedModule，各功能模块按需导入具体依赖

    // Prometheus 监控
    PrometheusModule.register({
      path: '/metrics',
      defaultMetrics: {
        enabled: true,
      },
    }),

    // 健康检查
    TerminusModule,

    // Redis 模块 - 使用新的v3.0架构，统一前缀隔离，移除数据库分离
    RedisModule.forRootAsync({
      service: MICROSERVICE_NAMES.GATEWAY_SERVICE,
      // 🔥 移除database参数，使用统一的Redis数据库0 + 前缀隔离
      useFactory: (configService: ConfigService) => ({
        host: configService.get('REDIS_HOST'),
        port: configService.get('REDIS_PORT'),
        password: configService.get('REDIS_PASSWORD'),
      }),
      inject: [ConfigService],
    }),

    // 备份模块
    BackupModule.forRoot({
      enabled: true,
      schedulerEnabled: true,
    }),

    // 服务注册与发现
    ServiceRegistryModule,

    // 限流模块
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        throttlers: [{
          ttl: configService.get('THROTTLE_TTL', 60000),
          limit: configService.get('THROTTLE_LIMIT', 100),
        }],
      }),
    }),

    // 微服务通信
    MicroserviceKitModule.forClient({
      services: [
        MICROSERVICE_NAMES.AUTH_SERVICE,
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        MICROSERVICE_NAMES.HERO_SERVICE,
        MICROSERVICE_NAMES.GAME_SERVICE,
        MICROSERVICE_NAMES.CLUB_SERVICE,
        MICROSERVICE_NAMES.MATCH_SERVICE,
        MICROSERVICE_NAMES.CARD_SERVICE,
        MICROSERVICE_NAMES.ECONOMY_SERVICE,
        MICROSERVICE_NAMES.SOCIAL_SERVICE,
        MICROSERVICE_NAMES.ACTIVITY_SERVICE,
        MICROSERVICE_NAMES.NOTIFICATION_SERVICE,
      ],
    }),

    // HTTP客户端
    HttpModule.register({
      timeout: 5000,
      maxRedirects: 5,
    }),

    // 网络安全
    NetworkSecurityModule,

    // 功能模块 (按功能职责划分，参考Auth服务成功经验)
    RoutingModule,              // 路由功能
    GatewayAuthModule,          // 网关认证功能
    LoadBalancingModule,         // 负载均衡功能
    CircuitBreakerModule,       // 熔断器功能
    RateLimitingModule,         // 限流功能
    CachingModule,              // 缓存功能
    WebSocketModule,            // WebSocket功能
    ServerDiscoveryModule,      // 区服发现功能
    CharacterModule,            // 角色管理功能
    GlobalMessagingModule,      // 全局消息功能
    GraphQLModule,              // GraphQL功能
    HealthModule,               // 健康检查功能
    MonitoringModule,           // 监控功能
    AdminModule,       // 管理功能
  ],
  controllers: [
    // 所有控制器现在由功能模块提供，避免重复注册
  ],
  providers: [
    // 所有服务现在由功能模块提供，避免重复注册
  ],
})
export class AppModule implements NestModule {
  constructor(private configService: ConfigService) {
    // 1. 首先输出环境变量调试信息
    const gatewayConfig = {
      serviceName: 'gateway',
      customEnvFiles: EnvDebugUtil.createCustomEnvFiles('gateway', [
        '.env.redis',
        '.env.routing',
        '.env.security'
      ])
    };

    EnvDebugUtil.logEnvironmentDetails(gatewayConfig);
    EnvDebugUtil.validateEnvironmentConsistency(gatewayConfig);

    // 2. 验证配置一致性
    this.validateConfigConsistency();

    // 3. 输出网关启动信息
    const environment = this.configService.get<string>('app.environment');
    const serviceName = this.configService.get<string>('app.name');
    const version = this.configService.get<string>('app.version');

    console.log(`
    🌐 足球经理网关服务
    📦 服务: ${serviceName}
    🏷️ 版本: ${version}
    🌍 环境: ${environment}
    🕐 启动时间: ${new Date().toISOString()}
    `);
  }

  /**
   * 验证配置一致性
   */
  private validateConfigConsistency(): void {
    console.log('\n🔍 ConfigService 配置验证:');

    const processNodeEnv = process.env.NODE_ENV;
    const configNodeEnv = this.configService.get<string>('NODE_ENV');
    const appEnvironment = this.configService.get<string>('app.environment');

    console.log(`   process.env.NODE_ENV: "${processNodeEnv}"`);
    console.log(`   configService.get('NODE_ENV'): "${configNodeEnv}"`);
    console.log(`   configService.get('app.environment'): "${appEnvironment}"`);

    // 检查一致性
    if (processNodeEnv !== configNodeEnv) {
      console.log('   🚨 警告: process.env.NODE_ENV 与 configService.get("NODE_ENV") 不一致！');
      console.log('   💡 建议: 使用 cross-env NODE_ENV=xxx 启动服务');
    } else {
      console.log('   ✅ 环境变量一致性检查通过');
    }

    console.log('');
  }

  configure(consumer: MiddlewareConsumer) {
    // 安全中间件 - 最高优先级
    consumer
      .apply(IPWhitelistMiddleware, ServiceAuthMiddleware)
      .forRoutes('*');

    // 应用全局中间件
    consumer
      .apply(AuthMiddleware)
      .forRoutes('*');

    consumer
      .apply(RateLimitMiddleware)
      .forRoutes('*');
  }
}
