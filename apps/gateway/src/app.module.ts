import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { JwtModule } from '@nestjs/jwt';
import { HttpModule } from '@nestjs/axios';
import { PrometheusModule } from '@willsoto/nestjs-prometheus';
import { TerminusModule } from '@nestjs/terminus';

// 配置
import gatewayConfig from './config/gateway.config';
import servicesConfig from './config/services.config';

// 公共模块
import { RedisModule } from '@common/redis';
import { MicroserviceKitModule } from '@common/microservice-kit';
import { BackupModule } from '@common/backup';
import { NetworkSecurityModule, IPWhitelistMiddleware, ServiceAuthMiddleware } from '@common/network-security';
import { EnvDebugUtil } from '@common/utils/env-debug.util';
import { MICROSERVICE_NAMES } from '@shared/constants';

// 现有模块
import { WebSocketModule } from './domain/websocket/websocket.module';
import { UserModule } from './domain/user/user.module';
import { ProxyModule } from './domain/proxy/proxy.module';
import { HealthModule } from './app/health/health.module';
import { MonitoringModule } from '@gateway/app/monitoring/monitoring.module';

// 新增共享模块
import { JwtSharedModule } from './infrastructure/jwt/jwt-shared.module';
import { CoreModule } from './core/shared/core.module';

// 中间件
import { AuthMiddleware } from './infrastructure/middleware/auth.middleware';
import { RateLimitMiddleware } from './infrastructure/middleware/rate-limit.middleware';

@Module({
  imports: [
    // Configuration - 使用标准化环境变量管理
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      expandVariables: true,
      load: [gatewayConfig, servicesConfig],
      envFilePath: [
        '.env',                                           // 1. 基础配置
        `.env.${process.env.NODE_ENV || 'development'}`, // 2. 环境特定配置
        '.env.local',                                     // 3. 本地覆盖

        // 业务模块特定配置
        'apps/gateway/.env.redis',                       // 4. Redis业务配置
        'apps/gateway/.env.routing',                     // 5. 路由业务配置
        'apps/gateway/.env.security',                    // 6. 安全业务配置

        // 环境特定的业务配置
        `apps/gateway/.env.redis.${process.env.NODE_ENV || 'development'}`,
        `apps/gateway/.env.routing.${process.env.NODE_ENV || 'development'}`,
        `apps/gateway/.env.security.${process.env.NODE_ENV || 'development'}`,

        // 最终覆盖
        'apps/gateway/.env.local',                       // 7. 服务本地覆盖
        '.env.backup',                                   // 8. 备份配置（保持兼容）
      ],
    }),

    // 使用共享 JWT 模块
    JwtSharedModule,

    // Prometheus 监控
    PrometheusModule.register({
      path: '/metrics',
      defaultMetrics: {
        enabled: true,
      },
    }),

    // 健康检查
    TerminusModule,

    // Redis 模块 - 使用新的v3.0架构，统一前缀隔离，移除数据库分离
    RedisModule.forRootAsync({
      service: MICROSERVICE_NAMES.GATEWAY_SERVICE,
      // 🔥 移除database参数，使用统一的Redis数据库0 + 前缀隔离
      useFactory: (configService: ConfigService) => ({
        host: configService.get('REDIS_HOST'),
        port: configService.get('REDIS_PORT'),
        password: configService.get('REDIS_PASSWORD'),
      }),
      inject: [ConfigService],
    }),

    // 备份模块
    BackupModule.forRoot({
      enabled: true,
      schedulerEnabled: true,
    }),

    // Rate limiting
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        throttlers: [
          {
            ttl: configService.get('THROTTLE_TTL', 60000),
            limit: configService.get('THROTTLE_LIMIT', 100),
          },
        ],
      }),
    }),

    // 统一的微服务公共库 - 客户端模式，选择性连接需要的服务
    MicroserviceKitModule.forClient({
      services: [
        MICROSERVICE_NAMES.AUTH_SERVICE,
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        MICROSERVICE_NAMES.HERO_SERVICE,
        MICROSERVICE_NAMES.GAME_SERVICE,
        MICROSERVICE_NAMES.CLUB_SERVICE,
        MICROSERVICE_NAMES.MATCH_SERVICE,
        MICROSERVICE_NAMES.CARD_SERVICE,
        MICROSERVICE_NAMES.ECONOMY_SERVICE,
        MICROSERVICE_NAMES.SOCIAL_SERVICE,
        MICROSERVICE_NAMES.ACTIVITY_SERVICE,
        MICROSERVICE_NAMES.NOTIFICATION_SERVICE,
      ],
    }),

    // HTTP客户端模块（用于HTTP健康检查）
    HttpModule.register({
      timeout: 5000,
      maxRedirects: 5,
    }),

    // 通用安全模块 - IP白名单和服务间认证
    NetworkSecurityModule,

    // 核心模块 - 提供所有核心服务
    CoreModule,

    // Feature modules - 具体路由模块必须在 ProxyModule 之前
    WebSocketModule,
    UserModule,
    HealthModule,
    MonitoringModule,
    ProxyModule,  // 简化的代理模块放在最后
  ],
  controllers: [],
  providers: [
    // 所有核心服务现在由 CoreModule 提供
    // 移除重复注册，避免与 ProxyModule 中的服务冲突
  ],
})
export class AppModule implements NestModule {
  constructor(private configService: ConfigService) {
    // 1. 首先输出环境变量调试信息
    const gatewayConfig = {
      serviceName: 'gateway',
      customEnvFiles: EnvDebugUtil.createCustomEnvFiles('gateway', [
        '.env.redis',
        '.env.routing',
        '.env.security'
      ])
    };

    EnvDebugUtil.logEnvironmentDetails(gatewayConfig);
    EnvDebugUtil.validateEnvironmentConsistency(gatewayConfig);

    // 2. 验证配置一致性
    this.validateConfigConsistency();

    // 3. 输出网关启动信息
    const environment = this.configService.get<string>('app.environment');
    const serviceName = this.configService.get<string>('app.name');
    const version = this.configService.get<string>('app.version');

    console.log(`
    🌐 足球经理网关服务
    📦 服务: ${serviceName}
    🏷️ 版本: ${version}
    🌍 环境: ${environment}
    🕐 启动时间: ${new Date().toISOString()}
    `);
  }

  /**
   * 验证配置一致性
   */
  private validateConfigConsistency(): void {
    console.log('\n🔍 ConfigService 配置验证:');

    const processNodeEnv = process.env.NODE_ENV;
    const configNodeEnv = this.configService.get<string>('NODE_ENV');
    const appEnvironment = this.configService.get<string>('app.environment');

    console.log(`   process.env.NODE_ENV: "${processNodeEnv}"`);
    console.log(`   configService.get('NODE_ENV'): "${configNodeEnv}"`);
    console.log(`   configService.get('app.environment'): "${appEnvironment}"`);

    // 检查一致性
    if (processNodeEnv !== configNodeEnv) {
      console.log('   🚨 警告: process.env.NODE_ENV 与 configService.get("NODE_ENV") 不一致！');
      console.log('   💡 建议: 使用 cross-env NODE_ENV=xxx 启动服务');
    } else {
      console.log('   ✅ 环境变量一致性检查通过');
    }

    console.log('');
  }

  configure(consumer: MiddlewareConsumer) {
    // 安全中间件 - 最高优先级
    consumer
      .apply(IPWhitelistMiddleware, ServiceAuthMiddleware)
      .forRoutes('*');

    // 应用全局中间件
    consumer
      .apply(AuthMiddleware)
      .forRoutes('*');

    consumer
      .apply(RateLimitMiddleware)
      .forRoutes('*');
  }
}
