import { Module } from '@nestjs/common';

// 导入核心过滤器
import { GatewayExceptionFilter } from './gateway-exception.filter';

// 导入全局过滤器
import { AllExceptionsFilter } from './all-exceptions.filter';

/**
 * 过滤器模块
 * 
 * 提供网关过滤器组件，包括：
 * - 网关异常过滤器
 * - 全局异常过滤器
 * - 自定义异常过滤器
 * 
 * 职责范围：
 * - 捕获和处理应用程序异常
 * - 提供统一的错误响应格式
 * - 实现异常日志记录和监控
 * - 支持自定义异常处理逻辑
 */
@Module({
  providers: [
    // 核心过滤器
    GatewayExceptionFilter,
    
    // 全局过滤器
    AllExceptionsFilter,
  ],
  exports: [
    // 核心过滤器
    GatewayExceptionFilter,
    
    // 全局过滤器
    AllExceptionsFilter,
  ],
})
export class FiltersModule {}
