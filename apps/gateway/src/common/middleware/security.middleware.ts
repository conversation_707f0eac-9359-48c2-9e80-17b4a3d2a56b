import { Request, Response, NextFunction } from 'express';
import helmet from 'helmet';
import compression from 'compression';

/**
 * 安全中间件
 * 应用各种安全头和保护措施
 */
export function SecurityMiddleware(req: Request, res: Response, next: NextFunction) {
  // 应用 Helmet 安全头
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
    crossOriginEmbedderPolicy: false,
  })(req, res, (err) => {
    if (err) {
      console.error('Helmet error:', err);
    }
    next(err);
  });
}

/**
 * 压缩中间件
 * 启用 gzip 压缩
 */
export function CompressionMiddleware(req: Request, res: Response, next: NextFunction) {
  compression({
    filter: (req, res) => {
      // 不压缩已经压缩的内容
      if (req.headers['x-no-compression']) {
        return false;
      }
      
      // 使用默认的压缩过滤器
      return compression.filter(req, res);
    },
    threshold: 1024, // 只压缩大于 1KB 的响应
    level: 6, // 压缩级别 (1-9)
  })(req, res, next);
}
