export interface User {
  id: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  level: number;
  status: 'active' | 'inactive' | 'banned' | 'suspended';
  metadata: Record<string, any>;
  createdAt: Date;
  lastLoginAt?: Date;
}

export interface AuthToken {
  accessToken: string;
  refreshToken?: string;
  tokenType: 'Bearer' | 'Basic' | 'ApiKey';
  expiresIn: number;
  expiresAt: Date;
  scope?: string[];
  metadata?: Record<string, any>;
}

export interface AuthContext {
  user?: User;
  token?: AuthToken;
  session?: AuthSession;
  apiKey?: ApiKey;
  authenticated: boolean;
  authMethod: 'jwt' | 'session' | 'apikey' | 'oauth' | 'none';
  permissions: string[];
  roles: string[];
  metadata: Record<string, any>;
}

export interface AuthSession {
  id: string;
  userId: string;
  deviceId?: string;
  deviceInfo?: DeviceInfo;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
  lastAccessAt: Date;
  expiresAt: Date;
  metadata: Record<string, any>;
}

export interface DeviceInfo {
  type: 'web' | 'mobile' | 'desktop' | 'tablet' | 'unknown';
  os: string;
  browser: string;
  version: string;
  fingerprint: string;
}

export interface ApiKey {
  id: string;
  key: string;
  name: string;
  userId?: string;
  scopes: string[];
  permissions: string[];
  rateLimit?: {
    requests: number;
    window: number;
  };
  ipWhitelist?: string[];
  enabled: boolean;
  expiresAt?: Date;
  lastUsedAt?: Date;
  createdAt: Date;
  metadata: Record<string, any>;
}

export interface AuthConfig {
  jwt: {
    secret: string;
    expiresIn: string;
    refreshExpiresIn: string;
    algorithm: string;
    issuer: string;
    audience: string;
  };
  session: {
    secret: string;
    maxAge: number;
    rolling: boolean;
    httpOnly: boolean;
    secure: boolean;
    sameSite: 'strict' | 'lax' | 'none';
  };
  oauth: {
    providers: OAuthProvider[];
  };
  security: {
    bcryptRounds: number;
    maxLoginAttempts: number;
    lockoutDuration: number;
    passwordMinLength: number;
    passwordRequireSpecialChars: boolean;
    enableTwoFactor: boolean;
    sessionConcurrency: number;
  };
}

export interface OAuthProvider {
  name: string;
  clientId: string;
  clientSecret: string;
  authUrl: string;
  tokenUrl: string;
  userInfoUrl: string;
  scope: string[];
  redirectUri: string;
  enabled: boolean;
}

export interface AuthRequest {
  method: 'login' | 'register' | 'refresh' | 'logout' | 'verify';
  credentials?: {
    username?: string;
    email?: string;
    password?: string;
    token?: string;
    refreshToken?: string;
    apiKey?: string;
    oauthCode?: string;
    oauthProvider?: string;
  };
  deviceInfo?: DeviceInfo;
  ipAddress: string;
  userAgent: string;
  metadata?: Record<string, any>;
}

export interface AuthResponse {
  success: boolean;
  user?: User;
  token?: AuthToken;
  session?: AuthSession;
  message?: string;
  errors?: string[];
  metadata?: Record<string, any>;
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  conditions?: PermissionCondition[];
  description?: string;
}

export interface PermissionCondition {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'contains' | 'regex';
  value: any;
  description?: string;
}

export interface Role {
  id: string;
  name: string;
  permissions: string[];
  inherits?: string[];
  level: number;
  description?: string;
  metadata?: Record<string, any>;
}

export interface AuthPolicy {
  id: string;
  name: string;
  rules: AuthRule[];
  enabled: boolean;
  priority: number;
  description?: string;
}

export interface AuthRule {
  id: string;
  type: 'allow' | 'deny';
  conditions: AuthCondition[];
  effect: 'permit' | 'deny' | 'indeterminate';
  description?: string;
}

export interface AuthCondition {
  field: string;
  operator: string;
  value: any;
  type: 'user' | 'request' | 'resource' | 'environment';
}

export interface AuthAuditLog {
  id: string;
  userId?: string;
  action: string;
  resource?: string;
  result: 'success' | 'failure' | 'error';
  ipAddress: string;
  userAgent: string;
  details: Record<string, any>;
  timestamp: Date;
}

export interface SecurityEvent {
  id: string;
  type: 'login_failure' | 'suspicious_activity' | 'brute_force' | 'token_abuse' | 'privilege_escalation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  ipAddress: string;
  userAgent: string;
  details: Record<string, any>;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
}

export interface AuthMetrics {
  totalUsers: number;
  activeUsers: number;
  loginAttempts: number;
  successfulLogins: number;
  failedLogins: number;
  blockedAttempts: number;
  averageSessionDuration: number;
  tokenRefreshRate: number;
  securityEvents: number;
  timestamp: Date;
}

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: Date;
  retryAfter?: number;
}

export interface AuthMiddlewareOptions {
  required: boolean;
  roles?: string[];
  permissions?: string[];
  allowApiKey?: boolean;
  allowSession?: boolean;
  skipPaths?: string[];
  customValidator?: (context: AuthContext) => boolean | Promise<boolean>;
}
