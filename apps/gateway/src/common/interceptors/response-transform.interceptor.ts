import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Request, Response } from 'express';

export interface StandardResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
  path: string;
  method: string;
  requestId?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  meta?: Record<string, any>;
}

@Injectable()
export class ResponseTransformInterceptor<T> implements NestInterceptor<T, StandardResponse<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<StandardResponse<T>> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();

    return next.handle().pipe(
      map((data) => {
        // 如果响应已经是标准格式，直接返回
        if (this.isStandardResponse(data)) {
          return data;
        }

        // 如果是健康检查或指标端点，不进行转换
        if (this.shouldSkipTransform(request.path)) {
          return data;
        }

        // 构建标准响应格式
        const standardResponse: StandardResponse<T> = {
          success: true,
          data,
          timestamp: new Date().toISOString(),
          path: request.path,
          method: request.method,
          requestId: (request as any).requestId,
        };

        // 添加分页信息（如果存在）
        if (this.hasPaginationData(data)) {
          standardResponse.pagination = this.extractPaginationData(data);
        }

        // 添加元数据
        standardResponse.meta = this.buildMetadata(request, response);

        return standardResponse;
      }),
    );
  }

  private isStandardResponse(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      'success' in data &&
      'timestamp' in data
    );
  }

  private shouldSkipTransform(path: string): boolean {
    const skipPaths = [
      '/health',
      '/metrics',
      '/favicon.ico',
      '/robots.txt',
      '/sitemap.xml',
    ];

    return skipPaths.some(skipPath => path.startsWith(skipPath));
  }

  private hasPaginationData(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      ('page' in data || 'limit' in data || 'total' in data)
    );
  }

  private extractPaginationData(data: any): any {
    const page = data.page || 1;
    const limit = data.limit || 10;
    const total = data.total || 0;
    const totalPages = Math.ceil(total / limit);

    return {
      page,
      limit,
      total,
      totalPages,
    };
  }

  private buildMetadata(request: Request, response: Response): Record<string, any> {
    const meta: Record<string, any> = {
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
    };

    // 添加响应头信息
    const contentType = response.get('content-type');
    if (contentType) {
      meta.contentType = contentType;
    }

    const contentLength = response.get('content-length');
    if (contentLength) {
      meta.contentLength = parseInt(contentLength, 10);
    }

    // 添加缓存信息
    const cacheStatus = response.get('x-cache');
    if (cacheStatus) {
      meta.cache = cacheStatus;
    }

    // 添加限流信息
    const rateLimitRemaining = response.get('x-ratelimit-remaining');
    if (rateLimitRemaining) {
      meta.rateLimit = {
        remaining: parseInt(rateLimitRemaining, 10),
        limit: parseInt(response.get('x-ratelimit-limit') || '0', 10),
        reset: response.get('x-ratelimit-reset'),
      };
    }

    // 添加追踪信息
    const traceId = response.get('x-trace-id');
    if (traceId) {
      meta.traceId = traceId;
    }

    const spanId = response.get('x-span-id');
    if (spanId) {
      meta.spanId = spanId;
    }

    return meta;
  }
}
