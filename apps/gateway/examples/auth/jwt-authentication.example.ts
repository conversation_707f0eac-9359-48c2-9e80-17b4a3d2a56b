/**
 * JWT 认证示例
 *
 * 此示例演示如何在网关中使用JWT认证。
 * 展示了令牌生成、验证和刷新的工作流程。
 */

import { AuthService } from '../../src/modules/gateway-auth/services/auth.service';
import { User } from '../../src/common/interfaces/auth.interface';

// 示例用户数据
const exampleUser: User = {
  id: 'user-12345',
  username: 'john_doe',
  email: '<EMAIL>',
  roles: ['user', 'player'], // 用户角色：普通用户、球员
  permissions: ['game:play', 'profile:read', 'profile:write'], // 权限：游戏、读取资料、写入资料
  level: 5, // 用户等级
  status: 'active', // 账户状态：活跃
  metadata: {
    lastLogin: new Date(), // 最后登录时间
    preferredLanguage: 'en', // 首选语言
    timezone: 'UTC', // 时区
  },
  createdAt: new Date('2023-01-01'), // 账户创建时间
};

/**
 * 示例 1: 生成 JWT 令牌
 */
export async function generateTokenExample(authService: AuthService) {
  console.log('=== JWT 令牌生成示例 ===');

  try {
    // 为用户生成 JWT 令牌
    const tokenResult = await authService.generateJwtToken(exampleUser);

    console.log('令牌生成成功:');
    console.log('访问令牌:', tokenResult.accessToken);
    console.log('刷新令牌:', tokenResult.refreshToken);
    console.log('令牌类型:', tokenResult.tokenType);
    console.log('过期时间:', tokenResult.expiresAt);
    console.log('有效期:', tokenResult.expiresIn, '秒');

    return tokenResult;
  } catch (error) {
    console.error('令牌生成失败:', error.message);
    throw error;
  }
}

/**
 * 示例 2: 验证 JWT 令牌
 */
export async function validateTokenExample(authService: AuthService, token: string) {
  console.log('\n=== JWT 令牌验证示例 ===');

  try {
    // 验证 JWT 令牌
    const authContext = await authService.validateJwtToken(token);

    console.log('令牌验证成功:');
    console.log('已认证:', authContext.authenticated);
    console.log('认证方式:', authContext.authMethod);
    console.log('用户ID:', authContext.user?.id);
    console.log('用户名:', authContext.user?.username);
    console.log('角色:', authContext.roles);
    console.log('权限:', authContext.permissions);

    return authContext;
  } catch (error) {
    console.error('令牌验证失败:', error.message);
    throw error;
  }
}

/**
 * Example 3: Refresh JWT Token
 */
export async function refreshTokenExample(authService: AuthService, refreshToken: string) {
  console.log('\n=== JWT Token Refresh Example ===');
  
  try {
    // Refresh JWT token
    const newTokenResult = await authService.refreshJwtToken(refreshToken);
    
    console.log('Token refresh successful:');
    console.log('New Access Token:', newTokenResult.accessToken);
    console.log('New Refresh Token:', newTokenResult.refreshToken);
    console.log('New Expires At:', newTokenResult.expiresAt);
    
    return newTokenResult;
  } catch (error) {
    console.error('Token refresh failed:', error.message);
    throw error;
  }
}

/**
 * Example 4: Blacklist Token (Logout)
 */
export async function blacklistTokenExample(authService: AuthService, token: string) {
  console.log('\n=== JWT Token Blacklist Example ===');
  
  try {
    // Blacklist token (logout)
    await authService.blacklistToken(token);
    
    console.log('Token blacklisted successfully');
    
    // Try to validate blacklisted token
    try {
      await authService.validateJwtToken(token);
      console.log('ERROR: Blacklisted token should not be valid!');
    } catch (error) {
      console.log('✓ Blacklisted token correctly rejected:', error.message);
    }
  } catch (error) {
    console.error('Token blacklist failed:', error.message);
    throw error;
  }
}

/**
 * Example 5: Permission Check
 */
export async function permissionCheckExample(authService: AuthService, token: string) {
  console.log('\n=== Permission Check Example ===');
  
  try {
    // Validate token first
    const authContext = await authService.validateJwtToken(token);
    
    // Check various permissions
    const permissions = [
      { resource: 'game', action: 'play' },
      { resource: 'profile', action: 'read' },
      { resource: 'profile', action: 'write' },
      { resource: 'admin', action: 'delete' }, // Should fail
    ];
    
    for (const { resource, action } of permissions) {
      const hasPermission = await authService.checkPermission(authContext, resource, action);
      console.log(`Permission ${resource}:${action}:`, hasPermission ? '✓ ALLOWED' : '✗ DENIED');
    }
  } catch (error) {
    console.error('Permission check failed:', error.message);
    throw error;
  }
}

/**
 * Example 6: Role Check
 */
export async function roleCheckExample(authService: AuthService, token: string) {
  console.log('\n=== Role Check Example ===');
  
  try {
    // Validate token first
    const authContext = await authService.validateJwtToken(token);
    
    // Check various roles
    const roles = ['user', 'player', 'admin', 'super_admin'];
    
    for (const role of roles) {
      const hasRole = authService.checkRole(authContext, [role]);
      console.log(`Role ${role}:`, hasRole ? '✓ HAS ROLE' : '✗ NO ROLE');
    }
  } catch (error) {
    console.error('Role check failed:', error.message);
    throw error;
  }
}

/**
 * Complete Authentication Flow Example
 */
export async function completeAuthFlowExample(authService: AuthService) {
  console.log('\n=== Complete Authentication Flow Example ===');
  
  try {
    // Step 1: Generate token
    console.log('Step 1: Generating token...');
    const tokenResult = await generateTokenExample(authService);
    
    // Step 2: Validate token
    console.log('\nStep 2: Validating token...');
    const authContext = await validateTokenExample(authService, tokenResult.accessToken);
    
    // Step 3: Check permissions
    console.log('\nStep 3: Checking permissions...');
    await permissionCheckExample(authService, tokenResult.accessToken);
    
    // Step 4: Check roles
    console.log('\nStep 4: Checking roles...');
    await roleCheckExample(authService, tokenResult.accessToken);
    
    // Step 5: Refresh token
    console.log('\nStep 5: Refreshing token...');
    const newTokenResult = await refreshTokenExample(authService, tokenResult.refreshToken);
    
    // Step 6: Blacklist old token
    console.log('\nStep 6: Blacklisting old token...');
    await blacklistTokenExample(authService, tokenResult.accessToken);
    
    console.log('\n✓ Complete authentication flow completed successfully!');
    
    return newTokenResult;
  } catch (error) {
    console.error('Authentication flow failed:', error.message);
    throw error;
  }
}

/**
 * Usage Example
 */
export async function runAuthExamples() {
  // Note: In a real application, you would inject AuthService through DI
  // This is just for demonstration purposes
  
  console.log('JWT Authentication Examples');
  console.log('==========================');
  
  // You would get authService from your NestJS application context
  // const authService = app.get(AuthService);
  
  // For this example, we'll show the structure
  console.log('To run these examples:');
  console.log('1. Get AuthService from your NestJS application');
  console.log('2. Call the example functions with the service instance');
  console.log('3. Handle errors appropriately in your application');
  
  // Example usage:
  // try {
  //   await completeAuthFlowExample(authService);
  // } catch (error) {
  //   console.error('Example failed:', error);
  // }
}

// Export all examples for easy import
export {
  generateTokenExample,
  validateTokenExample,
  refreshTokenExample,
  blacklistTokenExample,
  permissionCheckExample,
  roleCheckExample,
  completeAuthFlowExample,
};
