/**
 * Rate Limiting Examples
 * 
 * This example demonstrates various rate limiting strategies and configurations
 * available in the gateway service.
 */

import { RateLimitService, RateLimitConfig } from '../../src/modules/rate-limiting/services/rate-limit.service';

/**
 * Example 1: Basic Rate Limiting
 */
export async function basicRateLimitExample(rateLimitService: RateLimitService) {
  console.log('=== Basic Rate Limiting Example ===');
  
  const config: RateLimitConfig = {
    windowMs: 60000, // 1 minute window
    max: 10, // 10 requests per minute
    strategy: 'sliding-window',
  };
  
  const userId = 'user-123';
  const key = rateLimitService.generateUserKey(userId, '/api/users');
  
  console.log(`Testing rate limit for user ${userId}`);
  console.log(`Config: ${config.max} requests per ${config.windowMs}ms`);
  
  // Simulate multiple requests
  for (let i = 1; i <= 12; i++) {
    try {
      const result = await rateLimitService.checkRateLimit(key, config);
      
      console.log(`Request ${i}:`, {
        allowed: result.allowed,
        remaining: result.remaining,
        totalHits: result.totalHits,
        resetTime: result.resetTime,
      });
      
      if (!result.allowed) {
        console.log(`  ⚠️  Rate limit exceeded! Retry after: ${result.retryAfter}ms`);
      }
    } catch (error) {
      console.error(`Request ${i} failed:`, error.message);
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 100));
  }
}

/**
 * Example 2: Different Rate Limiting Strategies
 */
export async function strategyComparisonExample(rateLimitService: RateLimitService) {
  console.log('\n=== Rate Limiting Strategy Comparison ===');
  
  const strategies: RateLimitConfig[] = [
    {
      windowMs: 10000, // 10 seconds
      max: 5,
      strategy: 'sliding-window',
    },
    {
      windowMs: 10000,
      max: 5,
      strategy: 'fixed-window',
    },
    {
      windowMs: 10000,
      max: 5,
      strategy: 'token-bucket',
      capacity: 5,
      refillRate: 1,
      refillPeriod: 2000, // 1 token every 2 seconds
    },
    {
      windowMs: 10000,
      max: 5,
      strategy: 'leaky-bucket',
      capacity: 5,
      leakRate: 1,
      leakPeriod: 2000, // Leak 1 request every 2 seconds
    },
  ];
  
  for (const config of strategies) {
    console.log(`\n--- Testing ${config.strategy} strategy ---`);
    
    const key = rateLimitService.generateUserKey('test-user', `/test/${config.strategy}`);
    
    // Send 7 requests quickly
    for (let i = 1; i <= 7; i++) {
      const result = await rateLimitService.checkRateLimit(key, config);
      console.log(`Request ${i}: ${result.allowed ? '✓ ALLOWED' : '✗ BLOCKED'} (remaining: ${result.remaining})`);
      
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    // Clear rate limit for next test
    await rateLimitService.clearRateLimit(key);
  }
}

/**
 * Example 3: Multi-dimensional Rate Limiting
 */
export async function multiDimensionalExample(rateLimitService: RateLimitService) {
  console.log('\n=== Multi-dimensional Rate Limiting Example ===');
  
  const userId = 'user-456';
  const ipAddress = '*************';
  const endpoint = '/api/sensitive-operation';
  
  // Different rate limits for different dimensions
  const configs = {
    user: { windowMs: 60000, max: 20, strategy: 'sliding-window' as const },
    ip: { windowMs: 60000, max: 50, strategy: 'sliding-window' as const },
    api: { windowMs: 60000, max: 100, strategy: 'sliding-window' as const },
    global: { windowMs: 60000, max: 1000, strategy: 'sliding-window' as const },
  };
  
  const keys = {
    user: rateLimitService.generateUserKey(userId, endpoint),
    ip: rateLimitService.generateIpKey(ipAddress, endpoint),
    api: rateLimitService.generateApiKey(endpoint),
    global: rateLimitService.generateGlobalKey(),
  };
  
  console.log('Checking rate limits across multiple dimensions:');
  
  // Check all dimensions
  for (const [dimension, key] of Object.entries(keys)) {
    const config = configs[dimension];
    const result = await rateLimitService.checkRateLimit(key, config);
    
    console.log(`${dimension.toUpperCase()} limit:`, {
      allowed: result.allowed,
      remaining: result.remaining,
      limit: result.limit,
    });
  }
  
  // Simulate hitting user limit
  console.log('\nSimulating user rate limit breach:');
  const userKey = keys.user;
  const userConfig = configs.user;
  
  for (let i = 1; i <= userConfig.max + 2; i++) {
    const result = await rateLimitService.checkRateLimit(userKey, userConfig);
    
    if (i <= userConfig.max) {
      console.log(`Request ${i}: ✓ ALLOWED (remaining: ${result.remaining})`);
    } else {
      console.log(`Request ${i}: ✗ BLOCKED - User rate limit exceeded`);
    }
  }
}

/**
 * Example 4: Dynamic Rate Limiting Based on User Level
 */
export async function dynamicRateLimitExample(rateLimitService: RateLimitService) {
  console.log('\n=== Dynamic Rate Limiting Example ===');
  
  const users = [
    { id: 'basic-user', level: 1, type: 'basic' },
    { id: 'premium-user', level: 5, type: 'premium' },
    { id: 'vip-user', level: 10, type: 'vip' },
  ];
  
  const baseConfig: RateLimitConfig = {
    windowMs: 60000,
    max: 10, // Base limit
    strategy: 'sliding-window',
  };
  
  for (const user of users) {
    console.log(`\n--- Rate limiting for ${user.type} user (level ${user.level}) ---`);
    
    // Calculate dynamic limit based on user level
    const multiplier = Math.min(user.level / 2 + 1, 5); // Max 5x multiplier
    const dynamicConfig: RateLimitConfig = {
      ...baseConfig,
      max: Math.floor(baseConfig.max * multiplier),
    };
    
    const key = rateLimitService.generateUserKey(user.id, '/api/game-action');
    
    console.log(`Base limit: ${baseConfig.max}, Dynamic limit: ${dynamicConfig.max} (${multiplier}x multiplier)`);
    
    // Test the dynamic limit
    for (let i = 1; i <= dynamicConfig.max + 2; i++) {
      const result = await rateLimitService.checkRateLimit(key, dynamicConfig);
      
      if (result.allowed) {
        console.log(`Request ${i}: ✓ ALLOWED (remaining: ${result.remaining})`);
      } else {
        console.log(`Request ${i}: ✗ BLOCKED - Rate limit exceeded`);
        break;
      }
    }
    
    // Clear for next user
    await rateLimitService.clearRateLimit(key);
  }
}

/**
 * Example 5: Rate Limiting with Custom Headers
 */
export async function customHeadersExample(rateLimitService: RateLimitService) {
  console.log('\n=== Rate Limiting with Custom Headers Example ===');
  
  const config: RateLimitConfig = {
    windowMs: 30000, // 30 seconds
    max: 5,
    strategy: 'sliding-window',
  };
  
  const key = rateLimitService.generateUserKey('header-test-user', '/api/test');
  
  console.log('Simulating HTTP requests with rate limit headers:');
  
  for (let i = 1; i <= 7; i++) {
    const result = await rateLimitService.checkRateLimit(key, config);
    
    // Simulate setting HTTP headers (as would be done in middleware)
    const headers = {
      'X-RateLimit-Limit': result.limit.toString(),
      'X-RateLimit-Remaining': result.remaining.toString(),
      'X-RateLimit-Reset': Math.ceil(result.resetTime.getTime() / 1000).toString(),
    };
    
    if (!result.allowed && result.retryAfter) {
      headers['Retry-After'] = Math.ceil(result.retryAfter / 1000).toString();
    }
    
    console.log(`Request ${i}:`, {
      status: result.allowed ? '200 OK' : '429 Too Many Requests',
      headers,
    });
    
    await new Promise(resolve => setTimeout(resolve, 100));
  }
}

/**
 * Example 6: Rate Limiting Cleanup and Management
 */
export async function managementExample(rateLimitService: RateLimitService) {
  console.log('\n=== Rate Limiting Management Example ===');
  
  const config: RateLimitConfig = {
    windowMs: 10000,
    max: 3,
    strategy: 'sliding-window',
  };
  
  const testKey = rateLimitService.generateUserKey('management-test', '/api/test');
  
  // Create some rate limit data
  console.log('Creating rate limit data...');
  for (let i = 1; i <= 3; i++) {
    await rateLimitService.checkRateLimit(testKey, config);
    console.log(`Request ${i} processed`);
  }
  
  // Check current status
  const result = await rateLimitService.checkRateLimit(testKey, config);
  console.log('Current status:', {
    allowed: result.allowed,
    remaining: result.remaining,
    totalHits: result.totalHits,
  });
  
  // Clear rate limit
  console.log('\nClearing rate limit data...');
  await rateLimitService.clearRateLimit(testKey);
  
  // Verify cleared
  const clearedResult = await rateLimitService.checkRateLimit(testKey, config);
  console.log('After clearing:', {
    allowed: clearedResult.allowed,
    remaining: clearedResult.remaining,
    totalHits: clearedResult.totalHits,
  });
}

/**
 * Complete Rate Limiting Demo
 */
export async function completeRateLimitDemo(rateLimitService: RateLimitService) {
  console.log('Rate Limiting Examples Demo');
  console.log('===========================');
  
  try {
    await basicRateLimitExample(rateLimitService);
    await strategyComparisonExample(rateLimitService);
    await multiDimensionalExample(rateLimitService);
    await dynamicRateLimitExample(rateLimitService);
    await customHeadersExample(rateLimitService);
    await managementExample(rateLimitService);
    
    console.log('\n✓ All rate limiting examples completed successfully!');
  } catch (error) {
    console.error('Rate limiting demo failed:', error);
  }
}

/**
 * Usage Example
 */
export async function runRateLimitExamples() {
  console.log('Rate Limiting Examples');
  console.log('======================');
  
  console.log('To run these examples:');
  console.log('1. Get RateLimitService from your NestJS application');
  console.log('2. Ensure Redis is running and configured');
  console.log('3. Call the example functions with the service instance');
  
  // Example usage:
  // const rateLimitService = app.get(RateLimitService);
  // await completeRateLimitDemo(rateLimitService);
}

// Export all examples
export {
  basicRateLimitExample,
  strategyComparisonExample,
  multiDimensionalExample,
  dynamicRateLimitExample,
  customHeadersExample,
  managementExample,
  completeRateLimitDemo,
};
